{"name": "@taptap/shared", "version": "1.0.0", "description": "Shared components and utilities for TapTap multi-platform app", "type": "module", "main": "index.ts", "types": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext ts,tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "zustand": "^4.5.0"}, "optionalDependencies": {"zmp-sdk": "^2.46.5", "zmp-ui": "^1.11.11"}, "devDependencies": {"@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "typescript": "^5.5.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}