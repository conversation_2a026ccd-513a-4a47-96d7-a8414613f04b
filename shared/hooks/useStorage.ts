import { useState, useEffect } from 'react';
import { storageService } from '../services/storage';

export const useStorage = <T>(key: string, defaultValue: T) => {
  const [value, setValue] = useState<T>(defaultValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadValue = async () => {
      try {
        setLoading(true);
        setError(null);
        const storedValue = await storageService.getItem(key);
        if (storedValue !== null) {
          setValue(storedValue);
        }
      } catch (err) {
        console.error('Failed to load from storage:', err);
        setError(err instanceof Error ? err.message : 'Failed to load from storage');
      } finally {
        setLoading(false);
      }
    };

    loadValue();
  }, [key]);

  const updateValue = async (newValue: T) => {
    try {
      setError(null);
      setValue(newValue);
      await storageService.setItem(key, newValue);
    } catch (err) {
      console.error('Failed to save to storage:', err);
      setError(err instanceof Error ? err.message : 'Failed to save to storage');
      // Rollback on error
      setValue(value);
    }
  };

  const removeValue = async () => {
    try {
      setError(null);
      setValue(defaultValue);
      await storageService.removeItem(key);
    } catch (err) {
      console.error('Failed to remove from storage:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove from storage');
    }
  };

  return {
    value,
    setValue: updateValue,
    removeValue,
    loading,
    error
  };
};
