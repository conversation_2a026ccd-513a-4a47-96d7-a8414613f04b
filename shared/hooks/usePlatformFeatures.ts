import { useState, useEffect } from 'react';
import { isZaloMiniApp } from '../utils/platform';
import { ZMPService } from '../services/zalo/zmp-sdk';

export const usePlatformFeatures = () => {
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    accuracy: number;
  } | null>(null);
  const [canUseZaloFeatures, setCanUseZaloFeatures] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setCanUseZaloFeatures(isZaloMiniApp());
  }, []);

  const getLocation = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (isZaloMiniApp()) {
        const loc = await ZMPService.getLocation();
        setLocation(loc);
        return loc;
      } else {
        // Web geolocation API
        return new Promise<{ latitude: number; longitude: number; accuracy: number }>((resolve, reject) => {
          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
              (position) => {
                const loc = {
                  latitude: position.coords.latitude,
                  longitude: position.coords.longitude,
                  accuracy: position.coords.accuracy
                };
                setLocation(loc);
                resolve(loc);
              },
              (err) => {
                setError(err.message);
                reject(err);
              }
            );
          } else {
            const err = new Error('Geolocation not supported');
            setError(err.message);
            reject(err);
          }
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get location';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const shareContent = async (content: {
    title: string;
    description: string;
    thumbnail: string;
    url: string;
  }) => {
    try {
      setError(null);
      
      if (isZaloMiniApp()) {
        await ZMPService.shareToChat(content);
      } else if (navigator.share) {
        // Web Share API
        await navigator.share({
          title: content.title,
          text: content.description,
          url: content.url,
        });
      } else {
        // Fallback sharing options
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(content.url)}`;
        window.open(shareUrl, '_blank');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to share content';
      setError(errorMessage);
      throw err;
    }
  };

  const makePayment = async (orderData: {
    amount: number;
    description: string;
    orderId: string;
    extraData?: Record<string, any>;
  }) => {
    try {
      setError(null);
      
      if (isZaloMiniApp()) {
        return await ZMPService.createPayment(orderData);
      } else {
        // Redirect to web payment gateway
        const paymentUrl = `/payment?orderId=${orderData.orderId}&amount=${orderData.amount}`;
        window.location.href = paymentUrl;
        return { success: true, transactionId: null };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process payment';
      setError(errorMessage);
      throw err;
    }
  };

  const showToast = async (message: string) => {
    try {
      setError(null);
      await ZMPService.showToast(message);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to show toast';
      setError(errorMessage);
      console.error('Toast error:', errorMessage);
    }
  };

  const showModal = async (params: {
    title: string;
    content: string;
    showCancel?: boolean;
    cancelText?: string;
    confirmText?: string;
  }) => {
    try {
      setError(null);
      return await ZMPService.showModal(params);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to show modal';
      setError(errorMessage);
      throw err;
    }
  };

  return {
    location,
    canUseZaloFeatures,
    loading,
    error,
    getLocation,
    shareContent,
    makePayment,
    showToast,
    showModal
  };
};
