{"compilerOptions": {"composite": true, "declaration": true, "declarationMap": true, "noEmit": false, "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "moduleDetection": "force", "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "outDir": "dist", "rootDir": ".", "baseUrl": ".", "paths": {}}, "include": ["**/*"], "exclude": ["node_modules", "dist"]}