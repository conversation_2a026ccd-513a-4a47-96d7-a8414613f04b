import { detectPlatform, Platform } from '../../utils/platform';
import { WebStorageService } from './storage.web';
import { ZaloStorageService } from './storage.zalo';
import { StorageService } from './storage.web';

// Storage Service Factory
class StorageServiceFactory {
  private static instance: StorageService;

  static getInstance(): StorageService {
    if (!this.instance) {
      const platform = detectPlatform();
      this.instance = platform === Platform.ZALO 
        ? new ZaloStorageService() 
        : new WebStorageService();
    }
    return this.instance;
  }
}

export const storageService = StorageServiceFactory.getInstance();
export * from './storage.web';
export { ZaloStorageService } from './storage.zalo';
