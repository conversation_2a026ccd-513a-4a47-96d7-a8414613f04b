import { StorageService } from './storage.web';

// Zalo Storage Implementation
export class ZaloStorageService implements StorageService {
  async setItem(key: string, value: any): Promise<void> {
    try {
      // Use dynamic import to avoid issues when zmp-sdk is not available
      const { setStorage } = await import('zmp-sdk');
      await setStorage({ key, data: value });
    } catch (error) {
      console.error('Zalo storage setItem failed:', error);
      // Fallback to localStorage if zmp-sdk is not available
      try {
        localStorage.setItem(key, JSON.stringify(value));
      } catch (fallbackError) {
        throw error;
      }
    }
  }

  async getItem(key: string): Promise<any> {
    try {
      const { getStorage } = await import('zmp-sdk');
      const result = await getStorage({ key });
      return result.data;
    } catch (error) {
      console.error('Zalo storage getItem failed:', error);
      // Fallback to localStorage if zmp-sdk is not available
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      } catch (fallbackError) {
        return null;
      }
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      const { setStorage } = await import('zmp-sdk');
      await setStorage({ key, data: null });
    } catch (error) {
      console.error('Zalo storage removeItem failed:', error);
      // Fallback to localStorage
      try {
        localStorage.removeItem(key);
      } catch (fallbackError) {
        throw error;
      }
    }
  }

  async clear(): Promise<void> {
    try {
      const { clearStorage } = await import('zmp-sdk');
      await clearStorage();
    } catch (error) {
      console.error('Zalo storage clear failed:', error);
      // Fallback to localStorage
      try {
        localStorage.clear();
      } catch (fallbackError) {
        throw error;
      }
    }
  }
}
