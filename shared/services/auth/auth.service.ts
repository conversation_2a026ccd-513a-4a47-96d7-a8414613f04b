import { User, LoginRequest, RegisterRequest } from '../../types';

// Common Auth Service Interface
export interface AuthService {
  login(credentials?: LoginRequest): Promise<User>;
  logout(): Promise<void>;
  getCurrentUser(): Promise<User | null>;
  register(userData: RegisterRequest): Promise<User>;
  refreshToken(): Promise<string>;
}

// Base Auth Service with common functionality
export abstract class BaseAuthService implements AuthService {
  protected currentUser: User | null = null;
  protected token: string | null = null;

  abstract login(credentials?: LoginRequest): Promise<User>;
  abstract logout(): Promise<void>;
  abstract register(userData: RegisterRequest): Promise<User>;
  abstract refreshToken(): Promise<string>;

  async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) {
      return this.currentUser;
    }

    // Try to load from storage
    try {
      const { storageService } = await import('../storage');
      const userData = await storageService.getItem('user_profile');
      const token = await storageService.getItem('user_token');
      
      if (userData && token) {
        this.currentUser = userData;
        this.token = token;
        return this.currentUser;
      }
    } catch (error) {
      console.error('Failed to load user from storage:', error);
    }

    return null;
  }

  protected async saveUserData(user: User, token: string): Promise<void> {
    try {
      const { storageService } = await import('../storage');
      await storageService.setItem('user_profile', user);
      await storageService.setItem('user_token', token);
      this.currentUser = user;
      this.token = token;
    } catch (error) {
      console.error('Failed to save user data:', error);
      throw error;
    }
  }

  protected async clearUserData(): Promise<void> {
    try {
      const { storageService } = await import('../storage');
      await storageService.removeItem('user_profile');
      await storageService.removeItem('user_token');
      this.currentUser = null;
      this.token = null;
    } catch (error) {
      console.error('Failed to clear user data:', error);
      throw error;
    }
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null && this.token !== null;
  }
}
