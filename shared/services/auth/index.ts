import { detectPlatform, Platform } from '../../utils/platform';
import { WebAuthService } from './auth.web';
import { ZaloAuthService } from './auth.zalo';
import { AuthService } from './auth.service';

// Auth Service Factory
class AuthServiceFactory {
  private static instance: AuthService;

  static getInstance(): AuthService {
    if (!this.instance) {
      const platform = detectPlatform();
      this.instance = platform === Platform.ZALO 
        ? new ZaloAuthService() 
        : new WebAuthService();
    }
    return this.instance;
  }

  static resetInstance(): void {
    this.instance = undefined as any;
  }
}

export const authService = AuthServiceFactory.getInstance();

// Export all auth-related types and classes
export * from './auth.service';
export { WebAuthService } from './auth.web';
export { ZaloAuthService } from './auth.zalo';
export { AuthServiceFactory };
