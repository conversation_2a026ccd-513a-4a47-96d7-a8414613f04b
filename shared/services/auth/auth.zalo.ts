import { BaseAuthService } from './auth.service';
import { User, LoginRequest, RegisterRequest } from '../../types';
import { ZMPService } from '../zalo/zmp-sdk';
import { API_CONFIG } from '../../constants/config';

export class ZaloAuthService extends BaseAuthService {
  private apiBaseUrl = API_CONFIG.baseURL;

  async login(_credentials?: LoginRequest): Promise<User> {
    try {
      // Zalo Mini App login flow
      const accessToken = await ZMPService.getAccessToken();
      const userInfo = await ZMPService.getUserInfo();
      
      // Send to backend for verification and user creation/login
      const response = await fetch(`${this.apiBaseUrl}/auth/zalo-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessToken,
          userInfo,
          platform: 'zalo'
        }),
      });

      if (!response.ok) {
        throw new Error(`Zalo login failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const user: User = {
          id: data.data.user.id,
          name: data.data.user.name,
          avatar: data.data.user.avatar,
          phone: data.data.user.phone,
          email: data.data.user.email,
          createdAt: new Date(data.data.user.createdAt),
          updatedAt: new Date(data.data.user.updatedAt),
        };
        
        const token = data.data.token;
        await this.saveUserData(user, token);
        return user;
      } else {
        throw new Error(data.message || 'Zalo login failed');
      }
    } catch (error) {
      console.error('Zalo login failed:', error);
      throw error;
    }
  }

  async register(userData: RegisterRequest): Promise<User> {
    try {
      // For Zalo Mini App, registration is usually handled during login
      // But we can still support additional profile completion
      const accessToken = await ZMPService.getAccessToken();
      const userInfo = await ZMPService.getUserInfo();
      
      const response = await fetch(`${this.apiBaseUrl}/auth/zalo-register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessToken,
          userInfo,
          additionalData: userData,
          platform: 'zalo'
        }),
      });

      if (!response.ok) {
        throw new Error(`Zalo registration failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const user: User = {
          id: data.data.user.id,
          name: data.data.user.name,
          avatar: data.data.user.avatar,
          phone: data.data.user.phone,
          email: data.data.user.email,
          createdAt: new Date(data.data.user.createdAt),
          updatedAt: new Date(data.data.user.updatedAt),
        };
        
        const token = data.data.token;
        await this.saveUserData(user, token);
        return user;
      } else {
        throw new Error(data.message || 'Zalo registration failed');
      }
    } catch (error) {
      console.error('Zalo registration failed:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.token) {
        await fetch(`${this.apiBaseUrl}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Zalo logout API call failed:', error);
    } finally {
      // Always clear local data
      await this.clearUserData();
    }
  }

  async refreshToken(): Promise<string> {
    try {
      // For Zalo Mini App, we can use the access token to refresh
      const accessToken = await ZMPService.getAccessToken();
      
      const response = await fetch(`${this.apiBaseUrl}/auth/zalo-refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accessToken }),
      });

      if (!response.ok) {
        throw new Error(`Zalo token refresh failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const newToken = data.data.token;
        const { storageService } = await import('../storage');
        await storageService.setItem('user_token', newToken);
        this.token = newToken;
        return newToken;
      } else {
        throw new Error(data.message || 'Zalo token refresh failed');
      }
    } catch (error) {
      console.error('Zalo token refresh failed:', error);
      // If refresh fails, clear user data
      await this.clearUserData();
      throw error;
    }
  }

  // Zalo-specific methods
  async getPhoneNumber(): Promise<string> {
    try {
      return await ZMPService.getPhoneNumber();
    } catch (error) {
      console.error('Failed to get phone number:', error);
      throw error;
    }
  }

  async followOfficialAccount(oaId: string): Promise<void> {
    try {
      await ZMPService.followOA(oaId);
    } catch (error) {
      console.error('Failed to follow OA:', error);
      throw error;
    }
  }

  async shareToChat(content: {
    title: string;
    description: string;
    thumbnail: string;
    url: string;
  }): Promise<void> {
    try {
      await ZMPService.shareToChat(content);
    } catch (error) {
      console.error('Failed to share to chat:', error);
      throw error;
    }
  }

  async openChat(userId: string): Promise<void> {
    try {
      await ZMPService.openChat(userId);
    } catch (error) {
      console.error('Failed to open chat:', error);
      throw error;
    }
  }
}
