import { BaseAuthService } from './auth.service';
import { User, LoginRequest, RegisterRequest } from '../../types';
import { API_CONFIG } from '../../constants/config';

export class WebAuthService extends BaseAuthService {
  private apiBaseUrl = API_CONFIG.baseURL;

  async login(credentials: LoginRequest): Promise<User> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...credentials,
          platform: 'web'
        }),
      });

      if (!response.ok) {
        throw new Error(`Login failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const user = data.data.user;
        const token = data.data.token;
        
        await this.saveUserData(user, token);
        return user;
      } else {
        throw new Error(data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Web login failed:', error);
      throw error;
    }
  }

  async register(userData: RegisterRequest): Promise<User> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...userData,
          platform: 'web'
        }),
      });

      if (!response.ok) {
        throw new Error(`Registration failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const user = data.data.user;
        const token = data.data.token;
        
        await this.saveUserData(user, token);
        return user;
      } else {
        throw new Error(data.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Web registration failed:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.token) {
        await fetch(`${this.apiBaseUrl}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Web logout API call failed:', error);
    } finally {
      // Always clear local data
      await this.clearUserData();
    }
  }

  async refreshToken(): Promise<string> {
    try {
      const { storageService } = await import('../storage');
      const refreshToken = await storageService.getItem('refresh_token');
      
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${this.apiBaseUrl}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        const newToken = data.data.token;
        await storageService.setItem('user_token', newToken);
        this.token = newToken;
        return newToken;
      } else {
        throw new Error(data.message || 'Token refresh failed');
      }
    } catch (error) {
      console.error('Web token refresh failed:', error);
      // If refresh fails, clear user data
      await this.clearUserData();
      throw error;
    }
  }

  // Web-specific methods
  async loginWithGoogle(): Promise<User> {
    // Implementation would depend on Google OAuth setup
    throw new Error('Google login not implemented yet');
  }

  async loginWithFacebook(): Promise<User> {
    // Implementation would depend on Facebook OAuth setup
    throw new Error('Facebook login not implemented yet');
  }

  async forgotPassword(email: string): Promise<void> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        throw new Error(`Forgot password failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Forgot password failed');
      }
    } catch (error) {
      console.error('Web forgot password failed:', error);
      throw error;
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, newPassword }),
      });

      if (!response.ok) {
        throw new Error(`Reset password failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Reset password failed');
      }
    } catch (error) {
      console.error('Web reset password failed:', error);
      throw error;
    }
  }
}
