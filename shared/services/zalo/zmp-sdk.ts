// ZMP Service - Wrapper for Zalo Mini App SDK
export class ZMPService {
  private static isZaloEnvironment(): boolean {
    return typeof window !== 'undefined' && 
           (window.ZaloJavaScriptInterface || 
            navigator.userAgent.includes('Zalo') ||
            window.zmp);
  }

  // Authentication
  static async getAccessToken(): Promise<string> {
    if (!this.isZaloEnvironment()) {
      console.warn('getAccessToken called outside Zalo environment');
      return 'DEFAULT_ACCESS_TOKEN';
    }
    
    try {
      const { getAccessToken } = await import('zmp-sdk');
      return await getAccessToken();
    } catch (error) {
      console.error('Failed to get access token:', error);
      throw error;
    }
  }

  static async getUserInfo() {
    if (!this.isZaloEnvironment()) {
      console.warn('getUserInfo called outside Zalo environment');
      return {
        id: 'test-user',
        name: 'Test User',
        avatar: 'https://via.placeholder.com/150',
        phone: '+84123456789'
      };
    }
    
    try {
      const { getUserInfo } = await import('zmp-sdk');
      return await getUserInfo();
    } catch (error) {
      console.error('Failed to get user info:', error);
      throw error;
    }
  }

  static async getPhoneNumber(): Promise<string> {
    if (!this.isZaloEnvironment()) {
      console.warn('getPhoneNumber called outside Zalo environment');
      return '+84123456789';
    }
    
    try {
      const { getPhoneNumber } = await import('zmp-sdk');
      return await getPhoneNumber();
    } catch (error) {
      console.error('Failed to get phone number:', error);
      throw error;
    }
  }

  // Location Services
  static async getLocation() {
    if (!this.isZaloEnvironment()) {
      console.warn('getLocation called outside Zalo environment');
      // Return mock location for development
      return {
        latitude: 21.0285,
        longitude: 105.8542,
        accuracy: 10
      };
    }
    
    try {
      const { getLocation } = await import('zmp-sdk');
      return await getLocation();
    } catch (error) {
      console.error('Failed to get location:', error);
      throw error;
    }
  }

  // Social Features
  static async openChat(userId: string) {
    if (!this.isZaloEnvironment()) {
      console.warn('openChat called outside Zalo environment');
      return;
    }
    
    try {
      const { openChat } = await import('zmp-sdk');
      return await openChat({ userId });
    } catch (error) {
      console.error('Failed to open chat:', error);
      throw error;
    }
  }

  static async followOA(oaId: string) {
    if (!this.isZaloEnvironment()) {
      console.warn('followOA called outside Zalo environment');
      return;
    }
    
    try {
      const { followOA } = await import('zmp-sdk');
      return await followOA({ oaId });
    } catch (error) {
      console.error('Failed to follow OA:', error);
      throw error;
    }
  }

  static async shareToChat(params: {
    title: string;
    description: string;
    thumbnail: string;
    url: string;
  }) {
    if (!this.isZaloEnvironment()) {
      console.warn('shareToChat called outside Zalo environment');
      return;
    }
    
    try {
      const { shareToChat } = await import('zmp-sdk');
      return await shareToChat(params);
    } catch (error) {
      console.error('Failed to share to chat:', error);
      throw error;
    }
  }

  // Payment
  static async createPayment(order: {
    amount: number;
    description: string;
    orderId: string;
    extraData?: Record<string, any>;
  }) {
    if (!this.isZaloEnvironment()) {
      console.warn('createPayment called outside Zalo environment');
      return {
        success: false,
        error: 'Payment not supported outside Zalo environment'
      };
    }
    
    try {
      const { payment } = await import('zmp-sdk');
      return await payment(order);
    } catch (error) {
      console.error('Failed to create payment:', error);
      throw error;
    }
  }

  // Storage
  static async setStorage(key: string, data: any) {
    if (!this.isZaloEnvironment()) {
      console.warn('setStorage called outside Zalo environment, using localStorage');
      localStorage.setItem(key, JSON.stringify(data));
      return;
    }
    
    try {
      const { setStorage } = await import('zmp-sdk');
      return await setStorage({ key, data });
    } catch (error) {
      console.error('Failed to set storage:', error);
      throw error;
    }
  }

  static async getStorage(key: string) {
    if (!this.isZaloEnvironment()) {
      console.warn('getStorage called outside Zalo environment, using localStorage');
      const item = localStorage.getItem(key);
      return { data: item ? JSON.parse(item) : null };
    }
    
    try {
      const { getStorage } = await import('zmp-sdk');
      return await getStorage({ key });
    } catch (error) {
      console.error('Failed to get storage:', error);
      throw error;
    }
  }

  static async clearStorage() {
    if (!this.isZaloEnvironment()) {
      console.warn('clearStorage called outside Zalo environment, using localStorage');
      localStorage.clear();
      return;
    }
    
    try {
      const { clearStorage } = await import('zmp-sdk');
      return await clearStorage();
    } catch (error) {
      console.error('Failed to clear storage:', error);
      throw error;
    }
  }

  // UI
  static async setTitle(title: string) {
    if (!this.isZaloEnvironment()) {
      console.warn('setTitle called outside Zalo environment');
      document.title = title;
      return;
    }
    
    try {
      const { setTitle } = await import('zmp-sdk');
      return await setTitle(title);
    } catch (error) {
      console.error('Failed to set title:', error);
      throw error;
    }
  }

  static async showToast(message: string) {
    if (!this.isZaloEnvironment()) {
      console.warn('showToast called outside Zalo environment');
      console.log('Toast:', message);
      return;
    }
    
    try {
      const { showToast } = await import('zmp-sdk');
      return await showToast(message);
    } catch (error) {
      console.error('Failed to show toast:', error);
      throw error;
    }
  }

  static async showModal(params: {
    title: string;
    content: string;
    showCancel?: boolean;
    cancelText?: string;
    confirmText?: string;
  }) {
    if (!this.isZaloEnvironment()) {
      console.warn('showModal called outside Zalo environment');
      return { confirm: window.confirm(`${params.title}\n${params.content}`) };
    }
    
    try {
      const { showModal } = await import('zmp-sdk');
      return await showModal(params);
    } catch (error) {
      console.error('Failed to show modal:', error);
      throw error;
    }
  }
}
