// Platform Detection
export enum Platform {
  WEB = 'web',
  ZALO = 'zalo'
}

// Extend Window interface for Zalo Mini App
declare global {
  interface Window {
    ZaloJavaScriptInterface?: any;
    zmp?: any;
  }
}

export const detectPlatform = (): Platform => {
  if (typeof window === 'undefined') return Platform.WEB;
  
  // Check if running in Zalo environment
  if (window.ZaloJavaScriptInterface || 
      navigator.userAgent.includes('Zalo') ||
      // Additional checks for Zalo Mini App environment
      window.zmp ||
      window.ZaloJavaScriptInterface) {
    return Platform.ZALO;
  }
  
  return Platform.WEB;
};

export const isZaloMiniApp = (): boolean => detectPlatform() === Platform.ZALO;
export const isWebApp = (): boolean => detectPlatform() === Platform.WEB;

// Platform-specific utilities
export const getPlatformConfig = () => {
  const platform = detectPlatform();
  
  return {
    platform,
    isZalo: platform === Platform.ZALO,
    isWeb: platform === Platform.WEB,
    maxFileSize: platform === Platform.ZALO ? 3 * 1024 * 1024 : 10 * 1024 * 1024, // 3MB for Zalo, 10MB for Web
    supportedFeatures: {
      geolocation: true,
      camera: platform === Platform.ZALO,
      payment: platform === Platform.ZALO,
      nativeStorage: platform === Platform.ZALO,
      webShare: platform === Platform.WEB,
      pushNotifications: platform === Platform.WEB
    }
  };
};

// Environment detection for development
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};
