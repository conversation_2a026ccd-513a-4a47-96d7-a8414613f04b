import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Input } from './Input';

describe('Input Component', () => {
  // Test cơ bản
  describe('Basic Functionality', () => {
    it('renders correctly', () => {
      render(<Input placeholder="Enter text" />);
      const input = screen.getByPlaceholderText('Enter text');
      expect(input).toBeInTheDocument();
    });

    it('handles text input correctly', () => {
      render(<Input placeholder="Enter text" />);
      const input = screen.getByPlaceholderText('Enter text') as HTMLInputElement;
      
      fireEvent.change(input, { target: { value: 'Test value' } });
      expect(input.value).toBe('Test value');
    });

    it('renders with custom className', () => {
      render(<Input className="custom-class" placeholder="Enter text" />);
      const input = screen.getByPlaceholderText('Enter text');
      expect(input).toHaveClass('custom-class');
    });

    it('forwards ref correctly', () => {
      const ref = jest.fn();
      render(<Input placeholder="Enter text" ref={ref} />);
      expect(ref).toHaveBeenCalled();
    });
  });

  // Test variants
  describe('Variants', () => {
    it('renders normal variant correctly', () => {
      render(<Input variant="normal" placeholder="Normal input" />);
      const input = screen.getByPlaceholderText('Normal input');
      expect(input).toHaveClass('border-grey-3');
    });

    it('renders error variant correctly', () => {
      render(<Input variant="error" placeholder="Error input" />);
      const input = screen.getByPlaceholderText('Error input');
      expect(input).toHaveClass('border-red-500');
    });

    it('renders success variant correctly', () => {
      render(<Input variant="success" placeholder="Success input" />);
      const input = screen.getByPlaceholderText('Success input');
      expect(input).toHaveClass('border-green-500');
    });

    it('renders error state when error prop is provided regardless of variant', () => {
      render(
        <Input 
          variant="normal" 
          error="This field is required" 
          placeholder="Input with error" 
        />
      );
      const input = screen.getByPlaceholderText('Input with error');
      expect(input).toHaveClass('border-red-500');
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });
  });

  // Test label and helper text
  describe('Label and Helper Text', () => {
    it('renders with label correctly', () => {
      render(<Input label="Username" placeholder="Enter username" />);
      expect(screen.getByText('Username')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter username')).toBeInTheDocument();
    });

    it('renders with helper text correctly', () => {
      render(<Input helperText="Must be at least 8 characters" placeholder="Enter password" />);
      expect(screen.getByText('Must be at least 8 characters')).toBeInTheDocument();
    });

    it('renders with both label and helper text', () => {
      render(
        <Input 
          label="Password" 
          helperText="Must be at least 8 characters" 
          placeholder="Enter password" 
        />
      );
      expect(screen.getByText('Password')).toBeInTheDocument();
      expect(screen.getByText('Must be at least 8 characters')).toBeInTheDocument();
    });
  });

  // Test states
  describe('States', () => {
    it('renders disabled state correctly', () => {
      render(<Input disabled placeholder="Disabled input" />);
      const input = screen.getByPlaceholderText('Disabled input');
      expect(input).toBeDisabled();
      expect(input).toHaveClass('bg-grey-6', 'text-grey-2', 'cursor-not-allowed');
    });

    it('renders fullWidth correctly', () => {
      render(<Input fullWidth placeholder="Full width input" />);
      const input = screen.getByPlaceholderText('Full width input');
      expect(input).toHaveClass('w-full');
    });

    it('renders with width auto when fullWidth is false', () => {
      render(<Input fullWidth={false} placeholder="Auto width input" />);
      const input = screen.getByPlaceholderText('Auto width input');
      expect(input).not.toHaveClass('w-full');
    });
  });

  // Test icons
  describe('Icons', () => {
    it('renders with start icon correctly', () => {
      render(
        <Input 
          startIcon={<span data-testid="start-icon">🔍</span>} 
          placeholder="Search" 
        />
      );
      expect(screen.getByTestId('start-icon')).toBeInTheDocument();
      const input = screen.getByPlaceholderText('Search');
      expect(input).toHaveClass('pl-10');
    });

    it('renders with end icon correctly', () => {
      render(
        <Input 
          endIcon={<span data-testid="end-icon">❌</span>} 
          placeholder="Clear" 
        />
      );
      expect(screen.getByTestId('end-icon')).toBeInTheDocument();
      const input = screen.getByPlaceholderText('Clear');
      expect(input).toHaveClass('pr-10');
    });

    it('renders with both icons correctly', () => {
      render(
        <Input 
          startIcon={<span data-testid="start-icon">🔍</span>}
          endIcon={<span data-testid="end-icon">❌</span>}
          placeholder="Search and clear" 
        />
      );
      expect(screen.getByTestId('start-icon')).toBeInTheDocument();
      expect(screen.getByTestId('end-icon')).toBeInTheDocument();
      const input = screen.getByPlaceholderText('Search and clear');
      expect(input).toHaveClass('pl-10', 'pr-10');
    });
  });

  // Test accessibility
  describe('Accessibility', () => {
    it('associates label with input using htmlFor', () => {
      render(<Input label="Username" id="username" placeholder="Enter username" />);
      const label = screen.getByText('Username');
      const input = screen.getByPlaceholderText('Enter username');
      
      expect(label).toBeInTheDocument();
      expect(input).toHaveAttribute('id', 'username');
    });

    it('passes through aria attributes', () => {
      render(
        <Input 
          placeholder="Accessible input" 
          aria-label="Accessible input"
          aria-required="true"
        />
      );
      const input = screen.getByPlaceholderText('Accessible input');
      expect(input).toHaveAttribute('aria-label', 'Accessible input');
      expect(input).toHaveAttribute('aria-required', 'true');
    });
  });
});
