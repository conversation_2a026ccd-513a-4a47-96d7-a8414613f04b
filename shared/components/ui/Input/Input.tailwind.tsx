import React from 'react';
import { isZaloMiniApp } from '../../../utils/platform';

interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  label?: string;
  error?: string;
  size?: 'small' | 'medium' | 'large';
}

export const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  defaultValue,
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  required = false,
  className = '',
  label,
  error,
  size = 'medium',
  ...props
}) => {
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-5 py-3 text-lg'
  };

  const baseClasses = 'w-full border-2 rounded-lg font-inherit transition-all duration-200 outline-none bg-white';
  const normalClasses = 'border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200';
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200' : '';
  const disabledClasses = disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed border-gray-300' : '';
  
  const inputClasses = `${baseClasses} ${sizeClasses[size]} ${error ? errorClasses : normalClasses} ${disabledClasses} ${className}`;

  return (
    <div className="flex flex-col gap-1">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        disabled={disabled}
        required={required}
        className={inputClasses}
        {...props}
      />
      {error && (
        <span className="text-red-500 text-xs mt-1">
          {error}
        </span>
      )}
    </div>
  );
};

export default Input;
