import React from 'react';
import styled from 'styled-components';
import { isZaloMiniApp } from '../../../utils/platform';

interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  label?: string;
  error?: string;
  size?: 'small' | 'medium' | 'large';
}

const InputWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const Label = styled.label`
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
`;

const StyledInput = styled.input<{
  size: string;
  hasError?: boolean;
}>`
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-family: inherit;
  font-size: 16px;
  transition: all 0.2s ease;
  outline: none;
  background: white;
  width: 100%;
  
  /* Size variations */
  ${props => {
    switch (props.size) {
      case 'small':
        return `
          padding: 8px 12px;
          font-size: 14px;
          min-height: 32px;
        `;
      case 'large':
        return `
          padding: 16px 20px;
          font-size: 18px;
          min-height: 48px;
        `;
      default:
        return `
          padding: 12px 16px;
          font-size: 16px;
          min-height: 40px;
        `;
    }
  }}
  
  /* Platform-specific styles */
  ${() => isZaloMiniApp() ? `
    /* Zalo Mini App specific styles */
    border-color: var(--zmp-border-color, #e0e0e0);
    
    &:focus {
      border-color: var(--zmp-primary-color, #1877f2);
      box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
    }
  ` : `
    /* Web specific styles */
    border-color: #e0e0e0;
    
    &:focus {
      border-color: #1976d2;
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
    }
    
    &:hover:not(:disabled) {
      border-color: #bdbdbd;
    }
  `}
  
  /* Error state */
  ${props => props.hasError && `
    border-color: #f44336;
    
    &:focus {
      border-color: #f44336;
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1);
    }
  `}
  
  /* Disabled state */
  &:disabled {
    background: #f5f5f5;
    color: #9e9e9e;
    cursor: not-allowed;
    border-color: #e0e0e0;
    
    &:hover {
      border-color: #e0e0e0;
    }
  }
  
  /* Placeholder styles */
  &::placeholder {
    color: #9e9e9e;
    opacity: 1;
  }
`;

const ErrorMessage = styled.span`
  color: #f44336;
  font-size: 12px;
  margin-top: 4px;
`;

export const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  defaultValue,
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  required = false,
  className,
  label,
  error,
  size = 'medium',
  ...props
}) => {
  return (
    <InputWrapper className={className}>
      {label && <Label>{label}</Label>}
      <StyledInput
        type={type}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        disabled={disabled}
        required={required}
        size={size}
        hasError={!!error}
        {...props}
      />
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </InputWrapper>
  );
};

export default Input;
