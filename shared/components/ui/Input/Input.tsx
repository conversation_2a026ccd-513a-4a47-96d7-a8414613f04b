import React, { forwardRef } from 'react';

/**
 * Props cho Input component
 */
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'normal' | 'error' | 'success';
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

/**
 * Input Component theo TapTap Design System
 * Hỗ trợ cả Web và Zalo Mini App
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      variant = 'normal',
      fullWidth = true,
      startIcon,
      endIcon,
      className = '',
      disabled = false,
      ...props
    },
    ref
  ) => {
    // Determine variant based on error prop
    const inputVariant = error ? 'error' : variant;

    // Base classes cho input
    const baseClasses = 'border-2 rounded-lg font-archia transition-all duration-200 outline-none bg-primary-white px-4 py-2 text-14';
    
    // Variant classes theo design system
    const variantClasses = {
      normal: 'border-grey-3 focus:border-primary-pink focus:ring-2 focus:ring-pink-200',
      error: 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200',
      success: 'border-green-500 focus:border-green-500 focus:ring-2 focus:ring-green-200',
    };

    // Disabled styles
    const disabledClasses = disabled 
      ? 'bg-grey-6 text-grey-2 cursor-not-allowed' 
      : 'hover:border-grey-2';

    // Width classes
    const widthClasses = fullWidth ? 'w-full' : '';

    // Icon padding adjustments
    const iconPaddingClasses = startIcon && endIcon 
      ? 'pl-10 pr-10' 
      : startIcon 
      ? 'pl-10' 
      : endIcon 
      ? 'pr-10' 
      : '';

    // Combine all classes
    const inputClasses = `${baseClasses} ${variantClasses[inputVariant]} ${disabledClasses} ${widthClasses} ${iconPaddingClasses} ${className}`;

    return (
      <div className={`${fullWidth ? 'w-full' : ''}`}>
        {/* Label */}
        {label && (
          <label className="block text-14 font-medium text-primary-black mb-2">
            {label}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Start Icon */}
          {startIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className="text-grey-2">{startIcon}</div>
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            className={inputClasses}
            disabled={disabled}
            {...props}
          />

          {/* End Icon */}
          {endIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <div className="text-grey-2">{endIcon}</div>
            </div>
          )}
        </div>

        {/* Helper Text / Error Message */}
        {(error || helperText) && (
          <p className={`mt-2 text-12 ${error ? 'text-red-500' : 'text-grey-2'}`}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
