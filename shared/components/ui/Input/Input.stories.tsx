import type { Meta, StoryObj } from '@storybook/react';
import { Input } from './Input';

const meta: Meta<typeof Input> = {
  title: 'TapTap/Input',
  component: Input,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Input component với các variant, trạng thái và tùy chọn khác nhau cho TapTap Design System.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['normal', 'error', 'success'],
      description: 'Trạng thái hiển thị của input'
    },
    disabled: {
      control: 'boolean',
      description: 'Vô hiệu hóa input'
    },
    fullWidth: {
      control: 'boolean',
      description: 'Input chiếm toàn bộ chiều rộng'
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text'
    },
    label: {
      control: 'text',
      description: 'Label hiển thị phía trên input'
    },
    helperText: {
      control: 'text',
      description: 'Text hỗ trợ hiển thị dưới input'
    },
    error: {
      control: 'text',
      description: 'Text lỗi hiển thị dưới input'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Examples
export const Default: Story = {
  args: {
    placeholder: 'Nhập text...',
  },
};

export const WithLabel: Story = {
  args: {
    label: 'Tên đăng nhập',
    placeholder: 'Nhập tên đăng nhập...',
  },
};

// Variants
export const Normal: Story = {
  args: {
    label: 'Email',
    variant: 'normal',
    placeholder: 'Nhập email...',
  },
};

export const Success: Story = {
  args: {
    label: 'Email',
    variant: 'success',
    placeholder: 'Nhập email...',
    defaultValue: '<EMAIL>',
    helperText: 'Email hợp lệ',
  },
};

export const WithError: Story = {
  args: {
    label: 'Email',
    placeholder: 'Nhập email...',
    defaultValue: 'invalid-email',
    error: 'Email không hợp lệ'
  },
};

export const Disabled: Story = {
  args: {
    label: 'Input bị vô hiệu hóa',
    disabled: true,
    placeholder: 'Input bị vô hiệu hóa',
    defaultValue: 'Disabled value'
  },
};

// Helper Text
export const WithHelperText: Story = {
  args: {
    label: 'Mật khẩu',
    type: 'password',
    placeholder: 'Nhập mật khẩu...',
    helperText: 'Mật khẩu phải có ít nhất 8 ký tự',
  },
};

// Input Types
export const EmailInput: Story = {
  args: {
    label: 'Email',
    type: 'email',
    placeholder: 'Nhập email...',
  },
};

export const PasswordInput: Story = {
  args: {
    label: 'Mật khẩu',
    type: 'password',
    placeholder: 'Nhập mật khẩu...',
  },
};

export const NumberInput: Story = {
  args: {
    label: 'Số điện thoại',
    type: 'tel',
    placeholder: 'Nhập số điện thoại...',
  },
};

// Full Width
export const FullWidth: Story = {
  args: {
    label: 'Input full width',
    fullWidth: true,
    placeholder: 'Input full width',
  },
  parameters: {
    layout: 'padded'
  }
};

export const NotFullWidth: Story = {
  args: {
    label: 'Input không full width',
    fullWidth: false,
    placeholder: 'Input không full width',
  },
};

// Form Examples
export const LoginForm: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input
        label="Email"
        type="email"
        placeholder="Nhập email..."
        fullWidth
      />
      <Input
        label="Mật khẩu"
        type="password"
        placeholder="Nhập mật khẩu..."
        fullWidth
      />
    </div>
  ),
  parameters: {
    layout: 'centered'
  }
};

export const RegistrationForm: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input
        label="Họ và tên"
        placeholder="Nhập họ và tên..."
        fullWidth
      />
      <Input
        label="Email"
        type="email"
        placeholder="Nhập email..."
        helperText="Chúng tôi sẽ gửi xác nhận đến email này"
        fullWidth
      />
      <Input
        label="Mật khẩu"
        type="password"
        placeholder="Nhập mật khẩu..."
        helperText="Mật khẩu phải có ít nhất 8 ký tự"
        fullWidth
      />
    </div>
  ),
  parameters: {
    layout: 'centered'
  }
};

// Interactive Examples
export const AllVariants: Story = {
  render: () => (
    <div className="space-y-4">
      <Input 
        variant="normal" 
        label="Normal"
        placeholder="Normal input"
        helperText="Trạng thái bình thường"
      />
      <Input 
        variant="success" 
        label="Success"
        placeholder="Success input"
        helperText="Nhập thành công"
        defaultValue="Valid input"
      />
      <Input 
        label="Error"
        placeholder="Error input"
        error="Có lỗi xảy ra"
        defaultValue="Invalid input"
      />
    </div>
  ),
};

export const AllStates: Story = {
  render: () => (
    <div className="space-y-4">
      <Input 
        label="Normal" 
        placeholder="Normal input"
        helperText="Trạng thái bình thường"
      />
      <Input 
        label="Disabled" 
        placeholder="Disabled input"
        disabled
        helperText="Input bị vô hiệu hóa"
      />
      <Input 
        label="With Helper Text" 
        placeholder="Input with helper"
        helperText="Text hỗ trợ người dùng"
      />
      <Input 
        label="With Error" 
        placeholder="Input with error"
        error="Có lỗi xảy ra"
        defaultValue="Invalid value"
      />
    </div>
  ),
};
