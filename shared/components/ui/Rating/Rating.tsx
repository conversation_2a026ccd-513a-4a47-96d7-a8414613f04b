import React, { useState } from 'react';

/**
 * Props cho Rating component
 */
interface RatingProps {
  value?: number;
  max?: number;
  precision?: number; // 0.5 để hỗ trợ half-star, 1 cho full star
  readOnly?: boolean;
  size?: 'small' | 'medium' | 'large';
  onChange?: (value: number) => void;
  onHover?: (value: number) => void;
  className?: string;
}

/**
 * Star Icon Component
 */
const StarIcon: React.FC<{ filled: boolean; half?: boolean; size: number }> = ({ 
  filled, 
  half = false, 
  size 
}) => {
  if (half) {
    return (
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="inline-block"
      >
        <defs>
          <linearGradient id="halfStarGradient">
            <stop offset="50%" stopColor="#F9DB5B" />
            <stop offset="50%" stopColor="#ECECEC" />
          </linearGradient>
        </defs>
        <path
          d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
          fill="url(#halfStarGradient)"
          stroke="#F9DB5B"
          strokeWidth="1"
        />
      </svg>
    );
  }

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="inline-block"
    >
      <path
        d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
        fill={filled ? '#F9DB5B' : '#ECECEC'}
        stroke={filled ? '#F9DB5B' : '#CACACA'}
        strokeWidth="1"
      />
    </svg>
  );
};

/**
 * Rating Component theo TapTap Design System
 * Hỗ trợ full-star và half-star rating
 */
export const Rating: React.FC<RatingProps> = ({
  value = 0,
  max = 5,
  precision = 1,
  readOnly = false,
  size = 'medium',
  onChange,
  onHover,
  className = '',
}) => {
  const [hoverValue, setHoverValue] = useState<number | null>(null);

  // Size mapping
  const sizeMap = {
    small: 16,
    medium: 20,
    large: 24,
  };

  const starSize = sizeMap[size];

  // Calculate display value (either hover value or actual value)
  const displayValue = hoverValue !== null ? hoverValue : value;

  // Handle star click
  const handleStarClick = (starIndex: number, event: React.MouseEvent) => {
    if (readOnly || !onChange) return;

    if (precision === 0.5) {
      const rect = event.currentTarget.getBoundingClientRect();
      const isLeftHalf = event.clientX - rect.left < rect.width / 2;
      const newValue = isLeftHalf ? starIndex + 0.5 : starIndex + 1;
      onChange(newValue);
    } else {
      onChange(starIndex + 1);
    }
  };

  // Handle star hover
  const handleStarHover = (starIndex: number, event: React.MouseEvent) => {
    if (readOnly) return;

    if (precision === 0.5) {
      const rect = event.currentTarget.getBoundingClientRect();
      const isLeftHalf = event.clientX - rect.left < rect.width / 2;
      const hoverVal = isLeftHalf ? starIndex + 0.5 : starIndex + 1;
      setHoverValue(hoverVal);
      onHover?.(hoverVal);
    } else {
      const hoverVal = starIndex + 1;
      setHoverValue(hoverVal);
      onHover?.(hoverVal);
    }
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    if (!readOnly) {
      setHoverValue(null);
    }
  };

  // Render stars
  const renderStars = () => {
    const stars = [];
    
    for (let i = 0; i < max; i++) {
      const starValue = i + 1;
      const isFullFilled = displayValue >= starValue;
      const isHalfFilled = displayValue >= i + 0.5 && displayValue < starValue;

      stars.push(
        <span
          key={i}
          className={`${readOnly ? 'cursor-default' : 'cursor-pointer'} inline-block transition-transform hover:scale-110`}
          onClick={(e) => handleStarClick(i, e)}
          onMouseMove={(e) => handleStarHover(i, e)}
        >
          <StarIcon
            filled={isFullFilled}
            half={isHalfFilled}
            size={starSize}
          />
        </span>
      );
    }

    return stars;
  };

  return (
    <div
      className={`inline-flex items-center gap-1 ${className}`}
      onMouseLeave={handleMouseLeave}
      role="group"
      aria-label={readOnly ? `Rating: ${value} out of ${max} stars` : `Rate this with ${max} stars`}
    >
      {renderStars()}
    </div>
  );
};

export default Rating;
