import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Rating } from './Rating';
import { Typography } from '../Typography/Typography';

const meta: Meta<typeof Rating> = {
  title: 'TapTap Design System/Rating',
  component: Rating,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Rating component hỗ trợ 5-star rating với full-star và half-star, phù hợp cho việc đánh giá game',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: { type: 'number', min: 0, max: 5, step: 0.5 },
      description: 'Giá trị rating hiện tại',
    },
    max: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Số sao tối đa',
    },
    precision: {
      control: 'select',
      options: [0.5, 1],
      description: '<PERSON><PERSON> chính xác (0.5 cho half-star, 1 cho full-star)',
    },
    readOnly: {
      control: 'boolean',
      description: 'Chỉ đọc, không thể tương tác',
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Kích thước rating stars',
    },
    onChange: {
      action: 'rating-changed',
      description: 'Callback khi rating thay đổi',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Stories
export const Default: Story = {
  args: {
    value: 4,
    readOnly: false,
  },
};

export const ReadOnly: Story = {
  args: {
    value: 4.5,
    readOnly: true,
  },
};

// Size Stories
export const Small: Story = {
  args: {
    value: 4,
    size: 'small',
    readOnly: true,
  },
};

export const Medium: Story = {
  args: {
    value: 4,
    size: 'medium',
    readOnly: true,
  },
};

export const Large: Story = {
  args: {
    value: 4,
    size: 'large',
    readOnly: true,
  },
};

// Precision Stories
export const FullStarPrecision: Story = {
  args: {
    value: 4,
    precision: 1,
    readOnly: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Rating với precision = 1, chỉ cho phép rating nguyên sao',
      },
    },
  },
};

export const HalfStarPrecision: Story = {
  args: {
    value: 4.5,
    precision: 0.5,
    readOnly: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Rating với precision = 0.5, cho phép rating nửa sao',
      },
    },
  },
};

// Different Values
export const ZeroStars: Story = {
  args: {
    value: 0,
    readOnly: true,
  },
};

export const OneAndHalfStars: Story = {
  args: {
    value: 1.5,
    readOnly: true,
  },
};

export const ThreeStars: Story = {
  args: {
    value: 3,
    readOnly: true,
  },
};

export const FourAndHalfStars: Story = {
  args: {
    value: 4.5,
    readOnly: true,
  },
};

export const FiveStars: Story = {
  args: {
    value: 5,
    readOnly: true,
  },
};

// Interactive Example
export const InteractiveRating: Story = {
  render: () => {
    const [rating, setRating] = React.useState(0);
    
    return (
      <div className="space-y-4">
        <div>
          <Typography variant="body-demibold">Đánh giá game này:</Typography>
          <Rating
            value={rating}
            precision={0.5}
            onChange={setRating}
            size="large"
          />
        </div>
        <Typography variant="caption" color="grey-2">
          Bạn đã đánh giá: {rating} sao
        </Typography>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Ví dụ interactive cho phép người dùng chọn rating',
      },
    },
  },
};

// Different Sizes Showcase
export const SizeShowcase: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-4">
        <Typography variant="caption">Small:</Typography>
        <Rating value={4} size="small" readOnly />
      </div>
      <div className="flex items-center space-x-4">
        <Typography variant="caption">Medium:</Typography>
        <Rating value={4} size="medium" readOnly />
      </div>
      <div className="flex items-center space-x-4">
        <Typography variant="caption">Large:</Typography>
        <Rating value={4} size="large" readOnly />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'So sánh các kích thước rating khác nhau',
      },
    },
  },
};

// Game Card Example
export const GameCardExample: Story = {
  render: () => (
    <div className="max-w-sm bg-white rounded-lg shadow-md p-4 space-y-3">
      <div className="aspect-video bg-grey-6 rounded-lg flex items-center justify-center">
        <Typography variant="caption" color="grey-2">Game Screenshot</Typography>
      </div>
      
      <div>
        <Typography variant="h2" color="primary-black">Mobile Legends</Typography>
        <Typography variant="caption" color="grey-2">MOBA • Strategy</Typography>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Rating value={4.8} readOnly size="small" />
          <Typography variant="status-demibold" color="primary-pink">4.8</Typography>
        </div>
        <Typography variant="caption" color="grey-2">50k đánh giá</Typography>
      </div>
      
      <div className="flex space-x-2">
        <Typography variant="status-bold" color="primary-pink">HOT</Typography>
        <Typography variant="status-bold" color="primary-pink">MIỄN PHÍ</Typography>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Ví dụ sử dụng Rating trong card game thực tế',
      },
    },
  },
};

// Rating with Reviews
export const RatingWithReviews: Story = {
  render: () => (
    <div className="space-y-4 max-w-md">
      <div className="text-center">
        <Typography variant="h1" color="primary-black">4.8</Typography>
        <Rating value={4.8} readOnly size="large" className="justify-center" />
        <Typography variant="caption" color="grey-2">Dựa trên 1,234 đánh giá</Typography>
      </div>
      
      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map((stars) => (
          <div key={stars} className="flex items-center space-x-2">
            <Typography variant="caption" color="grey-2">{stars} sao</Typography>
            <div className="flex-1 bg-grey-6 rounded-full h-2">
              <div 
                className="bg-secondary-yellow-70 h-2 rounded-full" 
                style={{ width: `${stars === 5 ? 70 : stars === 4 ? 20 : stars === 3 ? 7 : stars === 2 ? 2 : 1}%` }}
              />
            </div>
            <Typography variant="caption" color="grey-2">
              {stars === 5 ? '865' : stars === 4 ? '247' : stars === 3 ? '86' : stars === 2 ? '25' : '11'}
            </Typography>
          </div>
        ))}
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Hiển thị rating chi tiết với breakdown theo số sao',
      },
    },
  },
};
