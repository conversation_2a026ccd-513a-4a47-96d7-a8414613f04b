import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Typography } from './Typography';

const meta: Meta<typeof Typography> = {
  title: 'TapTap Design System/Typography',
  component: Typography,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Typography component theo TapTap Design System sử dụng font Archia với các variant từ Figma',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['h1', 'h2', 'body', 'body-demibold', 'caption', 'caption-demibold', 'status', 'status-demibold', 'status-bold'],
      description: 'Variant typography theo design system',
    },
    component: {
      control: 'select',
      options: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'],
      description: 'HTML element để render',
    },
    color: {
      control: 'select',
      options: ['primary-black', 'primary-pink', 'grey-1', 'grey-2', 'primary-white', 'inherit'],
      description: 'Màu text theo design system',
    },
    align: {
      control: 'select',
      options: ['left', 'center', 'right', 'justify'],
      description: 'Text alignment',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Header Stories
export const Header1: Story = {
  args: {
    children: 'Header 1 - Bold 24px',
    variant: 'h1',
  },
};

export const Header2: Story = {
  args: {
    children: 'Header 2 - DemiBold 18px',
    variant: 'h2',
  },
};

// Body Stories
export const Body: Story = {
  args: {
    children: 'Body text - Regular 14px. Đây là đoạn text body thông thường trong design system.',
    variant: 'body',
  },
};

export const BodyDemiBold: Story = {
  args: {
    children: 'Body DemiBold - DemiBold 14px. Đây là đoạn text body với font weight demibold.',
    variant: 'body-demibold',
  },
};

// Caption Stories
export const Caption: Story = {
  args: {
    children: 'Caption text - Regular 10px',
    variant: 'caption',
  },
};

export const CaptionDemiBold: Story = {
  args: {
    children: 'Caption DemiBold - DemiBold 10px',
    variant: 'caption-demibold',
  },
};

// Status Stories
export const Status: Story = {
  args: {
    children: 'Status text - Regular 12px',
    variant: 'status',
  },
};

export const StatusDemiBold: Story = {
  args: {
    children: 'Status DemiBold - DemiBold 12px',
    variant: 'status-demibold',
  },
};

export const StatusBold: Story = {
  args: {
    children: 'Status Bold - Bold 12px Uppercase',
    variant: 'status-bold',
  },
};

// Color Stories
export const PrimaryPink: Story = {
  args: {
    children: 'Text với màu Primary Pink',
    variant: 'body',
    color: 'primary-pink',
  },
};

export const GreyText: Story = {
  args: {
    children: 'Text với màu Grey',
    variant: 'body',
    color: 'grey-2',
  },
};

// Alignment Stories
export const CenterAlign: Story = {
  args: {
    children: 'Text căn giữa',
    variant: 'body',
    align: 'center',
  },
};

export const RightAlign: Story = {
  args: {
    children: 'Text căn phải',
    variant: 'body',
    align: 'right',
  },
};

// Typography Showcase
export const AllVariants: Story = {
  render: () => (
    <div className="space-y-4">
      <Typography variant="h1">Header 1 - Bold 24px/32px</Typography>
      <Typography variant="h2">Header 2 - DemiBold 18px/24px</Typography>
      <Typography variant="body">Body - Regular 14px/22px - Đây là text body thông thường</Typography>
      <Typography variant="body-demibold">Body DemiBold - DemiBold 14px/22px - Text body với trọng lượng cao hơn</Typography>
      <Typography variant="caption">Caption - Regular 10px/16px - Text nhỏ cho ghi chú</Typography>
      <Typography variant="caption-demibold">Caption DemiBold - DemiBold 10px/16px - Caption với trọng lượng cao</Typography>
      <Typography variant="status">Status - Regular 12px/18px - Text trạng thái</Typography>
      <Typography variant="status-demibold">Status DemiBold - DemiBold 12px/18px - Status với trọng lượng cao</Typography>
      <Typography variant="status-bold">STATUS BOLD - BOLD 12PX/18PX UPPERCASE</Typography>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Tất cả các variant typography trong TapTap Design System',
      },
    },
  },
};

// Color Showcase
export const ColorShowcase: Story = {
  render: () => (
    <div className="space-y-4">
      <Typography variant="body" color="primary-black">Primary Black - Màu text chính</Typography>
      <Typography variant="body" color="primary-pink">Primary Pink - Màu brand chính</Typography>
      <Typography variant="body" color="grey-1">Grey 1 - Text tối</Typography>
      <Typography variant="body" color="grey-2">Grey 2 - Text phụ</Typography>
      <div className="bg-primary-black p-4 rounded">
        <Typography variant="body" color="primary-white">Primary White - Text trên nền tối</Typography>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Các màu text có sẵn trong design system',
      },
    },
  },
};

// Real-world Example
export const RealWorldExample: Story = {
  render: () => (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6 space-y-4">
      <Typography variant="h2" color="primary-black">TapTap Game</Typography>
      <Typography variant="body" color="grey-1">
        Khám phá thế giới game mobile tuyệt vời với hàng ngàn tựa game hấp dẫn.
      </Typography>
      <div className="flex items-center justify-between">
        <Typography variant="status-demibold" color="primary-pink">★ 4.8</Typography>
        <Typography variant="caption" color="grey-2">1.2k đánh giá</Typography>
      </div>
      <Typography variant="status-bold" color="primary-pink">MỚI CẬP NHẬT</Typography>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Ví dụ thực tế sử dụng typography trong card game',
      },
    },
  },
};
