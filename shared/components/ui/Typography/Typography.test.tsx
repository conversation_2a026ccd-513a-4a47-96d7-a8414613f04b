import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Typography } from './Typography';

describe('Typography Component', () => {
  // Test c<PERSON> bản
  describe('Basic Functionality', () => {
    it('renders correctly', () => {
      render(<Typography>Hello World</Typography>);
      expect(screen.getByText('Hello World')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      render(<Typography className="custom-class">Custom Class</Typography>);
      const element = screen.getByText('Custom Class');
      expect(element).toHaveClass('custom-class');
    });
  });

  // Test variants
  describe('Variants', () => {
    it('renders h1 variant correctly', () => {
      render(<Typography variant="h1">Header 1</Typography>);
      const heading = screen.getByText('Header 1');
      expect(heading.tagName).toBe('H1');
      expect(heading).toHaveClass('text-24', 'font-bold', 'leading-32');
    });

    it('renders h2 variant correctly', () => {
      render(<Typography variant="h2">Header 2</Typography>);
      const heading = screen.getByText('Header 2');
      expect(heading.tagName).toBe('H2');
      expect(heading).toHaveClass('text-18', 'font-demibold', 'leading-24');
    });

    it('renders body variant correctly', () => {
      render(<Typography variant="body">Body text</Typography>);
      const text = screen.getByText('Body text');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-14', 'font-regular', 'leading-22');
    });

    it('renders body-demibold variant correctly', () => {
      render(<Typography variant="body-demibold">Body Demibold</Typography>);
      const text = screen.getByText('Body Demibold');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-14', 'font-demibold', 'leading-22');
    });

    it('renders caption variant correctly', () => {
      render(<Typography variant="caption">Caption text</Typography>);
      const text = screen.getByText('Caption text');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-10', 'font-regular', 'leading-16');
    });

    it('renders caption-demibold variant correctly', () => {
      render(<Typography variant="caption-demibold">Caption Demibold</Typography>);
      const text = screen.getByText('Caption Demibold');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-10', 'font-demibold', 'leading-16');
    });

    it('renders status variant correctly', () => {
      render(<Typography variant="status">Status text</Typography>);
      const text = screen.getByText('Status text');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-12', 'font-regular', 'leading-18');
    });

    it('renders status-demibold variant correctly', () => {
      render(<Typography variant="status-demibold">Status Demibold</Typography>);
      const text = screen.getByText('Status Demibold');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-12', 'font-demibold', 'leading-18');
    });

    it('renders status-bold variant correctly', () => {
      render(<Typography variant="status-bold">Status Bold</Typography>);
      const text = screen.getByText('Status Bold');
      expect(text.tagName).toBe('P');
      expect(text).toHaveClass('text-12', 'font-bold', 'leading-18', 'uppercase');
    });
  });

  // Test colors
  describe('Colors', () => {
    it('renders primary-black color correctly', () => {
      render(<Typography color="primary-black">Black Text</Typography>);
      const text = screen.getByText('Black Text');
      expect(text).toHaveClass('text-primary-black');
    });

    it('renders primary-pink color correctly', () => {
      render(<Typography color="primary-pink">Pink Text</Typography>);
      const text = screen.getByText('Pink Text');
      expect(text).toHaveClass('text-primary-pink');
    });

    it('renders grey-1 color correctly', () => {
      render(<Typography color="grey-1">Grey 1 Text</Typography>);
      const text = screen.getByText('Grey 1 Text');
      expect(text).toHaveClass('text-grey-1');
    });

    it('renders grey-2 color correctly', () => {
      render(<Typography color="grey-2">Grey 2 Text</Typography>);
      const text = screen.getByText('Grey 2 Text');
      expect(text).toHaveClass('text-grey-2');
    });

    it('renders primary-white color correctly', () => {
      render(<Typography color="primary-white">White Text</Typography>);
      const text = screen.getByText('White Text');
      expect(text).toHaveClass('text-primary-white');
    });

    it('renders inherit color correctly', () => {
      render(<Typography color="inherit">Inherit Text</Typography>);
      const text = screen.getByText('Inherit Text');
      expect(text).toHaveClass('text-inherit');
    });
  });

  // Test alignment
  describe('Alignment', () => {
    it('renders left alignment correctly', () => {
      render(<Typography align="left">Left Text</Typography>);
      const text = screen.getByText('Left Text');
      expect(text).toHaveClass('text-left');
    });

    it('renders center alignment correctly', () => {
      render(<Typography align="center">Center Text</Typography>);
      const text = screen.getByText('Center Text');
      expect(text).toHaveClass('text-center');
    });

    it('renders right alignment correctly', () => {
      render(<Typography align="right">Right Text</Typography>);
      const text = screen.getByText('Right Text');
      expect(text).toHaveClass('text-right');
    });

    it('renders justify alignment correctly', () => {
      render(<Typography align="justify">Justify Text</Typography>);
      const text = screen.getByText('Justify Text');
      expect(text).toHaveClass('text-justify');
    });
  });

  // Test custom component
  describe('Custom Component', () => {
    it('renders as specified component', () => {
      render(<Typography component="div">DIV Element</Typography>);
      const element = screen.getByText('DIV Element');
      expect(element.tagName).toBe('DIV');
    });

    it('overrides default component based on variant', () => {
      render(<Typography variant="h1" component="span">Span instead of H1</Typography>);
      const element = screen.getByText('Span instead of H1');
      expect(element.tagName).toBe('SPAN');
      expect(element).toHaveClass('text-24', 'font-bold');
    });
  });
});
