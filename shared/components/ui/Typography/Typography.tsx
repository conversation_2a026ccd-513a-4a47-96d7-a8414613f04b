import React from 'react';

/**
 * Props cho Typography component
 */
interface TypographyProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'body' | 'body-demibold' | 'caption' | 'caption-demibold' | 'status' | 'status-demibold' | 'status-bold';
  component?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
  color?: 'primary-black' | 'primary-pink' | 'grey-1' | 'grey-2' | 'primary-white' | 'inherit';
  align?: 'left' | 'center' | 'right' | 'justify';
  className?: string;
}

/**
 * Typography Component theo TapTap Design System
 * Sử dụng font Archia với các variant đã định nghĩa trong design system
 */
export const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body',
  component,
  color = 'primary-black',
  align = 'left',
  className = '',
}) => {
  // Mapping variant to default component
  const getDefaultComponent = (variant: string): keyof JSX.IntrinsicElements => {
    switch (variant) {
      case 'h1':
        return 'h1';
      case 'h2':
        return 'h2';
      case 'body':
      case 'body-demibold':
      case 'caption':
      case 'caption-demibold':
      case 'status':
      case 'status-demibold':
      case 'status-bold':
        return 'p';
      default:
        return 'p';
    }
  };

  // Variant classes theo design system
  const variantClasses = {
    'h1': 'font-archia text-24 font-bold leading-32', // Header 1: Bold, 24px, line-height 32px
    'h2': 'font-archia text-18 font-demibold leading-24', // Header 2: DemiBold, 18px, line-height 24px
    'body': 'font-archia text-14 font-regular leading-22', // Body: Regular, 14px, line-height 22px
    'body-demibold': 'font-archia text-14 font-demibold leading-22', // Body DemiBold: DemiBold, 14px, line-height 22px
    'caption': 'font-archia text-10 font-regular leading-16', // Caption: Regular, 10px, line-height 16px
    'caption-demibold': 'font-archia text-10 font-demibold leading-16', // Caption DemiBold: DemiBold, 10px, line-height 16px
    'status': 'font-archia text-12 font-regular leading-18', // Status: Regular, 12px, line-height 18px
    'status-demibold': 'font-archia text-12 font-demibold leading-18', // Status DemiBold: DemiBold, 12px, line-height 18px
    'status-bold': 'font-archia text-12 font-bold leading-18 uppercase', // Status Bold: Bold, 12px, line-height 18px, uppercase
  };

  // Color classes
  const colorClasses = {
    'primary-black': 'text-primary-black',
    'primary-pink': 'text-primary-pink',
    'grey-1': 'text-grey-1',
    'grey-2': 'text-grey-2',
    'primary-white': 'text-primary-white',
    'inherit': 'text-inherit',
  };

  // Alignment classes
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
    justify: 'text-justify',
  };

  // Combine all classes
  const combinedClasses = `${variantClasses[variant]} ${colorClasses[color]} ${alignClasses[align]} ${className}`;

  // Get the component to render
  const Component = component || getDefaultComponent(variant);

  return (
    <Component className={combinedClasses}>
      {children}
    </Component>
  );
};

export default Typography;
