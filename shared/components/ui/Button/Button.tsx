import React from 'react';

/**
 * Props cho Button component
 */
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  fullWidth?: boolean;
}

/**
 * Button Component theo TapTap Design System
 * Hỗ trợ cả Web và Zalo Mini App
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  className = '',
  type = 'button',
  fullWidth = false,
}) => {
  // Base classes cho tất cả buttons
  const baseClasses = 'inline-flex items-center justify-center font-archia font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  // Variant classes theo design system
  const variantClasses = {
    primary: 'bg-primary-pink text-primary-white hover:bg-opacity-90 focus:ring-primary-pink',
    secondary: 'bg-primary-white text-primary-pink border border-primary-pink hover:bg-grey-6 focus:ring-primary-pink',
    outline: 'bg-transparent text-primary-pink border border-primary-pink hover:bg-primary-pink hover:text-primary-white focus:ring-primary-pink',
    text: 'bg-transparent text-primary-pink hover:bg-grey-6 focus:ring-primary-pink',
  };
  
  // Size classes theo design system
  const sizeClasses = {
    small: 'px-3 py-1.5 text-12 rounded-md',
    medium: 'px-4 py-2 text-14 rounded-lg',
    large: 'px-6 py-3 text-18 rounded-xl',
  };
  
  // Width classes
  const widthClasses = fullWidth ? 'w-full' : '';
  
  // Combine all classes
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClasses} ${className}`;
  
  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };
  
  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;
