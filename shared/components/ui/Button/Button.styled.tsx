import React from 'react';
import styled from 'styled-components';
import { isZaloMiniApp } from '../../../utils/platform';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

// Styled component with platform-specific styling
const StyledButton = styled.button<{
  variant: string;
  size: string;
  disabled?: boolean;
  loading?: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;

  /* Size variations */
  ${props => {
    switch (props.size) {
      case 'small':
        return `
          padding: 8px 16px;
          font-size: 14px;
          min-height: 32px;
        `;
      case 'large':
        return `
          padding: 16px 32px;
          font-size: 18px;
          min-height: 48px;
        `;
      default:
        return `
          padding: 12px 24px;
          font-size: 16px;
          min-height: 40px;
        `;
    }
  }}

  /* Platform-specific base styles */
  ${() => isZaloMiniApp() ? `
    /* Zalo Mini App specific styles */
    background: var(--zmp-primary-color, #1877f2);
    color: white;
    box-shadow: none;
    font-family: inherit;
  ` : `
    /* Web specific styles */
    background: #1976d2;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    &:hover:not(:disabled) {
      background: #1565c0;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
  `}

  /* Variant styles */
  ${props => {
    switch (props.variant) {
      case 'secondary':
        return `
          background: #f5f5f5;
          color: #333;
          
          ${!isZaloMiniApp() ? `
            &:hover:not(:disabled) {
              background: #e0e0e0;
            }
          ` : ''}
        `;
      case 'outline':
        return `
          background: transparent;
          border: 2px solid currentColor;
          color: #1976d2;
          
          ${!isZaloMiniApp() ? `
            &:hover:not(:disabled) {
              background: #1976d2;
              color: white;
            }
          ` : ''}
        `;
      case 'ghost':
        return `
          background: transparent;
          color: #1976d2;
          box-shadow: none;
          
          ${!isZaloMiniApp() ? `
            &:hover:not(:disabled) {
              background: rgba(25, 118, 210, 0.1);
            }
          ` : ''}
        `;
      default:
        return '';
    }
  }}

  /* Disabled state */
  ${props => props.disabled && `
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    
    ${!isZaloMiniApp() ? `
      &:hover {
        transform: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }
    ` : ''}
  `}

  /* Loading state */
  ${props => props.loading && `
    cursor: not-allowed;
    
    &::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      margin: auto;
      border: 2px solid transparent;
      border-top-color: currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `}
`;

// Loading Spinner Component
const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  className,
  type = 'button',
  ...props
}) => {
  // For Zalo Mini App, we could use ZaUI Button if needed
  // But for now, we'll use our unified styled component
  
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }
    onClick && onClick();
  };

  return (
    <StyledButton
      type={type}
      variant={variant}
      size={size}
      disabled={disabled || loading}
      loading={loading}
      onClick={handleClick}
      className={className}
      {...props}
    >
      {loading && <LoadingSpinner />}
      <span style={{ opacity: loading ? 0.7 : 1 }}>
        {children}
      </span>
    </StyledButton>
  );
};

export default Button;
