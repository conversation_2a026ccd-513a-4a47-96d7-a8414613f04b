import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Card } from './Card';

describe('Card Component', () => {
  // Test cơ bản
  describe('Basic Functionality', () => {
    it('renders correctly', () => {
      render(<Card>Card Content</Card>);
      expect(screen.getByText('Card Content')).toBeInTheDocument();
    });

    it('renders with custom className', () => {
      render(<Card className="custom-class">Card Content</Card>);
      const card = screen.getByText('Card Content').closest('div');
      expect(card).toHaveClass('custom-class');
    });

    it('renders children correctly', () => {
      render(
        <Card>
          <h1>Card Title</h1>
          <p>Card Description</p>
        </Card>
      );
      expect(screen.getByText('Card Title')).toBeInTheDocument();
      expect(screen.getByText('Card Description')).toBeInTheDocument();
    });
  });

  // Test variants
  describe('Variants', () => {
    it('renders default variant correctly', () => {
      render(<Card variant="default">Default Card</Card>);
      const card = screen.getByText('Default Card').closest('div');
      expect(card).toHaveClass('bg-primary-white', 'border', 'border-grey-5');
    });

    it('renders outlined variant correctly', () => {
      render(<Card variant="outlined">Outlined Card</Card>);
      const card = screen.getByText('Outlined Card').closest('div');
      expect(card).toHaveClass('bg-primary-white', 'border-2', 'border-grey-3');
    });

    it('renders elevated variant correctly', () => {
      render(<Card variant="elevated">Elevated Card</Card>);
      const card = screen.getByText('Elevated Card').closest('div');
      expect(card).toHaveClass('bg-primary-white', 'shadow-md', 'border', 'border-grey-6');
    });
  });

  // Test padding
  describe('Padding', () => {
    it('renders none padding correctly', () => {
      render(<Card padding="none">No Padding</Card>);
      const card = screen.getByText('No Padding').closest('div');
      expect(card).toHaveClass('p-0');
    });

    it('renders small padding correctly', () => {
      render(<Card padding="small">Small Padding</Card>);
      const card = screen.getByText('Small Padding').closest('div');
      expect(card).toHaveClass('p-3');
    });

    it('renders medium padding correctly', () => {
      render(<Card padding="medium">Medium Padding</Card>);
      const card = screen.getByText('Medium Padding').closest('div');
      expect(card).toHaveClass('p-4');
    });

    it('renders large padding correctly', () => {
      render(<Card padding="large">Large Padding</Card>);
      const card = screen.getByText('Large Padding').closest('div');
      expect(card).toHaveClass('p-6');
    });
  });

  // Test border radius
  describe('Border Radius', () => {
    it('renders none border radius correctly', () => {
      render(<Card borderRadius="none">No Border Radius</Card>);
      const card = screen.getByText('No Border Radius').closest('div');
      expect(card).toHaveClass('rounded-none');
    });

    it('renders small border radius correctly', () => {
      render(<Card borderRadius="small">Small Border Radius</Card>);
      const card = screen.getByText('Small Border Radius').closest('div');
      expect(card).toHaveClass('rounded-sm');
    });

    it('renders medium border radius correctly', () => {
      render(<Card borderRadius="medium">Medium Border Radius</Card>);
      const card = screen.getByText('Medium Border Radius').closest('div');
      expect(card).toHaveClass('rounded-lg');
    });

    it('renders large border radius correctly', () => {
      render(<Card borderRadius="large">Large Border Radius</Card>);
      const card = screen.getByText('Large Border Radius').closest('div');
      expect(card).toHaveClass('rounded-xl');
    });
  });

  // Test interactive features
  describe('Interactive Features', () => {
    it('handles click events', () => {
      const handleClick = jest.fn();
      render(<Card onClick={handleClick}>Clickable Card</Card>);
      const card = screen.getByText('Clickable Card').closest('div');
      
      fireEvent.click(card);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not call onClick when disabled', () => {
      const handleClick = jest.fn();
      render(<Card onClick={handleClick} disabled>Disabled Card</Card>);
      const card = screen.getByText('Disabled Card').closest('div');
      
      fireEvent.click(card);
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('applies cursor-pointer when clickable', () => {
      render(<Card onClick={() => {}}>Clickable Card</Card>);
      const card = screen.getByText('Clickable Card').closest('div');
      expect(card).toHaveClass('cursor-pointer');
    });

    it('applies cursor-pointer when hoverable', () => {
      render(<Card hoverable>Hoverable Card</Card>);
      const card = screen.getByText('Hoverable Card').closest('div');
      expect(card).toHaveClass('cursor-pointer');
    });
  });

  // Test states
  describe('States', () => {
    it('renders disabled state correctly', () => {
      render(<Card disabled>Disabled Card</Card>);
      const card = screen.getByText('Disabled Card').closest('div');
      expect(card).toHaveClass('opacity-50', 'cursor-not-allowed');
    });

    it('applies correct hover classes for elevated variant', () => {
      render(<Card variant="elevated" hoverable>Hover Elevated Card</Card>);
      const card = screen.getByText('Hover Elevated Card').closest('div');
      expect(card).toHaveClass('hover:shadow-lg', 'hover:scale-105');
    });

    it('applies correct hover classes for non-elevated variants', () => {
      render(<Card variant="default" hoverable>Hover Default Card</Card>);
      const card = screen.getByText('Hover Default Card').closest('div');
      expect(card).toHaveClass('hover:border-grey-2', 'hover:shadow-sm');
    });

    it('does not apply hover classes when disabled', () => {
      render(<Card hoverable disabled>Disabled Hoverable Card</Card>);
      const card = screen.getByText('Disabled Hoverable Card').closest('div');
      expect(card).not.toHaveClass('hover:shadow-lg');
      expect(card).not.toHaveClass('hover:shadow-sm');
    });
  });

  // Test accessibility
  describe('Accessibility', () => {
    it('adds role="button" when clickable', () => {
      render(<Card onClick={() => {}}>Accessible Card</Card>);
      const card = screen.getByRole('button');
      expect(card).toHaveTextContent('Accessible Card');
    });

    it('adds tabIndex when clickable', () => {
      render(<Card onClick={() => {}}>Focusable Card</Card>);
      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('tabIndex', '0');
    });

    it('adds tabIndex=-1 when disabled', () => {
      render(<Card onClick={() => {}} disabled>Disabled Card</Card>);
      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('tabIndex', '-1');
    });

    it('adds aria-disabled when disabled', () => {
      render(<Card onClick={() => {}} disabled>Disabled Card</Card>);
      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('aria-disabled', 'true');
    });

    it('triggers onClick on Enter keypress', () => {
      const handleClick = jest.fn();
      render(<Card onClick={handleClick}>Keyboard Accessible Card</Card>);
      const card = screen.getByRole('button');
      
      fireEvent.keyDown(card, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('triggers onClick on Space keypress', () => {
      const handleClick = jest.fn();
      render(<Card onClick={handleClick}>Keyboard Accessible Card</Card>);
      const card = screen.getByRole('button');
      
      fireEvent.keyDown(card, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not trigger onClick on other keypress', () => {
      const handleClick = jest.fn();
      render(<Card onClick={handleClick}>Keyboard Accessible Card</Card>);
      const card = screen.getByRole('button');
      
      fireEvent.keyDown(card, { key: 'A' });
      expect(handleClick).not.toHaveBeenCalled();
    });
  });
});
