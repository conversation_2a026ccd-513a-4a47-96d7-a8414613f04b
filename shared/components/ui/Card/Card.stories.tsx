import type { Meta, StoryObj } from '@storybook/react';
import { Card } from './Card';
import { Typography } from '../Typography/Typography';
import { Button } from '../Button/Button';
import { Rating } from '../Rating/Rating';

const meta: Meta<typeof Card> = {
  title: 'TapTap Design System/Card',
  component: Card,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Card component linh hoạt để tạo container với các style và tương tác khác nhau theo TapTap Design System',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'outlined', 'elevated'],
      description: 'Variant hiển thị của card',
    },
    padding: {
      control: 'select',
      options: ['none', 'small', 'medium', 'large'],
      description: 'Padding bên trong card',
    },
    borderRadius: {
      control: 'select',
      options: ['none', 'small', 'medium', 'large'],
      description: 'Border radius của card',
    },
    hoverable: {
      control: 'boolean',
      description: 'Card có hiệu ứng hover không',
    },
    disabled: {
      control: 'boolean',
      description: 'Card bị vô hiệu hóa',
    },
    onClick: {
      action: 'clicked',
      description: 'Callback khi card được click',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Examples
export const Default: Story = {
  args: {
    children: (
      <div>
        <Typography variant="h2">Default Card</Typography>
        <Typography variant="body" color="grey-2">
          Đây là một card với style mặc định.
        </Typography>
      </div>
    ),
  },
};

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: (
      <div>
        <Typography variant="h2">Outlined Card</Typography>
        <Typography variant="body" color="grey-2">
          Card với border outline rõ ràng hơn.
        </Typography>
      </div>
    ),
  },
};

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: (
      <div>
        <Typography variant="h2">Elevated Card</Typography>
        <Typography variant="body" color="grey-2">
          Card với shadow tạo cảm giác nổi lên.
        </Typography>
      </div>
    ),
  },
};

// Padding Variants
export const NoPadding: Story = {
  args: {
    padding: 'none',
    children: (
      <div className="p-4 bg-grey-6">
        <Typography variant="body">
          Card không có padding mặc định
        </Typography>
      </div>
    ),
  },
};

export const SmallPadding: Story = {
  args: {
    padding: 'small',
    children: (
      <Typography variant="body">Card với padding nhỏ</Typography>
    ),
  },
};

export const LargePadding: Story = {
  args: {
    padding: 'large',
    children: (
      <Typography variant="body">Card với padding lớn</Typography>
    ),
  },
};

// Interactive Cards
export const Clickable: Story = {
  args: {
    variant: 'elevated',
    onClick: () => alert('Card clicked!'),
    children: (
      <div>
        <Typography variant="h2">Clickable Card</Typography>
        <Typography variant="body" color="grey-2">
          Click vào card này để thấy hiệu ứng!
        </Typography>
      </div>
    ),
  },
};

export const Hoverable: Story = {
  args: {
    variant: 'outlined',
    hoverable: true,
    children: (
      <div>
        <Typography variant="h2">Hoverable Card</Typography>
        <Typography variant="body" color="grey-2">
          Hover vào card này để thấy hiệu ứng!
        </Typography>
      </div>
    ),
  },
};

export const Disabled: Story = {
  args: {
    variant: 'elevated',
    disabled: true,
    onClick: () => alert('Should not trigger'),
    children: (
      <div>
        <Typography variant="h2">Disabled Card</Typography>
        <Typography variant="body" color="grey-2">
          Card này bị vô hiệu hóa.
        </Typography>
      </div>
    ),
  },
};

// Border Radius Variants
export const BorderRadiusShowcase: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4">
      <Card borderRadius="none" variant="outlined">
        <Typography variant="body">No Border Radius</Typography>
      </Card>
      <Card borderRadius="small" variant="outlined">
        <Typography variant="body">Small Border Radius</Typography>
      </Card>
      <Card borderRadius="medium" variant="outlined">
        <Typography variant="body">Medium Border Radius</Typography>
      </Card>
      <Card borderRadius="large" variant="outlined">
        <Typography variant="body">Large Border Radius</Typography>
      </Card>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Các option border radius khác nhau',
      },
    },
  },
};

// Real-world Examples
export const GameCard: Story = {
  render: () => (
    <Card variant="elevated" hoverable className="max-w-sm">
      <div className="space-y-3">
        {/* Game Image */}
        <div className="aspect-video bg-grey-6 rounded-lg flex items-center justify-center">
          <Typography variant="caption" color="grey-2">Game Screenshot</Typography>
        </div>
        
        {/* Game Info */}
        <div>
          <Typography variant="h2" color="primary-black">Mobile Legends</Typography>
          <Typography variant="caption" color="grey-2">MOBA • Strategy</Typography>
        </div>
        
        {/* Rating */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Rating value={4.8} readOnly size="small" />
            <Typography variant="status-demibold" color="primary-pink">4.8</Typography>
          </div>
          <Typography variant="caption" color="grey-2">50k đánh giá</Typography>
        </div>
        
        {/* Tags */}
        <div className="flex space-x-2">
          <span className="px-2 py-1 bg-secondary-yellow-70 text-primary-black text-10 font-bold rounded uppercase">
            HOT
          </span>
          <span className="px-2 py-1 bg-primary-pink text-primary-white text-10 font-bold rounded uppercase">
            MIỄN PHÍ
          </span>
        </div>
        
        {/* Download Button */}
        <Button variant="primary" size="small" fullWidth>
          Tải xuống
        </Button>
      </div>
    </Card>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Ví dụ thực tế của Game Card trong TapTap',
      },
    },
  },
};

export const ProfileCard: Story = {
  render: () => (
    <Card variant="outlined" className="max-w-md">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-primary-pink rounded-full flex items-center justify-center">
            <Typography variant="body-demibold" color="primary-white">M</Typography>
          </div>
          <div>
            <Typography variant="body-demibold">MaxUser123</Typography>
            <Typography variant="caption" color="grey-2">Thành viên từ 2023</Typography>
          </div>
        </div>
        
        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 py-3 border-t border-b border-grey-5">
          <div className="text-center">
            <Typography variant="h2" color="primary-pink">127</Typography>
            <Typography variant="caption" color="grey-2">Games</Typography>
          </div>
          <div className="text-center">
            <Typography variant="h2" color="primary-pink">1.2k</Typography>
            <Typography variant="caption" color="grey-2">Followers</Typography>
          </div>
          <div className="text-center">
            <Typography variant="h2" color="primary-pink">89</Typography>
            <Typography variant="caption" color="grey-2">Reviews</Typography>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex space-x-2">
          <Button variant="primary" size="small" className="flex-1">
            Theo dõi
          </Button>
          <Button variant="outline" size="small" className="flex-1">
            Nhắn tin
          </Button>
        </div>
      </div>
    </Card>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Ví dụ Profile Card cho user',
      },
    },
  },
};

export const ReviewCard: Story = {
  render: () => (
    <Card variant="default" className="max-w-lg">
      <div className="space-y-3">
        {/* Reviewer Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-secondary-yellow-70 rounded-full flex items-center justify-center">
              <Typography variant="caption-demibold">A</Typography>
            </div>
            <div>
              <Typography variant="status-demibold">Anonymous User</Typography>
              <Typography variant="caption" color="grey-2">2 ngày trước</Typography>
            </div>
          </div>
          <Rating value={5} readOnly size="small" />
        </div>
        
        {/* Review Content */}
        <Typography variant="body">
          "Game rất hay và chơi mượt! Graphics đẹp, gameplay thú vị. 
          Đặc biệt là tính năng PvP rất cân bằng. Highly recommended!"
        </Typography>
        
        {/* Actions */}
        <div className="flex items-center space-x-4 pt-2">
          <button className="flex items-center space-x-1 text-grey-2 hover:text-primary-pink">
            <span>👍</span>
            <Typography variant="caption">124</Typography>
          </button>
          <button className="flex items-center space-x-1 text-grey-2 hover:text-primary-pink">
            <span>💬</span>
            <Typography variant="caption">12</Typography>
          </button>
        </div>
      </div>
    </Card>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Ví dụ Review Card cho đánh giá game',
      },
    },
  },
};

// All Variants Showcase
export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card variant="default">
        <Typography variant="h2">Default</Typography>
        <Typography variant="body" color="grey-2">
          Card với style mặc định
        </Typography>
      </Card>
      <Card variant="outlined">
        <Typography variant="h2">Outlined</Typography>
        <Typography variant="body" color="grey-2">
          Card với border rõ ràng
        </Typography>
      </Card>
      <Card variant="elevated">
        <Typography variant="h2">Elevated</Typography>
        <Typography variant="body" color="grey-2">
          Card với shadow nổi bật
        </Typography>
      </Card>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Tất cả các variant của Card component',
      },
    },
  },
};
