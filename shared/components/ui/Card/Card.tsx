import React from 'react';

/**
 * Props cho Card component
 */
interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'outlined' | 'elevated';
  padding?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large';
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
  disabled?: boolean;
}

/**
 * Card Component theo TapTap Design System
 * Được sử dụng để tạo container có thể tái sử dụng với các style khác nhau
 */
export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'medium',
  borderRadius = 'medium',
  className = '',
  onClick,
  hoverable = false,
  disabled = false,
}) => {
  // Base classes cho card
  const baseClasses = 'transition-all duration-200 font-archia';

  // Variant classes theo design system
  const variantClasses = {
    default: 'bg-primary-white border border-grey-5',
    outlined: 'bg-primary-white border-2 border-grey-3',
    elevated: 'bg-primary-white shadow-md border border-grey-6',
  };

  // Padding classes
  const paddingClasses = {
    none: 'p-0',
    small: 'p-3',
    medium: 'p-4',
    large: 'p-6',
  };

  // Border radius classes
  const borderRadiusClasses = {
    none: 'rounded-none',
    small: 'rounded-sm',
    medium: 'rounded-lg',
    large: 'rounded-xl',
  };

  // Interactive classes
  const interactiveClasses = onClick || hoverable ? 'cursor-pointer' : '';
  const hoverClasses = (onClick || hoverable) && !disabled 
    ? variant === 'elevated' 
      ? 'hover:shadow-lg hover:scale-105' 
      : 'hover:border-grey-2 hover:shadow-sm'
    : '';

  // Disabled classes
  const disabledClasses = disabled 
    ? 'opacity-50 cursor-not-allowed' 
    : '';

  // Focus classes for accessibility
  const focusClasses = onClick && !disabled 
    ? 'focus:outline-none focus:ring-2 focus:ring-primary-pink focus:ring-offset-2' 
    : '';

  // Combine all classes
  const cardClasses = `${baseClasses} ${variantClasses[variant]} ${paddingClasses[padding]} ${borderRadiusClasses[borderRadius]} ${interactiveClasses} ${hoverClasses} ${disabledClasses} ${focusClasses} ${className}`;

  const handleClick = () => {
    if (onClick && !disabled) {
      onClick();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (onClick && !disabled && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      onClick();
    }
  };

  // Render as interactive element if clickable
  if (onClick) {
    return (
      <div
        className={cardClasses}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="button"
        aria-disabled={disabled}
      >
        {children}
      </div>
    );
  }

  // Render as static container
  return (
    <div className={cardClasses}>
      {children}
    </div>
  );
};

export default Card;
