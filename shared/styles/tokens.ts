/**
 * Design Tokens cho TapTap Design System
 * <PERSON><PERSON><PERSON><PERSON> tạo từ Figma design system analysis
 */

export const designTokens = {
  // <PERSON><PERSON>u sắc chính
  colors: {
    primary: {
      pink: '#F65D79',
      white: '#FFFFFF',
      black: '#1A1818',
    },
    secondary: {
      yellow: {
        70: '#F9DB5B',
        90: '#88700c',
        40: '#fadd62',
      },
    },
    grey: {
      1: '#5A5A5A',
      2: '#9A9A9A',
      3: '#CACACA',
      5: '#ECECEC',
      6: '#F8F8F8',
    },
  },

  // Typography - Font Archia
  typography: {
    fontFamily: {
      archia: ['Archia', 'sans-serif'],
    },
    fontSize: {
      10: '10px',
      12: '12px',
      14: '14px',
      18: '18px',
      24: '24px',
    },
    fontWeight: {
      regular: 400,
      medium: 500,
      demibold: 600,
      bold: 700,
    },
    lineHeight: {
      16: '16px',
      18: '18px',
      22: '22px',
      24: '24px',
      32: '32px',
    },
  },

  // Spacing
  spacing: {
    0: '0px',
    3: '6px',
    6: '16px',
  },

  // Border radius
  borderRadius: {
    sm: '4px',
    md: '6px',
    lg: '8px',
    xl: '12px',
  },

  // Shadows
  shadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
} as const;

// Export individual categories for easier access
export const { colors, typography, spacing, borderRadius, shadow } = designTokens;

// Type definitions
export type ColorToken = typeof colors;
export type TypographyToken = typeof typography;
export type SpacingToken = typeof spacing;
