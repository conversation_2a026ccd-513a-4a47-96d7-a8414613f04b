/**
 * Font Archia import cho TapTap Design System
 * Cần được import vào các app để sử dụng font family
 */

/* Import font <PERSON><PERSON> từ Google Fonts hoặc local files */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Fallback font configuration */
:root {
  --font-archia: 'Archia', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Base font settings */
html {
  font-family: var(--font-archia);
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
}

/* Font weight classes */
.font-regular {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-demibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Typography utilities */
.text-display-h1 {
  font-family: var(--font-archia);
  font-size: 24px;
  font-weight: 700;
  line-height: 32px;
}

.text-display-h2 {
  font-family: var(--font-archia);
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
}

.text-display-body {
  font-family: var(--font-archia);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.text-display-body-demibold {
  font-family: var(--font-archia);
  font-size: 14px;
  font-weight: 600;
  line-height: 22px;
}

.text-display-caption {
  font-family: var(--font-archia);
  font-size: 10px;
  font-weight: 400;
  line-height: 16px;
}

.text-display-caption-demibold {
  font-family: var(--font-archia);
  font-size: 10px;
  font-weight: 600;
  line-height: 16px;
}

.text-display-status {
  font-family: var(--font-archia);
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}

.text-display-status-demibold {
  font-family: var(--font-archia);
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
}

.text-display-status-bold {
  font-family: var(--font-archia);
  font-size: 12px;
  font-weight: 700;
  line-height: 18px;
  text-transform: uppercase;
}
