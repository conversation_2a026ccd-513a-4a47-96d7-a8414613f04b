// Common constants for the application
export const APP_CONFIG = {
  name: 'TapTap',
  version: '1.0.0',
  description: 'Multi-platform TapTap application',
  author: 'TapTap Team'
};

export const API_CONFIG = {
  baseURL: process.env.VITE_API_BASE_URL || 'https://api.taptap.com',
  timeout: 10000,
  retryAttempts: 3
};

export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_PROFILE: 'user_profile',
  GAME_SETTINGS: 'game_settings',
  THEME_PREFERENCE: 'theme_preference',
  LANGUAGE: 'language'
};

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  PROFILE: '/profile',
  GAMES: '/games',
  GAME_DETAIL: '/games/:id',
  SETTINGS: '/settings',
  ABOUT: '/about'
};

export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
} as const;

export const LANGUAGES = {
  EN: 'en',
  VI: 'vi'
} as const;

export const GAME_CATEGORIES = {
  ACTION: 'action',
  ADVENTURE: 'adventure',
  PUZZLE: 'puzzle',
  STRATEGY: 'strategy',
  CASUAL: 'casual',
  SPORTS: 'sports'
} as const;

export const ZALO_CONFIG = {
  appId: process.env.VITE_ZALO_APP_ID || '',
  oaId: process.env.VITE_OA_ID || '',
  maxFileSize: 3 * 1024 * 1024, // 3MB
  supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif'],
  supportedVideoTypes: ['video/mp4', 'video/quicktime']
};

export const WEB_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  supportedVideoTypes: ['video/mp4', 'video/webm', 'video/quicktime']
};
