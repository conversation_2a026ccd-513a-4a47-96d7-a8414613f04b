// ZMP SDK Type definitions
declare module 'zmp-sdk' {
  // Storage APIs
  export function setStorage(params: { key: string; data: any }): Promise<void>;
  export function getStorage(params: { key: string }): Promise<{ data: any }>;
  export function clearStorage(): Promise<void>;
  
  // Auth APIs
  export function getAccessToken(): Promise<string>;
  export function getUserInfo(): Promise<{
    id: string;
    name: string;
    avatar: string;
    phone?: string;
  }>;
  export function login(): Promise<void>;
  export function getPhoneNumber(): Promise<string>;
  
  // Location APIs
  export function getLocation(): Promise<{
    latitude: number;
    longitude: number;
    accuracy: number;
  }>;
  
  // Social APIs
  export function openChat(params: { userId: string }): Promise<void>;
  export function followOA(params: { oaId: string }): Promise<void>;
  export function shareToChat(params: {
    title: string;
    description: string;
    thumbnail: string;
    url: string;
  }): Promise<void>;
  
  // Payment APIs
  export function payment(params: {
    amount: number;
    description: string;
    orderId: string;
    extraData?: Record<string, any>;
  }): Promise<{
    success: boolean;
    transactionId?: string;
    error?: string;
  }>;
  
  // UI APIs
  export function setTitle(title: string): Promise<void>;
  export function changeStatusBarColor(color: string): Promise<void>;
  export function showToast(message: string): Promise<void>;
  export function showModal(params: {
    title: string;
    content: string;
    showCancel?: boolean;
    cancelText?: string;
    confirmText?: string;
  }): Promise<{ confirm: boolean }>;
}
