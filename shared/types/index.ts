// User types
export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile extends User {
  bio?: string;
  preferences: UserPreferences;
  stats: UserStats;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'en' | 'vi';
  notifications: boolean;
  autoPlay: boolean;
}

export interface UserStats {
  gamesPlayed: number;
  totalPlayTime: number;
  favoriteGames: string[];
  achievements: Achievement[];
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: Date;
}

// Game types
export interface Game {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  screenshots: string[];
  category: GameCategory;
  rating: number;
  downloads: number;
  size: number;
  version: string;
  developer: string;
  publishedAt: Date;
  updatedAt: Date;
  tags: string[];
  isNew: boolean;
  isFeatured: boolean;
}

export interface GameDetail extends Game {
  fullDescription: string;
  requirements: GameRequirements;
  reviews: GameReview[];
  relatedGames: Game[];
}

export interface GameRequirements {
  minAndroidVersion?: string;
  minIOSVersion?: string;
  minRAM: number;
  minStorage: number;
  internetRequired: boolean;
}

export interface GameReview {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  createdAt: Date;
  likes: number;
  replies: GameReviewReply[];
}

export interface GameReviewReply {
  id: string;
  userId: string;
  userName: string;
  comment: string;
  createdAt: Date;
}

export type GameCategory = 
  | 'action'
  | 'adventure'
  | 'puzzle'
  | 'strategy'
  | 'casual'
  | 'sports';

// API types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: ApiError;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Auth types
export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface LoginRequest {
  email?: string;
  phone?: string;
  password?: string;
  platform: 'web' | 'zalo';
}

export interface RegisterRequest {
  name: string;
  email?: string;
  phone?: string;
  password?: string;
  platform: 'web' | 'zalo';
}

// Platform-specific types
export interface ZaloUserInfo {
  id: string;
  name: string;
  avatar: string;
  phone?: string;
}

export interface ZaloPaymentRequest {
  amount: number;
  description: string;
  orderId: string;
  extraData?: Record<string, any>;
}

export interface WebPaymentRequest {
  amount: number;
  description: string;
  orderId: string;
  currency: string;
  returnUrl: string;
  cancelUrl: string;
}
