---
applyTo: '**'
---

# TapTap Multi-Platform Development Instructions

## Project Overview
This is a **multi-platform monorepo** project that supports both **Web** and **Zalo Mini App** platforms using shared components and utilities. The project uses **Yarn 4 workspaces**, **TypeScript**, **React**, **Vite**, and **Tailwind CSS**.

## Architecture & Structure

### Workspace Structure
```
taptap-web-zalo/
├── apps/
│   ├── web/          # Web application (React + Vite)
│   └── zalo/         # Zalo Mini App (React + Vite + ZMP)
├── shared/           # Shared code between platforms
│   ├── components/   # Reusable UI components
│   ├── services/     # Business logic & API services
│   ├── hooks/        # Custom React hooks
│   ├── utils/        # Utility functions
│   └── types/        # TypeScript type definitions
└── packages/         # Additional packages (if needed)
```

### Platform Detection
- Use `isZaloMiniApp()` utility to detect platform
- Conditional rendering for platform-specific features
- Shared components should work on both platforms

## Technology Stack

### Core Technologies
- **TypeScript** - All code must be typed
- **React 18** - With hooks and functional components
- **Vite 5** - Build tool and dev server
- **Tailwind CSS v3.4.x** - For styling (NOT v4)
- **Yarn 4** - Package manager with workspaces
- **React Router** - For web navigation
- **Zustand** - State management

### Platform-Specific
- **Web**: Standard React web app
- **Zalo**: Uses `zmp-ui` components and `zmp-sdk` APIs

## Coding Standards

### File Naming & Structure
- Use **PascalCase** for component files: `Button.tsx`, `HomePage.tsx`
- Use **camelCase** for utility files: `storage.ts`, `auth.service.ts`
- Use **kebab-case** for CSS files: `tailwind.css`
- Index files should export components: `index.ts`

### Component Structure
```typescript
// Example component structure
import React from 'react';
import { isZaloMiniApp } from '../../../utils/platform';

interface ComponentProps {
  // Props with proper TypeScript types
  title: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

export const Component: React.FC<ComponentProps> = ({
  title,
  onClick,
  variant = 'primary',
  disabled = false,
}) => {
  // Component logic here
  
  return (
    <div className="tailwind-classes">
      {/* Component JSX */}
    </div>
  );
};

export default Component;
```

### TypeScript Rules
- **Always use TypeScript** - No `.js` files
- **Explicit types** for all props, state, and function parameters
- **Interface over type** for object shapes
- **Strict mode** enabled in tsconfig.json
- **No `any` type** - use proper typing or `unknown`

### Styling Rules (Tailwind CSS)

#### Version & Configuration
- Use **Tailwind CSS v3.4.x** (NOT v4)
- PostCSS config: `tailwindcss` and `autoprefixer` plugins
- Shared tailwind config extends from base

#### Color System
```typescript
// Use standard Tailwind colors, not custom primary/secondary
✅ CORRECT:
'bg-blue-600 text-white hover:bg-blue-700'
'border-gray-300 focus:border-blue-500'

❌ INCORRECT:
'bg-primary-600 text-white hover:bg-primary-700'
'border-gray-300 focus:border-primary-500'
```

#### Utility Classes
- Use standard Tailwind utilities: `rounded-lg`, `px-4`, `py-2`
- Avoid custom CSS when possible
- Use `@apply` sparingly in component classes only

#### Responsive Design
```typescript
// Mobile-first responsive design
className="w-full md:w-1/2 lg:w-1/3"
```

### Component Development

#### Shared Components
- Must work on both Web and Zalo platforms
- Use platform detection when needed
- Export both named and default exports

#### Button Component Example
```typescript
// Standard button with variants
const variantClasses = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
  secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
  outline: 'border border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500',
};
```

#### Input Component Example
```typescript
// Standard input with error states
const baseClasses = 'w-full border-2 rounded-lg font-inherit transition-all duration-200 outline-none bg-white';
const normalClasses = 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200';
const errorClasses = 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200';
```

### Service Layer

#### API Services
- Use async/await syntax
- Proper error handling with try/catch
- TypeScript interfaces for request/response
- Platform-specific implementations when needed

#### Storage Service
```typescript
// Platform-agnostic storage interface
interface StorageService {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
}
```

### State Management (Zustand)
```typescript
// Store structure
interface AppState {
  user: User | null;
  isLoading: boolean;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  isLoading: false,
  setUser: (user) => set({ user }),
  setLoading: (isLoading) => set({ isLoading }),
}));
```

### Platform-Specific Development

#### Web Platform
- Use standard React Router for navigation
- Standard DOM events and APIs
- Web-specific storage (localStorage)

#### Zalo Platform
- Use `zmp-ui` components when available
- ZMP SDK for platform-specific features
- Zalo-specific storage and APIs

### Error Handling
- Use proper error boundaries
- Graceful fallbacks for platform differences
- Console errors should be informative

### Performance
- Lazy loading for routes
- Optimize bundle size
- Use React.memo for expensive components
- Proper dependency arrays in hooks

## Development Workflow

### Scripts
```bash
# Development
yarn dev:web      # Start web dev server
yarn dev:zalo     # Start zalo dev server

# Building
yarn build:web    # Build web app
yarn build:zalo   # Build zalo app
yarn build:all    # Build both apps

# Linting
yarn lint         # Run ESLint
yarn lint:fix     # Fix lint issues
```

### Testing
- Write unit tests for utilities and services
- Component testing with React Testing Library
- Platform-specific testing when needed

### Git Workflow
- Use conventional commits
- Feature branches for new features
- Code review required for main branch

## Common Patterns

### Platform Detection
```typescript
import { isZaloMiniApp } from '../../../utils/platform';

const Component = () => {
  const isZalo = isZaloMiniApp();
  
  return (
    <div>
      {isZalo ? (
        <ZaloSpecificComponent />
      ) : (
        <WebSpecificComponent />
      )}
    </div>
  );
};
```

### Conditional Styling
```typescript
const buttonClasses = `
  ${baseClasses} 
  ${variantClasses[variant]} 
  ${sizeClasses[size]} 
  ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
  ${className}
`;
```

### Service Usage
```typescript
const Component = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const result = await apiService.getData();
        setData(result);
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  return <div>{/* Component content */}</div>;
};
```

## Migration Notes

### From Styled-Components to Tailwind
- **NEVER** use styled-components in new code
- **ALWAYS** use Tailwind utility classes
- Replace theme colors with standard Tailwind colors
- Remove ThemeProvider and styled imports

### Dependencies to Avoid
- `styled-components` - Use Tailwind CSS
- `@emotion/styled` - Use Tailwind CSS
- `@tailwindcss/postcss` - Use `tailwindcss` plugin
- Tailwind v4 - Use v3.4.x only

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Zalo Mini App WebView
- Mobile-first responsive design

## Performance Targets
- First Contentful Paint < 2s
- Largest Contentful Paint < 3s
- Bundle size < 500KB (gzipped)
- Lighthouse score > 90

## Security Guidelines
- Validate all inputs
- Sanitize user data
- Use HTTPS in production
- Secure storage for sensitive data
- Follow Zalo Mini App security guidelines

## Accessibility
- Semantic HTML elements
- Proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## Documentation
- JSDoc comments for complex functions
- README files for major features
- Component prop documentation
- API endpoint documentation

---

## Quick Reference

### Forbidden Patterns
❌ `import styled from 'styled-components'`
❌ `const StyledButton = styled.button`
❌ `bg-primary-600` (use `bg-blue-600`)
❌ `@tailwindcss/postcss` (use `tailwindcss`)
❌ `any` type in TypeScript

### Recommended Patterns
✅ `className="bg-blue-600 text-white hover:bg-blue-700"`
✅ `interface Props { title: string; onClick?: () => void; }`
✅ `const isZalo = isZaloMiniApp();`
✅ `export const Component: React.FC<Props> = ({ ... }) => { ... }`
✅ `plugins: { tailwindcss: {}, autoprefixer: {} }`

### File Templates
- Use component templates with proper TypeScript interfaces
- Include platform detection when needed
- Always export both named and default exports
- Use proper Tailwind utility classes

This instruction set should be followed for all development work on the TapTap multi-platform project.
