---
applyTo: 'apps/web/**,shared/components/**'
---

# Storybook Development Guide

## Overview
Storybook is integrated into the TapTap project to help develop, test, and document UI components in isolation. This allows for better component development workflow and provides living documentation for the design system.

## Getting Started

### Running Storybook
```bash
# Start Storybook development server
yarn storybook

# Or from the root directory
yarn workspace @taptap/web storybook

# Build static Storybook
yarn build-storybook
```

Storybook will be available at: `http://localhost:6006`

## Project Structure

### Storybook Configuration
```
apps/web/
├── .storybook/
│   ├── main.ts          # Main Storybook configuration
│   └── preview.ts       # Global parameters and decorators
└── src/
    └── **/*.stories.tsx # Story files
```

### Story File Locations
```
shared/components/ui/
├── Button/
│   ├── Button.tsx
│   ├── Button.stories.tsx    # ✅ Component stories
│   └── index.ts
└── Input/
    ├── Input.tsx
    ├── Input.stories.tsx     # ✅ Component stories
    └── index.ts

apps/web/src/components/
├── Layout.tsx
├── Layout.stories.tsx        # ✅ Web-specific stories
├── LoadingSpinner.tsx
└── LoadingSpinner.stories.tsx
```

## Writing Stories

### Basic Story Template
```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { ComponentName } from './ComponentName';

const meta: Meta<typeof ComponentName> = {
  title: 'Category/ComponentName',
  component: ComponentName,
  parameters: {
    layout: 'centered', // or 'fullscreen', 'padded'
    docs: {
      description: {
        component: 'Brief description of the component.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    propName: {
      control: 'select', // or 'boolean', 'text', 'number'
      options: ['option1', 'option2'],
      description: 'Description of this prop',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    propName: 'defaultValue',
  },
};
```

### Story Categories

#### Shared Components
- **Category**: `Shared/`
- **Examples**: `Shared/Button`, `Shared/Input`, `Shared/Card`
- **Location**: `shared/components/ui/*/Component.stories.tsx`

#### Web Components
- **Category**: `Web/`
- **Examples**: `Web/Layout`, `Web/LoadingSpinner`, `Web/Header`
- **Location**: `apps/web/src/components/Component.stories.tsx`

#### Zalo Components (Future)
- **Category**: `Zalo/`
- **Examples**: `Zalo/ZaloButton`, `Zalo/ZaloHeader`
- **Location**: `apps/zalo/src/components/Component.stories.tsx`

### Component Props Documentation

#### ArgTypes Configuration
```typescript
argTypes: {
  variant: {
    control: 'select',
    options: ['primary', 'secondary', 'outline', 'ghost'],
    description: 'The visual style variant of the component',
    table: {
      type: { summary: 'string' },
      defaultValue: { summary: 'primary' },
    },
  },
  size: {
    control: 'radio',
    options: ['small', 'medium', 'large'],
    description: 'The size of the component',
  },
  disabled: {
    control: 'boolean',
    description: 'Whether the component is disabled',
  },
  onClick: {
    action: 'clicked',
    description: 'Function called when component is clicked',
  },
}
```

#### Control Types
- `boolean` - Checkbox control
- `text` - Text input
- `number` - Number input
- `select` - Dropdown selection
- `radio` - Radio button selection
- `range` - Slider control
- `color` - Color picker
- `date` - Date picker
- `object` - JSON editor
- `action` - Action logger

### Story Variants

#### Essential Stories
Every component should have these basic stories:

1. **Default** - Basic usage
```typescript
export const Default: Story = {
  args: {
    children: 'Default Button',
  },
};
```

2. **All Variants** - Show all visual variants
```typescript
export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-2">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
    </div>
  ),
};
```

3. **All Sizes** - Show all size options
```typescript
export const AllSizes: Story = {
  render: () => (
    <div className="flex gap-2 items-center">
      <Button size="small">Small</Button>
      <Button size="medium">Medium</Button>
      <Button size="large">Large</Button>
    </div>
  ),
};
```

4. **States** - Show different states
```typescript
export const States: Story = {
  render: () => (
    <div className="flex gap-2">
      <Button>Normal</Button>
      <Button disabled>Disabled</Button>
      <Button loading>Loading</Button>
    </div>
  ),
};
```

### Advanced Story Features

#### Custom Decorators
```typescript
const meta: Meta<typeof Component> = {
  // ...other config
  decorators: [
    (Story) => (
      <div style={{ margin: '3em', padding: '1em', border: '1px solid #ccc' }}>
        <Story />
      </div>
    ),
  ],
};
```

#### Play Function for Interactions
```typescript
export const InteractiveExample: Story = {
  args: {
    onClick: fn(),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = canvas.getByRole('button');
    
    await userEvent.click(button);
    expect(button).toHaveClass('focus:ring-2');
  },
};
```

#### Multiple Story Templates
```typescript
const Template: StoryFn<typeof Component> = (args) => <Component {...args} />;

export const Example1 = Template.bind({});
Example1.args = { prop1: 'value1' };

export const Example2 = Template.bind({});
Example2.args = { prop1: 'value2' };
```

## Platform-Specific Stories

### Web Platform Stories
```typescript
const meta: Meta<typeof WebComponent> = {
  title: 'Web/ComponentName',
  parameters: {
    platform: 'web',
  },
};
```

### Zalo Platform Stories (Future)
```typescript
const meta: Meta<typeof ZaloComponent> = {
  title: 'Zalo/ComponentName',
  parameters: {
    platform: 'zalo',
  },
  decorators: [
    (Story) => (
      <div className="zalo-mini-app-container">
        <Story />
      </div>
    ),
  ],
};
```

## Documentation Standards

### Component Description
```typescript
parameters: {
  docs: {
    description: {
      component: `
# ComponentName

Brief description of what the component does and when to use it.

## Usage Guidelines
- When to use this component
- Best practices
- Accessibility considerations

## Design Tokens
- Uses standard Tailwind colors
- Follows design system spacing
- Responsive by default
      `,
    },
  },
},
```

### Story Description
```typescript
export const ExampleStory: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Detailed explanation of what this specific story demonstrates.',
      },
    },
  },
};
```

## Best Practices

### DO's ✅
- **Write comprehensive stories** for all component variants
- **Use meaningful story names** that describe the use case
- **Document all props** with clear descriptions
- **Include interactive examples** where applicable
- **Test different states** (loading, error, disabled)
- **Show responsive behavior** with viewport controls
- **Group related stories** in logical categories
- **Use consistent naming conventions**

### DON'Ts ❌
- Don't write stories for internal/utility components
- Don't duplicate stories across categories
- Don't forget to update stories when components change
- Don't use hardcoded values without explanation
- Don't neglect accessibility testing in stories
- Don't mix multiple concepts in one story

## Storybook Configuration

### Addons Enabled
- **@storybook/addon-essentials** - Core functionality
- **@storybook/addon-links** - Link between stories
- **@storybook/addon-interactions** - User interaction testing

### Global Parameters
- **Backgrounds** - Light, dark, and gray backgrounds
- **Viewport** - Responsive testing
- **Platform selector** - Web/Zalo context switching
- **Docs generation** - Auto-generated documentation

### Tailwind CSS Integration
Storybook is configured to use the project's Tailwind CSS styles:
```typescript
// .storybook/preview.ts
import '../src/styles/tailwind.css';
```

## Development Workflow

### Component Development Process
1. **Create component** in appropriate directory
2. **Write basic story** with default example
3. **Add variant stories** for different props
4. **Test interactivity** with play functions
5. **Document props** with argTypes
6. **Add accessibility tests** if needed
7. **Review in Storybook** before integration

### Story Maintenance
- Update stories when component APIs change
- Add new stories for new features
- Remove outdated stories
- Keep documentation current
- Review visual regressions

## Deployment

### Static Build
```bash
# Build static Storybook for deployment
yarn build-storybook

# Output will be in storybook-static/
```

### CI/CD Integration
Storybook can be deployed to various platforms:
- **Vercel** - Automatic deployments
- **Netlify** - Static site hosting
- **GitHub Pages** - Free hosting
- **Chromatic** - Visual testing platform

## Troubleshooting

### Common Issues

#### Import Errors
Make sure component imports use the correct paths:
```typescript
// ✅ Correct
import { Button } from './Button';
import { Input } from '../Input/Input';

// ❌ Incorrect
import { Button } from '@taptap/shared';
```

#### Styling Issues
Ensure Tailwind CSS is properly imported:
```typescript
// In .storybook/preview.ts
import '../src/styles/tailwind.css';
```

#### Build Errors
Check that all dependencies are correctly configured in `main.ts`.

---

**Remember**: Storybook is a living documentation system. Keep stories updated and comprehensive to help the entire development team.
