---
applyTo: 'shared/components/**'
---

# Component Development Patterns

## Component Template

### Basic Component Structure
```typescript
import React from 'react';
import { isZaloMiniApp } from '../../../utils/platform';

interface ComponentNameProps {
  // Required props
  title: string;
  
  // Optional props with defaults
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  
  // Event handlers
  onClick?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  
  // Styling
  className?: string;
  
  // Platform-specific props
  zaloProps?: Record<string, any>;
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  title,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  onFocus,
  onBlur,
  className = '',
  zaloProps,
}) => {
  const isZalo = isZaloMiniApp();
  
  // Base classes - always include these patterns
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  // Variant classes - use standard Tailwind colors
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    outline: 'border border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500',
    ghost: 'text-blue-700 hover:bg-blue-50 focus:ring-blue-500'
  };
  
  // Size classes
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm rounded-md',
    medium: 'px-4 py-2 text-sm rounded-lg',
    large: 'px-6 py-3 text-base rounded-lg'
  };
  
  // Combine classes
  const componentClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  // Platform-specific rendering
  if (isZalo && zaloProps) {
    // Use zmp-ui components when available
    return (
      <div className={componentClasses} {...zaloProps}>
        {loading ? 'Loading...' : title}
      </div>
    );
  }
  
  return (
    <button
      type="button"
      className={componentClasses}
      onClick={onClick}
      onFocus={onFocus}
      onBlur={onBlur}
      disabled={disabled || loading}
    >
      {loading ? 'Loading...' : title}
    </button>
  );
};

export default ComponentName;
```

## Form Component Patterns

### Input Component
```typescript
import React from 'react';

interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  label?: string;
  error?: string;
  size?: 'small' | 'medium' | 'large';
}

export const Input: React.FC<InputProps> = ({
  type = 'text',
  placeholder,
  value,
  defaultValue,
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  required = false,
  className = '',
  label,
  error,
  size = 'medium',
  ...props
}) => {
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-5 py-3 text-lg'
  };

  const baseClasses = 'w-full border-2 rounded-lg font-inherit transition-all duration-200 outline-none bg-white';
  const normalClasses = 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200';
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200' : '';
  const disabledClasses = disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed border-gray-300' : '';
  
  const inputClasses = `${baseClasses} ${sizeClasses[size]} ${error ? errorClasses : normalClasses} ${disabledClasses} ${className}`;

  return (
    <div className="flex flex-col gap-1">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        disabled={disabled}
        required={required}
        className={inputClasses}
        {...props}
      />
      {error && (
        <span className="text-red-500 text-xs mt-1">
          {error}
        </span>
      )}
    </div>
  );
};
```

### Select Component
```typescript
interface SelectProps {
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  value?: string;
  defaultValue?: string;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  label?: string;
  error?: string;
  placeholder?: string;
}

export const Select: React.FC<SelectProps> = ({
  options,
  value,
  defaultValue,
  onChange,
  disabled = false,
  required = false,
  className = '',
  label,
  error,
  placeholder,
  ...props
}) => {
  const baseClasses = 'w-full border-2 rounded-lg px-4 py-2 bg-white font-inherit transition-all duration-200 outline-none';
  const normalClasses = 'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200';
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200' : '';
  const disabledClasses = disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed border-gray-300' : '';
  
  const selectClasses = `${baseClasses} ${error ? errorClasses : normalClasses} ${disabledClasses} ${className}`;

  return (
    <div className="flex flex-col gap-1">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1">
          {label}
        </label>
      )}
      <select
        value={value}
        defaultValue={defaultValue}
        onChange={onChange}
        disabled={disabled}
        required={required}
        className={selectClasses}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map(option => (
          <option 
            key={option.value} 
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <span className="text-red-500 text-xs mt-1">
          {error}
        </span>
      )}
    </div>
  );
};
```

## Layout Component Patterns

### Container Component
```typescript
interface ContainerProps {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  className?: string;
}

export const Container: React.FC<ContainerProps> = ({
  children,
  maxWidth = 'lg',
  className = '',
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  };

  const containerClasses = `mx-auto px-4 ${maxWidthClasses[maxWidth]} ${className}`;

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};
```

### Card Component
```typescript
interface CardProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  title,
  className = '',
  onClick,
}) => {
  const baseClasses = 'bg-white rounded-lg shadow-sm border transition-shadow';
  const interactiveClasses = onClick ? 'hover:shadow-md cursor-pointer' : '';
  const cardClasses = `${baseClasses} ${interactiveClasses} ${className}`;

  return (
    <div className={cardClasses} onClick={onClick}>
      {title && (
        <div className="px-6 py-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};
```

## Export Patterns

### Component Index File
```typescript
// shared/components/ui/Button/index.ts
export { Button } from './Button';
export { default } from './Button';
export type { ButtonProps } from './Button';
```

### Main Components Index
```typescript
// shared/components/ui/index.ts
export { Button } from './Button';
export { Input } from './Input';
export { Select } from './Select';
export { Card } from './Card';
export { Container } from './Container';

// Re-export types
export type { ButtonProps } from './Button';
export type { InputProps } from './Input';
export type { SelectProps } from './Select';
export type { CardProps } from './Card';
export type { ContainerProps } from './Container';
```

## Color Guidelines

### Standard Color Palette
```typescript
// Primary Actions
'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'

// Secondary Actions  
'bg-gray-200 hover:bg-gray-300 focus:ring-gray-500'

// Success States
'bg-green-600 hover:bg-green-700 focus:ring-green-500'

// Warning States
'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'

// Error States
'bg-red-600 hover:bg-red-700 focus:ring-red-500'

// Neutral/Text
'text-gray-900 text-gray-700 text-gray-500'

// Borders
'border-gray-300 border-gray-200 border-blue-300'
```

## Common Patterns

### Loading States
```typescript
const LoadingSpinner = () => (
  <div className="flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
);

// In component
{loading ? <LoadingSpinner /> : <ComponentContent />}
```

### Error States
```typescript
const ErrorMessage = ({ message }: { message: string }) => (
  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
    <p className="text-red-600 text-sm">{message}</p>
  </div>
);
```

### Empty States
```typescript
const EmptyState = ({ title, description }: { title: string; description: string }) => (
  <div className="text-center py-12">
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-500">{description}</p>
  </div>
);
```

**Remember**: Always follow these patterns for consistency across the multi-platform project.
