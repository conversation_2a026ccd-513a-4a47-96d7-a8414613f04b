---
applyTo: '**'
---

# TapTap Development Quick Rules

## 🚨 CRITICAL RULES - NEVER VIOLATE

### Styling System
- **ONLY Tailwind CSS v3.4.x** - NO styled-components, NO emotion, NO custom CSS
- **Standard colors only**: `bg-blue-600`, `text-gray-900`, `border-red-500`
- **NO custom colors**: ❌ `bg-primary-600`, ❌ `text-secondary-500`
- **PostCSS config**: `tailwindcss` and `autoprefixer` plugins ONLY

### TypeScript Rules
- **100% TypeScript** - NO JavaScript files
- **Explicit typing** - NO `any` type
- **Interface over type** for object shapes
- **Strict mode** enabled

### Component Standards
```typescript
// TEMPLATE - Always follow this pattern
import React from 'react';
import { isZaloMiniApp } from '../../../utils/platform';

interface ComponentProps {
  title: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  className?: string;
}

export const Component: React.FC<ComponentProps> = ({
  title,
  onClick,
  variant = 'primary',
  disabled = false,
  className = '',
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors';
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
  };
  
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`;
  
  return (
    <button
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {title}
    </button>
  );
};

export default Component;
```

### Platform Detection
```typescript
import { isZaloMiniApp } from '../../../utils/platform';

const Component = () => {
  const isZalo = isZaloMiniApp();
  
  return isZalo ? <ZaloComponent /> : <WebComponent />;
};
```

### File Structure
```
shared/
├── components/ui/
│   ├── Button/
│   │   ├── Button.tsx      # Component implementation
│   │   └── index.ts        # Export { Button }; export default Button;
│   └── Input/
│       ├── Input.tsx
│       └── index.ts
├── services/
│   ├── api/
│   ├── auth/
│   └── storage/
└── utils/
    ├── platform.ts
    └── helpers.ts
```

## 🎨 Tailwind CSS Rules

### Color System
```typescript
// ✅ CORRECT - Use standard Tailwind colors
'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
'border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200'
'text-red-500 border-red-500 focus:ring-red-200'

// ❌ WRONG - NO custom colors
'bg-primary-600 text-white hover:bg-primary-700'
'border-gray-300 focus:border-primary-500'
```

### Utility Classes
```typescript
// Size variants
const sizeClasses = {
  small: 'px-3 py-1.5 text-sm',
  medium: 'px-4 py-2 text-base',
  large: 'px-5 py-3 text-lg'
};

// Common patterns
const inputClasses = 'w-full border-2 rounded-lg bg-white px-4 py-2 transition-colors focus:outline-none';
const cardClasses = 'bg-white rounded-lg shadow-sm border p-6';
const buttonClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
```

## 📦 Package.json Rules

### Dependencies
```json
{
  "name": "@taptap/zalo",
  "version": "1.0.0",
  "type": "module",
  "dependencies": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-router-dom": "^6.26.0",
    "zustand": "^4.5.0"
  },
  "devDependencies": {
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.21",
    "postcss": "^8.5.6",
    "@tailwindcss/forms": "^0.5.0"
  }
}
```

### PostCSS Config
```javascript
// postcss.config.js
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

## 🔧 Development Commands

```bash
# Development
yarn dev:web      # Web app dev server
yarn dev:zalo     # Zalo app dev server

# Building
yarn build:web    # Build web app
yarn build:zalo   # Build zalo app
yarn build:all    # Build both apps

# Linting
yarn lint         # ESLint check
yarn lint:fix     # Fix lint issues
```

## 🚫 FORBIDDEN PATTERNS

### Never Use These
```typescript
// ❌ FORBIDDEN
import styled from 'styled-components';
const StyledButton = styled.button`...`;
const theme = { primary: '#007bff' };
<ThemeProvider theme={theme}>

// ❌ FORBIDDEN PostCSS
export default {
  plugins: {
    '@tailwindcss/postcss': {},  // WRONG
  },
}

// ❌ FORBIDDEN Colors
'bg-primary-600'
'text-secondary-500'
'border-primary-300'
'focus:ring-primary-500'

// ❌ FORBIDDEN TypeScript
const data: any = response;
function handler(event) { ... }
```

### Always Use These
```typescript
// ✅ CORRECT
import React from 'react';
const Component: React.FC<Props> = ({ ... }) => {
  return <div className="bg-blue-600 text-white">...</div>;
};

// ✅ CORRECT PostCSS
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}

// ✅ CORRECT Colors
'bg-blue-600'
'text-gray-900'
'border-gray-300'
'focus:ring-blue-500'

// ✅ CORRECT TypeScript
interface Props { title: string; onClick?: () => void; }
const Component: React.FC<Props> = ({ title, onClick }) => { ... };
```

## 🔍 Code Review Checklist

Before submitting code, verify:
- [ ] No styled-components imports
- [ ] No custom primary/secondary colors
- [ ] All components have TypeScript interfaces
- [ ] PostCSS config uses `tailwindcss` plugin
- [ ] Components export both named and default
- [ ] Platform detection used when needed
- [ ] Tailwind classes follow standard patterns
- [ ] No `any` types in TypeScript
- [ ] Proper error handling in async functions
- [ ] Mobile-responsive design considered

## 📱 Platform-Specific Guidelines

### Web Platform
- Use React Router for navigation
- Standard DOM events and APIs
- localStorage for client storage
- Standard form elements

### Zalo Platform  
- Use `zmp-ui` components when available
- ZMP SDK for platform-specific features
- Zalo storage APIs
- Follow Zalo Mini App guidelines

---

**Remember: This is a multi-platform project. Always consider both Web and Zalo compatibility when writing code.**
