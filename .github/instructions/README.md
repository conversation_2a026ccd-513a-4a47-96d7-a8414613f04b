---
applyTo: '**'
---

# TapTap Multi-Platform Development Instructions

## 📖 Instruction Files Overview

This directory contains comprehensive development instructions for the TapTap multi-platform project. Follow these guidelines for consistent and maintainable code.

### 📋 Instruction Files

1. **[copilot-rules.md](./copilot-rules.md)** - Complete development guidelines
   - Project architecture and structure
   - Technology stack details
   - Coding standards and TypeScript rules
   - Tailwind CSS styling guidelines
   - Component development patterns
   - Service layer architecture
   - Platform-specific development
   - Performance and security guidelines

2. **[development-rules.md](./development-rules.md)** - Quick reference rules
   - Critical rules (never violate)
   - Component templates
   - Tailwind CSS patterns
   - Package.json configurations
   - Forbidden and recommended patterns
   - Code review checklist

3. **[component-patterns.md](./component-patterns.md)** - UI component standards
   - Basic component structure
   - Form component patterns (Input, Select)
   - Layout component patterns (Container, Card)
   - Export patterns
   - Color guidelines
   - Common patterns (loading, error, empty states)

4. **[zalo-platform.md](./zalo-platform.md)** - Zalo Mini App specific rules
   - Platform detection
   - ZMP SDK usage
   - ZMP UI components
   - Zalo-specific services
   - Navigation patterns
   - App configuration
   - Performance optimization

5. **[storybook.md](./storybook.md)** - Component development and documentation
   - Storybook setup and configuration
   - Writing component stories
   - Documentation standards
   - Interactive testing
   - Multi-platform story support
   - Best practices and workflow

## 🚨 Critical Rules Summary

### Technology Stack
- **TypeScript only** - No JavaScript files
- **Tailwind CSS v3.4.x** - No styled-components
- **React 18** with hooks and functional components
- **Vite 5** for build tooling
- **Yarn 4** workspaces

### File Structure
```
taptap-web-zalo/
├── apps/
│   ├── web/          # Web application
│   └── zalo/         # Zalo Mini App
├── shared/           # Shared components and utilities
└── .github/
    └── instructions/ # This directory
```

### Component Rules
- Use TypeScript interfaces for all props
- Export both named and default exports
- Use standard Tailwind colors (blue, gray, red, etc.)
- Include platform detection when needed
- Follow consistent naming conventions

### Styling Rules
- **Only Tailwind CSS** - No styled-components
- **Standard colors only** - No custom primary/secondary
- **PostCSS config** - Use `tailwindcss` and `autoprefixer`
- **Responsive design** - Mobile-first approach

### Development Commands
```bash
yarn dev:web      # Start web development server
yarn dev:zalo     # Start Zalo development server
yarn build:web    # Build web application
yarn build:zalo   # Build Zalo application
yarn build:all    # Build both applications
yarn storybook    # Start Storybook for component development
```

## 🔍 Before You Code

1. **Read the relevant instruction file** for your work area
2. **Check the component patterns** if creating UI components
3. **Review the development rules** for quick reference
4. **Follow the Zalo platform guide** if working on Zalo-specific features

## 💡 Best Practices

### For All Development
- Always use TypeScript with explicit types
- Follow the component templates provided
- Use consistent naming conventions
- Write responsive, accessible code
- Test on both platforms when applicable

### For Web Development
- Use React Router for navigation
- Implement proper error boundaries
- Use standard web APIs and patterns
- Optimize for web performance

### For Zalo Development
- Use ZMP SDK for platform features
- Follow Zalo Mini App guidelines
- Test in Zalo simulator
- Handle permissions properly
- Use zmp-ui components when available

## 🛠️ Tools and Extensions

### Required VS Code Extensions
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint

### Recommended Browser Extensions
- React Developer Tools
- Tailwind CSS DevTools

## 📞 Support

If you encounter issues or need clarification on any rules:
1. Check the relevant instruction file
2. Review the component patterns
3. Look at existing code examples
4. Ask for clarification in code reviews

---

**Remember**: This is a multi-platform project. Always consider both Web and Zalo compatibility when writing code. Follow the instructions strictly to maintain code quality and consistency across the project.
