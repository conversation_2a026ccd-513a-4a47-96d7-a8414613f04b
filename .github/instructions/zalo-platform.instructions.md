---
applyTo: 'apps/zalo/**'
---

# Zalo Mini App Development Rules

## Zalo-Specific Guidelines

### Platform Detection
```typescript
import { isZaloMiniApp } from '../../../utils/platform';

const Component = () => {
  const isZalo = isZaloMiniApp();
  
  if (!isZalo) {
    return <div>This component only works in Zalo Mini App</div>;
  }
  
  return <ZaloSpecificContent />;
};
```

### ZMP SDK Usage
```typescript
// Import ZMP SDK
import { getStorage, setStorage } from 'zmp-sdk/apis';
import { getUserInfo } from 'zmp-sdk/apis';
import { openWebview } from 'zmp-sdk/apis';

// Use ZMP APIs
const handleGetUserInfo = async () => {
  try {
    const userInfo = await getUserInfo();
    console.log('User info:', userInfo);
  } catch (error) {
    console.error('Failed to get user info:', error);
  }
};
```

### ZMP UI Components
```typescript
// Prefer zmp-ui components when available
import { Button, Input, Select } from 'zmp-ui';

// Use zmp-ui for Zalo-specific UI
<Button variant="primary" onClick={handleClick}>
  Zalo Button
</Button>

// Fallback to shared components when zmp-ui doesn't have the component
import { CustomCard } from '../../../shared/components/ui';
```

### Zalo Storage Service
```typescript
// apps/zalo/src/services/storage.zalo.ts
import { getStorage, setStorage, removeStorage } from 'zmp-sdk/apis';

export class ZaloStorageService {
  async getItem(key: string): Promise<string | null> {
    try {
      const result = await getStorage({ keys: [key] });
      return result[key] || null;
    } catch (error) {
      console.error('Failed to get item from Zalo storage:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      await setStorage({ [key]: value });
    } catch (error) {
      console.error('Failed to set item in Zalo storage:', error);
      throw error;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await removeStorage({ keys: [key] });
    } catch (error) {
      console.error('Failed to remove item from Zalo storage:', error);
      throw error;
    }
  }
}
```

### Zalo Navigation
```typescript
// Use ZMP navigation APIs
import { openWebview, closeApp } from 'zmp-sdk/apis';

// Open external link
const handleOpenLink = (url: string) => {
  openWebview({ url });
};

// Close app
const handleCloseApp = () => {
  closeApp();
};
```

### Zalo App Configuration
```typescript
// apps/zalo/src/app.config.ts
export const appConfig = {
  app: {
    title: 'TapTap Game Hub',
    backgroundColor: '#ffffff',
    textColor: '#000000',
  },
  navigationBar: {
    backgroundColor: '#ffffff',
    textColor: '#000000',
    title: 'TapTap',
  },
  permissions: [
    'getUserInfo',
    'getLocation',
    'uploadFile',
  ],
};
```

### Zalo-Specific Hooks
```typescript
// apps/zalo/src/hooks/useZaloAuth.ts
import { useState, useEffect } from 'react';
import { getUserInfo, login } from 'zmp-sdk/apis';

interface ZaloUser {
  id: string;
  name: string;
  avatar: string;
}

export const useZaloAuth = () => {
  const [user, setUser] = useState<ZaloUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const userInfo = await getUserInfo();
        setUser({
          id: userInfo.id,
          name: userInfo.name,
          avatar: userInfo.avatar,
        });
      } catch (error) {
        console.error('Failed to get user info:', error);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const signIn = async () => {
    try {
      await login();
      const userInfo = await getUserInfo();
      setUser({
        id: userInfo.id,
        name: userInfo.name,
        avatar: userInfo.avatar,
      });
    } catch (error) {
      console.error('Failed to sign in:', error);
      throw error;
    }
  };

  const signOut = () => {
    setUser(null);
  };

  return { user, loading, signIn, signOut };
};
```

### Zalo App Structure
```
apps/zalo/
├── src/
│   ├── components/       # Zalo-specific components
│   │   ├── ZaloButton.tsx
│   │   ├── ZaloHeader.tsx
│   │   └── ZaloTabBar.tsx
│   ├── pages/           # Zalo app pages
│   │   ├── HomePage.tsx
│   │   ├── ProfilePage.tsx
│   │   └── GameDetailPage.tsx
│   ├── services/        # Zalo-specific services
│   │   ├── storage.zalo.ts
│   │   ├── auth.zalo.ts
│   │   └── api.zalo.ts
│   ├── hooks/           # Zalo-specific hooks
│   │   ├── useZaloAuth.ts
│   │   └── useZaloStorage.ts
│   ├── styles/          # Zalo-specific styles
│   │   └── tailwind.css
│   ├── App.tsx          # Zalo app entry point
│   └── main.tsx         # Zalo app bootstrap
├── public/              # Zalo app assets
│   ├── icon.png
│   └── manifest.json
├── app.config.ts        # Zalo app configuration
├── package.json
├── tailwind.config.js
├── postcss.config.js
└── vite.config.ts
```

### Zalo Component Patterns
```typescript
// Zalo-specific component with zmp-ui integration
import React from 'react';
import { Button as ZMPButton } from 'zmp-ui';
import { Button as SharedButton } from '../../../shared/components/ui';

interface ZaloButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  useZMPUI?: boolean;
}

export const ZaloButton: React.FC<ZaloButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  useZMPUI = true,
}) => {
  if (useZMPUI) {
    return (
      <ZMPButton variant={variant} onClick={onClick}>
        {children}
      </ZMPButton>
    );
  }

  return (
    <SharedButton variant={variant} onClick={onClick}>
      {children}
    </SharedButton>
  );
};
```

### Zalo API Integration
```typescript
// apps/zalo/src/services/api.zalo.ts
import { getAccessToken } from 'zmp-sdk/apis';

export class ZaloApiService {
  private baseURL = 'https://your-api-endpoint.com';

  async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const accessToken = await getAccessToken();
      
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
          ...options?.headers,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request error:', error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint);
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}
```

### Zalo Build Configuration
```typescript
// apps/zalo/vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  define: {
    // Define process.env for browser compatibility
    'process.env': '{}',
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'process.env.VITE_API_BASE_URL': JSON.stringify(process.env.VITE_API_BASE_URL),
    'process.env.VITE_PLATFORM': JSON.stringify(process.env.VITE_PLATFORM || 'zalo'),
    'process.env.VITE_ZALO_APP_ID': JSON.stringify(process.env.VITE_ZALO_APP_ID),
    'process.env.VITE_OA_ID': JSON.stringify(process.env.VITE_OA_ID),
    // Add process polyfill for Vite client
    'process': JSON.stringify({ env: {} }),
    'global': 'globalThis',
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@shared': resolve(__dirname, '../../shared'),
    },
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      external: ['zmp-sdk', 'zmp-ui'],
    },
  },
  server: {
    port: 9080,
    host: true,
  },
});
```

### Zalo HTML Template
```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TapTap - Zalo Mini App</title>
  </head>
  <body>
    <div id="app"></div>
    <script>
      // Polyfill for process global in browser
      if (typeof process === 'undefined') {
        window.process = { env: {} };
      }
    </script>
    <script type="module" src="/src/app.tsx"></script>
  </body>
</html>
```

## Zalo-Specific Rules

### DO's ✅
- Use zmp-ui components when available
- Implement proper error handling for ZMP APIs
- Follow Zalo Mini App design guidelines
- Use ZMP storage for persistent data
- Handle Zalo-specific permissions properly
- Test on Zalo Mini App simulator
- Use Zalo-specific navigation patterns

### DON'Ts ❌
- Don't use web-specific APIs (localStorage, sessionStorage)
- Don't use browser-specific features
- Don't ignore Zalo permission requirements
- Don't use custom routing (use Zalo navigation)
- Don't hardcode URLs (use dynamic configuration)
- Don't skip Zalo app configuration

### Testing
```typescript
// Mock ZMP SDK for testing
jest.mock('zmp-sdk/apis', () => ({
  getUserInfo: jest.fn(),
  getStorage: jest.fn(),
  setStorage: jest.fn(),
  openWebview: jest.fn(),
}));
```

### Performance Optimization
- Lazy load components
- Optimize bundle size for mobile
- Use efficient image formats
- Minimize API calls
- Cache frequently used data
- Use proper loading states

### Security
- Validate all user inputs
- Use HTTPS for all API calls
- Handle sensitive data properly
- Follow Zalo security guidelines
- Implement proper authentication
- Use secure storage for tokens

## Troubleshooting

### Common Issues

#### "process is not defined" Error
If you encounter `ReferenceError: process is not defined`, ensure:
1. **Vite Config**: Add proper `process` polyfill in `vite.config.ts`:
```typescript
define: {
  'process.env': '{}',
  'process': JSON.stringify({ env: {} }),
  'global': 'globalThis',
}
```

2. **HTML Template**: Add process polyfill in `index.html`:
```html
<script>
  if (typeof process === 'undefined') {
    window.process = { env: {} };
  }
</script>
```

#### Build Issues
- Ensure all ZMP SDK imports are in the `external` array
- Check that all environment variables are properly defined
- Verify Tailwind CSS configuration is correct
- **ES Module Warning**: Add `"type": "module"` to package.json to eliminate parsing overhead:
```json
{
  "name": "@taptap/zalo",
  "type": "module",
  "main": "src/app.tsx"
}
```

#### Runtime Issues
- Test in Zalo Mini App simulator
- Check browser console for errors
- Verify ZMP SDK permissions are granted

**Remember**: Always test your Zalo Mini App components in the Zalo simulator before deployment.
