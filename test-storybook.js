#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Testing Storybook startup...');

const webDir = path.join(__dirname, 'apps/web');
process.chdir(webDir);

console.log('📂 Working directory:', process.cwd());
console.log('📦 Checking package.json...');

try {
  const packageJson = require('./package.json');
  console.log('✅ Package.json loaded');
  
  const storybookDeps = Object.keys(packageJson.devDependencies || {})
    .filter(dep => dep.includes('storybook'))
    .map(dep => `${dep}@${packageJson.devDependencies[dep]}`);
  
  console.log('📚 Storybook dependencies:', storybookDeps);
  
  // Try to run storybook
  console.log('🎬 Attempting to start Storybook...');
  
  const child = spawn('yarn', ['storybook'], {
    stdio: 'inherit',
    shell: true
  });
  
  child.on('error', (error) => {
    console.error('❌ Error starting Storybook:', error.message);
  });
  
  child.on('exit', (code) => {
    console.log(`🏁 Storybook process exited with code ${code}`);
  });
  
  // Kill after 30 seconds for testing
  setTimeout(() => {
    console.log('⏰ Test timeout, killing process...');
    child.kill();
  }, 30000);
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
