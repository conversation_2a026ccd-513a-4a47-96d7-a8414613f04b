# TapTap Multi-Platform Project

A modern multi-platform application built with React, supporting both Web and Zalo Mini App platforms with 90%+ code sharing.

## 🚀 Quick Start

```bash
# Clone and install dependencies
git clone <repository-url>
cd taptap-web-zalo
yarn install

# Start Web Development
yarn dev:web

# Start Zalo Mini App Development
yarn dev:zalo

# Component Development with Storybook
yarn storybook

# Build for production
yarn build:all
```

## 📁 Project Structure

```
taptap-web-zalo/
├── 📁 shared/                          # Shared code (90% of codebase)
│   ├── 📁 components/ui/              # Platform-aware UI components
│   ├── 📁 hooks/                      # Custom React hooks
│   ├── 📁 services/                   # API & business logic
│   ├── 📁 utils/                      # Utility functions
│   ├── 📁 types/                      # TypeScript definitions
│   └── 📁 constants/                  # Configuration constants
├── 📁 apps/
│   ├── 📁 web/                        # React Web App (Vite)
│   └── 📁 zalo/                       # Zalo Mini App
├── package.json                       # Workspace configuration
├── turbo.json                         # Turborepo configuration
└── tsconfig.json                      # TypeScript configuration
```

## 🔧 Technology Stack

### Core Technologies
- **React 18.3.1** - UI library
- **TypeScript 5.5.0** - Type safety
- **Vite 5.4.0** - Build tool
- **Tailwind CSS 4.1.11** - Utility-first CSS framework
- **React Router 6.26.0** - Routing
- **Zustand 4.5.0** - State management

### Platform-Specific
- **Web**: React SPA with modern web APIs
- **Zalo Mini App**: zmp-sdk 2.46.5 + zmp-ui 1.11.11

### Development Tools
- **Yarn Workspaces** - Monorepo management
- **Turborepo** - Build system optimization
- **ESLint** - Code linting
- **TypeScript** - Type checking
- **Storybook 9.0.15** - Component development and documentation

## 🎯 Key Features

### ✅ Code Sharing (90%+)
- **Shared Components**: Platform-aware UI components
- **Shared Business Logic**: API services, authentication, storage
- **Shared Hooks**: Custom React hooks for common functionality
- **Shared Types**: TypeScript definitions across platforms

### ✅ Platform Detection
- **Automatic Detection**: Runtime platform detection
- **Conditional Rendering**: Platform-specific UI adaptations
- **Feature Detection**: Platform capability awareness

### ✅ Authentication
- **Web**: Email/password, OAuth (Google, Facebook)
- **Zalo**: Zalo Mini App authentication
- **Unified Interface**: Same auth hooks across platforms

### ✅ Storage
- **Web**: localStorage with fallbacks
- **Zalo**: Native storage with localStorage fallback
- **Unified API**: Same storage interface across platforms

### ✅ UI Components
- **Platform-Aware**: Adapts to platform capabilities
- **Consistent Design**: Unified design system
- **Responsive**: Mobile-first design approach

### ✅ Component Development
- **Storybook Integration**: Interactive component playground
- **Living Documentation**: Auto-generated component docs
- **Multi-Platform Testing**: Test components across platforms
- **Design System**: Centralized component library

## 🔄 Latest Updates (2025)

### Package Versions
- **zmp-sdk**: `2.46.5` (latest) - Published 4 days ago
- **zmp-ui**: `1.11.11` (latest) - Published 8 days ago
- **React**: `18.3.1` (recommended)
- **TypeScript**: `5.5.0` (recommended)
- **Vite**: `5.4.0` (recommended)

### Recent Improvements
- Fixed nativeStorage bug on web platform
- Enhanced TypeScript support
- Improved platform detection
- Better error handling and fallbacks

## 🛠️ Development Commands

```bash
# Install dependencies
yarn install

# Development
yarn dev:web              # Start web development server
yarn dev:zalo             # Start Zalo Mini App development

# Building
yarn build:web            # Build web application
yarn build:zalo           # Build Zalo Mini App
yarn build:all            # Build both platforms

# Component Development
yarn storybook            # Start Storybook development server
yarn build-storybook     # Build static Storybook

# Code Quality
yarn lint                 # Lint all packages
yarn lint:fix             # Fix linting issues
yarn type-check           # TypeScript type checking

# Cleaning
yarn clean                # Clean node_modules
```

## 🎨 UI Components

### Platform-Aware Components
```typescript
import { Button, Input } from '@taptap/shared';

// Automatically adapts to platform
<Button variant="primary" onClick={handleClick}>
  Click me
</Button>

// Web: Uses Tailwind CSS classes
// Zalo: Uses zmp-ui components when needed
```

### Conditional Platform Logic
```typescript
import { isZaloMiniApp, isWebApp } from '@taptap/shared';

if (isZaloMiniApp()) {
  // Zalo-specific logic
} else if (isWebApp()) {
  // Web-specific logic
}
```

## 🔐 Authentication

### Usage Example
```typescript
import { useAuth } from '@taptap/shared';

const LoginComponent = () => {
  const { login, user, loading } = useAuth();

  const handleLogin = async () => {
    try {
      await login({ email, password });
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  return (
    <div>
      {user ? (
        <p>Welcome, {user.name}!</p>
      ) : (
        <Button onClick={handleLogin} loading={loading}>
          Login
        </Button>
      )}
    </div>
  );
};
```

## 💾 Storage

### Usage Example
```typescript
import { useStorage } from '@taptap/shared';

const SettingsComponent = () => {
  const { value: theme, setValue: setTheme } = useStorage('theme', 'light');

  return (
    <div>
      <p>Current theme: {theme}</p>
      <Button onClick={() => setTheme('dark')}>
        Switch to Dark
      </Button>
    </div>
  );
};
```

## 📱 Platform Features

### Usage Example
```typescript
import { usePlatformFeatures } from '@taptap/shared';

const MapComponent = () => {
  const { getLocation, shareContent, makePayment } = usePlatformFeatures();

  const handleGetLocation = async () => {
    try {
      const location = await getLocation();
      console.log('Location:', location);
    } catch (error) {
      console.error('Location access failed:', error);
    }
  };

  return (
    <div>
      <Button onClick={handleGetLocation}>
        Get Location
      </Button>
    </div>
  );
};
```

## 🚀 Deployment

### Web Deployment
```bash
# Build for production
yarn build:web

# Deploy to Vercel/Netlify/etc.
# Built files are in apps/web/dist/
```

### Zalo Mini App Deployment
```bash
# Build for Zalo
yarn build:zalo

# Deploy using zmp CLI
cd apps/zalo
zmp deploy
```

## 📊 Performance Optimizations

### Code Splitting
- **Lazy Loading**: Pages are lazy-loaded
- **Chunk Splitting**: Vendor libraries separated
- **Tree Shaking**: Unused code elimination

### Platform Optimizations
- **Web**: Service workers, caching strategies
- **Zalo**: Optimized bundle size (<10MB)
- **Shared**: Efficient re-renders, memoization

## 🔍 Development Tips

### Adding New Features
1. **Start with Shared**: Implement in `shared/` first
2. **Platform-Specific**: Add platform differences if needed
3. **Testing**: Test on both platforms
4. **Documentation**: Update README and docs

### Debugging
- **Web**: Use browser dev tools
- **Zalo**: Use Zalo Mini App DevTools
- **Shared**: Console logging with platform context

### Best Practices
- **Type Safety**: Always use TypeScript
- **Error Handling**: Implement proper error boundaries
- **Performance**: Use React.memo for expensive components
- **Accessibility**: Follow WCAG guidelines

## 📈 Bundle Analysis

### Size Targets
- **Web**: ~500KB initial bundle
- **Zalo**: <3MB total size (platform limit)
- **Shared**: ~300KB (60% of codebase)

### Optimization Techniques
- **Dynamic Imports**: Platform-specific code
- **Tree Shaking**: Remove unused exports
- **Code Splitting**: Route-based splitting

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes
4. **Test** on both platforms
5. **Submit** a pull request

### Development Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# Test on web
yarn dev:web

# Test on Zalo
yarn dev:zalo

# Build and test
yarn build:all

# Commit and push
git add .
git commit -m "Add new feature"
git push origin feature/new-feature
```

## 📚 Documentation

- **API Documentation**: See `docs/api.md`
- **Component Documentation**: See `docs/components.md`
- **Platform Guide**: See `docs/platform-guide.md`
- **Deployment Guide**: See `docs/deployment.md`

## 🐛 Troubleshooting

### Common Issues

#### Dependencies Not Found
```bash
# Clean and reinstall
yarn clean
yarn install
```

#### Platform Detection Issues
```typescript
// Check platform detection
import { detectPlatform } from '@taptap/shared';
console.log('Platform:', detectPlatform());
```

#### Build Errors
```bash
# Check TypeScript errors
yarn type-check

# Fix linting issues
yarn lint:fix
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team** - For the amazing React ecosystem
- **Zalo Team** - For the Zalo Mini App platform
- **Open Source Community** - For all the great tools and libraries

---

**Built with ❤️ by the TapTap Team**
