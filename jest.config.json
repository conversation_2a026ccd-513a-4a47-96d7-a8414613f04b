{"testEnvironment": "jsdom", "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx"], "setupFilesAfterEnv": ["<rootDir>/jest.setup.ts"], "testMatch": ["**/__tests__/**/*.(ts|tsx|js)", "**/*.(test|spec).(ts|tsx|js)"], "collectCoverageFrom": ["shared/**/*.{ts,tsx}", "!shared/**/*.d.ts", "!shared/**/*.stories.tsx"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/shared/$1"}}