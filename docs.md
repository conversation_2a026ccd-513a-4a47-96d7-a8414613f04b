# Multi-Platform Project Structure (Web + Zalo Mini App)

## � Latest Updates (2025)

### Package Versions (Updated July 2025)
- **zmp-sdk**: `2.46.5` (latest) - Published 4 days ago
- **zmp-ui**: `1.11.11` (latest) - Published 8 days ago  
- **React**: `^18.3.1` (recommended)
- **TypeScript**: `^5.5.0` (recommended)
- **Vite**: `^5.4.0` (recommended)

### Recent Bug Fixes & Improvements
- **v2.44.2 (2025-02-11)**: Fixed nativeStorage bug on web platform
- **v1.11.11 (2025-07)**: Latest ZaUI components with improved performance
- Enhanced TypeScript support and better error handling
- Improved web compatibility for hybrid development

### New Features in 2025
- Better localStorage/nativeStorage compatibility
- Enhanced platform detection
- Improved performance optimizations
- Better developer experience with updated tooling

## �📁 Project Structure

```
my-app/
├── 📁 shared/                          # Shared code for both platforms
│   ├── 📁 components/                  # Reusable React components
│   │   ├── 📁 ui/                     # Base UI components
│   │   │   ├── Button/
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Button.web.tsx     # Web-specific implementation
│   │   │   │   ├── Button.zalo.tsx    # Zalo-specific implementation
│   │   │   │   └── index.ts
│   │   │   ├── Input/
│   │   │   ├── Modal/
│   │   │   └── index.ts
│   │   ├── 📁 business/               # Business logic components
│   │   │   ├── ProductCard/
│   │   │   ├── UserProfile/
│   │   │   └── ShoppingCart/
│   │   └── index.ts
│   ├── 📁 hooks/                      # Custom React hooks
│   │   ├── useAuth.ts
│   │   ├── useApi.ts
│   │   ├── useLocalStorage.ts
│   │   └── index.ts
│   ├── 📁 services/                   # API and business logic
│   │   ├── api/
│   │   │   ├── auth.ts
│   │   │   ├── products.ts
│   │   │   └── index.ts
│   │   ├── auth/
│   │   │   ├── auth.service.ts
│   │   │   ├── auth.web.ts           # Web-specific auth
│   │   │   ├── auth.zalo.ts          # Zalo-specific auth
│   │   │   └── index.ts
│   │   └── storage/
│   │       ├── storage.service.ts
│   │       ├── storage.web.ts        # localStorage for web
│   │       ├── storage.zalo.ts       # Zalo storage APIs
│   │       └── index.ts
│   ├── 📁 utils/                     # Utility functions
│   │   ├── platform.ts               # Platform detection
│   │   ├── constants.ts
│   │   ├── helpers.ts
│   │   └── index.ts
│   ├── 📁 types/                     # TypeScript types
│   │   ├── api.ts
│   │   ├── user.ts
│   │   ├── product.ts
│   │   └── index.ts
│   ├── 📁 store/                     # State management
│   │   ├── authStore.ts
│   │   ├── productStore.ts
│   │   └── index.ts
│   └── 📁 constants/
│       ├── config.ts
│       ├── routes.ts
│       └── index.ts
│
├── 📁 apps/                          # Platform-specific applications
│   ├── 📁 web/                       # React.js Web Application (SPA)
│   │   ├── 📁 public/
│   │   │   ├── index.html
│   │   │   ├── favicon.ico
│   │   │   └── manifest.json
│   │   ├── 📁 src/
│   │   │   ├── 📁 pages/             # Page components
│   │   │   │   ├── HomePage/
│   │   │   │   │   ├── HomePage.tsx
│   │   │   │   │   └── HomePage.css
│   │   │   │   ├── LoginPage/
│   │   │   │   ├── ProductsPage/
│   │   │   │   └── index.ts
│   │   │   ├── 📁 components/        # Web-specific components
│   │   │   │   ├── Layout/
│   │   │   │   │   ├── Layout.tsx
│   │   │   │   │   └── Layout.css
│   │   │   │   ├── Navigation/
│   │   │   │   │   ├── Navigation.tsx
│   │   │   │   │   └── Navigation.css
│   │   │   │   ├── Router/
│   │   │   │   │   └── AppRouter.tsx
│   │   │   │   └── SEO/
│   │   │   ├── 📁 styles/
│   │   │   │   ├── index.css
│   │   │   │   ├── App.css
│   │   │   │   └── variables.css
│   │   │   ├── 📁 lib/
│   │   │   │   ├── providers.tsx
│   │   │   │   └── utils.ts
│   │   │   ├── App.tsx               # Main App component
│   │   │   ├── index.tsx             # Entry point
│   │   │   └── setupTests.ts
│   │   ├── vite.config.ts            # Vite config
│   │   ├── tailwind.config.js
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   └── 📁 zalo/                      # Zalo Mini App
│       ├── 📁 src/
│       │   ├── 📁 pages/            # Zalo Mini App pages
│       │   │   ├── index/
│       │   │   │   ├── index.tsx
│       │   │   │   └── index.scss
│       │   │   ├── login/
│       │   │   └── products/
│       │   ├── 📁 components/       # Zalo-specific components
│       │   │   ├── ZaloLayout/
│       │   │   ├── ZaloNavigation/
│       │   │   └── ZaloHeader/
│       │   ├── 📁 services/         # Zalo-specific services
│       │   │   ├── zalo-sdk.ts
│       │   │   └── navigation.ts
│       │   ├── 📁 styles/
│       │   │   ├── app.scss
│       │   │   └── variables.scss
│       │   └── app.tsx              # Root component
│       ├── app-config.json          # Zalo Mini App config
│       ├── package.json
│       ├── tsconfig.json
│       ├── vite.config.ts
│       └── tailwind.config.js
│
├── 📁 packages/                     # Optional: Shared packages
│   ├── 📁 ui-kit/                   # Shared UI components as package
│   └── 📁 core/                     # Core business logic package
│
├── 📁 tools/                        # Build tools and scripts
│   ├── build-web.js
│   ├── build-zalo.js
│   └── deploy.js
│
├── package.json                     # Root package.json (workspace)
├── turbo.json                       # Turborepo config (optional)
├── tsconfig.json                    # Base TypeScript config
└── README.md
```

## 🔧 Core Implementation Files

### 1. Platform Detection (`shared/utils/platform.ts`)

```typescript
export enum Platform {
  WEB = 'web',
  ZALO = 'zalo'
}

export const detectPlatform = (): Platform => {
  if (typeof window === 'undefined') return Platform.WEB;
  
  // Check if running in Zalo environment
  if (window.ZaloJavaScriptInterface || 
      navigator.userAgent.includes('Zalo')) {
    return Platform.ZALO;
  }
  
  return Platform.WEB;
};

export const isZaloMiniApp = () => detectPlatform() === Platform.ZALO;
export const isWebApp = () => detectPlatform() === Platform.WEB;
```

### 2. Platform-Specific Component Factory (`shared/components/ui/Button/index.ts`)

```typescript
import { lazy } from 'react';
import { detectPlatform, Platform } from '../../../utils/platform';

// Dynamic import based on platform
const ButtonWeb = lazy(() => import('./Button.web'));
const ButtonZalo = lazy(() => import('./Button.zalo'));

export const Button = detectPlatform() === Platform.ZALO ? ButtonZalo : ButtonWeb;

export default Button;
```

### 3. Authentication Service (`shared/services/auth/index.ts`)

```typescript
import { detectPlatform, Platform } from '../../utils/platform';
import { WebAuthService } from './auth.web';
import { ZaloAuthService } from './auth.zalo';

class AuthServiceFactory {
  private static instance: WebAuthService | ZaloAuthService;

  static getInstance() {
    if (!this.instance) {
      const platform = detectPlatform();
      this.instance = platform === Platform.ZALO 
        ? new ZaloAuthService() 
        : new WebAuthService();
    }
    return this.instance;
  }
}

export const authService = AuthServiceFactory.getInstance();
export * from './auth.service'; // Common interfaces
```

### 4. Build Configuration

#### Web App (`apps/web/package.json`)
```json
{
  "name": "@myapp/web",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "react-router-dom": "^6.8.0",
    "@myapp/shared": "workspace:*"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.4.0",
    "vitest": "^0.34.0"
  }
}
```

#### Zalo Mini App (`apps/zalo/package.json`)
```json
{
  "name": "@myapp/zalo",
  "scripts": {
    "dev": "zmp start",
    "build": "zmp build",
    "deploy": "zmp deploy"
  },
  "dependencies": {
    "react": "^18.0.0",
    "zmp-ui": "^1.11.7",
    "zmp-sdk": "latest",
    "@myapp/shared": "workspace:*"
  }
}
```

#### Root Workspace (`package.json`)
```json
{
  "name": "myapp-monorepo",
  "private": true,
  "workspaces": [
    "shared",
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "dev:web": "yarn workspace @myapp/web dev",
    "dev:zalo": "yarn workspace @myapp/zalo dev",
    "build:web": "yarn workspace @myapp/web build",
    "build:zalo": "yarn workspace @myapp/zalo build",
    "build:all": "yarn build:web && yarn build:zalo"
  },
  "devDependencies": {
    "turbo": "^1.10.0",
    "typescript": "^5.0.0"
  }
}
```

## 🎯 Usage Examples

### 1. Web App Entry Point (`apps/web/src/index.tsx`)

```typescript
import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import './styles/index.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>
);
```

### 2. Web App Main Component (`apps/web/src/App.tsx`)

```typescript
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout } from './components/Layout';
import { HomePage, LoginPage, ProductsPage } from './pages';
import { AuthProvider } from '@myapp/shared';
import './App.css';

const App: React.FC = () => {
  return (
    <AuthProvider>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/products" element={<ProductsPage />} />
        </Routes>
      </Layout>
    </AuthProvider>
  );
};

export default App;
```

### 3. Web Layout Component (`apps/web/src/components/Layout/Layout.tsx`)

```typescript
import React from 'react';
import { Navigation } from '../Navigation';
import './Layout.css';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="layout">
      <Navigation />
      <main className="main-content">
        {children}
      </main>
    </div>
  );
};
```

### 4. Web Router Component (`apps/web/src/components/Router/AppRouter.tsx`)

```typescript
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@myapp/shared';
import { HomePage, LoginPage, ProductsPage } from '../../pages';

export const AppRouter: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route 
        path="/login" 
        element={user ? <Navigate to="/" /> : <LoginPage />} 
      />
      <Route 
        path="/products" 
        element={user ? <ProductsPage /> : <Navigate to="/login" />} 
      />
    </Routes>
  );
};
```

### 5. Vite Configuration (`apps/web/vite.config.ts`)

```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../../shared'),
    },
  },
  server: {
    port: 9070,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
```

### 6. Platform-Specific Button Component (Improved Approach)

#### ❌ Old Approach: Separate Component Files
```typescript
// shared/components/ui/Button/Button.web.tsx
import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

export const ButtonWeb: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary' 
}) => {
  return (
    <button 
      className={`btn btn-${variant}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

export default ButtonWeb;
```

```typescript
// shared/components/ui/Button/Button.zalo.tsx
import React from 'react';
import { Button as ZaUIButton } from 'zmp-ui';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

export const ButtonZalo: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary' 
}) => {
  return (
    <ZaUIButton 
      type={variant}
      onClick={onClick}
    >
      {children}
    </ZaUIButton>
  );
};

export default ButtonZalo;
```

#### ✅ **Improved Approach: Unified Component with Conditional Logic**

```typescript
// shared/components/ui/Button/Button.tsx
import React from 'react';
import styled from 'styled-components';
import { Button as ZaUIButton } from 'zmp-ui';
import { isZaloMiniApp } from '../../../utils/platform';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  loading?: boolean;
  size?: 'small' | 'medium' | 'large';
}

// Styled component with platform-specific styling
const StyledButton = styled.button<{ variant: string; disabled?: boolean }>`
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  /* Platform-specific styles */
  ${() => isZaloMiniApp() ? `
    /* Zalo Mini App specific styles */
    background: var(--zmp-primary-color);
    color: white;
    box-shadow: none;
    font-family: inherit;
  ` : `
    /* Web specific styles */
    background: #1976d2;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    &:hover {
      background: #1565c0;
      transform: translateY(-1px);
    }
  `}
  
  ${props => props.variant === 'secondary' && `
    background: transparent;
    border: 2px solid currentColor;
    color: #1976d2;
  `}
  
  ${props => props.disabled && `
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  `}
`;

export const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  disabled = false,
  loading = false,
  size = 'medium'
}) => {
  // Platform-specific rendering logic
  if (isZaloMiniApp()) {
    return (
      <ZaUIButton
        type={variant}
        onClick={onClick}
        disabled={disabled || loading}
        loading={loading}
        size={size}
      >
        {children}
      </ZaUIButton>
    );
  }

  // Web implementation
  return (
    <StyledButton
      variant={variant}
      onClick={onClick}
      disabled={disabled || loading}
    >
      {loading ? (
        <>
          <span style={{ marginRight: '8px' }}>⟳</span>
          {children}
        </>
      ) : (
        children
      )}
    </StyledButton>
  );
};

export default Button;
```

#### Platform-Aware Styled Components

```typescript
// shared/components/ui/Container/Container.tsx
import styled from 'styled-components';
import { isZaloMiniApp } from '../../../utils/platform';

const StyledContainer = styled.div`
  padding: 16px;
  border-radius: 8px;
  
  /* Platform-specific conditional styling */
  ${() => isZaloMiniApp() ? `
    /* Zalo Mini App styles */
    background-color: var(--zmp-background-color);
    box-shadow: none;
    border: 1px solid var(--zmp-border-color);
    margin: 8px;
  ` : `
    /* Web styles */
    background-color: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    margin: 16px;
    
    @media (max-width: 768px) {
      margin: 8px;
      padding: 12px;
    }
  `}
`;

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
}

export const Container: React.FC<ContainerProps> = ({ children, className }) => {
  return (
    <StyledContainer className={className}>
      {children}
    </StyledContainer>
  );
};
```

```typescript
// shared/components/ui/Button/Button.web.tsx
import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

export const ButtonWeb: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary' 
}) => {
  return (
    <button 
      className={`btn btn-${variant}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

export default ButtonWeb;
```

```typescript
// shared/components/ui/Button/Button.zalo.tsx
import React from 'react';
import { Button as ZaUIButton } from 'zmp-ui';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

export const ButtonZalo: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary' 
}) => {
  return (
    <ZaUIButton 
      type={variant}
      onClick={onClick}
    >
      {children}
    </ZaUIButton>
  );
};

export default ButtonZalo;
```

### 7. Service Abstraction with Adapter Pattern

#### Storage Service Implementation

```typescript
// shared/services/storage/storage.service.ts
export interface StorageService {
  setItem(key: string, value: any): Promise<void>;
  getItem(key: string): Promise<any>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

// shared/services/storage/storage.web.ts
export class WebStorageService implements StorageService {
  async setItem(key: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error('Web storage setItem failed:', error);
      throw error;
    }
  }

  async getItem(key: string): Promise<any> {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Web storage getItem failed:', error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    localStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    localStorage.clear();
  }
}

// shared/services/storage/storage.zalo.ts
import { setStorage, getStorage, clearStorage } from 'zmp-sdk';

export class ZaloStorageService implements StorageService {
  async setItem(key: string, value: any): Promise<void> {
    try {
      await setStorage({ key, data: value });
    } catch (error) {
      console.error('Zalo storage setItem failed:', error);
      throw error;
    }
  }

  async getItem(key: string): Promise<any> {
    try {
      const result = await getStorage({ key });
      return result.data;
    } catch (error) {
      console.error('Zalo storage getItem failed:', error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await setStorage({ key, data: null });
    } catch (error) {
      console.error('Zalo storage removeItem failed:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      await clearStorage();
    } catch (error) {
      console.error('Zalo storage clear failed:', error);
    }
  }
}

// shared/services/storage/index.ts
import { detectPlatform, Platform } from '../../utils/platform';
import { WebStorageService } from './storage.web';
import { ZaloStorageService } from './storage.zalo';
import { StorageService } from './storage.service';

class StorageServiceFactory {
  private static instance: StorageService;

  static getInstance(): StorageService {
    if (!this.instance) {
      const platform = detectPlatform();
      this.instance = platform === Platform.ZALO 
        ? new ZaloStorageService() 
        : new WebStorageService();
    }
    return this.instance;
  }
}

export const storageService = StorageServiceFactory.getInstance();
export * from './storage.service';
```

#### Analytics Service Abstraction

```typescript
// shared/services/analytics/analytics.service.ts
export interface AnalyticsService {
  track(event: string, properties?: Record<string, any>): Promise<void>;
  identify(userId: string, traits?: Record<string, any>): Promise<void>;
  page(name: string, properties?: Record<string, any>): Promise<void>;
}

// shared/services/analytics/analytics.web.ts
export class WebAnalyticsService implements AnalyticsService {
  async track(event: string, properties?: Record<string, any>): Promise<void> {
    // Google Analytics, Mixpanel, or other web analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', event, properties);
    }
  }

  async identify(userId: string, traits?: Record<string, any>): Promise<void> {
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_TRACKING_ID', {
        user_id: userId,
        custom_map: traits
      });
    }
  }

  async page(name: string, properties?: Record<string, any>): Promise<void> {
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_TRACKING_ID', {
        page_title: name,
        page_location: window.location.href,
        ...properties
      });
    }
  }
}

// shared/services/analytics/analytics.zalo.ts
export class ZaloAnalyticsService implements AnalyticsService {
  async track(event: string, properties?: Record<string, any>): Promise<void> {
    // Zalo Mini App analytics or custom tracking
    console.log('Zalo Analytics Track:', event, properties);
    // Could integrate with Zalo's analytics APIs when available
  }

  async identify(userId: string, traits?: Record<string, any>): Promise<void> {
    console.log('Zalo Analytics Identify:', userId, traits);
  }

  async page(name: string, properties?: Record<string, any>): Promise<void> {
    console.log('Zalo Analytics Page:', name, properties);
  }
}
```

#### Custom Hook for Storage

```typescript
// shared/hooks/useStorage.ts
import { useState, useEffect } from 'react';
import { storageService } from '../services/storage';

export const useStorage = <T>(key: string, defaultValue: T) => {
  const [value, setValue] = useState<T>(defaultValue);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadValue = async () => {
      try {
        const storedValue = await storageService.getItem(key);
        if (storedValue !== null) {
          setValue(storedValue);
        }
      } catch (error) {
        console.error('Failed to load from storage:', error);
      } finally {
        setLoading(false);
      }
    };

    loadValue();
  }, [key]);

  const updateValue = async (newValue: T) => {
    try {
      setValue(newValue);
      await storageService.setItem(key, newValue);
    } catch (error) {
      console.error('Failed to save to storage:', error);
      // Rollback on error
      setValue(value);
    }
  };

  const removeValue = async () => {
    try {
      setValue(defaultValue);
      await storageService.removeItem(key);
    } catch (error) {
      console.error('Failed to remove from storage:', error);
    }
  };

  return {
    value,
    setValue: updateValue,
    removeValue,
    loading
  };
};
```

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Multi-platform React app" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>My Multi-Platform App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
```

### 8. Zalo Mini App Configuration (`apps/zalo/app-config.json`)

```json
{
  "app": {
    "title": "My Multi-Platform App",
    "statusBarColor": "#1976d2"
  },
  "template": {
    "primaryColor": "#1976d2",
    "secondaryColor": "#ff9800"
  }
}
```

### 9. Zalo-Specific Authentication Service (`shared/services/auth/auth.zalo.ts`)

```typescript
import { getAccessToken, getUserInfo, login } from 'zmp-sdk';
import { AuthService, User } from './auth.service';

export class ZaloAuthService implements AuthService {
  async login(): Promise<User> {
    try {
      // Zalo Mini App login flow
      await login();
      const accessToken = await getAccessToken();
      const userInfo = await getUserInfo();
      
      return {
        id: userInfo.id,
        name: userInfo.name,
        avatar: userInfo.avatar,
        phone: userInfo.phone
      };
    } catch (error) {
      throw new Error('Zalo login failed');
    }
  }

  async logout(): Promise<void> {
    // Zalo logout logic
    // Note: Zalo Mini App doesn't provide explicit logout
    // You may need to clear local storage/state
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const userInfo = await getUserInfo();
      return {
        id: userInfo.id,
        name: userInfo.name,
        avatar: userInfo.avatar,
        phone: userInfo.phone
      };
    } catch (error) {
      return null;
    }
  }
}
```

### 10. ZMP SDK Integration (`shared/services/zalo/zmp-sdk.ts`)

```typescript
import { 
  getAccessToken, 
  getUserInfo, 
  getPhoneNumber,
  getLocation,
  openChat,
  followOA,
  payment,
  setStorage,
  getStorage,
  clearStorage
} from 'zmp-sdk';

export class ZMPService {
  // Authentication
  static async getAccessToken() {
    return await getAccessToken();
  }

  static async getUserInfo() {
    return await getUserInfo();
  }

  static async getPhoneNumber() {
    return await getPhoneNumber();
  }

  // Location Services
  static async getLocation() {
    return await getLocation();
  }

  // Social Features
  static async openChat(userId: string) {
    return await openChat({ userId });
  }

  static async followOA(oaId: string) {
    return await followOA({ oaId });
  }

  // Payment
  static async createPayment(order: any) {
    return await payment({
      amount: order.amount,
      description: order.description,
      orderId: order.id
    });
  }

  // Storage
  static async setStorage(key: string, data: any) {
    return await setStorage({ key, data });
  }

  static async getStorage(key: string) {
    return await getStorage({ key });
  }

  static async clearStorage() {
    return await clearStorage();
  }
}
```

### 11. Environment Configuration

#### Web Environment (`apps/web/.env`)
```bash
VITE_API_BASE_URL=https://api.yourapp.com
VITE_PLATFORM=web
VITE_ANALYTICS_ID=your-analytics-id
```

#### Zalo Environment (`apps/zalo/.env.development`)
```bash
VITE_API_BASE_URL=https://api.yourapp.com
VITE_PLATFORM=zalo
VITE_DEFAULT_ACCESS_TOKEN=your-test-token
VITE_ZALO_APP_ID=your-zalo-app-id
VITE_OA_ID=your-oa-id
```

#### Zalo Production (`apps/zalo/.env.production`)
```bash
VITE_API_BASE_URL=https://api.yourapp.com
VITE_PLATFORM=zalo
VITE_ZALO_APP_ID=your-zalo-app-id
VITE_OA_ID=your-oa-id
```

### 12. Authentication Hook

```typescript
// shared/hooks/useAuth.ts
import { useState, useEffect } from 'react';
import { authService } from '../services/auth';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const login = async (credentials?: any) => {
    setLoading(true);
    try {
      const userData = await authService.login(credentials);
      setUser(userData);
      return userData;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    await authService.logout();
    setUser(null);
  };

  useEffect(() => {
    authService.getCurrentUser()
      .then(setUser)
      .finally(() => setLoading(false));
  }, []);

  return { user, login, logout, loading };
};
```

## 🚀 Deployment Commands

```bash
# Development
yarn dev:web      # Start React web development server (Vite)
yarn dev:zalo     # Start Zalo Mini App development

# Build
yarn build:web    # Build React web application
yarn build:zalo   # Build Zalo Mini App

# Deploy
yarn deploy:web   # Deploy to web hosting (Vercel, Netlify, GitHub Pages)
yarn deploy:zalo  # Deploy to Zalo Mini App platform
```

### 13. Platform-Specific Hooks (`shared/hooks/usePlatformFeatures.ts`)

```typescript
import { useState, useEffect } from 'react';
import { isZaloMiniApp } from '../utils/platform';
import { ZMPService } from '../services/zalo/zmp-sdk';

export const usePlatformFeatures = () => {
  const [location, setLocation] = useState(null);
  const [canUseZaloFeatures, setCanUseZaloFeatures] = useState(false);

  useEffect(() => {
    setCanUseZaloFeatures(isZaloMiniApp());
  }, []);

  const getLocation = async () => {
    if (isZaloMiniApp()) {
      try {
        const loc = await ZMPService.getLocation();
        setLocation(loc);
        return loc;
      } catch (error) {
        console.error('Failed to get location from Zalo:', error);
      }
    } else {
      // Web geolocation API
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const loc = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            };
            setLocation(loc);
          },
          (error) => console.error('Geolocation error:', error)
        );
      }
    }
  };

  const shareContent = async (content: { title: string; url: string }) => {
    if (isZaloMiniApp()) {
      // Use Zalo sharing APIs
      // Implementation depends on specific Zalo sharing methods
    } else if (navigator.share) {
      // Web Share API
      await navigator.share(content);
    } else {
      // Fallback sharing options
      console.log('Sharing not supported');
    }
  };

  const makePayment = async (orderData: any) => {
    if (isZaloMiniApp()) {
      return await ZMPService.createPayment(orderData);
    } else {
      // Redirect to web payment gateway
      window.location.href = `/payment?orderId=${orderData.id}`;
    }
  };

  return {
    location,
    canUseZaloFeatures,
    getLocation,
    shareContent,
    makePayment
  };
};
```

### 14. Storage Abstraction (`shared/services/storage/storage.service.ts`)

```typescript
export interface StorageService {
  setItem(key: string, value: any): Promise<void>;
  getItem(key: string): Promise<any>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

export interface StorageServiceFactory {
  getInstance(): StorageService;
}
```

### 15. Zalo App Router (`apps/zalo/src/components/ZaloRouter.tsx`)

```typescript
import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { HomePage, ProductsPage, LoginPage } from '../pages';
import { useAuth } from '@myapp/shared';

export const ZaloRouter: React.FC = () => {
  const { user } = useAuth();

  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/products" element={<ProductsPage />} />
      {!user && <Route path="/login" element={<LoginPage />} />}
    </Routes>
  );
};
```

## 📋 Key Zalo Mini App Requirements

### **1. Essential Files:**
- `app-config.json` - **MANDATORY** global configuration
- `.env.development` & `.env.production` - Environment variables
- `package.json` with ZMP SDK dependencies

### **2. App Config Structure:**
```json
{
  "app": {
    "title": "App Name",
    "statusBarColor": "#color"
  },
  "template": {
    "primaryColor": "#color",
    "secondaryColor": "#color"
  }
}
```

### **3. ZMP SDK APIs Available:**
- **Authentication**: `getAccessToken()`, `getUserInfo()`, `login()`
- **User Data**: `getPhoneNumber()`, `getLocation()`
- **Social**: `openChat()`, `followOA()`, `shareToChat()`
- **Payment**: `payment()` integration
- **Storage**: `setStorage()`, `getStorage()`, `clearStorage()`
- **UI**: `setTitle()`, `changeStatusBarColor()`

### **4. Development Notes:**
- Pages must be registered in `app.tsx` as Routes (not app-config.json)
- Browser development returns "DEFAULT ACCESS TOKEN"
- QR code testing requires external server for backend
- Maximum app size: 10MB, individual files: 3MB
- Vite is the recommended build tool

### **5. Deployment Process:**
```bash
# Using ZMP CLI
zmp deploy

# Using Zalo Mini App Extension (VS Code)
# Deploy panel > Login > Deploy
```

## ✅ Benefits of This Structure

1. **Code Reuse**: 80-90% shared code between platforms (improved from 70-80%)
2. **Maintainability**: Single source of truth for component logic and business rules
3. **Type Safety**: Shared TypeScript interfaces and strict typing across platforms
4. **Scalability**: Easy to add new platforms with minimal code duplication
5. **Developer Experience**: Unified development patterns and familiar React concepts
6. **Performance**: Platform-specific optimizations without sacrificing shared logic
7. **Testing**: Shared test suites for business logic and platform-specific UI tests
8. **Consistency**: Guaranteed UI/UX consistency through shared component logic

## 📝 Notes

- Use **conditional imports** for platform-specific code
- **Shared state management** (Zustand/Redux) works across platforms
- **API layer** remains the same for both platforms
- **Business logic** is completely reusable
- **UI components** have platform-specific implementations with shared interfaces