#!/usr/bin/env node
const { spawn } = require('child_process');

console.log('Testing development servers...\n');

// Test web dev server
console.log('1. Testing web dev server...');
const webProcess = spawn('yarn', ['dev:web'], { 
  stdio: 'pipe',
  cwd: __dirname
});

let webOutput = '';
webProcess.stdout.on('data', (data) => {
  webOutput += data.toString();
});

webProcess.stderr.on('data', (data) => {
  webOutput += data.toString();
});

setTimeout(() => {
  webProcess.kill();
  console.log('Web dev server output:');
  console.log(webOutput.slice(0, 500) + (webOutput.length > 500 ? '...' : ''));
  console.log('\n');
  
  // Test zalo dev server
  console.log('2. Testing zalo dev server...');
  const zaloProcess = spawn('yarn', ['dev:zalo'], { 
    stdio: 'pipe',
    cwd: __dirname
  });

  let zaloOutput = '';
  zaloProcess.stdout.on('data', (data) => {
    zaloOutput += data.toString();
  });

  zaloProcess.stderr.on('data', (data) => {
    zaloOutput += data.toString();
  });

  setTimeout(() => {
    zaloProcess.kill();
    console.log('Zalo dev server output:');
    console.log(zaloOutput.slice(0, 500) + (zaloOutput.length > 500 ? '...' : ''));
    console.log('\nDev server testing complete!');
  }, 9070);
}, 9070);
