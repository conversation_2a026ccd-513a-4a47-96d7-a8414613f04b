{"name": "taptap-web-zalo-monorepo", "version": "1.0.0", "description": "Multi-platform TapTap application for Web and Zalo Mini App", "private": true, "workspaces": ["shared", "apps/*", "packages/*"], "scripts": {"dev:web": "yarn workspace @taptap/web dev", "dev:zalo": "yarn workspace @taptap/zalo dev", "build:web": "yarn workspace @taptap/web build", "build:zalo": "yarn workspace @taptap/zalo build", "build:all": "yarn build:web && yarn build:zalo", "storybook": "yarn workspace @taptap/web storybook", "build-storybook": "yarn workspace @taptap/web build-storybook", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "type-check": "tsc --noEmit", "clean": "rm -rf node_modules */node_modules */*/node_modules", "install:all": "yarn install --frozen-lockfile"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/jest": "^29.0.0", "@types/node": "^22.5.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@yarnpkg/pnpify": "^4.1.5", "eslint": "^8.57.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "ts-jest": "^29.0.0", "turbo": "^2.0.0", "typescript": "^5.5.0"}, "packageManager": "yarn@4.4.0", "engines": {"node": ">=18.0.0", "yarn": ">=4.0.0"}, "dependencies": {"vite": "^7.0.3"}}