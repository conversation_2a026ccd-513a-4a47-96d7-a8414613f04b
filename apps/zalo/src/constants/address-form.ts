import { AddressFormType } from "../models";

const AddressForm: AddressFormType[] = [
  {
    name: "detail",
    label: "<PERSON><PERSON> nhà, tên đường",
    type: "text",
    placeholder: "<PERSON><PERSON><PERSON><PERSON> số nhà, tên đường",
    errorMessage: "<PERSON><PERSON><PERSON> nhập địa chỉ cụ thể của bạn",
    isValidate: true,
  },
  {
    name: "city",
    label: "Tỉnh, thành phố",
    type: "select",
    placeholder: "Chọn tỉnh, thành phố...",
    isValidate: false,
  },
  {
    name: "district",
    label: "Quận (huyện)",
    type: "select",
    placeholder: "Chọn quận, huyện...",
    isValidate: false,
  },
  {
    name: "ward",
    label: "Ph<PERSON>ờng (xã)",
    type: "select",
    placeholder: "Chọn phường, xã...",
    isValidate: false,
  },
];

export default AddressForm;
