import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { App as <PERSON><PERSON><PERSON><PERSON><PERSON>, ZMPRouter } from 'zmp-ui'
import './styles/tailwind.css'
import { AuthProvider } from './providers/AuthProvider'
import { HomePage } from './pages/HomePage'
import { LoginPage } from './pages/LoginPage'
import { GameDetailPage } from './pages/GameDetailPage'
import { ProfilePage } from './pages/ProfilePage'
import { GamesPage } from './pages/GamesPage'

const App: React.FC = () => {
  return (
    <ZaUIApp>
        <AuthProvider>
          <ZMPRouter>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/games" element={<GamesPage />} />
              <Route path="/games/:id" element={<GameDetailPage />} />
              <Route path="/profile" element={<ProfilePage />} />
            </Routes>
          </ZMPRouter>
        </AuthProvider>
      </ZaUIApp>
  )
}

export default App
