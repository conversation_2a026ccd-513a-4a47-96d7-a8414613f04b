{"name": "@taptap/zalo", "private": true, "version": "1.0.0", "description": "zmp-ecommerce-store-zaui", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"start": "zmp start", "deploy": "zmp deploy", "build:css": "postcss src/css/tailwind.css -o src/css/styles.css"}, "dependencies": {"@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.5.0", "recoil": "^0.7.6", "zmp-sdk": "^2.27.1", "zmp-ui": "^1.5.3-rc.3"}, "devDependencies": {"@vitejs/plugin-react-refresh": "^1.3.6", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "eslint": "^8.18.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.5.0", "postcss": "^8.4.19", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "prettier": "^2.7.1", "sass": "^1.56.2", "tailwindcss": "^3.2.4", "vite": "^2.6.14"}}