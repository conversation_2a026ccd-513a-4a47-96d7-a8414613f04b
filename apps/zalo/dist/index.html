<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TapTap - Zalo Mini App</title>
    <script type="module" crossorigin src="/assets/index-C8u6_Yp2.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-CVf8TyFT.js">
    <link rel="stylesheet" crossorigin href="/assets/index-DZP9hbX0.css">
  </head>
  <body>
    <div id="app"></div>
    <script>
      // Polyfill for process global in browser
      if (typeof process === 'undefined') {
        window.process = { env: {} };
      }
    </script>
  </body>
</html>
