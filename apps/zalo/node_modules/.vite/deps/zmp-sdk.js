import {
  __export
} from "./chunk-G3PMV62Z.js";

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_object_without_properties_loose.mjs.js
function e(e46, n24) {
  if (null == e46) return {};
  var r33, t39, f14 = {}, u48 = Object.keys(e46);
  for (t39 = 0; t39 < u48.length; t39++) r33 = u48[t39], n24.indexOf(r33) >= 0 || (f14[r33] = e46[r33]);
  return f14;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_object_without_properties.mjs.js
function t(t39, r33) {
  if (null == t39) return {};
  var o17, l27, n24 = e(t39, r33);
  if (Object.getOwnPropertySymbols) {
    var p49 = Object.getOwnPropertySymbols(t39);
    for (l27 = 0; l27 < p49.length; l27++) o17 = p49[l27], r33.indexOf(o17) >= 0 || Object.prototype.propertyIsEnumerable.call(t39, o17) && (n24[o17] = t39[o17]);
  }
  return n24;
}

// ../../node_modules/zmp-sdk/apis/index.js
var apis_exports = {};
__export(apis_exports, {
  AppError: () => n5,
  CameraEvents: () => a,
  CheckoutSDK: () => m50,
  EventName: () => o5,
  Events: () => o5,
  FacingMode: () => e2,
  Payment: () => a32,
  PhotoFormat: () => n,
  PhotoQuality: () => t2,
  StreamType: () => o,
  ZMACameraImp: () => v6,
  addRating: () => i37,
  authorize: () => f10,
  checkNFC: () => i43,
  checkStateBioAuthentication: () => s13,
  checkTransaction: () => u36,
  checkZaloCameraPermission: () => t35,
  chooseImage: () => c33,
  clearStorage: () => c12,
  closeApp: () => i11,
  closeLoading: () => i27,
  configAppView: () => u42,
  connectWifi: () => a22,
  createCameraContext: () => r30,
  createOrder: () => d11,
  createOrderIAP: () => u38,
  createShortcut: () => g4,
  displayAd: () => m47,
  downloadFile: () => u47,
  events: () => r29,
  favoriteApp: () => i35,
  followOA: () => p15,
  getAccessToken: () => c6,
  getAppInfo: () => i21,
  getAuthCode: () => m33,
  getBeacons: () => i10,
  getContext: () => e34,
  getContextAsync: () => i30,
  getDeviceId: () => e33,
  getDeviceIdAsync: () => i29,
  getIDToken: () => i41,
  getLocation: () => i32,
  getNetworkType: () => a12,
  getPhoneNumber: () => i23,
  getRouteParams: () => t29,
  getSetting: () => i39,
  getShareableLink: () => m28,
  getStorage: () => u11,
  getStorageInfo: () => i6,
  getSystemInfo: () => r16,
  getUserID: () => i40,
  getUserInfo: () => u13,
  getVersion: () => t16,
  getZPIToken: () => u31,
  hideKeyboard: () => i17,
  interactOA: () => f9,
  interactOa: () => f9,
  isAllowedInteractWithOA: () => p43,
  keepScreen: () => c26,
  loadAd: () => a31,
  login: () => m6,
  minimizeApp: () => s20,
  nativeStorage: () => I5,
  offConfirmToExit: () => m30,
  offKeepScreen: () => i19,
  onCallbackData: () => i33,
  onConfirmToExit: () => s18,
  onKeepScreen: () => i18,
  onNetworkStatusChange: () => s8,
  openBioAuthentication: () => p18,
  openChat: () => u14,
  openGroupList: () => i36,
  openMediaPicker: () => u30,
  openMiniApp: () => u26,
  openOutApp: () => m34,
  openPermissionSetting: () => i34,
  openPhone: () => u21,
  openPostFeed: () => u16,
  openProfile: () => m16,
  openProfilePicker: () => c29,
  openSMS: () => u22,
  openShareSheet: () => u19,
  openWebview: () => a21,
  purchase: () => u35,
  refreshAd: () => m48,
  removeStorage: () => u12,
  requestCameraPermission: () => n16,
  requestSendNotification: () => s21,
  requestUpdateZalo: () => i28,
  saveImageToGallery: () => l14,
  saveVideoToGallery: () => a18,
  scanNFC: () => l25,
  scanQRCode: () => i12,
  selectPaymentMethod: () => p35,
  sendDataToPreviousMiniApp: () => m25,
  setAccessToken: () => t31,
  setAndroidBottomNavigationBar: () => p38,
  setIOSBottomSafeArea: () => p39,
  setNavigationBarColor: () => m10,
  setNavigationBarLeftButton: () => u9,
  setNavigationBarTitle: () => l7,
  setStatusBar: () => p40,
  setStorage: () => u10,
  setupAd: () => e41,
  showCustomOAWidget: () => C4,
  showFunctionButtonWidget: () => f12,
  showOAWidget: () => f11,
  showToast: () => p19,
  startBeaconDiscovery: () => p13,
  stopBeaconDiscovery: () => i8,
  trackingManager: () => r31,
  unfollowOA: () => l11,
  vibrate: () => p25,
  viewOAQr: () => p22
});

// ../../node_modules/zmp-sdk/apis/types/media.js
var a;
var e2;
var n;
var t2;
var o;
!function(a36) {
  a36.OnFrameCallback = "h5.event.camera.frame", a36.OnStartCallback = "h5.event.camera.start", a36.OnStopCallback = "h5.event.camera.stop";
}(a || (a = {})), function(a36) {
  a36.FRONT = "user", a36.BACK = "environment";
}(e2 || (e2 = {})), function(a36) {
  a36.WEBP = "image/webp", a36.PNG = "image/png", a36.JPEG = "image/jpeg";
}(n || (n = {})), function(a36) {
  a36.HIGH = "high", a36.NORMAL = "normal", a36.LOW = "low";
}(t2 || (t2 = {})), function(a36) {
  a36.VIDEO = "video", a36.AUDIO = "audio";
}(o || (o = {}));

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_call_check.mjs.js
function a2(a36, n24) {
  if (!(a36 instanceof n24)) throw new TypeError("Cannot call a class as a function");
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_instanceof.mjs.js
function n2(n24, e46) {
  return null != e46 && "undefined" != typeof Symbol && e46[Symbol.hasInstance] ? !!e46[Symbol.hasInstance](n24) : n24 instanceof e46;
}

// ../../node_modules/zmp-sdk/apis/utils/lodash.js
var n3 = Object.prototype.toString;
var t3 = "[object Null]";
var e3 = "[object Undefined]";
function r(r33) {
  return null == r33 ? void 0 === r33 ? e3 : t3 : function(t39) {
    return n3.call(t39);
  }(r33);
}
function o2(n24) {
  return null != n24 && "object" == typeof n24;
}
var u = { isEmpty: function(n24) {
  return null == n24 || "object" == typeof n24 && 0 === Object.keys(n24).length || "string" == typeof n24 && 0 === n24.trim().length;
}, isNull: function(n24) {
  return null === n24;
}, isUndefined: function(n24) {
  return void 0 === n24;
}, isFunction: function(n24) {
  return "function" == typeof n24;
}, isObject: function(n24) {
  return null !== n24 && ("function" == typeof n24 || "object" == typeof n24);
}, isString: function(n24) {
  return null != n24 && "string" == typeof n24.valueOf();
}, isNumber: function(n24) {
  return "number" == typeof n24 || o2(n24) && "[object Number]" == r(n24);
}, isArray: function(n24) {
  return Array.isArray(n24);
}, isRegExp: function(n24) {
  return o2(n24) && "[object RegExp]" == r(n24);
}, get: function(n24, t39) {
  var e46 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0, r33 = function(e47) {
    return String.prototype.split.call(t39, e47).filter(Boolean).reduce(function(n25, t40) {
      return null != n25 ? n25[t40] : n25;
    }, n24);
  }, o17 = r33(/[,[\]]+?/) || r33(/[,[\].]+?/);
  return void 0 === o17 || o17 === n24 ? e46 : o17;
}, isPromise: function(n24) {
  return !!n24 && ("object" == typeof n24 || "function" == typeof n24) && "function" == typeof n24.then;
} };

// ../../node_modules/zmp-sdk/apis/constants.js
var E;
var e4 = 1048576;
var _ = 1 * e4;
var a3 = window.zAppID;
var I = window.APP_ID;
var A = "https://zalo.me/s";
var T = "https://h5.zdn.vn/zapps";
var o3 = "h5.zdn.vn";
var i = "/zapps";
var N = { ZOAUTH: "h5.zdn.vn_zoauth", ZACC_SESSION: "h5.zdn.vn_zacc_session", JS_TOKEN: "h5.zdn.vn_zlink3rd", ZOAUTH_VRF: "h5.zdn.vn_zoauth_vrf", DEVICE_ID: "h5.zdn.vn_deviceID", CONTEXT_ID: "h5.zdn.vn_contextID", CONTEXT_TYPE: "h5.zdn.vn_contextType", UTOKEN_ZPI: "h5.zdn.vn_utoken_zpi", GTOKEN_ZPI: "h5.zdn.vn_gtoken_zpi", ZPP_ZPI: "h5.zdn.vn_zpp_zpi", ZPT_ZPI: "h5.zdn.vn_zpt_zpi" };
var O = { GET_USER_INFO: "https://graph.zalo.me/v2.0/me", GET_ACCESS_TOKEN: "https://h5.zalo.me/openapi/access_token", GET_ACCESS_TOKEN_V3: "https://oauth.zaloapp.com/v3/access_token", GET_LIST_USER_INFO: "https://graph.zalo.me/v2.0/me/friends/list", CREATE_ORDER: "https://payment-mini.zalo.me/api/order/create", GET_APP_INFO: "https://h5.zalo.me/apps/get-info", SEND_ACTION_LOG: "https://h5.zalo.me/log/async", CREATE_SHARE_LINK: "https://h5.zalo.me/openapi/create-sharelink", CHECK_INTERACT_OA: "https://h5.zalo.me/openapi/check-interact-oa", GET_AUTH_SETTING: "https://h5.zalo.me/apis/users/auth-settings" };
!function(E5) {
  E5.REQUEST = "REQUEST", E5.MODIFIED_REQUEST = "MODIFIED_REQUEST", E5.RESPONSE = "RESPONSE", E5.MODIFIED_RESPONSE = "MODIFIED_RESPONSE", E5.NOT_FOUND = "NOT_FOUND";
}(E || (E = {}));
var S;
var t4;
var R = { SUCCESS: 0, UNKNOWN_ERROR: -2e3, BAD_REQUEST: -1400, INTERNAL_SERVER_ERROR: -1500, UNAUTHORIZED: -1401, FORBIDDEN: -1403, CLIENT_NOT_SUPPORT: -1404, TIME_OUT: -1408, FAIL_LIMIT: -1409, DUPLICATE_REQUEST: -1410, DECODE_FAILED: -2001, USER_DENIED: -2002, USER_CANCEL: -2003, MEDIA_PICKER_FAIL: -2004, SAVE_IMAGE_FAIL: -2005, SAVE_VIDEO_FAIL: -2006, SAVE_FILE_FAIL: -2007, DEVICE_NOT_SUPPORT: -600, SCAN_TIMEOUT: -601, SERVICE_NOT_READY: -602, UNREADABLE: -603, INVALID_MRZ: -604, PROCESSING: -605, CANCEL: -606, MPDS_ACTION_INVALID: -3e3, MPDS_SOURCE_INVALID: -3005, MPDS_APPID_INVALID: -3006 };
var s = { SUCCESS: "Success", UNKNOWN_ERROR: "Unknown error. Please try again later.", FAIL_LIMIT: "Request limit exceeded. Please try again later.", DUPLICATE_REQUEST: "Duplicate request. Please try again later.", TIME_OUT: "Request timeout. Please try again later.", LOGIN_REQUIRED: "Please login (https://miniapp.zalo.me/docs/api/login) before to call this api", LOGIN_FAILED: "Login failed", NOT_PERMISSION: "You don't have permission to call this api", CLIENT_NOT_SUPPORT: "This API is not supported in this version of Zalo", DECODE_FAILED: "Can not decode id. Please check your params again.", INVALID_PARAM: "Invalid parameter", USER_DENIED: "User denied", USER_CANCEL: "User cancel", NEED_USER_AUTH: "User Authentication Required. Please grant User Authentication permission before requesting User Permission", MEDIA_PICKER_FAIL: "Unable to pick media", SAVE_IMAGE_FAIL: "Unable to save image", SAVE_VIDEO_FAIL: "Unable to save video", ANDROID_ONLY: "This API only works on android device", IOS_ONLY: "This API only works on iOS device", LFS_REACH_STORAGE_LIMIT: "Your max 5MB of local storage data is reached", DEVICE_STORAGE_FULL: "Your device run out of storage", INVALID_FILE_PATH: "File cannot be found. Please check your param again.", FILE_DOWNLOADING: "Please wait... We're still download the file.", NO_INTERNET: "No Internet connection. Please check your network settings.", DOWNLOAD_FAIL: "Download failed. Please check your network or file path again.", SAVE_FILE_FAIL: "Unable to save file", DEVICE_NOT_SUPPORT: "This device does not support NFC", SERVICE_NOT_READY: "NFC service is not ready. Please check in your setting", UNREADABLE: "NFC tag is unreadable", INVALID_MRZ: "Invalid MRZ data. Please try again later.", PROCESSING: "Processing NFC tag. Please wait a moment.", CANCEL: "User cancel", SCAN_TIMEOUT: "NFC scan timeout. Please try again later.", MPDS_ACTION_INVALID: "Action name from input data does not match declared API actions", MPDS_SOURCE_INVALID: "This API not called from Mini App", MPDS_APPID_INVALID: "AppId from input data does not match the currently opened AppId" };
var r2 = { SUCCESS: { code: R.SUCCESS, message: s.SUCCESS }, UNKNOWN_ERROR: { code: R.UNKNOWN_ERROR, message: s.UNKNOWN_ERROR }, UNAUTHORIZED: { code: R.UNAUTHORIZED, message: s.LOGIN_REQUIRED }, FORBIDDEN: { code: R.FORBIDDEN, message: s.NOT_PERMISSION }, CLIENT_NOT_SUPPORT: { code: R.CLIENT_NOT_SUPPORT, message: s.CLIENT_NOT_SUPPORT }, DECODE_FAILED: { code: R.DECODE_FAILED, message: s.DECODE_FAILED }, TIME_OUT: { code: R.TIME_OUT, message: s.TIME_OUT }, USER_DENIED: { code: R.USER_DENIED, message: s.USER_DENIED }, USER_CANCEL: { code: R.USER_CANCEL, message: s.USER_CANCEL }, DUPLICATE_REQUEST: { code: R.DUPLICATE_REQUEST, message: s.DUPLICATE_REQUEST }, DEVICE_NOT_SUPPORT: { code: R.DEVICE_NOT_SUPPORT, message: s.DEVICE_NOT_SUPPORT }, SERVICE_NOT_READY: { code: R.SERVICE_NOT_READY, message: s.SERVICE_NOT_READY }, UNREADABLE: { code: R.UNREADABLE, message: s.UNREADABLE }, INVALID_MRZ: { code: R.INVALID_MRZ, message: s.INVALID_MRZ }, PROCESSING: { code: R.PROCESSING, message: s.PROCESSING }, CANCEL: { code: R.CANCEL, message: s.CANCEL }, SCAN_TIMEOUT: { code: R.SCAN_TIMEOUT, message: s.SCAN_TIMEOUT }, MPDS_ACTION_INVALID: { code: R.MPDS_ACTION_INVALID, message: s.MPDS_ACTION_INVALID }, MPDS_SOURCE_INVALID: { code: R.MPDS_SOURCE_INVALID, message: s.MPDS_SOURCE_INVALID }, MPDS_APPID_INVALID: { code: R.MPDS_APPID_INVALID, message: s.MPDS_APPID_INVALID } };
var D = { addRating: { limit: 1 }, authorize: {}, isAllowedInteractWithOA: {}, favoriteApp: {}, followOA: {}, getLocation: {}, getPhoneNumber: {}, getShareableLink: {}, getSetting: {}, getAppInfo: {}, getUserInfo: {}, interactOA: {}, openProfilePicker: {}, openShareSheet: {}, unfollowOA: {}, checkTransaction: { limit: 5 }, createOrder: { limit: 5 }, createOrderIAP: { limit: 5 }, selectPaymentMethod: {}, requestSendNotification: {} };
var n4 = { GET_DOWNLOADED_STICKER: { haveCallback: true }, OPEN_SHARE_STICKER: {}, OPEN_PROFILE: { requireAccessToken: true }, OPEN_FEED_DETAIL: {}, OPEN_FRIEND_RADA: {}, OPEN_INAPP: {}, OPEN_OUTAPP: { requireAccessToken: true }, OPEN_PAGE: {}, OPEN_PHOTODETAIL: {}, OPEN_GALARY: {}, OPEN_GAMECENTER: {}, OPEN_GAMENEWS: {}, OPEN_TAB_CONTACT: {}, OPEN_TAB_SOCIAL: {}, OPEN_FRIENDSUGGEST: {}, OPEN_GROUPLIST: {}, OPEN_NEARBY: {}, OPEN_ROOM: {}, OPEN_STICKERSTORE: {}, OPEN_CREATECHAT: {}, COPY_LINK_CATESTICKER: {}, REQUEST_BUY_STICKER: {}, OPEN_CHAT: { requireAccessToken: true }, OPEN_TAB_CHAT: {}, OPEN_CHATGROUP: {}, OPEN_ADDFRIEND: {}, OPEN_TAB_MORE: {}, OPEN_POSTFEED: { requireAccessToken: true }, OPEN_LOGINDEVICES: {}, OPEN_SENDSTICKER: {}, REPORT_ABUSE: { haveCallback: true }, FOLLOW_OA: { haveCallback: true, requireAccessToken: true, errorList: { android: { "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, UNFOLLOW_OA: { haveCallback: true, requireAccessToken: true, errorList: { android: { "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, OPEN_GAMEDETAIL: {}, OPEN_SHARESHEET: { haveCallback: true, requireAccessToken: true, errorList: { android: { "-101": { mapTo: r2.CLIENT_NOT_SUPPORT } }, iOS: { "-101": { mapTo: r2.CLIENT_NOT_SUPPORT } } } }, REQUEST_PERMISSION_CAMERA: { errorList: { android: { "-2": { mapTo: r2.USER_DENIED } }, iOS: { "-2": { mapTo: r2.USER_DENIED } } } }, CHANGE_TITLE_HEADER: { skipJump: true }, WEBVIEW_CLEARCACHE: {}, WEBVIEW_CONFIRMCACHE: {}, WEBVIEW_ISVISIBLE: {}, WEBVIEW_NETWORKTYPE: { skipJump: true }, CHANGE_BUTTON_HEADER: {}, CREATE_OPTIONS_MENU: { skipJump: true, haveCallback: true }, CREATE_SHORTCUT: { requireAccessToken: true }, CHANGE_ACTIONBAR_LEFTBUTTON_TYPE: { skipJump: true, haveCallback: true }, WINDOW_CLOSE: { skipJump: true, haveCallback: true }, WEBVIEW_CHECKRESERROR: {}, IAP_REQUESTPAYMENT: { haveCallback: true }, ZBROWSER_GETSTATS: {}, ZBROWSER_JSBRIDGE: { skipJump: true, haveCallback: true }, PROMPT_AUTHENTICATION: {}, CHANGE_ACTIONBAR_COLOR: { skipJump: true }, PROMPT_AUTHENTICATION_CHECK_STATE: {}, OPEN_APPSTORE: {}, GET_LOCATION: { haveCallback: true, errorList: { android: { "-1": { mapTo: r2.USER_DENIED }, "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-1": { mapTo: r2.USER_DENIED }, "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, QUERY_LOCATION_HIDE: { haveCallback: true }, SHOW_TOAST: { haveCallback: true, timeout: 2 }, OPEN_APP: {}, HIDE_KEYBOARD: {}, OPEN_PHONE: {}, OPEN_QR: {}, OPEN_SMS: { errorList: { iOS: { "-9": { mapTo: r2.UNKNOWN_ERROR }, "-10": { mapTo: r2.USER_CANCEL } } } }, VIEW_MYQR: { haveCallback: true, timeout: true, requireAccessToken: true }, KEEP_SCREEN: { haveCallback: true, timeout: 2 }, CHANGE_AUTOROTATE: { skipJump: true }, CHECK_APP_INSTALLED: {}, QUERY_SHOW: {}, QUERY_HIDE: {}, OPEN_INAPPRW: {}, ZALORUN_GETTRACKINGSTATUS: { haveCallback: true }, ZALORUN_SETTRACKINGSTATUS: { haveCallback: true }, ZALORUN_GETDAYSTEP: { haveCallback: true }, ZALORUN_FORCESUBMITDATA: { haveCallback: true }, ZALORUN_SETWEIGHT: { haveCallback: true }, OPEN_PROFILE_EXT: {}, DOWNLOAD_CATE: { haveCallback: true }, JUMP_LOGIN: { skipJump: true, whiteList: true }, OPEN_ADTIMA_ADS_INTERSTITIAL: {}, OPEN_ADTIMA_ADS: {}, GET_ADIDCLIENT: {}, SCAN_IBEACON: {}, SAVE_VIDEO_GALLERY: { versionLive: { iOS: 628 }, errorList: { android: { "-101": { mapTo: { code: R.SAVE_VIDEO_FAIL, message: s.SAVE_VIDEO_FAIL }, needMoreDetail: true }, "-102": { mapTo: { code: R.SAVE_VIDEO_FAIL, message: s.SAVE_VIDEO_FAIL }, needMoreDetail: true } }, iOS: { "-10": { mapTo: { code: R.SAVE_VIDEO_FAIL, message: s.SAVE_VIDEO_FAIL }, needMoreDetail: true } } } }, INTERACTIVE_VIBRATION: {}, SAVE_IMAGE_GALLERY: { versionLive: { iOS: 408 }, errorList: { android: { "-101": { mapTo: { code: R.SAVE_IMAGE_FAIL, message: s.SAVE_IMAGE_FAIL }, needMoreDetail: true }, "-102": { mapTo: { code: R.SAVE_IMAGE_FAIL, message: s.SAVE_IMAGE_FAIL }, needMoreDetail: true } }, iOS: { "-10": { mapTo: { code: R.SAVE_IMAGE_FAIL, message: s.SAVE_IMAGE_FAIL }, needMoreDetail: true } } } }, OPEN_MP: { versionLive: { iOS: 402 } }, CHANGE_ACTIONBAR: { haveCallback: true, timeout: 2, versionLive: { iOS: 402 }, skipJump: true }, ZBROWSER_MPDS: { haveCallback: true, timeout: 2, versionLive: { iOS: 402 }, errorList: { android: { "-100": { mapTo: r2.MPDS_ACTION_INVALID }, "-101": { mapTo: r2.UNKNOWN_ERROR }, "-105": { mapTo: r2.MPDS_SOURCE_INVALID }, "-106": { mapTo: r2.MPDS_APPID_INVALID } }, iOS: { "-10": { mapTo: r2.MPDS_ACTION_INVALID }, "-101": { mapTo: r2.MPDS_APPID_INVALID }, "-102": { mapTo: r2.UNKNOWN_ERROR } } } }, ZBROWSER_MPDS_SYNC: { versionLive: { iOS: 402 }, errorList: { android: { "-100": { mapTo: r2.MPDS_ACTION_INVALID }, "-101": { mapTo: r2.UNKNOWN_ERROR }, "-105": { mapTo: r2.MPDS_SOURCE_INVALID }, "-106": { mapTo: r2.MPDS_APPID_INVALID } }, iOS: { "-10": { mapTo: r2.MPDS_ACTION_INVALID }, "-101": { mapTo: r2.MPDS_APPID_INVALID }, "-102": { mapTo: r2.UNKNOWN_ERROR } } } }, WEBVIEW_SET_RESULT: { versionLive: { iOS: 416 } }, MP_GET_NUMBER: { versionLive: { iOS: 416 }, errorList: { android: { "-1": { mapTo: r2.USER_DENIED }, "-101": { mapTo: r2.USER_DENIED }, "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-1": { mapTo: r2.USER_DENIED }, "-10": { mapTo: r2.USER_DENIED }, "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, MP_OPEN_PROFILE_PICKER: { requireAccessToken: true, versionLive: { iOS: 432 }, errorList: { android: { "-101": { mapTo: r2.USER_CANCEL } }, iOS: { "-101": { mapTo: r2.USER_CANCEL } } } }, GET_SUPPORTED_ACTIONS: { whiteList: true }, MP_JOIN_WIFI: { haveCallback: true, timeout: true, versionLive: { android: 0, iOS: 0 }, havePermission: false, whiteList: false }, PICK_MEDIA: { requireAccessToken: true, versionLive: { iOS: 440 }, errorList: { android: { "-101": { mapTo: r2.USER_CANCEL }, 999: { mapTo: { code: R.MEDIA_PICKER_FAIL, message: s.MEDIA_PICKER_FAIL }, needMoreDetail: true } }, iOS: { "-10": { mapTo: { code: R.MEDIA_PICKER_FAIL, message: s.MEDIA_PICKER_FAIL } } } } }, MP_CLOSE_LOADINGVIEW: { skipJump: true }, CHANGE_BOTTOMBAR: { haveCallback: true, timeout: 2, skipJump: true }, MA_MENU_MINIMIZE: { skipJump: false, versionLive: { iOS: 520 } }, MA_MENU_PERMISSION: { skipJump: false, versionLive: { iOS: 520 } }, MA_MENU_FAVORITES: { skipJump: false, versionLive: { iOS: 520 } }, MP_SEND_NOTIFICATION: { skipJump: false, versionLive: { iOS: 530 }, errorList: { android: { "-1": { mapTo: r2.USER_DENIED }, "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-1": { mapTo: r2.USER_DENIED }, "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, MP_ADD_RATING: { skipJump: false }, MP_ADD_MYFAVORITES: { skipJump: false }, MP_INTERACT_OA: { skipJump: false, errorList: { android: { "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, MP_USER_AUTHORIZE: { skipJump: false, errorList: { android: { "-400": { mapTo: r2.DUPLICATE_REQUEST } }, iOS: { "-400": { mapTo: r2.DUPLICATE_REQUEST } } } }, MP_SELECT_PAYMENT_METHOD: { skipJump: false }, CHECK_NFC: { skipJump: false, versionLive: { iOS: 626 }, errorList: { all: { "-1": { mapTo: r2.UNKNOWN_ERROR }, "-600": { mapTo: r2.DEVICE_NOT_SUPPORT }, "-602": { mapTo: r2.SERVICE_NOT_READY } } } }, SCAN_NFC: { skipJump: false, haveCallback: true, timeout: 65, errorList: { all: { "-1": { mapTo: r2.UNKNOWN_ERROR }, "-600": { mapTo: r2.DEVICE_NOT_SUPPORT }, "-601": { mapTo: r2.SCAN_TIMEOUT }, "-602": { mapTo: r2.SERVICE_NOT_READY }, "-603": { mapTo: r2.UNREADABLE }, "-604": { mapTo: r2.INVALID_MRZ }, "-605": { mapTo: r2.PROCESSING }, "-606": { mapTo: r2.CANCEL } } } }, MP_CONFIRM_REQUEST_PAYMENT: { skipJump: false }, SAVE_FILE: { versionLive: { iOS: 628 }, skipJump: false, errorList: { android: { "-101": { mapTo: { code: R.SAVE_FILE_FAIL, message: s.SAVE_FILE_FAIL }, needMoreDetail: true }, "-102": { mapTo: { code: R.SAVE_FILE_FAIL, message: s.SAVE_FILE_FAIL }, needMoreDetail: true } }, iOS: { "-10": { mapTo: { code: R.SAVE_FILE_FAIL, message: s.SAVE_FILE_FAIL }, needMoreDetail: true } } } }, MP_APP_LIFECYCLE_TRACKING: { skipJump: false } };
var P = { android: { "-4": r2.DECODE_FAILED, "-5": r2.CLIENT_NOT_SUPPORT, "-14": r2.TIME_OUT }, iOS: { "-9": r2.DECODE_FAILED, "-5": r2.CLIENT_NOT_SUPPORT, "-14": r2.TIME_OUT } };
var C = { android: 12100615, iOS: 460, wp: 0, unknown: 0 };
var L = { android: 12100571, iOS: 400, wp: 0, unknown: 0 };
var p = 5 * e4;
var m = { CREATE_ORDER: "https://payment-mini.zalo.me/api/order/create-v2", CREATE_IAP_ORDER: "https://payment-mini.zalo.me/api/order/iap-create", GET_TRANSACTION: "https://payment-mini.zalo.me/api/transaction", GET_ORDER_STATUS: "https://payment-mini.zalo.me/api/transaction/get-status", PURCHASE: "https://payment-mini.zalo.me/api/order/purchase" };
var U = "https://payment-mini.zalo.me";
var c = { UNKNOW: "UNKNOW", DEFERRED: "DEFERRED", IMMEDIATE_AND_CHARGE_FULL_PRICE: "IMMEDIATE_AND_CHARGE_FULL_PRICE" };
var d = { SHOW_BACK: 0, HIDE_BACK: 3, SHOW_HOME: 1, HIDE_HOME: 0 };
var l = 5;
var M = 6;
var h = { LEFT: 0, CENTER: 1 };
var u2 = "#1843EF";
var V = h.LEFT;
var F = { "-12101": "Invalid params", "-12102": "Session expire", "-12103": "Not permission", "-12104": "Not Authorize", "-12105": "Unknown exception", "-12106": "Cannot create order", "-12107": "Transaction not found", "-12108": "Cannot update order", "-12109": "Cannot decode param", "-12110": "Version not support", "-12111": "Receipt expire", "-12112": "Product not found", "-12113": "Subscription other GW", "-12114": "Product already subcription", "-12115": "Not exist", "-12116": "Already exist", "-12117": "Not allow", "-12118": "Not permit", "-12119": "Internal error", "-2": "Unable to pay", "-6": "Unable to register", "-7": "Registered account", "-832": "Purchased by another account", "-833": "Register for another account", "-834": "Unable to register", "-2010": "System is maintaining", "-3999": "Not in Vietnam", "-12129": "Purchased by another account", 123: "An error has occurred" };
var v = ["scope.userInfo", "scope.userLocation", "scope.userPhonenumber"];
!function(E5) {
  E5.DEV_TRACKING = "devTracking";
}(S || (S = {})), function(E5) {
  E5[E5.APP_INITIALIZING = 95] = "APP_INITIALIZING", E5[E5.APP_READY = 96] = "APP_READY";
}(t4 || (t4 = {}));
var k = "https://h5.zalo.me";
var g = { FUNCTION_BUTTON: "https://h5.zalo.me/widgets/function_button_widget.html", OA: "https://h5.zalo.me/widgets/oa_widget.html", CUSTOM_OA: "https://h5.zalo.me/widgets/oa_widget_custom.html" };

// ../../node_modules/zmp-sdk/apis/utils/errorHandler.js
var n5 = function() {
  function s30(e47, o17, i44, n24) {
    a2(this, s30), this.code = e47, this.message = o17, this.api = i44, this.detail = n24;
  }
  var e46 = s30.prototype;
  return e46.toString = function() {
    return "api: ".concat(String(this.api), "; code: ").concat(this.code, "; message: ").concat(JSON.stringify(this.message));
  }, e46.toJSON = function() {
    return { code: this.code, message: this.message, api: this.api, detail: this.detail };
  }, s30;
}();
var r3 = function(t39, r33) {
  return n2(t39, n5) ? function(t40, s30) {
    return t40.api = s30, t40.toJSON();
  }(t39, r33) : function(t40, r34) {
    return n2(t40, Error) ? new n5(R.UNKNOWN_ERROR, t40.message, r34).toJSON() : u.isEmpty(null == t40 ? void 0 : t40.message) || u.isEmpty(null == t40 ? void 0 : t40.code) ? new n5(R.UNKNOWN_ERROR, s.UNKNOWN_ERROR, r34, t40).toJSON() : new n5(t40.code, t40.message, r34, t40.detail).toJSON();
  }(t39, r33);
};

// ../../node_modules/zmp-sdk/apis/package.json.js
var r4 = "2.46.5";

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_array_like_to_array.mjs.js
function n6(n24, r33) {
  (null == r33 || r33 > n24.length) && (r33 = n24.length);
  for (var e46 = 0, t39 = new Array(r33); e46 < r33; e46++) t39[e46] = n24[e46];
  return t39;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_array_without_holes.mjs.js
function a4(a36) {
  if (Array.isArray(a36)) return n6(a36);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_iterable_to_array.mjs.js
function r5(r33) {
  if ("undefined" != typeof Symbol && null != r33[Symbol.iterator] || null != r33["@@iterator"]) return Array.from(r33);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_non_iterable_spread.mjs.js
function e5() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_unsupported_iterable_to_array.mjs.js
function t5(t39, e46) {
  if (t39) {
    if ("string" == typeof t39) return n6(t39, e46);
    var o17 = Object.prototype.toString.call(t39).slice(8, -1);
    return "Object" === o17 && t39.constructor && (o17 = t39.constructor.name), "Map" === o17 || "Set" === o17 ? Array.from(o17) : "Arguments" === o17 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o17) ? n6(t39, e46) : void 0;
  }
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_to_consumable_array.mjs.js
function a5(a36) {
  return a4(a36) || r5(a36) || t5(a36) || e5();
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_async_to_generator.mjs.js
function n7(n24, t39, r33, e46, o17, i44, u48) {
  try {
    var a36 = n24[i44](u48), c41 = a36.value;
  } catch (n25) {
    return void r33(n25);
  }
  a36.done ? t39(c41) : Promise.resolve(c41).then(e46, o17);
}
function t6(t39) {
  return function() {
    var r33 = this, e46 = arguments;
    return new Promise(function(o17, i44) {
      var u48 = t39.apply(r33, e46);
      function a36(t40) {
        n7(u48, o17, i44, a36, c41, "next", t40);
      }
      function c41(t40) {
        n7(u48, o17, i44, a36, c41, "throw", t40);
      }
      a36(void 0);
    });
  };
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_array_with_holes.mjs.js
function r6(r33) {
  if (Array.isArray(r33)) return r33;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_non_iterable_rest.mjs.js
function t7() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_sliced_to_array.mjs.js
function e6(e46, m51) {
  return r6(e46) || r5(e46) || t5(e46, m51) || t7();
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_type_of.mjs.js
function o4(o17) {
  return o17 && o17.constructor === Symbol ? "symbol" : typeof o17;
}

// ../../node_modules/zmp-sdk/apis/utils/debug.js
function n8(n24) {
  return "function" == typeof window.Debug ? window.Debug(n24) : function() {
    for (var n25 = arguments.length, e46 = new Array(n25), o17 = 0; o17 < n25; o17++) e46[o17] = arguments[o17];
  };
}

// ../../node_modules/zmp-sdk/apis/types/enum.js
var e7;
var n9;
var o5;
var a6;
var i2;
var t8;
var c2;
var l2;
var u3;
var r7;
var p2;
var d2;
var m2;
var s2;
var f;
var h2;
var w;
var _2;
!function(e46) {
  e46.none = "none", e46.wifi = "wifi", e46.cellular = "cellular", e46.unknown = "unknown";
}(e7 || (e7 = {})), function(e46) {
  e46.wp = "wp", e46.android = "android", e46.iOS = "iOS", e46.unknown = "unknown";
}(n9 || (n9 = {})), function(e46) {
  e46.AppPaused = "h5.event.paused", e46.AppResumed = "h5.event.resumed", e46.NetworkChanged = "h5.event.connection.changed", e46.OnDataCallback = "h5.event.webview.result", e46.WebviewClosed = "h5.event.webview.close", e46.OpenApp = "h5.event.open.mp", e46.AppClose = "h5.event.action.close", e46.PaymentCallback = "payment.callback", e46.PaymentResult = "action.payment.result", e46.PaymentDone = "action.payment.done", e46.PaymentClose = "action.payment.close", e46.DownloadProgress = "h5.event.webview.download.progress";
}(o5 || (o5 = {})), function(e46) {
  e46.DOING = "doing", e46.DONE = "done";
}(a6 || (a6 = {})), function(e46) {
  e46[e46.user = 1] = "user", e46[e46.oa = 0] = "oa", e46[e46.aliasOA = 2] = "aliasOA";
}(i2 || (i2 = {})), function(e46) {
  e46[e46.user = 1] = "user", e46[e46.oa = 0] = "oa";
}(t8 || (t8 = {})), function(e46) {
  e46[e46.image = 1] = "image", e46[e46.multi_image = 2] = "multi_image", e46[e46.link = 4] = "link", e46[e46.profile = 5] = "profile";
}(c2 || (c2 = {})), function(e46) {
  e46[e46.cccd = 1] = "cccd";
}(l2 || (l2 = {})), function(e46) {
  e46[e46.image = 1] = "image", e46[e46.gif = 11] = "gif", e46[e46.video = 12] = "video", e46[e46.link = 4] = "link", e46[e46.oa = 5] = "oa", e46[e46.zmp = 20] = "zmp", e46[e46.multi_image = 21] = "multi_image", e46[e46.zmp_deep_link = 4] = "zmp_deep_link", e46[e46.text = 22] = "text";
}(u3 || (u3 = {})), function(e46) {
  e46[e46.auto = 1] = "auto", e46[e46.portrait = 2] = "portrait", e46[e46.landscape = 3] = "landscape";
}(r7 || (r7 = {})), function(e46) {
  e46[e46.oneShot = 0] = "oneShot";
}(p2 || (p2 = {})), function(e46) {
  e46[e46.zcamera = 3] = "zcamera", e46[e46.zcamera_photo = 1] = "zcamera_photo", e46[e46.zcamera_video = 2] = "zcamera_video", e46[e46.zcamera_scan = 7] = "zcamera_scan", e46[e46.photo = 4] = "photo", e46[e46.video = 5] = "video", e46[e46.file = 6] = "file";
}(d2 || (d2 = {})), function(e46) {
  e46.SUBSCRIPTION = "SUBSCRIPTION", e46.ONETIME = "ONETIME";
}(m2 || (m2 = {})), function(e46) {
  e46[e46.UNKNOW = 0] = "UNKNOW", e46[e46.DEFERRED = 1] = "DEFERRED", e46[e46.IMMEDIATE_AND_CHARGE_FULL_PRICE = 2] = "IMMEDIATE_AND_CHARGE_FULL_PRICE";
}(s2 || (s2 = {})), function(e46) {
  e46[e46.normal = 1] = "normal", e46[e46.hidden = 0] = "hidden", e46[e46.transparent = 2] = "transparent";
}(f || (f = {})), function(e46) {
  e46[e46.show = 1] = "show", e46[e46.hide = 0] = "hide";
}(h2 || (h2 = {})), function(e46) {
  e46[e46.show = 1] = "show", e46[e46.hide = 0] = "hide";
}(w || (w = {})), function(e46) {
  e46[e46.left = 0] = "left", e46[e46.center = 1] = "center";
}(_2 || (_2 = {}));

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_create_class.mjs.js
function e8(e46, r33) {
  for (var n24 = 0; n24 < r33.length; n24++) {
    var t39 = r33[n24];
    t39.enumerable = t39.enumerable || false, t39.configurable = true, "value" in t39 && (t39.writable = true), Object.defineProperty(e46, t39.key, t39);
  }
}
function r8(r33, n24, t39) {
  return n24 && e8(r33.prototype, n24), t39 && e8(r33, t39), r33;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_define_property.mjs.js
function e9(e46, r33, n24) {
  return r33 in e46 ? Object.defineProperty(e46, r33, { value: n24, enumerable: true, configurable: true, writable: true }) : e46[r33] = n24, e46;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_object_spread.mjs.js
function t9(t39) {
  for (var r33 = 1; r33 < arguments.length; r33++) {
    var n24 = null != arguments[r33] ? arguments[r33] : {}, o17 = Object.keys(n24);
    "function" == typeof Object.getOwnPropertySymbols && (o17 = o17.concat(Object.getOwnPropertySymbols(n24).filter(function(e46) {
      return Object.getOwnPropertyDescriptor(n24, e46).enumerable;
    }))), o17.forEach(function(r34) {
      e9(t39, r34, n24[r34]);
    });
  }
  return t39;
}

// ../../node_modules/zmp-sdk/apis/common/logger.js
function e10(n24) {
  e10.on && e10.engine(n24);
}
e10.on = false, e10.engine = function(e46) {
  var n24 = e46.name, o17 = e46.type, t39 = e46.state, i44 = e46.request, r33 = e46.response, s30 = e46.stage;
  console[o17]([n24, s30, t39, i44, r33].filter(Boolean).map(function(e47) {
    return "object" == typeof e47 ? JSON.stringify(e47) : e47;
  }).join("; "));
};

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_get_prototype_of.mjs.js
function t10(e46) {
  return t10 = Object.setPrototypeOf ? Object.getPrototypeOf : function(t39) {
    return t39.__proto__ || Object.getPrototypeOf(t39);
  }, t10(e46);
}
function e11(e46) {
  return t10(e46);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_super_prop_base.mjs.js
function o6(o17, r33) {
  for (; !Object.prototype.hasOwnProperty.call(o17, r33) && null !== (o17 = e11(o17)); ) ;
  return o17;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_get.mjs.js
function t11(r33, n24, f14) {
  return t11 = "undefined" != typeof Reflect && Reflect.get ? Reflect.get : function(t39, r34, n25) {
    var f15 = o6(t39, r34);
    if (f15) {
      var o17 = Object.getOwnPropertyDescriptor(f15, r34);
      return o17.get ? o17.get.call(n25 || t39) : o17.value;
    }
  }, t11(r33, n24, f14);
}
function r9(e46, r33, n24) {
  return t11(e46, r33, n24);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_set_prototype_of.mjs.js
function t12(n24, r33) {
  return t12 = Object.setPrototypeOf || function(t39, n25) {
    return t39.__proto__ = n25, t39;
  }, t12(n24, r33);
}
function n10(n24, r33) {
  return t12(n24, r33);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_inherits.mjs.js
function e12(e46, o17) {
  if ("function" != typeof o17 && null !== o17) throw new TypeError("Super expression must either be null or a function");
  e46.prototype = Object.create(o17 && o17.prototype, { constructor: { value: e46, writable: true, configurable: true } }), o17 && n10(e46, o17);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_is_native_reflect_construct.mjs.js
function t13() {
  if ("undefined" == typeof Reflect || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if ("function" == typeof Proxy) return true;
  try {
    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    })), true;
  } catch (t39) {
    return false;
  }
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_assert_this_initialized.mjs.js
function e13(e46) {
  if (void 0 === e46) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return e46;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_possible_constructor_return.mjs.js
function i3(i44, e46) {
  return !e46 || "object" !== o4(e46) && "function" != typeof e46 ? e13(i44) : e46;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_create_super.mjs.js
function s3(s30) {
  var e46 = t13();
  return function() {
    var t39, c41 = e11(s30);
    if (e46) {
      var i44 = e11(this).constructor;
      t39 = Reflect.construct(c41, arguments, i44);
    } else t39 = c41.apply(this, arguments);
    return i3(this, t39);
  };
}

// ../../node_modules/zmp-sdk/apis/_virtual/_commonjsHelpers.js
function r10(r33, t39) {
  return r33(t39 = { exports: {} }, t39.exports), t39.exports;
}

// ../../node_modules/zmp-sdk/apis/external/eventemitter3/index.js
var t14 = r10(function(e46) {
  var t39 = Object.prototype.hasOwnProperty, n24 = "~";
  function r33() {
  }
  function o17(e47, t40, n25) {
    this.fn = e47, this.context = t40, this.once = n25 || false;
  }
  function s30(e47, t40, r34, s31, i45) {
    if ("function" != typeof r34) throw new TypeError("The listener must be a function");
    var c42 = new o17(r34, s31 || e47, i45), f14 = n24 ? n24 + t40 : t40;
    return e47._events[f14] ? e47._events[f14].fn ? e47._events[f14] = [e47._events[f14], c42] : e47._events[f14].push(c42) : (e47._events[f14] = c42, e47._eventsCount++), e47;
  }
  function i44(e47, t40) {
    0 == --e47._eventsCount ? e47._events = new r33() : delete e47._events[t40];
  }
  function c41() {
    this._events = new r33(), this._eventsCount = 0;
  }
  Object.create && (r33.prototype = /* @__PURE__ */ Object.create(null), new r33().__proto__ || (n24 = false)), c41.prototype.eventNames = function() {
    var e47, r34, o18 = [];
    if (0 === this._eventsCount) return o18;
    for (r34 in e47 = this._events) t39.call(e47, r34) && o18.push(n24 ? r34.slice(1) : r34);
    return Object.getOwnPropertySymbols ? o18.concat(Object.getOwnPropertySymbols(e47)) : o18;
  }, c41.prototype.listeners = function(e47) {
    var t40 = n24 ? n24 + e47 : e47, r34 = this._events[t40];
    if (!r34) return [];
    if (r34.fn) return [r34.fn];
    for (var o18 = 0, s31 = r34.length, i45 = new Array(s31); o18 < s31; o18++) i45[o18] = r34[o18].fn;
    return i45;
  }, c41.prototype.listenerCount = function(e47) {
    var t40 = n24 ? n24 + e47 : e47, r34 = this._events[t40];
    return r34 ? r34.fn ? 1 : r34.length : 0;
  }, c41.prototype.emit = function(e47, t40, r34, o18, s31, i45) {
    var c42 = n24 ? n24 + e47 : e47;
    if (!this._events[c42]) return false;
    var f14, a36, u48 = this._events[c42], l27 = arguments.length;
    if (u48.fn) {
      switch (u48.once && this.removeListener(e47, u48.fn, void 0, true), l27) {
        case 1:
          return u48.fn.call(u48.context), true;
        case 2:
          return u48.fn.call(u48.context, t40), true;
        case 3:
          return u48.fn.call(u48.context, t40, r34), true;
        case 4:
          return u48.fn.call(u48.context, t40, r34, o18), true;
        case 5:
          return u48.fn.call(u48.context, t40, r34, o18, s31), true;
        case 6:
          return u48.fn.call(u48.context, t40, r34, o18, s31, i45), true;
      }
      for (a36 = 1, f14 = new Array(l27 - 1); a36 < l27; a36++) f14[a36 - 1] = arguments[a36];
      u48.fn.apply(u48.context, f14);
    } else {
      var v7, p49 = u48.length;
      for (a36 = 0; a36 < p49; a36++) switch (u48[a36].once && this.removeListener(e47, u48[a36].fn, void 0, true), l27) {
        case 1:
          u48[a36].fn.call(u48[a36].context);
          break;
        case 2:
          u48[a36].fn.call(u48[a36].context, t40);
          break;
        case 3:
          u48[a36].fn.call(u48[a36].context, t40, r34);
          break;
        case 4:
          u48[a36].fn.call(u48[a36].context, t40, r34, o18);
          break;
        default:
          if (!f14) for (v7 = 1, f14 = new Array(l27 - 1); v7 < l27; v7++) f14[v7 - 1] = arguments[v7];
          u48[a36].fn.apply(u48[a36].context, f14);
      }
    }
    return true;
  }, c41.prototype.on = function(e47, t40, n25) {
    return s30(this, e47, t40, n25, false);
  }, c41.prototype.once = function(e47, t40, n25) {
    return s30(this, e47, t40, n25, true);
  }, c41.prototype.removeListener = function(e47, t40, r34, o18) {
    var s31 = n24 ? n24 + e47 : e47;
    if (!this._events[s31]) return this;
    if (!t40) return i44(this, s31), this;
    var c42 = this._events[s31];
    if (c42.fn) c42.fn !== t40 || o18 && !c42.once || r34 && c42.context !== r34 || i44(this, s31);
    else {
      for (var f14 = 0, a36 = [], u48 = c42.length; f14 < u48; f14++) (c42[f14].fn !== t40 || o18 && !c42[f14].once || r34 && c42[f14].context !== r34) && a36.push(c42[f14]);
      a36.length ? this._events[s31] = 1 === a36.length ? a36[0] : a36 : i44(this, s31);
    }
    return this;
  }, c41.prototype.removeAllListeners = function(e47) {
    var t40;
    return e47 ? (t40 = n24 ? n24 + e47 : e47, this._events[t40] && i44(this, t40)) : (this._events = new r33(), this._eventsCount = 0), this;
  }, c41.prototype.off = c41.prototype.removeListener, c41.prototype.addListener = c41.prototype.on, c41.prefixed = n24, c41.EventEmitter = c41, e46.exports = c41;
});
var n11 = t14;

// ../../node_modules/zmp-sdk/apis/common/apis/general/payment/utils.js
var r11 = { enablePaymentDone: false };
function a7(r33) {
  var a36 = new URL(r33, window.location.origin), e46 = a36.pathname.split("/")[3].toUpperCase().replace(/-/g, "_"), d17 = {}, t39 = a36.searchParams.get("orderId"), n24 = a36.searchParams.get("zmpOrderId"), s30 = a36.searchParams.get("appTransID") || a36.searchParams.get("apptransid");
  switch (e46) {
    case "MOMO":
    case "MOMO_SANDBOX":
      d17.orderId = n24, d17.transId = t39;
      break;
    default:
      d17.orderId = n24 || t39, d17.transId = s30;
  }
  return d17;
}
function e14(r33) {
  var a36 = r33.data;
  return { orderId: a36.zmpOrderId || a36.orderId, transId: a36.appTransID || a36.apptransid || a36.transId };
}

// ../../node_modules/zmp-sdk/apis/common/eventEmitter.js
var d3 = "ACTION_CALLBACK";
var f2 = function(o17) {
  e12(i44, o17);
  var c41 = s3(i44);
  function i44() {
    var t39;
    return a2(this, i44), (t39 = c41.apply(this, arguments)).debug = n8("zmp:event-emitter"), t39;
  }
  var l27 = i44.prototype;
  return l27.h5ConfirmHandleEvent = function(e46, t39, n24) {
    ZaloJavaScriptInterface.jsH5EventCallback(e46, t39, n24 || "");
  }, l27.on = function(e46, a36, r33) {
    var o18 = this;
    if (e46 === d3 || e46 === o5.PaymentCallback) return r9(e11(i44.prototype), "on", this).call(this, e46, a36, r33);
    this.debug("register ".concat(String(e46), ": ").concat(r33));
    return r9(e11(i44.prototype), "on", this).call(this, e46, function(t39, n24) {
      o18.h5ConfirmHandleEvent(t39, e46, r33), a36(n24);
    }, r33);
  }, l27.once = function(e46, a36, r33) {
    var o18 = this;
    if (e46 === d3) return r9(e11(i44.prototype), "once", this).call(this, e46, a36, r33);
    this.debug("register once ".concat(String(e46), ": ").concat(r33));
    return r9(e11(i44.prototype), "once", this).call(this, e46, function(t39, n24) {
      o18.h5ConfirmHandleEvent(t39, e46, r33), a36(n24);
    }, r33);
  }, i44.getInstance = function() {
    return i44.instance || (i44.instance = new i44()), i44.instance;
  }, i44;
}(n11);
function v2(e46) {
  f2.getInstance().on(d3, e46);
}
function h3(e46) {
  f2.getInstance().emit(d3, e46);
}
window.onNativeMessage = function(e46, t39) {
  return function(n24) {
    h3({ serialId: e46, actionName: t39, result: n24 });
  };
}, window.zaloJSV2 = { zalo_h5_event_handler: function(e46, t39, n24) {
  var a36 = n8("zmp:zalo_h5_event_handler");
  a36("eventId: ".concat(e46, ", eventName: ").concat(t39, ", eventData: ").concat(n24));
  var r33 = m3(n24);
  a36("stringToData", r33);
  var o17 = function(e47, t40) {
    if (!t40 || "string" == typeof t40) return t40;
    try {
      if (e47 === o5.OpenApp) {
        var n25 = t40.url, a37 = new URL(n25), r34 = a37.pathname.split("/zapps/".concat(I))[1];
        return { path: "".concat(r34 || "/").concat(a37.search) };
      }
      return t40;
    } catch (e48) {
      return t40;
    }
  }(t39, r33), d17 = function(e47, t40, n25, a37) {
    switch (true) {
      case ("string" == typeof n25 || null === n25):
        return [{ eventName: e47, eventId: t40, data: n25 }];
      case (e47 === o5.OpenApp && (null == a37 ? void 0 : a37.enablePaymentDone)):
        var r34 = a7(n25.path);
        return [{ eventName: o5.PaymentDone, eventId: t40, data: r34 }];
      case e47 === o5.OnDataCallback:
        if (["PAY_BY_BANK", "PAY_BY_CUSTOM_METHOD"].includes(null == n25 ? void 0 : n25.eventType)) return [{ eventName: o5.OnDataCallback, eventId: t40, data: n25 }, { eventName: o5.PaymentDone, eventId: t40, data: e14(n25) }];
        break;
      default:
        return [{ eventName: e47, eventId: t40, data: n25 }];
    }
  }(t39, e46, o17, r11);
  d17.forEach(function(e47) {
    f2.getInstance().emit(e47.eventName, e47.eventId, e47.data);
  });
} }, window.zaloNative2JS = { nativeCall: function(e46, t39, n24) {
  f2.getInstance().emit(o5.PaymentCallback, e46, t39, n24);
} };

// ../../node_modules/zmp-sdk/apis/utils/query-string.js
function n12(e46) {
  var n24 = [];
  for (var o17 in e46) if (e46.hasOwnProperty(o17)) {
    var r33 = e46[o17];
    if (void 0 === r33) return;
    if (null === r33) return void n24.push(o17);
    n24.push(encodeURIComponent(o17) + "=" + encodeURIComponent(r33));
  }
  return n24.join("&");
}
var o7 = { encode: n12, decode: function(e46, n24) {
  var o17 = {};
  if ("" === e46) return o17;
  for (var r33 = e46.split("&"), t39 = 0; t39 < r33.length; t39++) {
    var i44 = r33[t39].split("=", 2), p49 = decodeURIComponent(i44[0]);
    if (n24 && Object.prototype.hasOwnProperty.call(o17, p49)) throw new URIError("Duplicate key: " + p49);
    o17[p49] = 2 === i44.length ? decodeURIComponent(i44[1]) : null;
  }
  return o17;
}, appendToUrl: function(e46, o17) {
  return e46 + (-1 !== e46.indexOf("?") ? "&" : "?") + ("string" == typeof o17 ? o17 : n12(o17));
}, getParameterByName: function(e46, n24) {
  n24 || (n24 = window.location.href), e46 = e46.replace(/[\[\]]/g, "\\$&");
  var o17 = new RegExp("[?&]" + e46 + "(=([^&#]*)|&|#|$)").exec(n24);
  return o17 ? o17[2] ? decodeURIComponent(o17[2].replace(/\+/g, " ")) : "" : null;
}, getParamsAsObject: function(e46) {
  var n24 = {};
  return e46 ? (e46.split("&").map(function(e47) {
    var o17 = e47.split("=");
    o17 && 2 == o17.length && (n24[o17[0]] = decodeURIComponent(o17[1].replace(/\+/g, " ")));
  }), n24) : n24;
}, toQueryString: function(n24) {
  if (!n24) return "";
  if ("object" != (void 0 === n24 || void 0 === n24 ? "undefined" : o4(n24))) return n24;
  var o17 = Object.keys(n24), r33 = [];
  return o17.map(function(o18) {
    var t39 = n24[o18], i44 = [o18, t39 = "object" == (void 0 === t39 || void 0 === t39 ? "undefined" : o4(t39)) ? JSON.stringify(t39) : encodeURIComponent(t39)].join("=");
    r33.push(i44);
  }), r33.join("&");
} };

// ../../node_modules/zmp-sdk/apis/utils/request.js
var t15 = {};
var n13 = function(e46, n24, r33) {
  var o17 = n24 || "default";
  return function() {
    return (o17 in t15 ? t15[o17](e46, r33) : e46).apply(this, arguments);
  };
};
n13.setWrapper = function(e46) {
  t15[arguments.length <= 1 || void 0 === arguments[1] ? "default" : arguments[1]] = e46;
};
var r12 = { error: { code: 1, error_subcode: 1357046, message: "Received Invalid JSON reply.", type: "http" } };
function o8(e46) {
  try {
    return null === e46 ? r12 : JSON.parse(e46);
  } catch (e47) {
    return r12;
  }
}
var u4 = { execute: function(t39, r33, u48, s30) {
  u48.suppress_http_code = 1;
  var a36 = o7.encode(u48);
  "post" != r33 && (t39 = o7.appendToUrl(t39, a36), a36 = "");
  var i44 = function(e46, t40) {
    if (!self.XMLHttpRequest) return null;
    var r34 = new XMLHttpRequest(), o17 = function() {
    };
    if (!("withCredentials" in r34)) return null;
    r34.open(e46, t40, true), r34.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    var u49 = { send: function(e47) {
      r34.send(e47);
    } }, s31 = n13(function() {
      s31 = o17, "onload" in u49 && u49.onload(r34);
    }, "entry", "XMLHttpRequest:load"), a37 = n13(function() {
      a37 = o17, "onerror" in u49 && u49.onerror(r34);
    }, "entry", "XMLHttpRequest:error");
    return r34.onload = function() {
      s31();
    }, r34.onerror = function() {
      a37();
    }, r34.onreadystatechange = function() {
      4 == r34.readyState && (200 == r34.status ? s31() : a37());
    }, u49;
  }(r33, t39);
  return !!i44 && (i44.onload = function(e46) {
    s30(o8(e46.responseText));
  }, i44.onerror = function(e46) {
    e46.responseText ? s30(o8(e46.responseText)) : s30({ error: { code: 1, error_subcode: 1357045, message: "unknown error (empty response)", status: e46.status, type: "http" } });
  }, i44.send(a36), true);
} };

// ../../node_modules/zmp-sdk/apis/external/tslib/tslib.es6.js
function e15(e46, r33) {
  var t39, n24, o17, l27, a36 = { label: 0, sent: function() {
    if (1 & o17[0]) throw o17[1];
    return o17[1];
  }, trys: [], ops: [] };
  return l27 = { next: u48(0), throw: u48(1), return: u48(2) }, "function" == typeof Symbol && (l27[Symbol.iterator] = function() {
    return this;
  }), l27;
  function u48(u49) {
    return function(i44) {
      return function(u50) {
        if (t39) throw new TypeError("Generator is already executing.");
        for (; l27 && (l27 = 0, u50[0] && (a36 = 0)), a36; ) try {
          if (t39 = 1, n24 && (o17 = 2 & u50[0] ? n24.return : u50[0] ? n24.throw || ((o17 = n24.return) && o17.call(n24), 0) : n24.next) && !(o17 = o17.call(n24, u50[1])).done) return o17;
          switch (n24 = 0, o17 && (u50 = [2 & u50[0], o17.value]), u50[0]) {
            case 0:
            case 1:
              o17 = u50;
              break;
            case 4:
              return a36.label++, { value: u50[1], done: false };
            case 5:
              a36.label++, n24 = u50[1], u50 = [0];
              continue;
            case 7:
              u50 = a36.ops.pop(), a36.trys.pop();
              continue;
            default:
              if (!(o17 = a36.trys, (o17 = o17.length > 0 && o17[o17.length - 1]) || 6 !== u50[0] && 2 !== u50[0])) {
                a36 = 0;
                continue;
              }
              if (3 === u50[0] && (!o17 || u50[1] > o17[0] && u50[1] < o17[3])) {
                a36.label = u50[1];
                break;
              }
              if (6 === u50[0] && a36.label < o17[1]) {
                a36.label = o17[1], o17 = u50;
                break;
              }
              if (o17 && a36.label < o17[2]) {
                a36.label = o17[2], a36.ops.push(u50);
                break;
              }
              o17[2] && a36.ops.pop(), a36.trys.pop();
              continue;
          }
          u50 = r33.call(e46, a36);
        } catch (e47) {
          u50 = [6, e47], n24 = 0;
        } finally {
          t39 = o17 = 0;
        }
        if (5 & u50[0]) throw u50[1];
        return { value: u50[0] ? u50[1] : void 0, done: true };
      }([u49, i44]);
    };
  }
}

// ../../node_modules/zmp-sdk/apis/utils/silent.js
function o9(r33) {
  return function() {
    for (var n24 = arguments.length, o17 = new Array(n24), s30 = 0; s30 < n24; s30++) o17[s30] = arguments[s30];
    try {
      return r33.apply(void 0, a5(o17));
    } catch (r34) {
      return "undefined" != typeof console && console.error && console.error(r34), null;
    }
  };
}
o9.async = function(o17) {
  return t6(function() {
    var r33, s30, t39, l27, c41 = arguments;
    return e15(this, function(n24) {
      switch (n24.label) {
        case 0:
          for (r33 = c41.length, s30 = new Array(r33), t39 = 0; t39 < r33; t39++) s30[t39] = c41[t39];
          n24.label = 1;
        case 1:
          return n24.trys.push([1, 3, , 4]), [4, o17.apply(void 0, a5(s30))];
        case 2:
          return [2, n24.sent()];
        case 3:
          return l27 = n24.sent(), "undefined" != typeof console && console.error && console.error(l27), [2, null];
        case 4:
          return [2];
      }
    });
  });
};

// ../../node_modules/zmp-sdk/apis/appEnv/getEnv.js
var e16 = function(i44) {
  return i44.isAndroid ? n9.android : i44.isIOS ? n9.iOS : n9.unknown;
};
function r13(i44) {
  var o17 = i44.split("zalo")[1];
  return o17 && o17.replace(" ", "").split("/")[1] || "";
}
function s4() {
  var n24 = function() {
    var n25 = o9(function() {
      return window.navigator.userAgent.toLowerCase();
    })();
    if (n25) {
      var s30, a36 = function(i44) {
        return { isAndroid: /android/i.test(i44) && !/iemobile/.test(i44), isIOS: /iphone|ios|ipad|ipod/.test(i44) && !/iemobile/.test(i44), isMobile: /android|iphone|ios|ipad|ipod|iemobile/.test(i44), isZalo: /zalo/.test(i44) };
      }(n25);
      return { appEnv: (s30 = new URL(window.location.href).searchParams.get("env"), { isMp: window.location.hostname.includes(o3) && ("TESTING_LOCAL" == s30 || window.location.pathname.startsWith(i)), isMpWeb: window.location.hostname.includes("localhost") }), platform: a36, platformName: e16(a36), zaloVersion: r13(n25).trim() };
    }
  }();
  if (n24) return n24;
}

// ../../node_modules/zmp-sdk/apis/common/utils.js
var s5;
var d4 = (s5 = t6(function(r33, n24, a36) {
  var s30, d17, u48, c41;
  return e15(this, function(i44) {
    switch (i44.label) {
      case 0:
        return s30 = { appId: r33, versionStatus: n24 }, u.isUndefined(a36) || (s30.currentVersion = a36), d17 = o7.appendToUrl(O.GET_APP_INFO, s30), [4, fetch(d17, { credentials: "include" })];
      case 1:
        return (u48 = i44.sent()).ok ? [4, u48.json()] : [3, 3];
      case 2:
        if ((c41 = i44.sent()).err < 0 || !c41.data) throw new Error(c41);
        return [2, { name: c41.data.name, version: c41.data.version, description: c41.data.description, appUrl: c41.data.appUrl, qrCodeUrl: c41.data.qrCodeUrl, logoUrl: c41.data.logoUrl, cateID: c41.data.cateType }];
      case 3:
        throw new Error("Can't get app info");
    }
  });
}), function(r33, e46, o17) {
  return s5.apply(this, arguments);
});
function u5(r33) {
  try {
    var e46 = r33.map(function(r34) {
      return { action: r34.action, error: r34.error, message: r34.message, data: r34.data };
    });
    u4.execute(O.SEND_ACTION_LOG, "post", { appId: window.APP_ID, data: JSON.stringify(e46) }, function() {
    });
  } catch (r34) {
  }
}
var c3 = function(r33) {
  return r33.replace(/[A-Z]/g, function(r34) {
    return "_".concat(r34.toLowerCase());
  });
};
var l3 = function() {
  var r33;
  return "android" === ((null === (r33 = s4()) || void 0 === r33 ? void 0 : r33.platformName) || "").toLowerCase();
};
var p3 = function() {
  var r33;
  return "ios" === ((null === (r33 = s4()) || void 0 === r33 ? void 0 : r33.platformName) || "").toLowerCase();
};
var f3 = function() {
  var r33 = 0;
  if (window.zaloVersionCode) r33 = window.zaloVersionCode;
  else {
    var e46, o17 = Number((null === (e46 = s4()) || void 0 === e46 ? void 0 : e46.zaloVersion) || "0");
    if (o17 >= 24112e3) r33 = o17;
    else {
      var t39, n24 = ((null === (t39 = s4()) || void 0 === t39 ? void 0 : t39.platformName) || "").toLowerCase();
      "android" === n24 ? r33 = o17 % 1e4 : "ios" === n24 && (r33 = o17);
    }
  }
  return function(r34) {
    var e47 = String(r34);
    try {
      if (/^\d{9}$/.test(e47) && "0" === e47[5]) return Number(e47.slice(0, 5) + e47.slice(6));
    } catch (r35) {
    }
    return Number(e47);
  }(r33);
};
var m4 = function(r33, e46) {
  var o17 = f3();
  return l3() && o17 < r33 || p3() && o17 < e46;
};

// ../../node_modules/zmp-sdk/apis/common/apis/general/getSetting.js
var u6;
var c4 = (u6 = t6(function(e46) {
  var r33;
  return e15(this, function(n24) {
    switch (n24.label) {
      case 0:
        return r33 = { headers: { "Content-Type": "application/x-www-form-urlencoded", authorization: "Bearer ".concat(e46) } }, [4, fetch(O.GET_AUTH_SETTING, t9({ method: "GET" }, r33))];
      case 1:
        return [4, n24.sent().json()];
      case 2:
        return [2, n24.sent()];
    }
  });
}), function(e46) {
  return u6.apply(this, arguments);
});
var m5 = t6(function() {
  return e15(this, function(t39) {
    return [2, new Promise((s30 = t6(function(e46, t40) {
      var s31, u48, m51, l27;
      return e15(this, function(a36) {
        switch (a36.label) {
          case 0:
            return a36.trys.push([0, 3, , 4]), [4, w2.jumpAndGetToken()];
          case 1:
            return a36.sent(), (u48 = null === (s31 = w2.miniProgramConfig) || void 0 === s31 ? void 0 : s31.jwt) || t40(new n5(R.UNAUTHORIZED, s.LOGIN_REQUIRED, "getSetting")), [4, c4(u48)];
          case 2:
            return (m51 = a36.sent()) && 0 === m51.err ? e46({ authSetting: m51.data }) : t40(new n5(m51.err, m51.msg, "getSetting")), [3, 4];
          case 3:
            return l27 = a36.sent(), t40(l27), [3, 4];
          case 4:
            return [2];
        }
      });
    }), function(e46, t40) {
      return s30.apply(this, arguments);
    }))];
    var s30;
  });
});

// ../../node_modules/zmp-sdk/apis/common/call/zaloNative.js
var I2;
var b;
var T2;
var M2;
var j;
var w4;
var E2 = (T2 = s4(), M2 = {}, j = f2.getInstance(), w4 = [], v2(function(o17) {
  var e46 = o17.serialId, r33 = d5(o17.result), t39 = O2(null == r33 ? void 0 : r33.action), n24 = u.isEmpty(null == r33 ? void 0 : r33.error_code) ? 0 : null == r33 ? void 0 : r33.error_code, l27 = null == r33 ? void 0 : r33.error_message, c41 = (null == r33 ? void 0 : r33.data) || r33;
  if (t39 && 0 === n24) {
    var u48 = JSON.parse(null == r33 ? void 0 : r33.data);
    n24 = u.isEmpty(null == u48 ? void 0 : u48.error_code) ? 0 : null == u48 ? void 0 : u48.error_code, l27 = null == u48 ? void 0 : u48.error_message, c41 = (null == u48 ? void 0 : u48.data) || u48;
  }
  var d17 = { error_code: n24, error_message: l27, data: c41, action: (null == r33 ? void 0 : r33.action) || o17.actionName };
  if (e46 && M2[e46]) {
    var v7 = M2[e46], m51 = v7.callback, p49 = v7.timeout, f14 = v7.isMultiCallback, _6 = v7.options, g8 = v7.downloadListener;
    !function(o18, e47) {
      var r34 = e47 || {}, t40 = r34.success, n25 = r34.fail;
      0 === o18.error_code ? u.isFunction(t40) && t40(o18) : u.isFunction(n25) && n25(o18);
    }(d17, m51), !f14 && delete M2[e46], p49 && clearTimeout(p49);
    var I6 = { action: null == d17 ? void 0 : d17.action, error: null == d17 ? void 0 : d17.error_code, message: null == d17 ? void 0 : d17.error_message, data: {} };
    try {
      if ("action.open.inapp" === d17.action || "action.open.outapp" === d17.action) {
        var b5 = new URL(null == _6 ? void 0 : _6.url), T4 = "".concat(b5.protocol, "//").concat(b5.host).concat(b5.pathname);
        I6.data = { url: T4 };
      }
      if ("action.follow.oa" === d17.action || "action.unfollow.oa" === d17.action) {
        var E5 = null == _6 ? void 0 : _6.uid;
        I6.data = { uid: E5 };
      }
      if ("action.open.chat" === d17.action) {
        var N4 = null == _6 ? void 0 : _6.uId, S5 = null == _6 ? void 0 : _6.type;
        I6.data = { uid: N4, openChatType: S5 };
      }
      (null == _6 ? void 0 : _6.onProgress) && g8 && j.off(o5.DownloadProgress, g8);
    } catch (o18) {
    }
    w4.push(I6);
  }
}), j.on(o5.AppPaused, function() {
  if (w4.length > 0) {
    var o17 = w4;
    w4 = [], u5(o17);
  }
}), I2 ? clearInterval(I2) : I2 = setInterval(function() {
  if (w4.length > 0) {
    var o17 = w4;
    w4 = [], u5(o17);
  }
}, 5e3), true, b = t6(function(o17, t39, n24, i44) {
  var p49, _6, I6, b5, w7, E5, N4, S5, k4, y8, O4, P3, U3, C5, A5, J3, L3, R3, D4, Z2, F4, H2, q2, x2, z2, B3;
  return e15(this, function(g8) {
    switch (g8.label) {
      case 0:
        return _6 = Math.floor(1e6 * Math.random()), I6 = "".concat(o17, "_").concat(_6), b5 = A2(o17), (null == i44 ? void 0 : i44.actionName) && i44.actionName.length > 0 && (b5 = null == i44 ? void 0 : i44.actionName), w7 = (null == i44 ? void 0 : i44.isMultiCallback) || false, E5 = false !== (null == i44 ? void 0 : i44.timeout) && ((null == i44 ? void 0 : i44.timeout) || true), N4 = (null == i44 ? void 0 : i44.haveCallback) || false, S5 = (null == i44 ? void 0 : i44.skipJump) || false, k4 = (null == i44 ? void 0 : i44.requireAccessToken) || false, y8 = w3(b5), t39 && n24 && (M2[I6] = { options: t39, callback: n24, isMultiCallback: w7 }, E5 && N4 && (M2[I6].timeout = setTimeout(function() {
          var a36 = { serialId: I6, result: { error_code: R.TIME_OUT, error_message: s.TIME_OUT, data: { timeout: E5 }, action: o17 } };
          return h3(a36), null;
        }, true === E5 ? 8e3 : 1e3 * E5))), y8 ? [4, m5()] : [3, 2];
      case 1:
        if (false === (null == (P3 = g8.sent()) || null === (O4 = P3.authSetting) || void 0 === O4 ? void 0 : O4["scope.userInfo"])) return C5 = { serialId: I6, result: { error_code: R.UNAUTHORIZED, error_message: s.NEED_USER_AUTH, data: { isMobile: null == T2 || null === (U3 = T2.platform) || void 0 === U3 ? void 0 : U3.isMobile }, action: o17 } }, h3(C5), [2];
        g8.label = 2;
      case 2:
        return A5 = O2(b5), !(null == T2 || null === (p49 = T2.platform) || void 0 === p49 ? void 0 : p49.isMobile) || !o17 || u.isUndefined(ZaloJavaScriptInterface) || T2.platform.isIOS && !p4(o17) || A5 && false === A5.isSupported ? (L3 = { serialId: I6, result: { error_code: R.CLIENT_NOT_SUPPORT, error_message: s.CLIENT_NOT_SUPPORT, data: { isMobile: null == T2 || null === (J3 = T2.platform) || void 0 === J3 ? void 0 : J3.isMobile }, action: o17 } }, h3(L3), [2]) : (R3 = l4(t39), S5 ? [3, 4] : [4, w2.getJSAccessToken()]);
      case 3:
        return Z2 = g8.sent(), [3, 5];
      case 4:
        Z2 = "", g8.label = 5;
      case 5:
        D4 = Z2, g8.label = 6;
      case 6:
        return g8.trys.push([6, 10, , 11]), S5 || !k4 ? [3, 8] : [4, w2.getAccessToken()];
      case 7:
        return H2 = g8.sent(), [3, 9];
      case 8:
        H2 = "", g8.label = 9;
      case 9:
        return F4 = H2, [3, 11];
      case 10:
        return q2 = g8.sent(), x2 = { serialId: I6, result: { error_code: null == q2 ? void 0 : q2.code, error_message: null == q2 ? void 0 : q2.message, action: o17 } }, h3(x2), [2];
      case 11:
        try {
          t39 && (null == t39 ? void 0 : t39.onProgress) && (z2 = function(o18) {
            !function(o19, e46) {
              var r33 = o19.progress;
              e46 && e46(r33);
            }(o18, null == t39 ? void 0 : t39.onProgress);
          }, M2[I6].downloadListener = z2, j.on(o5.DownloadProgress, z2));
        } catch (o18) {
        }
        return B3 = T2.platform.isIOS ? window.onNativeMessage(I6, b5) : 'window.onNativeMessage("'.concat(I6, '", "').concat(b5, '")'), [2, ZaloJavaScriptInterface.jsCall(D4, b5, F4, R3, B3)];
    }
  });
}), function(o17, e46, r33, a36) {
  return b.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/utils/response.js
var d6 = { success: function(o17, n24) {
  return { code: R.SUCCESS, message: n24 || s.SUCCESS, data: o17 || {} };
}, error: { failLimit: function() {
  return new n5(R.FAIL_LIMIT, s.FAIL_LIMIT);
}, badRequest: function(r33) {
  return new n5(R.BAD_REQUEST, r33);
}, loginFailed: function(o17) {
  return new n5(R.UNAUTHORIZED, "".concat(s.LOGIN_FAILED, ": ").concat(o17));
}, loginRequired: function() {
  return new n5(R.UNAUTHORIZED, s.LOGIN_REQUIRED);
}, cannotGetShareInfo: function() {
  return new n5(R.INTERNAL_SERVER_ERROR, "Can not get app info to share");
}, createIAPOrderFailed: function(e46) {
  var n24 = Object.keys(F).find(function(r33) {
    return r33 === e46.toString();
  });
  return new n5(e46, n24 || s.UNKNOWN_ERROR);
}, clientNotSupport: function() {
  return new n5(R.CLIENT_NOT_SUPPORT, s.CLIENT_NOT_SUPPORT);
} } };
var l5 = function(r33) {
  var o17, d17, l27, f14 = null === (o17 = s4()) || void 0 === o17 ? void 0 : o17.platformName, R3 = C2(r33.action), E5 = n4[R3], N4 = (null == E5 ? void 0 : E5.skipJump) || false, _6 = (null == E5 ? void 0 : E5.errorList) || {};
  if ("JUMP_LOGIN" === R3) return new n5(R.UNKNOWN_ERROR, "Can not get auth code: ".concat(r33.error_message));
  if (w2.jumpStatus !== a6.DONE && !N4) return new n5(r2.UNAUTHORIZED.code, r2.UNAUTHORIZED.message);
  if ("permission required!" === String(r33.error_message).toLowerCase() || "no permission!" === String(r33.error_message).toLowerCase()) return new n5(r2.FORBIDDEN.code, r2.FORBIDDEN.message);
  if (f14 && (null === (d17 = P[f14]) || void 0 === d17 ? void 0 : d17[r33.error_code])) return new n5(P[f14][r33.error_code].code, P[f14][r33.error_code].message);
  var p49 = null == _6 || null === (l27 = _6[f14]) || void 0 === l27 ? void 0 : l27[String(r33.error_code)];
  if (p49) {
    var v7, I6, O4, g8 = null === (v7 = p49.mapTo) || void 0 === v7 ? void 0 : v7.message;
    if (null == p49 ? void 0 : p49.needMoreDetail) g8 = "".concat(null == p49 || null === (O4 = p49.mapTo) || void 0 === O4 ? void 0 : O4.message, ": ").concat(r33.error_message);
    return new n5(null === (I6 = p49.mapTo) || void 0 === I6 ? void 0 : I6.code, g8);
  }
  return new n5(r33.error_code, r33.error_message);
};

// ../../node_modules/zmp-sdk/apis/common/call/index.js
var a8 = function(a36, f14, l27, m51) {
  var u48 = function(s30, e46) {
    function i44(i45) {
      return function(a37) {
        var f15 = u.isEmpty(e46) ? void 0 : e46[i45];
        if (f15) return e10({ stage: E.RESPONSE, type: "log", state: i45, name: s30, response: a37 }), f15("fail" === i45 ? l5(a37) : null == a37 ? void 0 : a37.data);
      };
    }
    return u.isUndefined(s30) && (s30 = ""), Object.assign({}, { success: i44("success"), fail: i44("fail") });
  }(a36, l27), c41 = n4[a36], p49 = (null == m51 ? void 0 : m51.delay) || 0;
  setTimeout(function() {
    E2(a36, f14, u48, t9({}, m51, c41));
  }, p49);
};

// ../../node_modules/zmp-sdk/apis/common/apis/general/authorize.js
var i4;
var r14 = (i4 = t6(function(s30) {
  return e15(this, function(e46) {
    return [2, new Promise(function(e47, i44) {
      a8("MP_USER_AUTHORIZE", { scopes: s30, forceUpdate: true }, { success: function(s31) {
        u.isEmpty(s31) || e47(s31);
      }, fail: function(s31) {
        i44(s31);
      } }, { isMultiCallback: false, timeout: false });
    })];
  });
}), function(s30) {
  return i4.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/common/token.js
var A3 = function() {
  function A5() {
    a2(this, A5), this._jumpStatus = void 0, this.WAITING_QUEUE = [], this.getAccessTokenCount = 0, this._accessToken = "", this._jsAccessToken = "", this.refreshToken = "", this._cookies = [], this.accessTokenExpiresIn = 0, this.prevGetAccessTokenTimestamp = (/* @__PURE__ */ new Date()).getTime(), this.manualSetAccessToken = false, this._miniProgramConfig = {}, this.retryLimitMap = {};
  }
  var w7 = A5.prototype;
  return w7.initRetryLimit = function(e46) {
    var n24 = D[e46], t39 = { lastCall: (/* @__PURE__ */ new Date()).getTime(), limit: n24 && n24.limit ? n24.limit : 3, retry: 0 };
    return this.retryLimitMap[e46] = t39, t39;
  }, w7.resetRetryLimit = function(e46) {
    this.retryLimitMap[e46].lastCall = (/* @__PURE__ */ new Date()).getTime(), this.retryLimitMap[e46].retry = 0;
  }, w7.getRetryLimit = function(e46) {
    var n24 = this.retryLimitMap[e46];
    return n24 && n24.lastCall && n24.retry || (n24 = this.initRetryLimit(e46)), n24;
  }, w7.increaseRetryLimit = function(e46) {
    this.retryLimitMap[e46].retry++;
  }, w7.jump = function() {
    var n24 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, t39) {
          n24._jumpStatus ? n24._jumpStatus === a6.DONE ? e47(a6.DONE) : e47(a6.DOING) : (n24._jumpStatus = a6.DOING, e10({ stage: E.REQUEST, type: "log", name: "jump" }), E2("JUMP_LOGIN", {}, { success: function(t40) {
            var s30 = t40.data;
            n24._miniProgramConfig = (null == s30 ? void 0 : s30.miniProgramConfig) || {};
            var r33 = (null == s30 ? void 0 : s30.cookiesOAuthLogins) || [], i44 = r33.find(function(e48) {
              return e48.name === N.JS_TOKEN;
            });
            i44 && (n24._jsAccessToken = i44.value, window.ZJSBridge.setJSToken && window.ZJSBridge.setJSToken(i44.value)), n24._cookies = r33;
            var c41 = n24._cookies.find(function(e48) {
              return e48.name === N.ZOAUTH;
            });
            (null == c41 ? void 0 : c41.value) || u5([{ action: "action.jump.login", error: -102, message: "no zoauth", data: {} }]), e47(a6.DONE);
          }, fail: function(e48) {
            u5([{ action: "action.jump.login", error: -101, message: e48.message || "jump error", data: {} }]), t39(e48);
          } }, { skipJump: true }));
        })];
      });
    })();
  }, w7.loginZaloV3 = function(n24, t39, s30) {
    var r33 = this;
    return t6(function() {
      var o17;
      return e15(this, function(c41) {
        switch (c41.label) {
          case 0:
            return o17 = "".concat(O.GET_ACCESS_TOKEN_V3, "?app_id=").concat(t39, "&redirect_uri=").concat(T, "/").concat(n24, "/&code=").concat(s30, "&isSDK=true"), [4, fetch(o17).then((a36 = t6(function(e46) {
              var n25, t40, s31;
              return e15(this, function(o18) {
                switch (o18.label) {
                  case 0:
                    return [4, e46.json()];
                  case 1:
                    return n25 = o18.sent(), t40 = null == n25 ? void 0 : n25.access_token, r33._accessToken = t40, s31 = 1e3 * parseInt(n25.expires_in), r33.accessTokenExpiresIn = s31, r33.prevGetAccessTokenTimestamp = (/* @__PURE__ */ new Date()).getTime(), window.ZJSBridge.setZAccSession && window.ZJSBridge.setZAccSession(r33._accessToken || ""), [2, t40];
                }
              });
            }), function(e46) {
              return a36.apply(this, arguments);
            }))];
          case 1:
            return [2, c41.sent()];
        }
        var a36;
      });
    })();
  }, w7.loginZaloV4 = function(n24) {
    var t39 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "", o17 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "", c41 = this;
    return t6(function() {
      var a36, u48;
      return e15(this, function(l27) {
        switch (l27.label) {
          case 0:
            return (a36 = new URLSearchParams()).append("app_id", n24), a36.append("code", t39), a36.append("code_verifier", o17), u.isNull(c41.refreshToken) || 0 === c41.refreshToken.length ? a36.append("grant_type", "authorization_code") : (a36.append("grant_type", "refresh_token"), a36.append("refresh_token", c41.refreshToken || "")), u48 = { headers: { "Content-Type": "application/x-www-form-urlencoded" } }, [4, fetch(O.GET_ACCESS_TOKEN, t9({ method: "POST", body: a36 }, u48)).then((m51 = t6(function(e46) {
              var n25, t40, s30, r33;
              return e15(this, function(o18) {
                switch (o18.label) {
                  case 0:
                    return [4, e46.json()];
                  case 1:
                    return n25 = o18.sent(), t40 = (null == n25 ? void 0 : n25.access_token) || "", s30 = (null == n25 ? void 0 : n25.refresh_token) || "", c41.refreshToken = s30, c41._accessToken = t40, r33 = 1e3 * parseInt(null == n25 ? void 0 : n25.expires_in), c41.accessTokenExpiresIn = r33, c41.prevGetAccessTokenTimestamp = (/* @__PURE__ */ new Date()).getTime(), window.ZJSBridge.setZAccSession && window.ZJSBridge.setZAccSession(t40), [2, t40];
                }
              });
            }), function(e46) {
              return m51.apply(this, arguments);
            }))];
          case 1:
            return [2, l27.sent()];
        }
        var m51;
      });
    })();
  }, w7.getAccessToken = function() {
    var n24 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], t39 = this;
    return t6(function() {
      var s30, r33, i44, u48, l27, m51, p49, f14, g8;
      function T4() {
        return _6.apply(this, arguments);
      }
      function _6() {
        return (_6 = t6(function() {
          return e15(this, function(e46) {
            switch (e46.label) {
              case 0:
                return [4, new Promise(function(e47) {
                  var n25 = setInterval(function() {
                    (r33.getAccessTokenCount <= 1 || r33._accessToken) && (e47("done"), clearInterval(n25));
                  }, 200);
                })];
              case 1:
                return [2, e46.sent()];
            }
          });
        })).apply(this, arguments);
      }
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return s30 = (/* @__PURE__ */ new Date()).getTime() - t39.prevGetAccessTokenTimestamp, (r33 = t39).getAccessTokenCount++, t39.getAccessTokenCount > 1 ? [4, T4()] : [3, 2];
          case 1:
            e46.sent(), e46.label = 2;
          case 2:
            return t39.manualSetAccessToken ? (r33.getAccessTokenCount--, [2, Promise.resolve(t39._accessToken)]) : n24 ? [4, t39.verifyUserAuthorized()] : [3, 4];
          case 3:
            return u48 = e46.sent(), [3, 5];
          case 4:
            u48 = true, e46.label = 5;
          case 5:
            return i44 = u48, t39._accessToken.length > 0 && s30 < t39.accessTokenExpiresIn && i44 ? (r33.getAccessTokenCount--, [2, Promise.resolve(t39._accessToken)]) : (t39.accessToken = "", [4, t39.jumpAndGetToken(N.ZOAUTH)]);
          case 6:
            return l27 = e46.sent(), [4, t39.jumpAndGetToken(N.ZOAUTH_VRF)];
          case 7:
            if (m51 = e46.sent(), p49 = "zmp.getaccesstoken.fail", !i44) throw r33.getAccessTokenCount--, u5([{ action: p49, error: -104, message: "no user auth", data: {} }]), d6.error.loginFailed("User Authentication Required");
            if (!l27) return [3, 15];
            e46.label = 8;
          case 8:
            return e46.trys.push([8, 13, , 14]), m51 ? [4, t39.loginZaloV4(I, l27, m51)] : [3, 10];
          case 9:
            return f14 = e46.sent(), [3, 12];
          case 10:
            return [4, t39.loginZaloV3(I, a3, l27)];
          case 11:
            f14 = e46.sent(), e46.label = 12;
          case 12:
            return [3, 14];
          case 13:
            return g8 = e46.sent(), f14 = "", console.log("Can not get access token", g8), u5([{ action: p49, error: -103, message: String(g8) || "Can not get access token", data: {} }]), [3, 14];
          case 14:
            return r33.getAccessTokenCount--, f14 ? [2, Promise.resolve(t39._accessToken)] : (u5([{ action: p49, error: -101, message: "no accessToken", data: {} }]), [2, Promise.reject(d6.error.loginFailed("Can't get accessToken. Please try again later."))]);
          case 15:
            throw r33.getAccessTokenCount--, u5([{ action: p49, error: -102, message: "no oauth", data: {} }]), d6.error.loginFailed("Zalo app has not been activated");
        }
      });
    })();
  }, w7.verifyUserAuthorized = function() {
    var n24 = this;
    return t6(function() {
      var e46, t39, s30, r33, o17, i44;
      return e15(this, function(c41) {
        switch (c41.label) {
          case 0:
            return t39 = true, [4, n24.jumpAndGetToken()];
          case 1:
            if (c41.sent(), false !== (null === (e46 = n24._miniProgramConfig) || void 0 === e46 ? void 0 : e46.userAuthorized)) return [3, 7];
            c41.label = 2;
          case 2:
            return c41.trys.push([2, 6, , 7]), (s30 = O2("action.mp.user.authorize")) && s30.isSupported ? [4, m5()] : [3, 5];
          case 3:
            return false !== (null == (o17 = c41.sent()) || null === (r33 = o17.authSetting) || void 0 === r33 ? void 0 : r33["scope.userInfo"]) ? [3, 5] : [4, r14()];
          case 4:
            i44 = c41.sent(), t39 = (null == i44 ? void 0 : i44["scope.userInfo"]) || false, c41.label = 5;
          case 5:
            return [3, 7];
          case 6:
            return c41.sent(), t39 = false, [3, 7];
          case 7:
            return [2, t39];
        }
      });
    })();
  }, w7.setAccessToken = function(e46) {
    this.manualSetAccessToken = true, this._accessToken = e46, window.ZJSBridge.setZAccSession && window.ZJSBridge.setZAccSession(e46);
  }, w7.getJSAccessToken = function() {
    var n24 = this;
    return t6(function() {
      var e46;
      return e15(this, function(t39) {
        switch (t39.label) {
          case 0:
            return n24._jsAccessToken.length > 0 ? [2, Promise.resolve(n24._jsAccessToken)] : [4, n24.jumpAndGetToken(N.JS_TOKEN)];
          case 1:
            return e46 = t39.sent(), [2, Promise.resolve(e46)];
        }
      });
    })();
  }, w7.getSync = function(e46) {
    var n24;
    return null === (n24 = this._cookies.find(function(n25) {
      return n25.name === e46;
    })) || void 0 === n24 ? void 0 : n24.value;
  }, w7.jumpAndGetToken = function(n24) {
    var t39, s30 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], o17 = this;
    return new Promise((t39 = t6(function(e46) {
      var t40, i44, c41, a36;
      return e15(this, function(u48) {
        switch (u48.label) {
          case 0:
            return !s30 && o17._cookies.length > 0 ? u.isString(n24) ? (t40 = o17._cookies.find(function(e47) {
              return e47.name === n24;
            }), [2, e46(null == t40 ? void 0 : t40.value)]) : [2, e46("")] : (s30 && o17._jumpStatus === a6.DONE && (o17._jumpStatus = void 0), i44 = { name: n24, cb: function(n25) {
              return e46(n25);
            } }, [4, o17.jump()]);
          case 1:
            return (c41 = u48.sent()) === a6.DONE ? (o17._jumpStatus = c41, o17.WAITING_QUEUE.length && (o17.WAITING_QUEUE.forEach(function(e47) {
              var n25 = o17._cookies.find(function(n26) {
                return n26.name === e47.name;
              });
              e47.cb(null == n25 ? void 0 : n25.value);
            }), o17.WAITING_QUEUE = []), a36 = o17._cookies.find(function(e47) {
              return e47.name === i44.name;
            }), i44.cb(null == a36 ? void 0 : a36.value)) : o17.WAITING_QUEUE.push(i44), [2];
        }
      });
    }), function(e46) {
      return t39.apply(this, arguments);
    }));
  }, A5.getInstance = function() {
    return A5.instance || (A5.instance = new A5()), A5.instance;
  }, r8(A5, [{ key: "jumpStatus", get: function() {
    return this._jumpStatus;
  } }, { key: "accessToken", set: function(e46) {
    this._accessToken = e46, window.ZJSBridge.setZAccSession && window.ZJSBridge.setZAccSession(e46);
  } }, { key: "miniProgramConfig", get: function() {
    return this._miniProgramConfig;
  } }]), A5;
}();
var w2 = A3.getInstance();

// ../../node_modules/zmp-sdk/apis/utils/common.js
var f4 = n8("zmp:utils:common");
var l4 = function(r33) {
  if (u.isObject(JSON) && JSON.stringify) return JSON.stringify(r33);
  if (void 0 === r33) return "undefined";
  if (null === r33) return "null";
  try {
    if ("string" == typeof r33 || null !== r33.constructor.toString().match(/string/i)) return '"' + r33.replace(/"/g, '\\"') + '"';
  } catch (r34) {
    console.log(r34);
  }
  var n24;
  if (null !== Object.prototype.toString.call(r33).match(/array/i)) {
    n24 = [];
    for (var t39 = r33.length, e46 = 0; e46 < t39; e46++) n24.push(l4(r33[e46]));
    return "[" + n24.join(",") + "]";
  }
  if ("object" == typeof r33) {
    for (var o17 in n24 = [], r33) n24.push('"' + o17 + '":' + l4(r33[o17]));
    return "{" + n24.join(",") + "}";
  }
  return r33.toString();
};
var p4 = function(r33) {
  var n24, t39 = n4[r33], o17 = f3(), i44 = "versionLive" in t39 && (null == t39 || null === (n24 = t39.versionLive) || void 0 === n24 ? void 0 : n24.iOS) || 0;
  return !!(Array.isArray(window.ignoreCheckSupport) && window.ignoreCheckSupport.length > 0 && window.ignoreCheckSupport.includes(r33)) || o17 >= i44;
};
var m3 = function(r33) {
  try {
    if (r33) {
      f4(r33);
      var n24 = r33.replace(/\\/g, "").replace(/&#39;/g, "'");
      try {
        n24 = decodeURIComponent(atob(r33));
      } catch (r34) {
      }
      var e46 = JSON.parse(n24);
      return f4("formattedValue", n24, e46, void 0 === e46 ? "undefined" : o4(e46)), e46;
    }
    return null;
  } catch (n25) {
    return f4("parse json error:", n25), r33;
  }
};
var d5 = function(r33) {
  if (u.isObject(JSON) && JSON.parse && u.isString(r33)) {
    var n24 = r33.replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/\t/g, "\\t").replace(/\x00/g, "");
    return JSON.parse(n24, function(r34, n25) {
      return n25;
    });
  }
  return u.isObject(r33) ? r33 : new Function("return " + r33)();
};
var g2 = function(r33) {
  for (var n24 = document.getElementsByTagName("meta"), t39 = 0; t39 < n24.length; t39++) if (n24[t39].getAttribute("name") === r33 || n24[t39].getAttribute("property") === r33) return n24[t39].getAttribute("content");
  return "";
};
var h4 = function(r33) {
  return btoa(encodeURIComponent(JSON.stringify(r33)));
};
var v3 = function(r33, n24) {
  var t39, e46 = 0;
  for (t39 in r33) {
    var o17 = t39.split("_")[0];
    r33.hasOwnProperty(t39) && (n24 && o17 !== n24 || (e46 += 2 * (r33[t39].length + t39.length)));
  }
  return e46;
};
function y(r33) {
  return r33.map(function(r34, n24) {
    var t39 = r34.path.length > 0 ? r34.path.map(function(r35, n25) {
      return n25 > 0 ? "[".concat(r35, "]") : r35;
    }).join("") : "arg";
    return "Error ".concat(n24 + 1, ": Code: ").concat(r34.code, " ~ Path: ").concat(t39, " ~ Message: ").concat(r34.message);
  }).join(" | ");
}
var j2;
var S2 = function(r33) {
  return r33 === f.hidden ? "hidden" : r33 === f.transparent ? "transparent" : "normal";
};
var b2 = function(r33) {
  return "hidden" === r33 ? f.hidden : "transparent" === r33 ? f.transparent : f.normal;
};
var O2 = function(r33) {
  if (void 0 !== r33) {
    var n24 = w2.miniProgramConfig;
    return ((null == n24 ? void 0 : n24.dynamicApis) || []).find(function(n25) {
      return n25.action === r33 && false === n25.clientHandle;
    });
  }
};
var w3 = function(r33) {
  if (void 0 === r33) return false;
  var n24 = w2.miniProgramConfig;
  return ((null == n24 ? void 0 : n24.requiredAuthenList) || []).indexOf(r33) >= 0;
};
var _3 = (j2 = t6(function() {
  return e15(this, function(n24) {
    return [2, new Promise(function() {
      var n25 = t6(function(r33, n26) {
        var t39;
        return e15(this, function(n27) {
          switch (n27.label) {
            case 0:
              return n27.trys.push([0, 2, , 3]), [4, w2.jumpAndGetToken()];
            case 1:
              return n27.sent(), t39 = w2.miniProgramConfig, r33(true === (null == t39 ? void 0 : t39.canUseAccessTokenByDefault)), [3, 3];
            case 2:
              return n27.sent(), r33(false), [3, 3];
            case 3:
              return [2];
          }
        });
      });
      return function(r33, t39) {
        return n25.apply(this, arguments);
      };
    }())];
  });
}), function() {
  return j2.apply(this, arguments);
});
var C2 = function(r33) {
  return r33.replace("action.", "").replace(/\./g, "_").toUpperCase();
};
var A2 = function(r33) {
  return "action.".concat(r33.replace(/\_/g, ".").toLowerCase());
};
var N2 = function(r33, t39, e46) {
  "string" == typeof e46 && e46.length > 0 ? r33.set(t39, e46) : "object" != typeof e46 || Array.isArray(e46) || Object.entries(e46).forEach(function(e47) {
    var o17 = e6(e47, 2), i44 = o17[0], c41 = o17[1];
    null != c41 && "" !== c41 && r33.set("".concat(t39).concat(i44), String(c41));
  });
};
function J(r33) {
  var n24 = new TextEncoder().encode(r33), t39 = Array.from(n24).map(function(r34) {
    return String.fromCharCode(r34);
  }).join("");
  return btoa(t39);
}

// ../../node_modules/zmp-sdk/apis/utils/decorator.js
function a9(a36, l27, m51, u48) {
  var c41, f14;
  !function(r33, i44) {
    if (D[r33]) {
      var o17 = w2.getRetryLimit(r33);
      o17.lastCall && (/* @__PURE__ */ new Date()).getTime() - o17.lastCall > 1e3 && w2.resetRetryLimit(r33);
      var a37 = o17.limit || 3;
      if (o17.retry && o17.retry >= a37) throw r3(d6.error.failLimit(), r33);
      w2.increaseRetryLimit(r33);
    }
  }(a36);
  var v7 = Array.isArray(m51) && m51.length > 0 && (null === (c41 = m51[0]) || void 0 === c41 ? void 0 : c41.success), d17 = Array.isArray(m51) && m51.length > 0 && (null === (f14 = m51[0]) || void 0 === f14 ? void 0 : f14.fail);
  try {
    var h11 = m51;
    l27.length > 0 && l27.forEach(function(r33, o17) {
      var s30 = r33.safeParse(m51[o17]);
      if (!s30.success) {
        var n24, l28 = null === (n24 = s30.error) || void 0 === n24 ? void 0 : n24.issues, u49 = y(l28), c42 = d6.error.badRequest(u49);
        throw c42.detail = l28, r3(c42, a36);
      }
      h11.push(s30.data);
    });
    var p49 = u48.apply(void 0, a5(h11));
    return u.isPromise(p49) ? new Promise(function(r33, i44) {
      p49.then(function(i45) {
        if (!u.isNull(v7) && u.isFunction(v7)) return v7(i45);
        r33(p49);
      }).catch(function(r34) {
        var e46 = r3(r34, a36);
        if (!u.isNull(d17) && u.isFunction(d17)) return d17(e46);
        i44(e46);
      });
    }) : p49;
  } catch (r33) {
    if (!u.isNull(d17) && u.isFunction(d17)) return d17(r33);
    throw r3(r33, a36);
  }
}

// ../../node_modules/zmp-sdk/apis/apis/getVersion.js
function t16() {
  return a9("getVersion", [], [], function() {
    return r4;
  });
}

// ../../node_modules/zmp-sdk/apis/appEnv/index.js
var e17;
var o10 = (null === (e17 = s4()) || void 0 === e17 ? void 0 : e17.appEnv) || {};

// ../../node_modules/zmp-sdk/apis/common/notFound.js
var o11 = function(o17, t39) {
  var a36, s30 = { error: -1404, errorMessage: "".concat(o17, " is not found") };
  null == t39 || null === (a36 = t39.fail) || void 0 === a36 || a36.call(t39, s30), e10({ stage: E.NOT_FOUND, type: "error", name: o17, request: t39, state: "fail", response: s30 });
};

// ../../node_modules/zmp-sdk/apis/apis/login.js
function m6(o17) {
  return c5.apply(this, arguments);
}
function c5() {
  return (c5 = t6(function(m51) {
    return e15(this, function(c41) {
      return [2, a9("login", [], [m51], t6(function() {
        return e15(this, function(o17) {
          switch (o17.label) {
            case 0:
              return o10.isMp ? [4, w2.jumpAndGetToken(N.ZOAUTH)] : [3, 2];
            case 1:
              return o17.sent(), [2, "Success"];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve("")] : [2, Promise.reject(o11("login", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getAccessToken.js
function c6(e46) {
  return m7.apply(this, arguments);
}
function m7() {
  return (m7 = t6(function(c41) {
    return e15(this, function(m51) {
      return [2, a9("getAccessToken", [], [c41], t6(function() {
        var e46;
        return e15(this, function(t39) {
          switch (t39.label) {
            case 0:
              return o10.isMp ? [4, _3()] : [3, 3];
            case 1:
              return e46 = !t39.sent(), [4, w2.getAccessToken(e46)];
            case 2:
              return [2, t39.sent() || ""];
            case 3:
              return o10.isMpWeb ? [2, Promise.resolve("")] : [2, Promise.reject(o11("getAccessToken", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getSystemInfo.js
function r15() {
  var r33;
  return { version: g2("version-id") || "", apiVersion: r4, zaloVersion: String(f3()), platform: (null === (r33 = s4()) || void 0 === r33 ? void 0 : r33.platformName) || "", language: navigator.language, zaloLanguage: window.zLanguage || "vi", zaloTheme: window.zTheme || "light" };
}

// ../../node_modules/zmp-sdk/apis/apis/getSystemInfo.js
function r16() {
  return a9("getSystemInfo", [], [], function() {
    return r15();
  });
}

// ../../node_modules/zmp-sdk/apis/external/zod/lib/index.mjs.js
var e18;
!function(e46) {
  e46.assertEqual = (e47) => e47, e46.assertIs = function(e47) {
  }, e46.assertNever = function(e47) {
    throw new Error();
  }, e46.arrayToEnum = (e47) => {
    const t39 = {};
    for (const a36 of e47) t39[a36] = a36;
    return t39;
  }, e46.getValidEnumValues = (t39) => {
    const a36 = e46.objectKeys(t39).filter((e47) => "number" != typeof t39[t39[e47]]), s30 = {};
    for (const e47 of a36) s30[e47] = t39[e47];
    return e46.objectValues(s30);
  }, e46.objectValues = (t39) => e46.objectKeys(t39).map(function(e47) {
    return t39[e47];
  }), e46.objectKeys = "function" == typeof Object.keys ? (e47) => Object.keys(e47) : (e47) => {
    const t39 = [];
    for (const a36 in e47) Object.prototype.hasOwnProperty.call(e47, a36) && t39.push(a36);
    return t39;
  }, e46.find = (e47, t39) => {
    for (const a36 of e47) if (t39(a36)) return a36;
  }, e46.isInteger = "function" == typeof Number.isInteger ? (e47) => Number.isInteger(e47) : (e47) => "number" == typeof e47 && isFinite(e47) && Math.floor(e47) === e47, e46.joinValues = function(e47, t39 = " | ") {
    return e47.map((e48) => "string" == typeof e48 ? `'${e48}'` : e48).join(t39);
  }, e46.jsonStringifyReplacer = (e47, t39) => "bigint" == typeof t39 ? t39.toString() : t39;
}(e18 || (e18 = {}));
var t17 = e18.arrayToEnum(["string", "nan", "number", "integer", "float", "boolean", "date", "bigint", "symbol", "function", "undefined", "null", "array", "object", "unknown", "promise", "void", "never", "map", "set"]);
var a10 = (e46) => {
  switch (typeof e46) {
    case "undefined":
      return t17.undefined;
    case "string":
      return t17.string;
    case "number":
      return isNaN(e46) ? t17.nan : t17.number;
    case "boolean":
      return t17.boolean;
    case "function":
      return t17.function;
    case "bigint":
      return t17.bigint;
    case "symbol":
      return t17.symbol;
    case "object":
      return Array.isArray(e46) ? t17.array : null === e46 ? t17.null : e46.then && "function" == typeof e46.then && e46.catch && "function" == typeof e46.catch ? t17.promise : "undefined" != typeof Map && e46 instanceof Map ? t17.map : "undefined" != typeof Set && e46 instanceof Set ? t17.set : "undefined" != typeof Date && e46 instanceof Date ? t17.date : t17.object;
    default:
      return t17.unknown;
  }
};
var s6 = e18.arrayToEnum(["invalid_type", "invalid_literal", "custom", "invalid_union", "invalid_union_discriminator", "invalid_enum_value", "unrecognized_keys", "invalid_arguments", "invalid_return_type", "invalid_date", "invalid_string", "too_small", "too_big", "invalid_intersection_types", "not_multiple_of", "not_finite"]);
var r17 = (e46) => JSON.stringify(e46, null, 2).replace(/"([^"]+)":/g, "$1:");
var n14 = class extends Error {
  constructor(e46) {
    super(), this.issues = [], this.addIssue = (e47) => {
      this.issues = [...this.issues, e47];
    }, this.addIssues = (e47 = []) => {
      this.issues = [...this.issues, ...e47];
    };
    const t39 = new.target.prototype;
    Object.setPrototypeOf ? Object.setPrototypeOf(this, t39) : this.__proto__ = t39, this.name = "ZodError", this.issues = e46;
  }
  get errors() {
    return this.issues;
  }
  format(e46) {
    const t39 = e46 || function(e47) {
      return e47.message;
    }, a36 = { _errors: [] }, s30 = (e47) => {
      for (const r33 of e47.issues) if ("invalid_union" === r33.code) r33.unionErrors.map(s30);
      else if ("invalid_return_type" === r33.code) s30(r33.returnTypeError);
      else if ("invalid_arguments" === r33.code) s30(r33.argumentsError);
      else if (0 === r33.path.length) a36._errors.push(t39(r33));
      else {
        let e48 = a36, s31 = 0;
        for (; s31 < r33.path.length; ) {
          const a37 = r33.path[s31];
          s31 === r33.path.length - 1 ? (e48[a37] = e48[a37] || { _errors: [] }, e48[a37]._errors.push(t39(r33))) : e48[a37] = e48[a37] || { _errors: [] }, e48 = e48[a37], s31++;
        }
      }
    };
    return s30(this), a36;
  }
  toString() {
    return this.message;
  }
  get message() {
    return JSON.stringify(this.issues, e18.jsonStringifyReplacer, 2);
  }
  get isEmpty() {
    return 0 === this.issues.length;
  }
  flatten(e46 = (e47) => e47.message) {
    const t39 = {}, a36 = [];
    for (const s30 of this.issues) s30.path.length > 0 ? (t39[s30.path[0]] = t39[s30.path[0]] || [], t39[s30.path[0]].push(e46(s30))) : a36.push(e46(s30));
    return { formErrors: a36, fieldErrors: t39 };
  }
  get formErrors() {
    return this.flatten();
  }
};
n14.create = (e46) => new n14(e46);
var i5 = (a36, r33) => {
  let n24;
  switch (a36.code) {
    case s6.invalid_type:
      n24 = a36.received === t17.undefined ? "Required" : `Expected ${a36.expected}, received ${a36.received}`;
      break;
    case s6.invalid_literal:
      n24 = `Invalid literal value, expected ${JSON.stringify(a36.expected, e18.jsonStringifyReplacer)}`;
      break;
    case s6.unrecognized_keys:
      n24 = `Unrecognized key(s) in object: ${e18.joinValues(a36.keys, ", ")}`;
      break;
    case s6.invalid_union:
      n24 = "Invalid input";
      break;
    case s6.invalid_union_discriminator:
      n24 = `Invalid discriminator value. Expected ${e18.joinValues(a36.options)}`;
      break;
    case s6.invalid_enum_value:
      n24 = `Invalid enum value. Expected ${e18.joinValues(a36.options)}, received '${a36.received}'`;
      break;
    case s6.invalid_arguments:
      n24 = "Invalid function arguments";
      break;
    case s6.invalid_return_type:
      n24 = "Invalid function return type";
      break;
    case s6.invalid_date:
      n24 = "Invalid date";
      break;
    case s6.invalid_string:
      "object" == typeof a36.validation ? "startsWith" in a36.validation ? n24 = `Invalid input: must start with "${a36.validation.startsWith}"` : "endsWith" in a36.validation ? n24 = `Invalid input: must end with "${a36.validation.endsWith}"` : e18.assertNever(a36.validation) : n24 = "regex" !== a36.validation ? `Invalid ${a36.validation}` : "Invalid";
      break;
    case s6.too_small:
      n24 = "array" === a36.type ? `Array must contain ${a36.exact ? "exactly" : a36.inclusive ? "at least" : "more than"} ${a36.minimum} element(s)` : "string" === a36.type ? `String must contain ${a36.exact ? "exactly" : a36.inclusive ? "at least" : "over"} ${a36.minimum} character(s)` : "number" === a36.type ? `Number must be ${a36.exact ? "exactly equal to " : a36.inclusive ? "greater than or equal to " : "greater than "}${a36.minimum}` : "date" === a36.type ? `Date must be ${a36.exact ? "exactly equal to " : a36.inclusive ? "greater than or equal to " : "greater than "}${new Date(a36.minimum)}` : "Invalid input";
      break;
    case s6.too_big:
      n24 = "array" === a36.type ? `Array must contain ${a36.exact ? "exactly" : a36.inclusive ? "at most" : "less than"} ${a36.maximum} element(s)` : "string" === a36.type ? `String must contain ${a36.exact ? "exactly" : a36.inclusive ? "at most" : "under"} ${a36.maximum} character(s)` : "number" === a36.type ? `Number must be ${a36.exact ? "exactly" : a36.inclusive ? "less than or equal to" : "less than"} ${a36.maximum}` : "date" === a36.type ? `Date must be ${a36.exact ? "exactly" : a36.inclusive ? "smaller than or equal to" : "smaller than"} ${new Date(a36.maximum)}` : "Invalid input";
      break;
    case s6.custom:
      n24 = "Invalid input";
      break;
    case s6.invalid_intersection_types:
      n24 = "Intersection results could not be merged";
      break;
    case s6.not_multiple_of:
      n24 = `Number must be a multiple of ${a36.multipleOf}`;
      break;
    case s6.not_finite:
      n24 = "Number must be finite";
      break;
    default:
      n24 = r33.defaultError, e18.assertNever(a36);
  }
  return { message: n24 };
};
var o12 = i5;
function d7(e46) {
  o12 = e46;
}
function c7() {
  return o12;
}
var u7 = (e46) => {
  const { data: t39, path: a36, errorMaps: s30, issueData: r33 } = e46, n24 = [...a36, ...r33.path || []], i44 = { ...r33, path: n24 };
  let o17 = "";
  const d17 = s30.filter((e47) => !!e47).slice().reverse();
  for (const e47 of d17) o17 = e47(i44, { data: t39, defaultError: o17 }).message;
  return { ...r33, path: n24, message: r33.message || o17 };
};
var l6 = [];
function p5(e46, t39) {
  const a36 = u7({ issueData: t39, data: e46.data, path: e46.path, errorMaps: [e46.common.contextualErrorMap, e46.schemaErrorMap, c7(), i5].filter((e47) => !!e47) });
  e46.common.issues.push(a36);
}
var h5 = class _h {
  constructor() {
    this.value = "valid";
  }
  dirty() {
    "valid" === this.value && (this.value = "dirty");
  }
  abort() {
    "aborted" !== this.value && (this.value = "aborted");
  }
  static mergeArray(e46, t39) {
    const a36 = [];
    for (const s30 of t39) {
      if ("aborted" === s30.status) return m8;
      "dirty" === s30.status && e46.dirty(), a36.push(s30.value);
    }
    return { status: e46.value, value: a36 };
  }
  static async mergeObjectAsync(e46, t39) {
    const a36 = [];
    for (const e47 of t39) a36.push({ key: await e47.key, value: await e47.value });
    return _h.mergeObjectSync(e46, a36);
  }
  static mergeObjectSync(e46, t39) {
    const a36 = {};
    for (const s30 of t39) {
      const { key: t40, value: r33 } = s30;
      if ("aborted" === t40.status) return m8;
      if ("aborted" === r33.status) return m8;
      "dirty" === t40.status && e46.dirty(), "dirty" === r33.status && e46.dirty(), (void 0 !== r33.value || s30.alwaysSet) && (a36[t40.value] = r33.value);
    }
    return { status: e46.value, value: a36 };
  }
};
var m8 = Object.freeze({ status: "aborted" });
var f5 = (e46) => ({ status: "dirty", value: e46 });
var y2 = (e46) => ({ status: "valid", value: e46 });
var _4 = (e46) => "aborted" === e46.status;
var v4 = (e46) => "dirty" === e46.status;
var g3 = (e46) => "valid" === e46.status;
var x = (e46) => "undefined" != typeof Promise && e46 instanceof Promise;
var b3;
!function(e46) {
  e46.errToObj = (e47) => "string" == typeof e47 ? { message: e47 } : e47 || {}, e46.toString = (e47) => "string" == typeof e47 ? e47 : null == e47 ? void 0 : e47.message;
}(b3 || (b3 = {}));
var k2 = class {
  constructor(e46, t39, a36, s30) {
    this.parent = e46, this.data = t39, this._path = a36, this._key = s30;
  }
  get path() {
    return this._path.concat(this._key);
  }
};
var w5 = (e46, t39) => {
  if (g3(t39)) return { success: true, data: t39.value };
  if (!e46.common.issues.length) throw new Error("Validation failed but no issues detected.");
  return { success: false, error: new n14(e46.common.issues) };
};
function Z(e46) {
  if (!e46) return {};
  const { errorMap: t39, invalid_type_error: a36, required_error: s30, description: r33 } = e46;
  if (t39 && (a36 || s30)) throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  if (t39) return { errorMap: t39, description: r33 };
  return { errorMap: (e47, t40) => "invalid_type" !== e47.code ? { message: t40.defaultError } : void 0 === t40.data ? { message: null != s30 ? s30 : t40.defaultError } : { message: null != a36 ? a36 : t40.defaultError }, description: r33 };
}
var T3 = class {
  constructor(e46) {
    this.spa = this.safeParseAsync, this._def = e46, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync = this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe = this.describe.bind(this), this.pipe = this.pipe.bind(this), this.isNullable = this.isNullable.bind(this), this.isOptional = this.isOptional.bind(this);
  }
  get description() {
    return this._def.description;
  }
  _getType(e46) {
    return a10(e46.data);
  }
  _getOrReturnCtx(e46, t39) {
    return t39 || { common: e46.parent.common, data: e46.data, parsedType: a10(e46.data), schemaErrorMap: this._def.errorMap, path: e46.path, parent: e46.parent };
  }
  _processInputParams(e46) {
    return { status: new h5(), ctx: { common: e46.parent.common, data: e46.data, parsedType: a10(e46.data), schemaErrorMap: this._def.errorMap, path: e46.path, parent: e46.parent } };
  }
  _parseSync(e46) {
    const t39 = this._parse(e46);
    if (x(t39)) throw new Error("Synchronous parse encountered promise.");
    return t39;
  }
  _parseAsync(e46) {
    const t39 = this._parse(e46);
    return Promise.resolve(t39);
  }
  parse(e46, t39) {
    const a36 = this.safeParse(e46, t39);
    if (a36.success) return a36.data;
    throw a36.error;
  }
  safeParse(e46, t39) {
    var s30;
    const r33 = { common: { issues: [], async: null !== (s30 = null == t39 ? void 0 : t39.async) && void 0 !== s30 && s30, contextualErrorMap: null == t39 ? void 0 : t39.errorMap }, path: (null == t39 ? void 0 : t39.path) || [], schemaErrorMap: this._def.errorMap, parent: null, data: e46, parsedType: a10(e46) }, n24 = this._parseSync({ data: e46, path: r33.path, parent: r33 });
    return w5(r33, n24);
  }
  async parseAsync(e46, t39) {
    const a36 = await this.safeParseAsync(e46, t39);
    if (a36.success) return a36.data;
    throw a36.error;
  }
  async safeParseAsync(e46, t39) {
    const s30 = { common: { issues: [], contextualErrorMap: null == t39 ? void 0 : t39.errorMap, async: true }, path: (null == t39 ? void 0 : t39.path) || [], schemaErrorMap: this._def.errorMap, parent: null, data: e46, parsedType: a10(e46) }, r33 = this._parse({ data: e46, path: s30.path, parent: s30 }), n24 = await (x(r33) ? r33 : Promise.resolve(r33));
    return w5(s30, n24);
  }
  refine(e46, t39) {
    const a36 = (e47) => "string" == typeof t39 || void 0 === t39 ? { message: t39 } : "function" == typeof t39 ? t39(e47) : t39;
    return this._refinement((t40, r33) => {
      const n24 = e46(t40), i44 = () => r33.addIssue({ code: s6.custom, ...a36(t40) });
      return "undefined" != typeof Promise && n24 instanceof Promise ? n24.then((e47) => !!e47 || (i44(), false)) : !!n24 || (i44(), false);
    });
  }
  refinement(e46, t39) {
    return this._refinement((a36, s30) => !!e46(a36) || (s30.addIssue("function" == typeof t39 ? t39(a36, s30) : t39), false));
  }
  _refinement(e46) {
    return new ce({ schema: this, typeName: xe.ZodEffects, effect: { type: "refinement", refinement: e46 } });
  }
  superRefine(e46) {
    return this._refinement(e46);
  }
  optional() {
    return ue.create(this, this._def);
  }
  nullable() {
    return le.create(this, this._def);
  }
  nullish() {
    return this.nullable().optional();
  }
  array() {
    return K.create(this, this._def);
  }
  promise() {
    return de.create(this, this._def);
  }
  or(e46) {
    return F2.create([this, e46], this._def);
  }
  and(e46) {
    return G.create(this, e46, this._def);
  }
  transform(e46) {
    return new ce({ ...Z(this._def), schema: this, typeName: xe.ZodEffects, effect: { type: "transform", transform: e46 } });
  }
  default(e46) {
    const t39 = "function" == typeof e46 ? e46 : () => e46;
    return new pe({ ...Z(this._def), innerType: this, defaultValue: t39, typeName: xe.ZodDefault });
  }
  brand() {
    return new ye({ typeName: xe.ZodBranded, type: this, ...Z(this._def) });
  }
  catch(e46) {
    const t39 = "function" == typeof e46 ? e46 : () => e46;
    return new he({ ...Z(this._def), innerType: this, catchValue: t39, typeName: xe.ZodCatch });
  }
  describe(e46) {
    return new (0, this.constructor)({ ...this._def, description: e46 });
  }
  pipe(e46) {
    return _e.create(this, e46);
  }
  isOptional() {
    return this.safeParse(void 0).success;
  }
  isNullable() {
    return this.safeParse(null).success;
  }
};
var N3 = /^c[^\s-]{8,}$/i;
var O3 = /^[a-z][a-z0-9]*$/;
var S3 = /^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i;
var E3 = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|([^-]([a-zA-Z0-9-]*\.)+[a-zA-Z]{2,}))$/;
var j3 = class _j extends T3 {
  constructor() {
    super(...arguments), this._regex = (e46, t39, a36) => this.refinement((t40) => e46.test(t40), { validation: t39, code: s6.invalid_string, ...b3.errToObj(a36) }), this.nonempty = (e46) => this.min(1, b3.errToObj(e46)), this.trim = () => new _j({ ...this._def, checks: [...this._def.checks, { kind: "trim" }] });
  }
  _parse(a36) {
    this._def.coerce && (a36.data = String(a36.data));
    if (this._getType(a36) !== t17.string) {
      const e46 = this._getOrReturnCtx(a36);
      return p5(e46, { code: s6.invalid_type, expected: t17.string, received: e46.parsedType }), m8;
    }
    const r33 = new h5();
    let n24;
    for (const t39 of this._def.checks) if ("min" === t39.kind) a36.data.length < t39.value && (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.too_small, minimum: t39.value, type: "string", inclusive: true, exact: false, message: t39.message }), r33.dirty());
    else if ("max" === t39.kind) a36.data.length > t39.value && (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.too_big, maximum: t39.value, type: "string", inclusive: true, exact: false, message: t39.message }), r33.dirty());
    else if ("length" === t39.kind) {
      const e46 = a36.data.length > t39.value, i45 = a36.data.length < t39.value;
      (e46 || i45) && (n24 = this._getOrReturnCtx(a36, n24), e46 ? p5(n24, { code: s6.too_big, maximum: t39.value, type: "string", inclusive: true, exact: true, message: t39.message }) : i45 && p5(n24, { code: s6.too_small, minimum: t39.value, type: "string", inclusive: true, exact: true, message: t39.message }), r33.dirty());
    } else if ("email" === t39.kind) E3.test(a36.data) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { validation: "email", code: s6.invalid_string, message: t39.message }), r33.dirty());
    else if ("uuid" === t39.kind) S3.test(a36.data) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { validation: "uuid", code: s6.invalid_string, message: t39.message }), r33.dirty());
    else if ("cuid" === t39.kind) N3.test(a36.data) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { validation: "cuid", code: s6.invalid_string, message: t39.message }), r33.dirty());
    else if ("cuid2" === t39.kind) O3.test(a36.data) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { validation: "cuid2", code: s6.invalid_string, message: t39.message }), r33.dirty());
    else if ("url" === t39.kind) try {
      new URL(a36.data);
    } catch (e46) {
      n24 = this._getOrReturnCtx(a36, n24), p5(n24, { validation: "url", code: s6.invalid_string, message: t39.message }), r33.dirty();
    }
    else if ("regex" === t39.kind) {
      t39.regex.lastIndex = 0;
      t39.regex.test(a36.data) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { validation: "regex", code: s6.invalid_string, message: t39.message }), r33.dirty());
    } else if ("trim" === t39.kind) a36.data = a36.data.trim();
    else if ("startsWith" === t39.kind) a36.data.startsWith(t39.value) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.invalid_string, validation: { startsWith: t39.value }, message: t39.message }), r33.dirty());
    else if ("endsWith" === t39.kind) a36.data.endsWith(t39.value) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.invalid_string, validation: { endsWith: t39.value }, message: t39.message }), r33.dirty());
    else if ("datetime" === t39.kind) {
      ((i44 = t39).precision ? i44.offset ? new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${i44.precision}}(([+-]\\d{2}(:?\\d{2})?)|Z)$`) : new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${i44.precision}}Z$`) : 0 === i44.precision ? i44.offset ? new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(([+-]\\d{2}(:?\\d{2})?)|Z)$") : new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$") : i44.offset ? new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$") : new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$")).test(a36.data) || (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.invalid_string, validation: "datetime", message: t39.message }), r33.dirty());
    } else e18.assertNever(t39);
    var i44;
    return { status: r33.value, value: a36.data };
  }
  _addCheck(e46) {
    return new _j({ ...this._def, checks: [...this._def.checks, e46] });
  }
  email(e46) {
    return this._addCheck({ kind: "email", ...b3.errToObj(e46) });
  }
  url(e46) {
    return this._addCheck({ kind: "url", ...b3.errToObj(e46) });
  }
  uuid(e46) {
    return this._addCheck({ kind: "uuid", ...b3.errToObj(e46) });
  }
  cuid(e46) {
    return this._addCheck({ kind: "cuid", ...b3.errToObj(e46) });
  }
  cuid2(e46) {
    return this._addCheck({ kind: "cuid2", ...b3.errToObj(e46) });
  }
  datetime(e46) {
    var t39;
    return "string" == typeof e46 ? this._addCheck({ kind: "datetime", precision: null, offset: false, message: e46 }) : this._addCheck({ kind: "datetime", precision: void 0 === (null == e46 ? void 0 : e46.precision) ? null : null == e46 ? void 0 : e46.precision, offset: null !== (t39 = null == e46 ? void 0 : e46.offset) && void 0 !== t39 && t39, ...b3.errToObj(null == e46 ? void 0 : e46.message) });
  }
  regex(e46, t39) {
    return this._addCheck({ kind: "regex", regex: e46, ...b3.errToObj(t39) });
  }
  startsWith(e46, t39) {
    return this._addCheck({ kind: "startsWith", value: e46, ...b3.errToObj(t39) });
  }
  endsWith(e46, t39) {
    return this._addCheck({ kind: "endsWith", value: e46, ...b3.errToObj(t39) });
  }
  min(e46, t39) {
    return this._addCheck({ kind: "min", value: e46, ...b3.errToObj(t39) });
  }
  max(e46, t39) {
    return this._addCheck({ kind: "max", value: e46, ...b3.errToObj(t39) });
  }
  length(e46, t39) {
    return this._addCheck({ kind: "length", value: e46, ...b3.errToObj(t39) });
  }
  get isDatetime() {
    return !!this._def.checks.find((e46) => "datetime" === e46.kind);
  }
  get isEmail() {
    return !!this._def.checks.find((e46) => "email" === e46.kind);
  }
  get isURL() {
    return !!this._def.checks.find((e46) => "url" === e46.kind);
  }
  get isUUID() {
    return !!this._def.checks.find((e46) => "uuid" === e46.kind);
  }
  get isCUID() {
    return !!this._def.checks.find((e46) => "cuid" === e46.kind);
  }
  get isCUID2() {
    return !!this._def.checks.find((e46) => "cuid2" === e46.kind);
  }
  get minLength() {
    let e46 = null;
    for (const t39 of this._def.checks) "min" === t39.kind && (null === e46 || t39.value > e46) && (e46 = t39.value);
    return e46;
  }
  get maxLength() {
    let e46 = null;
    for (const t39 of this._def.checks) "max" === t39.kind && (null === e46 || t39.value < e46) && (e46 = t39.value);
    return e46;
  }
};
function C3(e46, t39) {
  const a36 = (e46.toString().split(".")[1] || "").length, s30 = (t39.toString().split(".")[1] || "").length, r33 = a36 > s30 ? a36 : s30;
  return parseInt(e46.toFixed(r33).replace(".", "")) % parseInt(t39.toFixed(r33).replace(".", "")) / Math.pow(10, r33);
}
j3.create = (e46) => {
  var t39;
  return new j3({ checks: [], typeName: xe.ZodString, coerce: null !== (t39 = null == e46 ? void 0 : e46.coerce) && void 0 !== t39 && t39, ...Z(e46) });
};
var I3 = class _I extends T3 {
  constructor() {
    super(...arguments), this.min = this.gte, this.max = this.lte, this.step = this.multipleOf;
  }
  _parse(a36) {
    this._def.coerce && (a36.data = Number(a36.data));
    if (this._getType(a36) !== t17.number) {
      const e46 = this._getOrReturnCtx(a36);
      return p5(e46, { code: s6.invalid_type, expected: t17.number, received: e46.parsedType }), m8;
    }
    let r33;
    const n24 = new h5();
    for (const t39 of this._def.checks) if ("int" === t39.kind) e18.isInteger(a36.data) || (r33 = this._getOrReturnCtx(a36, r33), p5(r33, { code: s6.invalid_type, expected: "integer", received: "float", message: t39.message }), n24.dirty());
    else if ("min" === t39.kind) {
      (t39.inclusive ? a36.data < t39.value : a36.data <= t39.value) && (r33 = this._getOrReturnCtx(a36, r33), p5(r33, { code: s6.too_small, minimum: t39.value, type: "number", inclusive: t39.inclusive, exact: false, message: t39.message }), n24.dirty());
    } else if ("max" === t39.kind) {
      (t39.inclusive ? a36.data > t39.value : a36.data >= t39.value) && (r33 = this._getOrReturnCtx(a36, r33), p5(r33, { code: s6.too_big, maximum: t39.value, type: "number", inclusive: t39.inclusive, exact: false, message: t39.message }), n24.dirty());
    } else "multipleOf" === t39.kind ? 0 !== C3(a36.data, t39.value) && (r33 = this._getOrReturnCtx(a36, r33), p5(r33, { code: s6.not_multiple_of, multipleOf: t39.value, message: t39.message }), n24.dirty()) : "finite" === t39.kind ? Number.isFinite(a36.data) || (r33 = this._getOrReturnCtx(a36, r33), p5(r33, { code: s6.not_finite, message: t39.message }), n24.dirty()) : e18.assertNever(t39);
    return { status: n24.value, value: a36.data };
  }
  gte(e46, t39) {
    return this.setLimit("min", e46, true, b3.toString(t39));
  }
  gt(e46, t39) {
    return this.setLimit("min", e46, false, b3.toString(t39));
  }
  lte(e46, t39) {
    return this.setLimit("max", e46, true, b3.toString(t39));
  }
  lt(e46, t39) {
    return this.setLimit("max", e46, false, b3.toString(t39));
  }
  setLimit(e46, t39, a36, s30) {
    return new _I({ ...this._def, checks: [...this._def.checks, { kind: e46, value: t39, inclusive: a36, message: b3.toString(s30) }] });
  }
  _addCheck(e46) {
    return new _I({ ...this._def, checks: [...this._def.checks, e46] });
  }
  int(e46) {
    return this._addCheck({ kind: "int", message: b3.toString(e46) });
  }
  positive(e46) {
    return this._addCheck({ kind: "min", value: 0, inclusive: false, message: b3.toString(e46) });
  }
  negative(e46) {
    return this._addCheck({ kind: "max", value: 0, inclusive: false, message: b3.toString(e46) });
  }
  nonpositive(e46) {
    return this._addCheck({ kind: "max", value: 0, inclusive: true, message: b3.toString(e46) });
  }
  nonnegative(e46) {
    return this._addCheck({ kind: "min", value: 0, inclusive: true, message: b3.toString(e46) });
  }
  multipleOf(e46, t39) {
    return this._addCheck({ kind: "multipleOf", value: e46, message: b3.toString(t39) });
  }
  finite(e46) {
    return this._addCheck({ kind: "finite", message: b3.toString(e46) });
  }
  get minValue() {
    let e46 = null;
    for (const t39 of this._def.checks) "min" === t39.kind && (null === e46 || t39.value > e46) && (e46 = t39.value);
    return e46;
  }
  get maxValue() {
    let e46 = null;
    for (const t39 of this._def.checks) "max" === t39.kind && (null === e46 || t39.value < e46) && (e46 = t39.value);
    return e46;
  }
  get isInt() {
    return !!this._def.checks.find((t39) => "int" === t39.kind || "multipleOf" === t39.kind && e18.isInteger(t39.value));
  }
  get isFinite() {
    let e46 = null, t39 = null;
    for (const a36 of this._def.checks) {
      if ("finite" === a36.kind || "int" === a36.kind || "multipleOf" === a36.kind) return true;
      "min" === a36.kind ? (null === t39 || a36.value > t39) && (t39 = a36.value) : "max" === a36.kind && (null === e46 || a36.value < e46) && (e46 = a36.value);
    }
    return Number.isFinite(t39) && Number.isFinite(e46);
  }
};
I3.create = (e46) => new I3({ checks: [], typeName: xe.ZodNumber, coerce: (null == e46 ? void 0 : e46.coerce) || false, ...Z(e46) });
var P2 = class extends T3 {
  _parse(e46) {
    this._def.coerce && (e46.data = BigInt(e46.data));
    if (this._getType(e46) !== t17.bigint) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.bigint, received: a36.parsedType }), m8;
    }
    return y2(e46.data);
  }
};
P2.create = (e46) => {
  var t39;
  return new P2({ typeName: xe.ZodBigInt, coerce: null !== (t39 = null == e46 ? void 0 : e46.coerce) && void 0 !== t39 && t39, ...Z(e46) });
};
var R2 = class extends T3 {
  _parse(e46) {
    this._def.coerce && (e46.data = Boolean(e46.data));
    if (this._getType(e46) !== t17.boolean) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.boolean, received: a36.parsedType }), m8;
    }
    return y2(e46.data);
  }
};
R2.create = (e46) => new R2({ typeName: xe.ZodBoolean, coerce: (null == e46 ? void 0 : e46.coerce) || false, ...Z(e46) });
var A4 = class _A extends T3 {
  _parse(a36) {
    this._def.coerce && (a36.data = new Date(a36.data));
    if (this._getType(a36) !== t17.date) {
      const e46 = this._getOrReturnCtx(a36);
      return p5(e46, { code: s6.invalid_type, expected: t17.date, received: e46.parsedType }), m8;
    }
    if (isNaN(a36.data.getTime())) {
      return p5(this._getOrReturnCtx(a36), { code: s6.invalid_date }), m8;
    }
    const r33 = new h5();
    let n24;
    for (const t39 of this._def.checks) "min" === t39.kind ? a36.data.getTime() < t39.value && (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.too_small, message: t39.message, inclusive: true, exact: false, minimum: t39.value, type: "date" }), r33.dirty()) : "max" === t39.kind ? a36.data.getTime() > t39.value && (n24 = this._getOrReturnCtx(a36, n24), p5(n24, { code: s6.too_big, message: t39.message, inclusive: true, exact: false, maximum: t39.value, type: "date" }), r33.dirty()) : e18.assertNever(t39);
    return { status: r33.value, value: new Date(a36.data.getTime()) };
  }
  _addCheck(e46) {
    return new _A({ ...this._def, checks: [...this._def.checks, e46] });
  }
  min(e46, t39) {
    return this._addCheck({ kind: "min", value: e46.getTime(), message: b3.toString(t39) });
  }
  max(e46, t39) {
    return this._addCheck({ kind: "max", value: e46.getTime(), message: b3.toString(t39) });
  }
  get minDate() {
    let e46 = null;
    for (const t39 of this._def.checks) "min" === t39.kind && (null === e46 || t39.value > e46) && (e46 = t39.value);
    return null != e46 ? new Date(e46) : null;
  }
  get maxDate() {
    let e46 = null;
    for (const t39 of this._def.checks) "max" === t39.kind && (null === e46 || t39.value < e46) && (e46 = t39.value);
    return null != e46 ? new Date(e46) : null;
  }
};
A4.create = (e46) => new A4({ checks: [], coerce: (null == e46 ? void 0 : e46.coerce) || false, typeName: xe.ZodDate, ...Z(e46) });
var M3 = class extends T3 {
  _parse(e46) {
    if (this._getType(e46) !== t17.symbol) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.symbol, received: a36.parsedType }), m8;
    }
    return y2(e46.data);
  }
};
M3.create = (e46) => new M3({ typeName: xe.ZodSymbol, ...Z(e46) });
var $ = class extends T3 {
  _parse(e46) {
    if (this._getType(e46) !== t17.undefined) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.undefined, received: a36.parsedType }), m8;
    }
    return y2(e46.data);
  }
};
$.create = (e46) => new $({ typeName: xe.ZodUndefined, ...Z(e46) });
var D2 = class extends T3 {
  _parse(e46) {
    if (this._getType(e46) !== t17.null) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.null, received: a36.parsedType }), m8;
    }
    return y2(e46.data);
  }
};
D2.create = (e46) => new D2({ typeName: xe.ZodNull, ...Z(e46) });
var L2 = class extends T3 {
  constructor() {
    super(...arguments), this._any = true;
  }
  _parse(e46) {
    return y2(e46.data);
  }
};
L2.create = (e46) => new L2({ typeName: xe.ZodAny, ...Z(e46) });
var z = class extends T3 {
  constructor() {
    super(...arguments), this._unknown = true;
  }
  _parse(e46) {
    return y2(e46.data);
  }
};
z.create = (e46) => new z({ typeName: xe.ZodUnknown, ...Z(e46) });
var V2 = class extends T3 {
  _parse(e46) {
    const a36 = this._getOrReturnCtx(e46);
    return p5(a36, { code: s6.invalid_type, expected: t17.never, received: a36.parsedType }), m8;
  }
};
V2.create = (e46) => new V2({ typeName: xe.ZodNever, ...Z(e46) });
var U2 = class extends T3 {
  _parse(e46) {
    if (this._getType(e46) !== t17.undefined) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.void, received: a36.parsedType }), m8;
    }
    return y2(e46.data);
  }
};
U2.create = (e46) => new U2({ typeName: xe.ZodVoid, ...Z(e46) });
var K = class _K extends T3 {
  _parse(e46) {
    const { ctx: a36, status: r33 } = this._processInputParams(e46), n24 = this._def;
    if (a36.parsedType !== t17.array) return p5(a36, { code: s6.invalid_type, expected: t17.array, received: a36.parsedType }), m8;
    if (null !== n24.exactLength) {
      const e47 = a36.data.length > n24.exactLength.value, t39 = a36.data.length < n24.exactLength.value;
      (e47 || t39) && (p5(a36, { code: e47 ? s6.too_big : s6.too_small, minimum: t39 ? n24.exactLength.value : void 0, maximum: e47 ? n24.exactLength.value : void 0, type: "array", inclusive: true, exact: true, message: n24.exactLength.message }), r33.dirty());
    }
    if (null !== n24.minLength && a36.data.length < n24.minLength.value && (p5(a36, { code: s6.too_small, minimum: n24.minLength.value, type: "array", inclusive: true, exact: false, message: n24.minLength.message }), r33.dirty()), null !== n24.maxLength && a36.data.length > n24.maxLength.value && (p5(a36, { code: s6.too_big, maximum: n24.maxLength.value, type: "array", inclusive: true, exact: false, message: n24.maxLength.message }), r33.dirty()), a36.common.async) return Promise.all([...a36.data].map((e47, t39) => n24.type._parseAsync(new k2(a36, e47, a36.path, t39)))).then((e47) => h5.mergeArray(r33, e47));
    const i44 = [...a36.data].map((e47, t39) => n24.type._parseSync(new k2(a36, e47, a36.path, t39)));
    return h5.mergeArray(r33, i44);
  }
  get element() {
    return this._def.type;
  }
  min(e46, t39) {
    return new _K({ ...this._def, minLength: { value: e46, message: b3.toString(t39) } });
  }
  max(e46, t39) {
    return new _K({ ...this._def, maxLength: { value: e46, message: b3.toString(t39) } });
  }
  length(e46, t39) {
    return new _K({ ...this._def, exactLength: { value: e46, message: b3.toString(t39) } });
  }
  nonempty(e46) {
    return this.min(1, e46);
  }
};
var W;
function B(e46) {
  if (e46 instanceof q) {
    const t39 = {};
    for (const a36 in e46.shape) {
      const s30 = e46.shape[a36];
      t39[a36] = ue.create(B(s30));
    }
    return new q({ ...e46._def, shape: () => t39 });
  }
  return e46 instanceof K ? K.create(B(e46.element)) : e46 instanceof ue ? ue.create(B(e46.unwrap())) : e46 instanceof le ? le.create(B(e46.unwrap())) : e46 instanceof Q ? Q.create(e46.items.map((e47) => B(e47))) : e46;
}
K.create = (e46, t39) => new K({ type: e46, minLength: null, maxLength: null, exactLength: null, typeName: xe.ZodArray, ...Z(t39) }), function(e46) {
  e46.mergeShapes = (e47, t39) => ({ ...e47, ...t39 });
}(W || (W = {}));
var q = class _q extends T3 {
  constructor() {
    super(...arguments), this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend;
  }
  _getCached() {
    if (null !== this._cached) return this._cached;
    const t39 = this._def.shape(), a36 = e18.objectKeys(t39);
    return this._cached = { shape: t39, keys: a36 };
  }
  _parse(e46) {
    if (this._getType(e46) !== t17.object) {
      const a37 = this._getOrReturnCtx(e46);
      return p5(a37, { code: s6.invalid_type, expected: t17.object, received: a37.parsedType }), m8;
    }
    const { status: a36, ctx: r33 } = this._processInputParams(e46), { shape: n24, keys: i44 } = this._getCached(), o17 = [];
    if (!(this._def.catchall instanceof V2 && "strip" === this._def.unknownKeys)) for (const e47 in r33.data) i44.includes(e47) || o17.push(e47);
    const d17 = [];
    for (const e47 of i44) {
      const t39 = n24[e47], a37 = r33.data[e47];
      d17.push({ key: { status: "valid", value: e47 }, value: t39._parse(new k2(r33, a37, r33.path, e47)), alwaysSet: e47 in r33.data });
    }
    if (this._def.catchall instanceof V2) {
      const e47 = this._def.unknownKeys;
      if ("passthrough" === e47) for (const e48 of o17) d17.push({ key: { status: "valid", value: e48 }, value: { status: "valid", value: r33.data[e48] } });
      else if ("strict" === e47) o17.length > 0 && (p5(r33, { code: s6.unrecognized_keys, keys: o17 }), a36.dirty());
      else if ("strip" !== e47) throw new Error("Internal ZodObject error: invalid unknownKeys value.");
    } else {
      const e47 = this._def.catchall;
      for (const t39 of o17) {
        const a37 = r33.data[t39];
        d17.push({ key: { status: "valid", value: t39 }, value: e47._parse(new k2(r33, a37, r33.path, t39)), alwaysSet: t39 in r33.data });
      }
    }
    return r33.common.async ? Promise.resolve().then(async () => {
      const e47 = [];
      for (const t39 of d17) {
        const a37 = await t39.key;
        e47.push({ key: a37, value: await t39.value, alwaysSet: t39.alwaysSet });
      }
      return e47;
    }).then((e47) => h5.mergeObjectSync(a36, e47)) : h5.mergeObjectSync(a36, d17);
  }
  get shape() {
    return this._def.shape();
  }
  strict(e46) {
    return new _q({ ...this._def, unknownKeys: "strict", ...void 0 !== e46 ? { errorMap: (t39, a36) => {
      var s30, r33, n24, i44;
      const o17 = null !== (n24 = null === (r33 = (s30 = this._def).errorMap) || void 0 === r33 ? void 0 : r33.call(s30, t39, a36).message) && void 0 !== n24 ? n24 : a36.defaultError;
      return "unrecognized_keys" === t39.code ? { message: null !== (i44 = b3.errToObj(e46).message) && void 0 !== i44 ? i44 : o17 } : { message: o17 };
    } } : {} });
  }
  strip() {
    return new _q({ ...this._def, unknownKeys: "strip" });
  }
  passthrough() {
    return new _q({ ...this._def, unknownKeys: "passthrough" });
  }
  extend(e46) {
    return new _q({ ...this._def, shape: () => ({ ...this._def.shape(), ...e46 }) });
  }
  merge(e46) {
    return new _q({ unknownKeys: e46._def.unknownKeys, catchall: e46._def.catchall, shape: () => W.mergeShapes(this._def.shape(), e46._def.shape()), typeName: xe.ZodObject });
  }
  setKey(e46, t39) {
    return this.augment({ [e46]: t39 });
  }
  catchall(e46) {
    return new _q({ ...this._def, catchall: e46 });
  }
  pick(t39) {
    const a36 = {};
    return e18.objectKeys(t39).forEach((e46) => {
      t39[e46] && this.shape[e46] && (a36[e46] = this.shape[e46]);
    }), new _q({ ...this._def, shape: () => a36 });
  }
  omit(t39) {
    const a36 = {};
    return e18.objectKeys(this.shape).forEach((e46) => {
      t39[e46] || (a36[e46] = this.shape[e46]);
    }), new _q({ ...this._def, shape: () => a36 });
  }
  deepPartial() {
    return B(this);
  }
  partial(t39) {
    const a36 = {};
    return e18.objectKeys(this.shape).forEach((e46) => {
      const s30 = this.shape[e46];
      t39 && !t39[e46] ? a36[e46] = s30 : a36[e46] = s30.optional();
    }), new _q({ ...this._def, shape: () => a36 });
  }
  required(t39) {
    const a36 = {};
    return e18.objectKeys(this.shape).forEach((e46) => {
      if (t39 && !t39[e46]) a36[e46] = this.shape[e46];
      else {
        let t40 = this.shape[e46];
        for (; t40 instanceof ue; ) t40 = t40._def.innerType;
        a36[e46] = t40;
      }
    }), new _q({ ...this._def, shape: () => a36 });
  }
  keyof() {
    return ne(e18.objectKeys(this.shape));
  }
};
q.create = (e46, t39) => new q({ shape: () => e46, unknownKeys: "strip", catchall: V2.create(), typeName: xe.ZodObject, ...Z(t39) }), q.strictCreate = (e46, t39) => new q({ shape: () => e46, unknownKeys: "strict", catchall: V2.create(), typeName: xe.ZodObject, ...Z(t39) }), q.lazycreate = (e46, t39) => new q({ shape: e46, unknownKeys: "strip", catchall: V2.create(), typeName: xe.ZodObject, ...Z(t39) });
var F2 = class extends T3 {
  _parse(e46) {
    const { ctx: t39 } = this._processInputParams(e46), a36 = this._def.options;
    if (t39.common.async) return Promise.all(a36.map(async (e47) => {
      const a37 = { ...t39, common: { ...t39.common, issues: [] }, parent: null };
      return { result: await e47._parseAsync({ data: t39.data, path: t39.path, parent: a37 }), ctx: a37 };
    })).then(function(e47) {
      for (const t40 of e47) if ("valid" === t40.result.status) return t40.result;
      for (const a38 of e47) if ("dirty" === a38.result.status) return t39.common.issues.push(...a38.ctx.common.issues), a38.result;
      const a37 = e47.map((e48) => new n14(e48.ctx.common.issues));
      return p5(t39, { code: s6.invalid_union, unionErrors: a37 }), m8;
    });
    {
      let e47;
      const r33 = [];
      for (const s30 of a36) {
        const a37 = { ...t39, common: { ...t39.common, issues: [] }, parent: null }, n24 = s30._parseSync({ data: t39.data, path: t39.path, parent: a37 });
        if ("valid" === n24.status) return n24;
        "dirty" !== n24.status || e47 || (e47 = { result: n24, ctx: a37 }), a37.common.issues.length && r33.push(a37.common.issues);
      }
      if (e47) return t39.common.issues.push(...e47.ctx.common.issues), e47.result;
      const i44 = r33.map((e48) => new n14(e48));
      return p5(t39, { code: s6.invalid_union, unionErrors: i44 }), m8;
    }
  }
  get options() {
    return this._def.options;
  }
};
F2.create = (e46, t39) => new F2({ options: e46, typeName: xe.ZodUnion, ...Z(t39) });
var J2 = (e46) => e46 instanceof se ? J2(e46.schema) : e46 instanceof ce ? J2(e46.innerType()) : e46 instanceof re ? [e46.value] : e46 instanceof ie ? e46.options : e46 instanceof oe ? Object.keys(e46.enum) : e46 instanceof pe ? J2(e46._def.innerType) : e46 instanceof $ ? [void 0] : e46 instanceof D2 ? [null] : null;
var Y = class _Y extends T3 {
  _parse(e46) {
    const { ctx: a36 } = this._processInputParams(e46);
    if (a36.parsedType !== t17.object) return p5(a36, { code: s6.invalid_type, expected: t17.object, received: a36.parsedType }), m8;
    const r33 = this.discriminator, n24 = a36.data[r33], i44 = this.optionsMap.get(n24);
    return i44 ? a36.common.async ? i44._parseAsync({ data: a36.data, path: a36.path, parent: a36 }) : i44._parseSync({ data: a36.data, path: a36.path, parent: a36 }) : (p5(a36, { code: s6.invalid_union_discriminator, options: Array.from(this.optionsMap.keys()), path: [r33] }), m8);
  }
  get discriminator() {
    return this._def.discriminator;
  }
  get options() {
    return this._def.options;
  }
  get optionsMap() {
    return this._def.optionsMap;
  }
  static create(e46, t39, a36) {
    const s30 = /* @__PURE__ */ new Map();
    for (const a37 of t39) {
      const t40 = J2(a37.shape[e46]);
      if (!t40) throw new Error(`A discriminator value for key \`${e46}\` could not be extracted from all schema options`);
      for (const r33 of t40) {
        if (s30.has(r33)) throw new Error(`Discriminator property ${String(e46)} has duplicate value ${String(r33)}`);
        s30.set(r33, a37);
      }
    }
    return new _Y({ typeName: xe.ZodDiscriminatedUnion, discriminator: e46, options: t39, optionsMap: s30, ...Z(a36) });
  }
};
function H(s30, r33) {
  const n24 = a10(s30), i44 = a10(r33);
  if (s30 === r33) return { valid: true, data: s30 };
  if (n24 === t17.object && i44 === t17.object) {
    const t39 = e18.objectKeys(r33), a36 = e18.objectKeys(s30).filter((e46) => -1 !== t39.indexOf(e46)), n25 = { ...s30, ...r33 };
    for (const e46 of a36) {
      const t40 = H(s30[e46], r33[e46]);
      if (!t40.valid) return { valid: false };
      n25[e46] = t40.data;
    }
    return { valid: true, data: n25 };
  }
  if (n24 === t17.array && i44 === t17.array) {
    if (s30.length !== r33.length) return { valid: false };
    const e46 = [];
    for (let t39 = 0; t39 < s30.length; t39++) {
      const a36 = H(s30[t39], r33[t39]);
      if (!a36.valid) return { valid: false };
      e46.push(a36.data);
    }
    return { valid: true, data: e46 };
  }
  return n24 === t17.date && i44 === t17.date && +s30 == +r33 ? { valid: true, data: s30 } : { valid: false };
}
var G = class extends T3 {
  _parse(e46) {
    const { status: t39, ctx: a36 } = this._processInputParams(e46), r33 = (e47, r34) => {
      if (_4(e47) || _4(r34)) return m8;
      const n24 = H(e47.value, r34.value);
      return n24.valid ? ((v4(e47) || v4(r34)) && t39.dirty(), { status: t39.value, value: n24.data }) : (p5(a36, { code: s6.invalid_intersection_types }), m8);
    };
    return a36.common.async ? Promise.all([this._def.left._parseAsync({ data: a36.data, path: a36.path, parent: a36 }), this._def.right._parseAsync({ data: a36.data, path: a36.path, parent: a36 })]).then(([e47, t40]) => r33(e47, t40)) : r33(this._def.left._parseSync({ data: a36.data, path: a36.path, parent: a36 }), this._def.right._parseSync({ data: a36.data, path: a36.path, parent: a36 }));
  }
};
G.create = (e46, t39, a36) => new G({ left: e46, right: t39, typeName: xe.ZodIntersection, ...Z(a36) });
var Q = class _Q extends T3 {
  _parse(e46) {
    const { status: a36, ctx: r33 } = this._processInputParams(e46);
    if (r33.parsedType !== t17.array) return p5(r33, { code: s6.invalid_type, expected: t17.array, received: r33.parsedType }), m8;
    if (r33.data.length < this._def.items.length) return p5(r33, { code: s6.too_small, minimum: this._def.items.length, inclusive: true, exact: false, type: "array" }), m8;
    !this._def.rest && r33.data.length > this._def.items.length && (p5(r33, { code: s6.too_big, maximum: this._def.items.length, inclusive: true, exact: false, type: "array" }), a36.dirty());
    const n24 = [...r33.data].map((e47, t39) => {
      const a37 = this._def.items[t39] || this._def.rest;
      return a37 ? a37._parse(new k2(r33, e47, r33.path, t39)) : null;
    }).filter((e47) => !!e47);
    return r33.common.async ? Promise.all(n24).then((e47) => h5.mergeArray(a36, e47)) : h5.mergeArray(a36, n24);
  }
  get items() {
    return this._def.items;
  }
  rest(e46) {
    return new _Q({ ...this._def, rest: e46 });
  }
};
Q.create = (e46, t39) => {
  if (!Array.isArray(e46)) throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
  return new Q({ items: e46, typeName: xe.ZodTuple, rest: null, ...Z(t39) });
};
var X = class _X extends T3 {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(e46) {
    const { status: a36, ctx: r33 } = this._processInputParams(e46);
    if (r33.parsedType !== t17.object) return p5(r33, { code: s6.invalid_type, expected: t17.object, received: r33.parsedType }), m8;
    const n24 = [], i44 = this._def.keyType, o17 = this._def.valueType;
    for (const e47 in r33.data) n24.push({ key: i44._parse(new k2(r33, e47, r33.path, e47)), value: o17._parse(new k2(r33, r33.data[e47], r33.path, e47)) });
    return r33.common.async ? h5.mergeObjectAsync(a36, n24) : h5.mergeObjectSync(a36, n24);
  }
  get element() {
    return this._def.valueType;
  }
  static create(e46, t39, a36) {
    return new _X(t39 instanceof T3 ? { keyType: e46, valueType: t39, typeName: xe.ZodRecord, ...Z(a36) } : { keyType: j3.create(), valueType: e46, typeName: xe.ZodRecord, ...Z(t39) });
  }
};
var ee = class extends T3 {
  _parse(e46) {
    const { status: a36, ctx: r33 } = this._processInputParams(e46);
    if (r33.parsedType !== t17.map) return p5(r33, { code: s6.invalid_type, expected: t17.map, received: r33.parsedType }), m8;
    const n24 = this._def.keyType, i44 = this._def.valueType, o17 = [...r33.data.entries()].map(([e47, t39], a37) => ({ key: n24._parse(new k2(r33, e47, r33.path, [a37, "key"])), value: i44._parse(new k2(r33, t39, r33.path, [a37, "value"])) }));
    if (r33.common.async) {
      const e47 = /* @__PURE__ */ new Map();
      return Promise.resolve().then(async () => {
        for (const t39 of o17) {
          const s30 = await t39.key, r34 = await t39.value;
          if ("aborted" === s30.status || "aborted" === r34.status) return m8;
          "dirty" !== s30.status && "dirty" !== r34.status || a36.dirty(), e47.set(s30.value, r34.value);
        }
        return { status: a36.value, value: e47 };
      });
    }
    {
      const e47 = /* @__PURE__ */ new Map();
      for (const t39 of o17) {
        const s30 = t39.key, r34 = t39.value;
        if ("aborted" === s30.status || "aborted" === r34.status) return m8;
        "dirty" !== s30.status && "dirty" !== r34.status || a36.dirty(), e47.set(s30.value, r34.value);
      }
      return { status: a36.value, value: e47 };
    }
  }
};
ee.create = (e46, t39, a36) => new ee({ valueType: t39, keyType: e46, typeName: xe.ZodMap, ...Z(a36) });
var te = class _te extends T3 {
  _parse(e46) {
    const { status: a36, ctx: r33 } = this._processInputParams(e46);
    if (r33.parsedType !== t17.set) return p5(r33, { code: s6.invalid_type, expected: t17.set, received: r33.parsedType }), m8;
    const n24 = this._def;
    null !== n24.minSize && r33.data.size < n24.minSize.value && (p5(r33, { code: s6.too_small, minimum: n24.minSize.value, type: "set", inclusive: true, exact: false, message: n24.minSize.message }), a36.dirty()), null !== n24.maxSize && r33.data.size > n24.maxSize.value && (p5(r33, { code: s6.too_big, maximum: n24.maxSize.value, type: "set", inclusive: true, exact: false, message: n24.maxSize.message }), a36.dirty());
    const i44 = this._def.valueType;
    function o17(e47) {
      const t39 = /* @__PURE__ */ new Set();
      for (const s30 of e47) {
        if ("aborted" === s30.status) return m8;
        "dirty" === s30.status && a36.dirty(), t39.add(s30.value);
      }
      return { status: a36.value, value: t39 };
    }
    const d17 = [...r33.data.values()].map((e47, t39) => i44._parse(new k2(r33, e47, r33.path, t39)));
    return r33.common.async ? Promise.all(d17).then((e47) => o17(e47)) : o17(d17);
  }
  min(e46, t39) {
    return new _te({ ...this._def, minSize: { value: e46, message: b3.toString(t39) } });
  }
  max(e46, t39) {
    return new _te({ ...this._def, maxSize: { value: e46, message: b3.toString(t39) } });
  }
  size(e46, t39) {
    return this.min(e46, t39).max(e46, t39);
  }
  nonempty(e46) {
    return this.min(1, e46);
  }
};
te.create = (e46, t39) => new te({ valueType: e46, minSize: null, maxSize: null, typeName: xe.ZodSet, ...Z(t39) });
var ae = class _ae extends T3 {
  constructor() {
    super(...arguments), this.validate = this.implement;
  }
  _parse(e46) {
    const { ctx: a36 } = this._processInputParams(e46);
    if (a36.parsedType !== t17.function) return p5(a36, { code: s6.invalid_type, expected: t17.function, received: a36.parsedType }), m8;
    function r33(e47, t39) {
      return u7({ data: e47, path: a36.path, errorMaps: [a36.common.contextualErrorMap, a36.schemaErrorMap, c7(), i5].filter((e48) => !!e48), issueData: { code: s6.invalid_arguments, argumentsError: t39 } });
    }
    function o17(e47, t39) {
      return u7({ data: e47, path: a36.path, errorMaps: [a36.common.contextualErrorMap, a36.schemaErrorMap, c7(), i5].filter((e48) => !!e48), issueData: { code: s6.invalid_return_type, returnTypeError: t39 } });
    }
    const d17 = { errorMap: a36.common.contextualErrorMap }, l27 = a36.data;
    return this._def.returns instanceof de ? y2(async (...e47) => {
      const t39 = new n14([]), a37 = await this._def.args.parseAsync(e47, d17).catch((a38) => {
        throw t39.addIssue(r33(e47, a38)), t39;
      }), s30 = await l27(...a37);
      return await this._def.returns._def.type.parseAsync(s30, d17).catch((e48) => {
        throw t39.addIssue(o17(s30, e48)), t39;
      });
    }) : y2((...e47) => {
      const t39 = this._def.args.safeParse(e47, d17);
      if (!t39.success) throw new n14([r33(e47, t39.error)]);
      const a37 = l27(...t39.data), s30 = this._def.returns.safeParse(a37, d17);
      if (!s30.success) throw new n14([o17(a37, s30.error)]);
      return s30.data;
    });
  }
  parameters() {
    return this._def.args;
  }
  returnType() {
    return this._def.returns;
  }
  args(...e46) {
    return new _ae({ ...this._def, args: Q.create(e46).rest(z.create()) });
  }
  returns(e46) {
    return new _ae({ ...this._def, returns: e46 });
  }
  implement(e46) {
    return this.parse(e46);
  }
  strictImplement(e46) {
    return this.parse(e46);
  }
  static create(e46, t39, a36) {
    return new _ae({ args: e46 || Q.create([]).rest(z.create()), returns: t39 || z.create(), typeName: xe.ZodFunction, ...Z(a36) });
  }
};
var se = class extends T3 {
  get schema() {
    return this._def.getter();
  }
  _parse(e46) {
    const { ctx: t39 } = this._processInputParams(e46);
    return this._def.getter()._parse({ data: t39.data, path: t39.path, parent: t39 });
  }
};
se.create = (e46, t39) => new se({ getter: e46, typeName: xe.ZodLazy, ...Z(t39) });
var re = class extends T3 {
  _parse(e46) {
    if (e46.data !== this._def.value) {
      const t39 = this._getOrReturnCtx(e46);
      return p5(t39, { received: t39.data, code: s6.invalid_literal, expected: this._def.value }), m8;
    }
    return { status: "valid", value: e46.data };
  }
  get value() {
    return this._def.value;
  }
};
function ne(e46, t39) {
  return new ie({ values: e46, typeName: xe.ZodEnum, ...Z(t39) });
}
re.create = (e46, t39) => new re({ value: e46, typeName: xe.ZodLiteral, ...Z(t39) });
var ie = class _ie extends T3 {
  _parse(t39) {
    if ("string" != typeof t39.data) {
      const a36 = this._getOrReturnCtx(t39), r33 = this._def.values;
      return p5(a36, { expected: e18.joinValues(r33), received: a36.parsedType, code: s6.invalid_type }), m8;
    }
    if (-1 === this._def.values.indexOf(t39.data)) {
      const e46 = this._getOrReturnCtx(t39), a36 = this._def.values;
      return p5(e46, { received: e46.data, code: s6.invalid_enum_value, options: a36 }), m8;
    }
    return y2(t39.data);
  }
  get options() {
    return this._def.values;
  }
  get enum() {
    const e46 = {};
    for (const t39 of this._def.values) e46[t39] = t39;
    return e46;
  }
  get Values() {
    const e46 = {};
    for (const t39 of this._def.values) e46[t39] = t39;
    return e46;
  }
  get Enum() {
    const e46 = {};
    for (const t39 of this._def.values) e46[t39] = t39;
    return e46;
  }
  extract(e46) {
    return _ie.create(e46);
  }
  exclude(e46) {
    return _ie.create(this.options.filter((t39) => !e46.includes(t39)));
  }
};
ie.create = ne;
var oe = class extends T3 {
  _parse(a36) {
    const r33 = e18.getValidEnumValues(this._def.values), n24 = this._getOrReturnCtx(a36);
    if (n24.parsedType !== t17.string && n24.parsedType !== t17.number) {
      const t39 = e18.objectValues(r33);
      return p5(n24, { expected: e18.joinValues(t39), received: n24.parsedType, code: s6.invalid_type }), m8;
    }
    if (-1 === r33.indexOf(a36.data)) {
      const t39 = e18.objectValues(r33);
      return p5(n24, { received: n24.data, code: s6.invalid_enum_value, options: t39 }), m8;
    }
    return y2(a36.data);
  }
  get enum() {
    return this._def.values;
  }
};
oe.create = (e46, t39) => new oe({ values: e46, typeName: xe.ZodNativeEnum, ...Z(t39) });
var de = class extends T3 {
  unwrap() {
    return this._def.type;
  }
  _parse(e46) {
    const { ctx: a36 } = this._processInputParams(e46);
    if (a36.parsedType !== t17.promise && false === a36.common.async) return p5(a36, { code: s6.invalid_type, expected: t17.promise, received: a36.parsedType }), m8;
    const r33 = a36.parsedType === t17.promise ? a36.data : Promise.resolve(a36.data);
    return y2(r33.then((e47) => this._def.type.parseAsync(e47, { path: a36.path, errorMap: a36.common.contextualErrorMap })));
  }
};
de.create = (e46, t39) => new de({ type: e46, typeName: xe.ZodPromise, ...Z(t39) });
var ce = class extends T3 {
  innerType() {
    return this._def.schema;
  }
  sourceType() {
    return this._def.schema._def.typeName === xe.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
  }
  _parse(t39) {
    const { status: a36, ctx: s30 } = this._processInputParams(t39), r33 = this._def.effect || null;
    if ("preprocess" === r33.type) {
      const e46 = r33.transform(s30.data);
      return s30.common.async ? Promise.resolve(e46).then((e47) => this._def.schema._parseAsync({ data: e47, path: s30.path, parent: s30 })) : this._def.schema._parseSync({ data: e46, path: s30.path, parent: s30 });
    }
    const n24 = { addIssue: (e46) => {
      p5(s30, e46), e46.fatal ? a36.abort() : a36.dirty();
    }, get path() {
      return s30.path;
    } };
    if (n24.addIssue = n24.addIssue.bind(n24), "refinement" === r33.type) {
      const e46 = (e47) => {
        const t40 = r33.refinement(e47, n24);
        if (s30.common.async) return Promise.resolve(t40);
        if (t40 instanceof Promise) throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        return e47;
      };
      if (false === s30.common.async) {
        const t40 = this._def.schema._parseSync({ data: s30.data, path: s30.path, parent: s30 });
        return "aborted" === t40.status ? m8 : ("dirty" === t40.status && a36.dirty(), e46(t40.value), { status: a36.value, value: t40.value });
      }
      return this._def.schema._parseAsync({ data: s30.data, path: s30.path, parent: s30 }).then((t40) => "aborted" === t40.status ? m8 : ("dirty" === t40.status && a36.dirty(), e46(t40.value).then(() => ({ status: a36.value, value: t40.value }))));
    }
    if ("transform" === r33.type) {
      if (false === s30.common.async) {
        const e46 = this._def.schema._parseSync({ data: s30.data, path: s30.path, parent: s30 });
        if (!g3(e46)) return e46;
        const t40 = r33.transform(e46.value, n24);
        if (t40 instanceof Promise) throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
        return { status: a36.value, value: t40 };
      }
      return this._def.schema._parseAsync({ data: s30.data, path: s30.path, parent: s30 }).then((e46) => g3(e46) ? Promise.resolve(r33.transform(e46.value, n24)).then((e47) => ({ status: a36.value, value: e47 })) : e46);
    }
    e18.assertNever(r33);
  }
};
ce.create = (e46, t39, a36) => new ce({ schema: e46, typeName: xe.ZodEffects, effect: t39, ...Z(a36) }), ce.createWithPreprocess = (e46, t39, a36) => new ce({ schema: t39, effect: { type: "preprocess", transform: e46 }, typeName: xe.ZodEffects, ...Z(a36) });
var ue = class extends T3 {
  _parse(e46) {
    return this._getType(e46) === t17.undefined ? y2(void 0) : this._def.innerType._parse(e46);
  }
  unwrap() {
    return this._def.innerType;
  }
};
ue.create = (e46, t39) => new ue({ innerType: e46, typeName: xe.ZodOptional, ...Z(t39) });
var le = class extends T3 {
  _parse(e46) {
    return this._getType(e46) === t17.null ? y2(null) : this._def.innerType._parse(e46);
  }
  unwrap() {
    return this._def.innerType;
  }
};
le.create = (e46, t39) => new le({ innerType: e46, typeName: xe.ZodNullable, ...Z(t39) });
var pe = class extends T3 {
  _parse(e46) {
    const { ctx: a36 } = this._processInputParams(e46);
    let s30 = a36.data;
    return a36.parsedType === t17.undefined && (s30 = this._def.defaultValue()), this._def.innerType._parse({ data: s30, path: a36.path, parent: a36 });
  }
  removeDefault() {
    return this._def.innerType;
  }
};
pe.create = (e46, t39) => new pe({ innerType: e46, typeName: xe.ZodDefault, defaultValue: "function" == typeof t39.default ? t39.default : () => t39.default, ...Z(t39) });
var he = class extends T3 {
  _parse(e46) {
    const { ctx: t39 } = this._processInputParams(e46), a36 = this._def.innerType._parse({ data: t39.data, path: t39.path, parent: { ...t39, common: { ...t39.common, issues: [] } } });
    return x(a36) ? a36.then((e47) => ({ status: "valid", value: "valid" === e47.status ? e47.value : this._def.catchValue() })) : { status: "valid", value: "valid" === a36.status ? a36.value : this._def.catchValue() };
  }
  removeCatch() {
    return this._def.innerType;
  }
};
he.create = (e46, t39) => new he({ innerType: e46, typeName: xe.ZodCatch, catchValue: "function" == typeof t39.catch ? t39.catch : () => t39.catch, ...Z(t39) });
var me = class extends T3 {
  _parse(e46) {
    if (this._getType(e46) !== t17.nan) {
      const a36 = this._getOrReturnCtx(e46);
      return p5(a36, { code: s6.invalid_type, expected: t17.nan, received: a36.parsedType }), m8;
    }
    return { status: "valid", value: e46.data };
  }
};
me.create = (e46) => new me({ typeName: xe.ZodNaN, ...Z(e46) });
var fe = Symbol("zod_brand");
var ye = class extends T3 {
  _parse(e46) {
    const { ctx: t39 } = this._processInputParams(e46), a36 = t39.data;
    return this._def.type._parse({ data: a36, path: t39.path, parent: t39 });
  }
  unwrap() {
    return this._def.type;
  }
};
var _e = class __e extends T3 {
  _parse(e46) {
    const { status: t39, ctx: a36 } = this._processInputParams(e46);
    if (a36.common.async) {
      return (async () => {
        const e47 = await this._def.in._parseAsync({ data: a36.data, path: a36.path, parent: a36 });
        return "aborted" === e47.status ? m8 : "dirty" === e47.status ? (t39.dirty(), f5(e47.value)) : this._def.out._parseAsync({ data: e47.value, path: a36.path, parent: a36 });
      })();
    }
    {
      const e47 = this._def.in._parseSync({ data: a36.data, path: a36.path, parent: a36 });
      return "aborted" === e47.status ? m8 : "dirty" === e47.status ? (t39.dirty(), { status: "dirty", value: e47.value }) : this._def.out._parseSync({ data: e47.value, path: a36.path, parent: a36 });
    }
  }
  static create(e46, t39) {
    return new __e({ in: e46, out: t39, typeName: xe.ZodPipeline });
  }
};
var ve = (e46, t39 = {}, a36) => e46 ? L2.create().superRefine((s30, r33) => {
  if (!e46(s30)) {
    const e47 = "function" == typeof t39 ? t39(s30) : t39, n24 = "string" == typeof e47 ? { message: e47 } : e47;
    r33.addIssue({ code: "custom", ...n24, fatal: a36 });
  }
}) : L2.create();
var ge = { object: q.lazycreate };
var xe;
!function(e46) {
  e46.ZodString = "ZodString", e46.ZodNumber = "ZodNumber", e46.ZodNaN = "ZodNaN", e46.ZodBigInt = "ZodBigInt", e46.ZodBoolean = "ZodBoolean", e46.ZodDate = "ZodDate", e46.ZodSymbol = "ZodSymbol", e46.ZodUndefined = "ZodUndefined", e46.ZodNull = "ZodNull", e46.ZodAny = "ZodAny", e46.ZodUnknown = "ZodUnknown", e46.ZodNever = "ZodNever", e46.ZodVoid = "ZodVoid", e46.ZodArray = "ZodArray", e46.ZodObject = "ZodObject", e46.ZodUnion = "ZodUnion", e46.ZodDiscriminatedUnion = "ZodDiscriminatedUnion", e46.ZodIntersection = "ZodIntersection", e46.ZodTuple = "ZodTuple", e46.ZodRecord = "ZodRecord", e46.ZodMap = "ZodMap", e46.ZodSet = "ZodSet", e46.ZodFunction = "ZodFunction", e46.ZodLazy = "ZodLazy", e46.ZodLiteral = "ZodLiteral", e46.ZodEnum = "ZodEnum", e46.ZodEffects = "ZodEffects", e46.ZodNativeEnum = "ZodNativeEnum", e46.ZodOptional = "ZodOptional", e46.ZodNullable = "ZodNullable", e46.ZodDefault = "ZodDefault", e46.ZodCatch = "ZodCatch", e46.ZodPromise = "ZodPromise", e46.ZodBranded = "ZodBranded", e46.ZodPipeline = "ZodPipeline";
}(xe || (xe = {}));
var be = (e46, t39 = { message: `Input not instance of ${e46.name}` }) => ve((t40) => t40 instanceof e46, t39, true);
var ke = j3.create;
var we = I3.create;
var Ze = me.create;
var Te = P2.create;
var Ne = R2.create;
var Oe = A4.create;
var Se = M3.create;
var Ee = $.create;
var je = D2.create;
var Ce = L2.create;
var Ie = z.create;
var Pe = V2.create;
var Re = U2.create;
var Ae = K.create;
var Me = q.create;
var $e = q.strictCreate;
var De = F2.create;
var Le = Y.create;
var ze = G.create;
var Ve = Q.create;
var Ue = X.create;
var Ke = ee.create;
var We = te.create;
var Be = ae.create;
var qe = se.create;
var Fe = re.create;
var Je = ie.create;
var Ye = oe.create;
var He = de.create;
var Ge = ce.create;
var Qe = ue.create;
var Xe = le.create;
var et = ce.createWithPreprocess;
var tt = _e.create;
var at = () => ke().optional();
var st = () => we().optional();
var rt = () => Ne().optional();
var nt = { string: (e46) => j3.create({ ...e46, coerce: true }), number: (e46) => I3.create({ ...e46, coerce: true }), boolean: (e46) => R2.create({ ...e46, coerce: true }), bigint: (e46) => P2.create({ ...e46, coerce: true }), date: (e46) => A4.create({ ...e46, coerce: true }) };
var it = m8;
var ot = Object.freeze({ __proto__: null, defaultErrorMap: i5, setErrorMap: d7, getErrorMap: c7, makeIssue: u7, EMPTY_PATH: l6, addIssueToContext: p5, ParseStatus: h5, INVALID: m8, DIRTY: f5, OK: y2, isAborted: _4, isDirty: v4, isValid: g3, isAsync: x, get util() {
  return e18;
}, ZodParsedType: t17, getParsedType: a10, ZodType: T3, ZodString: j3, ZodNumber: I3, ZodBigInt: P2, ZodBoolean: R2, ZodDate: A4, ZodSymbol: M3, ZodUndefined: $, ZodNull: D2, ZodAny: L2, ZodUnknown: z, ZodNever: V2, ZodVoid: U2, ZodArray: K, get objectUtil() {
  return W;
}, ZodObject: q, ZodUnion: F2, ZodDiscriminatedUnion: Y, ZodIntersection: G, ZodTuple: Q, ZodRecord: X, ZodMap: ee, ZodSet: te, ZodFunction: ae, ZodLazy: se, ZodLiteral: re, ZodEnum: ie, ZodNativeEnum: oe, ZodPromise: de, ZodEffects: ce, ZodTransformer: ce, ZodOptional: ue, ZodNullable: le, ZodDefault: pe, ZodCatch: he, ZodNaN: me, BRAND: fe, ZodBranded: ye, ZodPipeline: _e, custom: ve, Schema: T3, ZodSchema: T3, late: ge, get ZodFirstPartyTypeKind() {
  return xe;
}, coerce: nt, any: Ce, array: Ae, bigint: Te, boolean: Ne, date: Oe, discriminatedUnion: Le, effect: Ge, enum: Je, function: Be, instanceof: be, intersection: ze, lazy: qe, literal: Fe, map: Ke, nan: Ze, nativeEnum: Ye, never: Pe, null: je, nullable: Xe, number: we, object: Me, oboolean: rt, onumber: st, optional: Qe, ostring: at, pipeline: tt, preprocess: et, promise: He, record: Ue, set: We, strictObject: $e, string: ke, symbol: Se, transformer: Ge, tuple: Ve, undefined: Ee, union: De, unknown: Ie, void: Re, NEVER: it, ZodIssueCode: s6, quotelessJson: r17, ZodError: n14 });

// ../../node_modules/zmp-sdk/apis/common/apis/general/view.js
var B2 = function() {
  function B3() {
    var i44, o17, n24, r33, v7, p49, h11, m51, g8, C5, T4, _7, y8, E5, O4, w7, I6, H2, S5, b5, N4, x2, j6, D4, R3, P3, M5;
    a2(this, B3), this.initConfig = window.APP_CONFIG;
    var G2 = u.isString(null === (i44 = this.initConfig) || void 0 === i44 || null === (o17 = i44.app) || void 0 === o17 ? void 0 : o17.headerColor) && (null === (n24 = this.initConfig) || void 0 === n24 || null === (r33 = n24.app) || void 0 === r33 || null === (v7 = r33.headerColor) || void 0 === v7 ? void 0 : v7.length) > 0 ? l : M;
    this.hideIOSSafeAreaBottom = !!(null === (p49 = this.initConfig) || void 0 === p49 || null === (h11 = p49.app) || void 0 === h11 ? void 0 : h11.hideIOSSafeAreaBottom), this.hideAndroidBottomNavigationBar = !!(null === (m51 = this.initConfig) || void 0 === m51 || null === (g8 = m51.app) || void 0 === g8 ? void 0 : g8.hideAndroidBottomNavigationBar);
    var L3, K2, U3 = "none" === (null === (C5 = this.initConfig) || void 0 === C5 || null === (T4 = C5.app) || void 0 === T4 ? void 0 : T4.leftButton) || "home" === (null === (_7 = this.initConfig) || void 0 === _7 || null === (y8 = _7.app) || void 0 === y8 ? void 0 : y8.leftButton) ? d.HIDE_BACK : d.SHOW_BACK, W2 = "home" === (null === (E5 = this.initConfig) || void 0 === E5 || null === (O4 = E5.app) || void 0 === O4 ? void 0 : O4.leftButton) || "both" === (null === (w7 = this.initConfig) || void 0 === w7 || null === (I6 = w7.app) || void 0 === I6 ? void 0 : I6.leftButton) ? d.SHOW_HOME : d.HIDE_HOME, k4 = new URL(location.href), F4 = b2(null === (H2 = this.initConfig) || void 0 === H2 || null === (S5 = H2.app) || void 0 === S5 ? void 0 : S5.statusBar);
    "boolean" == typeof (null === (b5 = this.initConfig) || void 0 === b5 || null === (N4 = b5.app) || void 0 === N4 ? void 0 : N4.actionBarHidden) && (G2 = (null === (L3 = this.initConfig) || void 0 === L3 || null === (K2 = L3.app) || void 0 === K2 ? void 0 : K2.actionBarHidden) ? M : l);
    this.config = { actionbarType: G2, buttonType: U3, statusbarType: F4, dataConfig: { backgroundColor: (null === (x2 = this.initConfig) || void 0 === x2 || null === (j6 = x2.app) || void 0 === j6 ? void 0 : j6.headerColor) || u2, textColor: (null === (D4 = this.initConfig) || void 0 === D4 || null === (R3 = D4.app) || void 0 === R3 ? void 0 : R3.textColor) || "", textAlign: (null === (P3 = this.initConfig) || void 0 === P3 || null === (M5 = P3.app) || void 0 === M5 ? void 0 : M5.textAlign) || V, viewInLeftType: U3, confirmToExit: 1 }, homeConfig: { enable: W2, url: "".concat(T, "/").concat(I, "/").concat(k4.search) } };
  }
  var _6 = B3.prototype;
  return _6.setNavigationBarTitle = function(t39) {
    return t6(function() {
      return e15(this, function(i44) {
        return [2, new Promise(function(i45, o17) {
          a8("CHANGE_TITLE_HEADER", { title: t39 }, { success: function() {
            i45(), document.title = t39;
          }, fail: function(i46) {
            o17(i46);
          } }, { timeout: false });
        })];
      });
    })();
  }, _6.setNavigationBarLeftButton = function(t39) {
    var e46 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.config.dataConfig.confirmToExit, a36 = this;
    return t6(function() {
      var i44, l27;
      return e15(this, function(s30) {
        return void 0 === (void 0 === t39 ? "undefined" : o4(t39)) && null === (void 0 === t39 ? "undefined" : o4(t39)) || (i44 = "none" === t39 || "home" === t39 ? d.HIDE_BACK : d.SHOW_BACK, l27 = "home" === t39 || "both" === t39 ? d.SHOW_HOME : d.HIDE_HOME, a36.config.buttonType = i44, a36.config.dataConfig.viewInLeftType = i44, a36.config.homeConfig.enable = l27), a36.config.dataConfig.confirmToExit = e46, [2, new Promise(function(i45, t40) {
          a8("CHANGE_ACTIONBAR_LEFTBUTTON_TYPE", t9({}, a36.config), { success: function() {
            i45();
          }, fail: function(i46) {
            t40(i46);
          } }, { timeout: false });
        })];
      });
    })();
  }, _6.setAndroidBottomNavigationBar = function() {
    var t39 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], o17 = this;
    return t6(function() {
      var i44, n24, e46;
      return e15(this, function(a36) {
        return n24 = t39 ? h2.hide : h2.show, e46 = null === (i44 = s4()) || void 0 === i44 ? void 0 : i44.platform, [2, new Promise(function(i45, a37) {
          if (!(null == e46 ? void 0 : e46.isAndroid)) return i45();
          a8("CHANGE_BOTTOMBAR", { bottombarType: n24 }, { success: function() {
            o17.hideAndroidBottomNavigationBar = !!t39, window.ANDROID_BOTTOM_NAVIGATION_BAR_HIDDEN = !!t39, i45();
          }, fail: function(i46) {
            a37(i46);
          } }, { timeout: false });
        })];
      });
    })();
  }, _6.setIOSBottomSafeArea = function() {
    var t39 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], o17 = this;
    return t6(function() {
      var i44, n24, e46;
      return e15(this, function(a36) {
        return n24 = t39 ? w.hide : w.show, e46 = null === (i44 = s4()) || void 0 === i44 ? void 0 : i44.platform, [2, new Promise(function(i45, a37) {
          if (!(null == e46 ? void 0 : e46.isIOS)) return i45();
          a8("CHANGE_BOTTOMBAR", { bottombarType: n24 }, { success: function() {
            o17.hideIOSSafeAreaBottom = !!t39, window.IOS_BOTTOM_SAFE_AREA_HIDDEN = !!t39, i45();
          }, fail: function(i46) {
            a37(i46);
          } }, { timeout: false, delay: 200 });
        })];
      });
    })();
  }, _6.configHeader = function(t39) {
    var n24 = this;
    return t6(function() {
      var i44;
      return e15(this, function(s30) {
        return i44 = n24.config, u.isString(t39.headerColor) && (i44.dataConfig.backgroundColor = t39.headerColor), u.isString(t39.headerTextColor) && (i44.dataConfig.textColor = t39.headerTextColor), u.isString(t39.textAlign) && ("left" === t39.textAlign ? i44.dataConfig.textAlign = _2.left : "center" === t39.textAlign && (i44.dataConfig.textAlign = _2.center)), true === t39.hideActionBar ? i44.actionbarType = M : false === t39.hideActionBar && (i44.actionbarType = l), "normal" === t39.statusBarType ? i44.statusbarType = f.normal : "hidden" === t39.statusBarType ? i44.statusbarType = f.hidden : "transparent" === t39.statusBarType && (i44.statusbarType = f.transparent), [2, new Promise(function(e46, l27) {
          a8("CHANGE_ACTIONBAR", t9({}, i44), { success: function() {
            var r33;
            n24.config = t9({}, i44);
            var l28 = null === (r33 = s4()) || void 0 === r33 ? void 0 : r33.platform;
            window.STATUS_BAR_TYPE = S2(n24.config.statusbarType), window.ACTION_BAR_HIDDEN = n24.config.actionbarType === M;
            var s31 = null === document || void 0 === document ? void 0 : document.querySelector(":root");
            if (null == l28 ? void 0 : l28.isAndroid) {
              var d17 = 0;
              if (("hidden" === t39.statusBarType || "transparent" === t39.statusBarType) && t39.hideActionBar) {
                var u48 = window.ANDROID_STATUS_BAR_HEIGHT;
                isNaN(u48) && (u48 = Math.round(ZaloJavaScriptInterface.getStatusBarHeight() / window.devicePixelRatio)), d17 = u48;
              }
              d17 ? null == s31 || s31.style.setProperty("--zaui-safe-area-inset-top", "".concat(d17, "px")) : null == s31 || s31.style.setProperty("--zaui-safe-area-inset-top", "env(safe-area-inset-top, 0px)");
            }
            e46();
          }, fail: function(i45) {
            l27(i45);
          } }, { timeout: false });
        })];
      });
    })();
  }, B3.getInstance = function() {
    return B3.instance || (B3.instance = new B3()), B3.instance;
  }, B3;
}().getInstance();

// ../../node_modules/zmp-sdk/apis/apis/setNavigationBarTitle.js
var m9 = [Me({ title: ke() }).optional()];
function l7(t39) {
  return p6.apply(this, arguments);
}
function p6() {
  return p6 = t6(function(r33) {
    return e15(this, function(e46) {
      return [2, a9("setNavigationBarTitle", m9, [r33], (l27 = t6(function(t39) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, B2.setNavigationBarTitle(t39.title)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("setNavigationBarTitle", {}))];
          }
        });
      }), function(t39) {
        return l27.apply(this, arguments);
      }))];
      var l27;
    });
  }), p6.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/setNavigationBarColor.js
var c8 = [Me({ color: ke(), textColor: ot.enum(["black", "white"]).optional(), statusBarColor: ke().optional() }).optional()];
function m10(o17) {
  return u8.apply(this, arguments);
}
function u8() {
  return u8 = t6(function(r33) {
    return e15(this, function(t39) {
      return [2, a9("setNavigationBarColor", c8, [r33], (e46 = t6(function(o17) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, B2.configHeader({ hideActionBar: !(o17.color.length > 0), headerTextColor: o17.textColor, headerColor: o17.color.length > 0 ? o17.color : o17.statusBarColor })] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("setNavigationBarColor", {}))];
          }
        });
      }), function(o17) {
        return e46.apply(this, arguments);
      }))];
      var e46;
    });
  }), u8.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/setNavigationBarLeftButton.js
var m11 = [Me({ type: ot.enum(["none", "back", "home", "both"]) }).optional()];
function u9(t39) {
  return p7.apply(this, arguments);
}
function p7() {
  return p7 = t6(function(o17) {
    return e15(this, function(e46) {
      return [2, a9("setNavigationBarLeftButton", m11, [o17], (u48 = t6(function(t39) {
        return e15(this, function(o18) {
          switch (o18.label) {
            case 0:
              return o10.isMp ? [4, B2.setNavigationBarLeftButton(t39.type)] : [3, 2];
            case 1:
              return [2, o18.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("setNavigationBarLeftButton", {}))];
          }
        });
      }), function(t39) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), p7.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_extract_field_descriptor.mjs.js
function t18(t39, e46, n24) {
  if (!e46.has(t39)) throw new TypeError("attempted to " + n24 + " private field on non-instance");
  return e46.get(t39);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_apply_descriptor_get.mjs.js
function e19(e46, t39) {
  return t39.get ? t39.get.call(e46) : t39.value;
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_private_field_get.mjs.js
function s7(s30, e46) {
  var o17 = t18(s30, e46, "get");
  return e19(s30, o17);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_check_private_redeclaration.mjs.js
function e20(e46, t39) {
  if (t39.has(e46)) throw new TypeError("Cannot initialize the same private elements twice on an object");
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_private_field_init.mjs.js
function t19(t39, r33, a36) {
  e20(t39, r33), r33.set(t39, a36);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_apply_descriptor_set.mjs.js
function e21(e46, t39, a36) {
  if (t39.set) t39.set.call(e46, a36);
  else {
    if (!t39.writable) throw new TypeError("attempted to set read only private field");
    t39.value = a36;
  }
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_class_private_field_set.mjs.js
function t20(t39, e46, o17) {
  var _6 = t18(t39, e46, "set");
  return e21(t39, _6, o17), o17;
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/storage/storage.js
var r18 = function() {
  function r33() {
    a2(this, r33);
  }
  var t39 = r33.prototype;
  return t39.getSynchronize = function(e46) {
    return /* @__PURE__ */ new Map();
  }, t39.setSynchronize = function(e46) {
    return { errorKeys: [] };
  }, t39.getInfoSynchronize = function() {
    return { currentSize: 0, limitSize: 0 };
  }, t39.removeSynchronize = function(e46) {
    return { errorKeys: [] };
  }, t39.clearSynchronize = function() {
  }, t39.getItem = function(e46) {
    return this.getSynchronize([e46]).get(e46) || null;
  }, t39.setItem = function(e46, r34) {
    if (this.setSynchronize(e9({}, e46, r34)).errorKeys.length > 0) throw new Error("Failed to set item: ".concat(e46));
  }, t39.removeItem = function(e46) {
    this.removeSynchronize([e46]);
  }, t39.clear = function() {
    this.clearSynchronize();
  }, t39.getStorageInfo = function() {
    return this.getInfoSynchronize();
  }, r33;
}();

// ../../node_modules/zmp-sdk/apis/common/apis/general/storage/localStorage.js
var p8 = /* @__PURE__ */ new WeakMap();
var _5 = /* @__PURE__ */ new WeakMap();
var d8 = /* @__PURE__ */ new WeakMap();
var y3 = function(u48) {
  e12(v7, u48);
  var y8 = s3(v7);
  function v7(t39) {
    var s30;
    return a2(this, v7), s30 = y8.call(this), t19(e13(s30), p8, { writable: true, value: localStorage }), t19(e13(s30), _5, { writable: true, value: void 0 }), t19(e13(s30), d8, { writable: true, value: void 0 }), t20(e13(s30), d8, { keys: /* @__PURE__ */ new Set(), currentSize: 0, limitSize: p }), t20(e13(s30), _5, t39), s30.findAllCurrentDataByApp(t39), s30;
  }
  var j6 = v7.prototype;
  return j6.findAllCurrentDataByApp = function(e46) {
    try {
      var t39;
      for (t39 in s7(this, p8)) {
        if (e46) {
          if (t39.split("_")[0] !== e46) continue;
        }
        s7(this, d8).keys.add(t39);
      }
    } catch (e47) {
    }
  }, j6.get = function(e46) {
    var t39 = this, r33 = /* @__PURE__ */ new Map();
    return e46.forEach(function(e47) {
      r33.set(e47, t39.getSync(e47));
    }), Promise.resolve(r33);
  }, j6.getSync = function(e46) {
    return s7(this, _5) && (e46 = "".concat(s7(this, _5), "_").concat(e46)), s7(this, d8).keys.has(e46) ? s7(this, p8).getItem(e46) : null;
  }, j6.set = function(e46) {
    var r33 = this;
    return t6(function() {
      return e15(this, function(t39) {
        return Object.entries(e46).forEach(function(e47) {
          var t40 = e6(e47, 2), s30 = t40[0], n24 = t40[1];
          r33.setSync(s30, n24.replace(/\\"/g, '"'));
        }), [2, Promise.resolve({ errorKeys: [] })];
      });
    })();
  }, j6.setSync = function(e46, t39) {
    s7(this, _5) && (e46 = "".concat(s7(this, _5), "_").concat(e46)), s7(this, p8).setItem(e46, t39), s7(this, d8).keys.add(e46);
  }, j6.getInfo = function() {
    var e46 = this;
    return t6(function() {
      return e15(this, function(t39) {
        return [2, Promise.resolve(e46.getInfoSync())];
      });
    })();
  }, j6.getInfoSync = function() {
    var e46 = v3(s7(this, p8), s7(this, _5));
    return { currentSize: u.isNumber(e46) ? Number((e46 / 1024).toFixed(2)) : 0, limitSize: Number((p / 1024).toFixed(2)) };
  }, j6.remove = function(e46) {
    var r33 = this;
    return t6(function() {
      return e15(this, function(t39) {
        return e46.forEach(function(e47) {
          r33.removeSync(e47);
        }), [2, Promise.resolve({ errorKeys: [] })];
      });
    })();
  }, j6.removeSync = function(e46) {
    s7(this, _5) && (e46 = "".concat(s7(this, _5), "_").concat(e46)), s7(this, d8).keys.has(e46) && (s7(this, p8).removeItem(e46), s7(this, d8).keys.delete(e46));
  }, j6.clearAsync = function() {
    var e46 = this;
    return t6(function() {
      return e15(this, function(t39) {
        return [2, Promise.resolve(e46.clearSync())];
      });
    })();
  }, j6.clearSync = function() {
    var e46 = this;
    s7(this, d8).keys.forEach(function(t39) {
      s7(e46, _5) && t39.indexOf(s7(e46, _5)) < 0 || (s7(e46, p8).removeItem(t39), s7(e46, d8).keys.delete(t39));
    });
  }, j6.setSynchronize = function(e46) {
    var t39 = this;
    return Object.entries(e46).forEach(function(e47) {
      var r33 = e6(e47, 2), s30 = r33[0], n24 = r33[1];
      t39.setSync(s30, n24);
    }), { errorKeys: [] };
  }, j6.getSynchronize = function(e46) {
    var t39 = this, r33 = /* @__PURE__ */ new Map();
    return e46.forEach(function(e47) {
      r33.set(e47, t39.getSync(e47));
    }), r33;
  }, j6.getInfoSynchronize = function() {
    return this.getInfoSync();
  }, j6.clearSynchronize = function() {
    this.clearSync();
  }, v7;
}(r18);

// ../../node_modules/zmp-sdk/apis/common/apis/general/storage/nativeStorage.js
var l8 = /* @__PURE__ */ new WeakMap();
var f6 = /* @__PURE__ */ new WeakMap();
var h6 = function(d17) {
  e12(v7, d17);
  var h11 = s3(v7);
  function v7(e46) {
    var r33;
    return a2(this, v7), r33 = h11.call(this), t19(e13(r33), l8, { writable: true, value: void 0 }), t19(e13(r33), f6, { writable: true, value: void 0 }), t20(e13(r33), f6, { keys: /* @__PURE__ */ new Set(), currentSize: 0, limitSize: p }), t20(e13(r33), l8, e46), r33;
  }
  var y8 = v7.prototype;
  return y8.get = function(s30) {
    var t39 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, a36) {
          a8("ZBROWSER_MPDS", { mpds_action: "mpds.get", mpds_data: { keys: s30 }, appId: s7(t39, l8) }, { success: function(s31) {
            var t40 = /* @__PURE__ */ new Map();
            s31 && s31.mpds_data && s31.mpds_data.map && (t40 = new Map(Object.entries(s31.mpds_data.map))), e47(t40);
          }, fail: function(s31) {
            a36(s31);
          } }, { isMultiCallback: false });
        })];
      });
    })();
  }, y8.getSynchronize = function(s30) {
    var e46 = { mpds_action: "mpds.get", mpds_data: { keys: s30 }, appId: s7(this, l8) }, t39 = /* @__PURE__ */ new Map(), a36 = this.handleResult(e46);
    return a36 && a36.mpds_data && a36.mpds_data.map && (t39 = new Map(Object.entries(a36.mpds_data.map))), t39;
  }, y8.set = function(s30) {
    var t39 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, a36) {
          a8("ZBROWSER_MPDS", { mpds_action: "mpds.set", mpds_data: { map: s30 }, appId: s7(t39, l8) }, { success: function(s31) {
            var t40 = [];
            s31 && s31.mpds_data && s31.mpds_data.error_keys && (t40 = s31.mpds_data.error_keys), e47({ errorKeys: t40 });
          }, fail: function(s31) {
            a36(s31);
          } }, { isMultiCallback: false });
        })];
      });
    })();
  }, y8.setSynchronize = function(s30) {
    var e46 = { mpds_action: "mpds.set", mpds_data: { map: s30 }, appId: s7(this, l8) }, t39 = [], a36 = this.handleResult(e46);
    return a36 && a36.mpds_data && a36.mpds_data.error_keys && (t39 = a36.mpds_data.error_keys), { errorKeys: t39 };
  }, y8.getInfo = function() {
    var s30 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, t39) {
          a8("ZBROWSER_MPDS", { mpds_action: "mpds.get.size", mpds_data: {}, appId: s7(s30, l8) }, { success: function(s31) {
            var t40 = 0;
            s31 && s31.mpds_data && s31.mpds_data.size && (t40 = s31.mpds_data.size), e47({ currentSize: u.isNumber(t40) ? Number(Number(t40) / 1024) : 0, limitSize: Number(p / 1024) });
          }, fail: function(s31) {
            t39(s31);
          } }, { isMultiCallback: false });
        })];
      });
    })();
  }, y8.getInfoSynchronize = function() {
    var s30 = { mpds_action: "mpds.get.size", mpds_data: {}, appId: s7(this, l8) }, e46 = 0, t39 = this.handleResult(s30);
    return t39 && t39.mpds_data && t39.mpds_data.size && (e46 = t39.mpds_data.size), { currentSize: u.isNumber(e46) ? Number(Number(e46) / 1024) : 0, limitSize: Number(p / 1024) };
  }, y8.remove = function(s30) {
    var t39 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, a36) {
          a8("ZBROWSER_MPDS", { mpds_action: "mpds.remove.key", mpds_data: { keys: s30 }, appId: s7(t39, l8) }, { success: function(s31) {
            var t40 = [];
            s31 && s31.mpds_data && s31.mpds_data.error_keys && (t40 = s31.mpds_data.error_keys), e47({ errorKeys: t40 });
          }, fail: function(s31) {
            a36(s31);
          } }, { isMultiCallback: false });
        })];
      });
    })();
  }, y8.removeSynchronize = function(s30) {
    var e46 = { mpds_action: "mpds.remove.key", mpds_data: { keys: s30 }, appId: s7(this, l8) }, t39 = [], a36 = this.handleResult(e46);
    return a36 && a36.mpds_data && a36.mpds_data.error_keys && (t39 = a36.mpds_data.error_keys), { errorKeys: t39 };
  }, y8.clearAsync = function() {
    var s30 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, t39) {
          a8("ZBROWSER_MPDS", { mpds_action: "mpds.clear.appData", mpds_data: {}, appId: s7(s30, l8) }, { success: function() {
            e47();
          }, fail: function(s31) {
            t39(s31);
          } }, { isMultiCallback: false });
        })];
      });
    })();
  }, y8.clearSynchronize = function() {
    var s30 = { mpds_action: "mpds.clear.appData", mpds_data: {}, appId: s7(this, l8) };
    this.handleResult(s30);
  }, y8.handleResult = function(s30) {
    var e46 = null;
    try {
      var t39 = ZaloJavaScriptInterface.processActionMPDS(JSON.stringify(s30));
      if (t39 = JSON.parse(t39)) {
        if (0 === t39.error_code) return t39.data;
        e46 = l5(t39).toJSON();
      }
    } catch (s31) {
      e46 = s31;
    } finally {
      if (null !== e46) throw e46;
    }
    return {};
  }, v7;
}(r18);

// ../../node_modules/zmp-sdk/apis/common/apis/general/storage/index.js
var j4;
var w6;
var I4 = function() {
  function t39() {
    a2(this, t39);
  }
  var n24 = t39.prototype;
  return n24.getDataFromStorage = function(t40) {
    var r33 = this;
    return t6(function() {
      var e46, n25;
      return e15(this, function(s30) {
        switch (s30.label) {
          case 0:
            return [4, r33.getInstance().get(t40)];
          case 1:
            return e46 = s30.sent(), n25 = {}, e46.forEach(function(t41, e47) {
              n25[e47] = m3(t41);
            }), [2, n25];
        }
      });
    })();
  }, n24.getItem = function(t40) {
    return this.getInstance().getItem(t40);
  }, n24.putDataToStorage = function(t40) {
    var r33 = this;
    return t6(function() {
      var e46, n25;
      return e15(this, function(s30) {
        switch (s30.label) {
          case 0:
            return e46 = r33.getInstance(), n25 = {}, Object.entries(t40).forEach(function(t41) {
              var e47 = e6(t41, 2), r34 = e47[0], s31 = e47[1];
              u.isUndefined(s31) || u.isNull(s31) || (n25[r34] = h4(s31));
            }), [4, e46.set(n25)];
          case 1:
            return [2, s30.sent()];
        }
      });
    })();
  }, n24.setItem = function(t40, e46) {
    return this.getInstance().setItem(t40, e46);
  }, n24.removeDataFromStorage = function(t40) {
    var r33 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return [4, r33.getInstance().remove(t40)];
          case 1:
            return [2, e46.sent()];
        }
      });
    })();
  }, n24.removeItem = function(t40) {
    this.getInstance().removeItem(t40);
  }, n24.clearStorage = function() {
    var t40 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return [4, t40.getInstance().clearAsync()];
          case 1:
            return e46.sent(), [2];
        }
      });
    })();
  }, n24.clearStorageSync = function() {
    this.getInstance().clear();
  }, n24.getStorageInfo = function() {
    var t40 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return [4, t40.getInstance().getInfo()];
          case 1:
            return [2, e46.sent()];
        }
      });
    })();
  }, n24.getStorageInfoSync = function() {
    return this.getInstance().getStorageInfo();
  }, t39;
}();
var S4 = /* @__PURE__ */ new WeakMap();
var b4 = function(e46) {
  e12(u48, e46);
  var c41 = s3(u48);
  function u48(e47) {
    var n24;
    return a2(this, u48), n24 = c41.call(this), t19(e13(n24), S4, { writable: true, value: void 0 }), t20(e13(n24), S4, e47), n24;
  }
  return u48.prototype.getInstance = function() {
    return new y3(s7(this, S4));
  }, u48;
}(I4);
var y4 = /* @__PURE__ */ new WeakMap();
var E4 = function(e46) {
  e12(u48, e46);
  var c41 = s3(u48);
  function u48(e47) {
    var n24;
    return a2(this, u48), n24 = c41.call(this), t19(e13(n24), y4, { writable: true, value: void 0 }), t20(e13(n24), y4, e47), n24;
  }
  return u48.prototype.getInstance = function() {
    return new h6(s7(this, y4));
  }, u48;
}(I4);
var k3 = new b4(I);
var D3 = f3();
var M4 = (null === (j4 = s4()) || void 0 === j4 ? void 0 : j4.platformName) || "unknown";
D3 > 0 && D3 >= L[M4] && (null === (w6 = s4()) || void 0 === w6 ? void 0 : w6.appEnv.isMp) && (k3 = new E4(I));
var F3 = k3;

// ../../node_modules/zmp-sdk/apis/apis/setStorage.js
var p9 = [Me({ data: ot.record(ke(), ot.any()) }).optional()];
function u10(t39) {
  return c9.apply(this, arguments);
}
function c9() {
  return c9 = t6(function(r33) {
    return e15(this, function(o17) {
      return [2, a9("setStorage", p9, [r33], (e46 = t6(function(t39) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp || o10.isMpWeb ? [4, F3.putDataToStorage(t39.data)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return [2, Promise.reject(o11("setStorage", {}))];
          }
        });
      }), function(t39) {
        return e46.apply(this, arguments);
      }))];
      var e46;
    });
  }), c9.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getStorage.js
var p10 = [Me({ keys: ot.array(ke()) }).optional()];
function u11(r33) {
  return c10.apply(this, arguments);
}
function c10() {
  return c10 = t6(function(t39) {
    return e15(this, function(o17) {
      return [2, a9("getStorage", p10, [t39], (e46 = t6(function(r33) {
        return e15(this, function(t40) {
          switch (t40.label) {
            case 0:
              return o10.isMp || o10.isMpWeb ? [4, F3.getDataFromStorage(r33.keys)] : [3, 2];
            case 1:
              return [2, t40.sent()];
            case 2:
              return [2, Promise.reject(o11("getStorage", {}))];
          }
        });
      }), function(r33) {
        return e46.apply(this, arguments);
      }))];
      var e46;
    });
  }), c10.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getStorageInfo.js
function i6(r33) {
  return m12.apply(this, arguments);
}
function m12() {
  return (m12 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getStorageInfo", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp || o10.isMpWeb ? [4, F3.getStorageInfo()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return [2, Promise.reject(o11("getStorageInfo", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/removeStorage.js
var p11 = [Me({ keys: ot.array(ke()) }).optional()];
function u12(r33) {
  return c11.apply(this, arguments);
}
function c11() {
  return c11 = t6(function(o17) {
    return e15(this, function(e46) {
      return [2, a9("removeStorage", p11, [o17], (t39 = t6(function(r33) {
        return e15(this, function(o18) {
          switch (o18.label) {
            case 0:
              return o10.isMp || o10.isMpWeb ? [4, F3.removeDataFromStorage(r33.keys)] : [3, 2];
            case 1:
              return [2, o18.sent()];
            case 2:
              return [2, Promise.reject(o11("removeStorage", {}))];
          }
        });
      }), function(r33) {
        return t39.apply(this, arguments);
      }))];
      var t39;
    });
  }), c11.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/clearStorage.js
var m13 = [Me({ prefix: ke().optional() }).optional()];
function c12(r33) {
  return p12.apply(this, arguments);
}
function p12() {
  return (p12 = t6(function(o17) {
    return e15(this, function(e46) {
      return [2, a9("clearStorage", m13, [o17], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp || o10.isMpWeb ? [4, F3.clearStorage()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return [2, Promise.reject(o11("clearStorage", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getUserInfo.js
var a11;
var c13 = (a11 = t6(function(a36, c41) {
  var u48, d17, m51, f14;
  return e15(this, function(v7) {
    switch (v7.label) {
      case 0:
        return (d17 = c41) ? [3, 2] : [4, _3()];
      case 1:
        d17 = !v7.sent(), v7.label = 2;
      case 2:
        return u48 = d17, [4, w2.getAccessToken(u48)];
      case 3:
        return m51 = v7.sent(), f14 = "id,name,user_id_by_oa,is_sensitive", f14 = a36 ? "".concat(f14, ",picture.type(").concat(a36, ")") : "".concat(f14, ",picture"), [4, fetch("".concat(O.GET_USER_INFO, "?fields=").concat(f14, "&miniapp_id=").concat(I), { headers: { access_token: m51 || "" } }).then(function() {
          var n24 = t6(function(e46) {
            var n25, t39, r33, i44, a37, c42;
            return e15(this, function(l27) {
              switch (l27.label) {
                case 0:
                  return [4, e46.json()];
                case 1:
                  if (r33 = l27.sent(), void 0 !== (i44 = null == r33 ? void 0 : r33.error) && i44 < 0) throw a37 = (null == r33 ? void 0 : r33.message) || s.UNKNOWN_ERROR, new n5(i44, a37);
                  return c42 = null == r33 ? void 0 : r33.user_id_by_oa, [2, { id: null == r33 ? void 0 : r33.id, name: (null == r33 ? void 0 : r33.name) || "", avatar: (null == r33 || null === (n25 = r33.picture) || void 0 === n25 || null === (t39 = n25.data) || void 0 === t39 ? void 0 : t39.url) || "", idByOA: c42, followedOA: !!(null == r33 ? void 0 : r33.is_follower), isSensitive: null == r33 ? void 0 : r33.is_sensitive }];
              }
            });
          });
          return function(e46) {
            return n24.apply(this, arguments);
          };
        }())];
      case 4:
        return [2, v7.sent()];
    }
  });
}), function(e46, n24) {
  return a11.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/getUserInfo.js
var m14 = [Me({ avatarType: ot.enum(["small", "normal", "large"]).optional(), autoRequestPermission: ot.boolean().default(false).optional() }).optional()];
function u13(e46) {
  return l9.apply(this, arguments);
}
function l9() {
  return l9 = t6(function(r33) {
    return e15(this, function(o17) {
      return [2, a9("getUserInfo", m14, [r33], (u48 = t6(function(e46) {
        var r34, o18, s30;
        return e15(this, function(i44) {
          switch (i44.label) {
            case 0:
              if (!o10.isMp) return [3, 4];
              r34 = null == e46 ? void 0 : e46.avatarType, o18 = null == e46 ? void 0 : e46.autoRequestPermission, i44.label = 1;
            case 1:
              return i44.trys.push([1, 3, , 4]), [4, c13(r34, o18)];
            case 2:
              return [2, { userInfo: i44.sent() }];
            case 3:
              return s30 = i44.sent(), [2, Promise.reject(s30)];
            case 4:
              return o10.isMpWeb ? [2, Promise.resolve({ userInfo: { id: "3368637342326461234", name: "User Name", avatar: "https://h5.zdn.vn/static/images/avatar.png" } })] : [2, Promise.reject(o11("getUserInfo", {}))];
          }
        });
      }), function(e46) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), l9.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getNetworkType.js
var t21 = function(e46) {
  return { NONE: e46 ? -2 : 0, WIFI: e46 ? 0 : 1, "2G": 2, "3G": e46 ? 15 : 2, "4G": e46 ? 13 : 2 };
};
var i7 = t6(function() {
  return e15(this, function(e46) {
    return [2, new Promise(function(e47, o17) {
      a8("WEBVIEW_NETWORKTYPE", { url: window.location.href, featureId: "" }, { success: function(n24) {
        var o18, i44, a36, u48 = n24.network, c41 = (null === (o18 = s4()) || void 0 === o18 || null === (i44 = o18.platform) || void 0 === i44 ? void 0 : i44.isAndroid) || false;
        switch (u48) {
          case t21(c41).NONE:
            a36 = e7.none;
            break;
          case t21(c41).WIFI:
            a36 = e7.wifi;
            break;
          case t21(c41)["2G"]:
          case t21(c41)["3G"]:
          case t21(c41)["4G"]:
            a36 = e7.cellular;
            break;
          default:
            a36 = e7.unknown;
        }
        e47(a36);
      }, fail: function(e48) {
        o17(e48);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/getNetworkType.js
function a12(e46) {
  return c14.apply(this, arguments);
}
function c14() {
  return (c14 = t6(function(a36) {
    return e15(this, function(c41) {
      return [2, a9("getNetworkType", [], [a36], t6(function() {
        var e46, t39;
        return e15(this, function(i44) {
          switch (i44.label) {
            case 0:
              return o10.isMp ? [4, i7()] : [3, 2];
            case 1:
              return [2, { networkType: i44.sent() }];
            case 2:
              if (o10.isMpWeb) {
                switch (e46 = navigator.connection || navigator.mozConnection || navigator.webkitConnection, e46.effectiveType) {
                  case "none":
                    t39 = e7.none;
                    break;
                  case "4g":
                    t39 = e7.wifi;
                    break;
                  case "3g":
                  case "2g":
                    t39 = e7.cellular;
                    break;
                  default:
                    t39 = e7.unknown;
                }
                return [2, Promise.resolve({ networkType: t39 })];
              }
              return [2, Promise.reject(o11("getNetworkType", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/onNetworkStatusChange.js
function o13(o17) {
  f2.getInstance().on(o5.NetworkChanged, function(t39) {
    var n24 = null == t39 ? void 0 : t39.state;
    i7().then(function(t40) {
      o17({ isConnected: "CONNECTED" === n24, networkType: t40 });
    });
  });
}

// ../../node_modules/zmp-sdk/apis/apis/onNetworkStatusChange.js
var a13 = [ot.function().args(Me({ isConnected: ot.boolean(), networkType: ot.enum(["none", "wifi", "cellular", "unknown"]) })).returns(ot.void())];
function s8(n24) {
  return a9("onNetworkStatusChange", a13, [n24], function(n25) {
    var e46 = n25;
    if (o10.isMp) o13(e46);
    else if (o10.isMpWeb) {
      var t39 = navigator.connection || navigator.mozConnection || navigator.webkitConnection, a36 = t39.effectiveType;
      t39.addEventListener("change", function() {
        var n26;
        switch (a36) {
          case "none":
            n26 = e7.none;
            break;
          case "4g":
            n26 = e7.wifi;
            break;
          case "3g":
          case "2g":
            n26 = e7.cellular;
            break;
          default:
            n26 = e7.unknown;
        }
        e46({ isConnected: true, networkType: n26 });
      });
    }
  });
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/startBeaconDiscovery.js
var t22;
var c15 = (t22 = t6(function(n24) {
  var t39, c41, r33, a36, l27, u48;
  return e15(this, function(s30) {
    return u48 = { subaction: 0, scanning_type: u.isNumber(null == n24 ? void 0 : n24.scanningType) ? n24.scanningType : 0, data: { scan_from_source: u.isString(null == n24 ? void 0 : n24.scanFromSource) ? n24.scanFromSource : "", expire: u.isNumber(null == n24 ? void 0 : n24.expire) ? n24.expire : 3e3, monitor_interval: u.isNumber(null == n24 ? void 0 : n24.monitorInterval) ? n24.monitorInterval : 60, domain: u.isString(null == n24 ? void 0 : n24.domain) ? n24.domain : "", scan_config: { scan_time: u.isNumber(null == n24 || null === (t39 = n24.scanConfig) || void 0 === t39 ? void 0 : t39.scanTime) ? n24.scanConfig.scanTime : 15e3, time_between_scan: u.isNumber(null == n24 || null === (c41 = n24.scanConfig) || void 0 === c41 ? void 0 : c41.timeBetweenScan) ? n24.scanConfig.timeBetweenScan : 1e4, beacon_timeout: u.isNumber(null == n24 || null === (r33 = n24.scanConfig) || void 0 === r33 ? void 0 : r33.beaconTimeout) ? n24.scanConfig.beaconTimeout : 3e4, delayCheckConnectedTimeout: u.isNumber(null == n24 || null === (a36 = n24.scanConfig) || void 0 === a36 ? void 0 : a36.delayCheckConnectedTimeout) ? n24.scanConfig.delayCheckConnectedTimeout : 5e3, beaconConnectedTimeout: u.isNumber(null == n24 || null === (l27 = n24.scanConfig) || void 0 === l27 ? void 0 : l27.beaconConnectedTimeout) ? n24.scanConfig.beaconConnectedTimeout : 6e5 }, items: Array.isArray(null == n24 ? void 0 : n24.items) ? null == n24 ? void 0 : n24.items : [] } }, [2, new Promise(function(n25, i44) {
      a8("SCAN_IBEACON", t9({}, u48), { success: function() {
        n25(true);
      }, fail: function(n26) {
        i44(n26);
      } }, {});
    })];
  });
}), function(n24) {
  return t22.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/startBeaconDiscovery.js
var m15 = [Me({ scanningType: ot.number().refine(function(o17) {
  return [0, 1].includes(o17);
}, { message: "The type must be either 0 or 1" }).default(0).optional(), expire: ot.number().optional(), monitorInterval: ot.number().optional(), domain: ot.string().optional(), scanConfig: Me({ scanTime: ot.number().optional(), timeBetweenScan: ot.number().optional(), beaconTimeout: ot.number().optional(), delayCheckConnectedTimeout: ot.number().optional(), beaconConnectedTimeout: ot.number().optional() }).optional(), items: ot.array(Me({ id: ot.string().optional(), distance: ot.number().optional(), monitor: Me({ enable: ot.boolean().optional(), movingRange: ot.number().optional() }).optional() })).optional(), scanFromSource: ot.string().optional() }).optional()];
function p13(o17) {
  return c16.apply(this, arguments);
}
function c16() {
  return (c16 = t6(function(n24) {
    return e15(this, function(e46) {
      return [2, a9("startBeaconDiscovery", m15, [n24], t6(function() {
        return e15(this, function(o17) {
          switch (o17.label) {
            case 0:
              return o10.isMp ? [4, c15(n24)] : [3, 2];
            case 1:
              return [2, o17.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve(true)] : [2, Promise.reject(o11("startBeaconDiscovery", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/stopBeaconDiscovery.js
var t23 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("SCAN_IBEACON", { subaction: 2 }, { success: function() {
        o18(true);
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/stopBeaconDiscovery.js
function i8(o17) {
  return c17.apply(this, arguments);
}
function c17() {
  return (c17 = t6(function(i44) {
    return e15(this, function(c41) {
      return [2, a9("stopBeaconDiscovery", [], [i44], t6(function() {
        return e15(this, function(o17) {
          switch (o17.label) {
            case 0:
              return o10.isMp ? [4, t23()] : [3, 2];
            case 1:
              return [2, o17.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve(true)] : [2, Promise.reject(o11("stopBeaconDiscovery", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getBeacons.js
var i9 = t6(function() {
  return e15(this, function(s30) {
    return [2, new Promise(function(s31, o17) {
      a8("SCAN_IBEACON", { subaction: 1 }, { success: function(r33) {
        var o18 = { beacons: [] };
        Array.isArray(r33) && r33.forEach(function(s32) {
          var r34 = { uuid: s32.uuid, major: s32.major, minor: s32.minor, distance: s32.distance, rssi: s32.rssi };
          o18.beacons.push(r34);
        }), s31(o18);
      }, fail: function(s32) {
        o17(s32);
      } }, { isMultiCallback: true });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/getBeacons.js
function i10(e46) {
  return c18.apply(this, arguments);
}
function c18() {
  return (c18 = t6(function(i44) {
    return e15(this, function(c41) {
      return [2, a9("getBeacons", [], [i44], t6(function() {
        return e15(this, function(e46) {
          switch (e46.label) {
            case 0:
              return o10.isMp ? [4, i9()] : [3, 2];
            case 1:
              return [2, e46.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ beacons: [] })] : [2, Promise.reject(o11("getBeacons", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/closeApp.js
var e22 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("WINDOW_CLOSE", {}, { success: function(s30) {
        o18(s30);
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/closeApp.js
function i11(r33) {
  return p14.apply(this, arguments);
}
function p14() {
  return (p14 = t6(function(i44) {
    return e15(this, function(p49) {
      return [2, a9("closeApp", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, e22()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("closeApp", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/scanQRCode.js
var e23 = t6(function() {
  return e15(this, function(n24) {
    return [2, new Promise(function(n25, o17) {
      a8("OPEN_QR", { skipOpenLink: true }, { success: function(s30) {
        n25(s30);
      }, fail: function(n26) {
        o17(n26);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/scanQRCode.js
function i12(o17) {
  return c19.apply(this, arguments);
}
function c19() {
  return (c19 = t6(function(i44) {
    return e15(this, function(c41) {
      return [2, a9("scanQRCode", [], [i44], t6(function() {
        return e15(this, function(o17) {
          switch (o17.label) {
            case 0:
              return o10.isMp ? [4, e23()] : [3, 2];
            case 1:
              return [2, o17.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ content: "" })] : [2, Promise.reject(o11("scanQRCode", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openProfile.js
var e24;
var i13 = (e24 = t6(function(o17, e46, i44, u48) {
  return e15(this, function(n24) {
    return [2, new Promise(function(n25, c41) {
      a8("OPEN_PROFILE", { uId: o17, type: i2[e46], zapp: a3, sourceId: i44, sourceIndex: u48 }, { success: function() {
        n25();
      }, fail: function(o18) {
        c41(o18);
      } }, {});
    })];
  });
}), function(o17, s30, t39, n24) {
  return e24.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openProfile.js
var a14 = [Me({ id: ke(), type: ot.enum(["user", "oa", "aliasOA"]), sourceId: ot.any().optional(), sourceIndex: ot.any().optional() }).optional()];
function m16(o17) {
  return c20.apply(this, arguments);
}
function c20() {
  return c20 = t6(function(r33) {
    return e15(this, function(e46) {
      return [2, a9("openProfile", a14, [r33], (n24 = t6(function(o17) {
        var r34, e47, n25, i44;
        return e15(this, function(u48) {
          switch (u48.label) {
            case 0:
              return o10.isMp ? (r34 = o17.id, e47 = o17.type, n25 = o17.sourceId, i44 = o17.sourceIndex, [4, i13(r34, e47, n25, i44)]) : [3, 2];
            case 1:
              return [2, u48.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openProfile", {}))];
          }
        });
      }), function(o17) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), c20.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openChat.js
var r19;
var i14 = (r19 = t6(function(o17, r33) {
  var i44, u48, m51, c41 = arguments;
  return e15(this, function(s30) {
    return i44 = c41.length > 2 && void 0 !== c41[2] ? c41[2] : "", u48 = c41.length > 3 ? c41[3] : void 0, m51 = c41.length > 4 ? c41[4] : void 0, [2, new Promise(function(s31, c42) {
      a8("OPEN_CHAT", { uId: o17, type: t8[r33], msg: i44, zapp: a3, sourceId: u48, sourceIndex: m51 }, { success: function() {
        s31();
      }, fail: function(o18) {
        c42(o18);
      } }, {});
    })];
  });
}), function(o17, t39) {
  return r19.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openChat.js
var m17 = [Me({ id: ke(), type: ot.enum(["user", "oa"]), sourceId: ot.any().optional(), sourceIndex: ot.any().optional(), message: ke().optional() }).optional()];
function u14(o17) {
  return c21.apply(this, arguments);
}
function c21() {
  return c21 = t6(function(e46) {
    return e15(this, function(r33) {
      return [2, a9("openChat", m17, [e46], (n24 = t6(function(o17) {
        var e47, r34, n25, i44, m51;
        return e15(this, function(p49) {
          switch (p49.label) {
            case 0:
              return o10.isMp ? (e47 = o17.id, r34 = o17.type, n25 = o17.message, i44 = o17.sourceId, m51 = o17.sourceIndex, [4, i14(e47, r34, n25, i44, m51)]) : [3, 2];
            case 1:
              return [2, p49.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openChat", {}))];
          }
        });
      }), function(o17) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), c21.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/external/@swc/helpers/src/_object_spread_props.mjs.js
function e25(e46, t39) {
  return t39 = null != t39 ? t39 : {}, Object.getOwnPropertyDescriptors ? Object.defineProperties(e46, Object.getOwnPropertyDescriptors(t39)) : function(e47, t40) {
    var r33 = Object.keys(e47);
    if (Object.getOwnPropertySymbols) {
      var n24 = Object.getOwnPropertySymbols(e47);
      t40 && (n24 = n24.filter(function(t41) {
        return Object.getOwnPropertyDescriptor(e47, t41).enumerable;
      })), r33.push.apply(r33, n24);
    }
    return r33;
  }(Object(t39)).forEach(function(r33) {
    Object.defineProperty(e46, r33, Object.getOwnPropertyDescriptor(t39, r33));
  }), e46;
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openPostFeed.js
var n15;
var u15 = (n15 = t6(function(o17, n24) {
  var u48, a36;
  return e15(this, function(r33) {
    switch (u48 = {}, a36 = o17, o17) {
      case "image":
        (null == n24 ? void 0 : n24.imageUrls) ? (a36 = "multi_image", u48 = { photoUrl: null == n24 ? void 0 : n24.imageUrls }) : u48 = { photoUrl: null == n24 ? void 0 : n24.imageUrl };
        break;
      case "link":
        u48 = { link: null == n24 ? void 0 : n24.link, title: null == n24 ? void 0 : n24.title, thumb: null == n24 ? void 0 : n24.thumb, description: null == n24 ? void 0 : n24.description };
        break;
      case "profile":
        u48 = { avt: null == n24 ? void 0 : n24.avatar, uid: null == n24 ? void 0 : n24.id, dpn: null == n24 ? void 0 : n24.displayName };
    }
    return [2, new Promise(function(o18, r34) {
      a8("OPEN_POSTFEED", e25(t9({ type: c2[a36] }, u48), { zapp: a3 }), { success: function(s30) {
        o18({ status: null == s30 ? void 0 : s30.status, shareType: null == s30 ? void 0 : s30.share_type, numberOfUser: null == s30 ? void 0 : s30.uid_to_size });
      }, fail: function(o19) {
        r34(o19);
      } }, {});
    })];
  });
}), function(o17, s30) {
  return n15.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openPostFeed.js
var m18 = [ot.union([Me({ type: ot.enum(["image"]), data: Me({ imageUrl: ke().url().optional(), imageUrls: ot.array(ke().url()).optional() }) }), Me({ type: ot.enum(["link"]), data: Me({ link: ke().url(), title: ke().max(100).optional(), thumb: ke().url().optional(), description: ke().max(400).default("").optional() }) }), Me({ type: ot.enum(["profile"]), data: Me({ id: ke().optional() }) })])];
function u16(e46) {
  return l10.apply(this, arguments);
}
function l10() {
  return l10 = t6(function(t39) {
    return e15(this, function(o17) {
      return [2, a9("openPostFeed", m18, [t39], (r33 = t6(function(e46) {
        var t40, o18;
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? (t40 = e46.type, o18 = e46.data, [4, u15(t40, o18)]) : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ status: 1, shareType: 0, numberOfUser: 0 })] : [2, Promise.reject(o11("openPostFeed", {}))];
          }
        });
      }), function(e46) {
        return r33.apply(this, arguments);
      }))];
      var r33;
    });
  }), l10.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/followOA.js
var r20;
var e26 = (r20 = t6(function(o17) {
  return e15(this, function(s30) {
    return [2, new Promise(function(s31, r33) {
      a8("FOLLOW_OA", { uid: o17, showDialogConfirm: true, forceUpdate: true, zapp: a3 }, { success: function(o18) {
        s31(o18);
      }, fail: function(o18) {
        r33(o18);
      } }, { timeout: false });
    })];
  });
}), function(o17) {
  return r20.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/followOA.js
var a15 = [Me({ id: ke(), showDialogConfirm: ot.boolean().optional() }).optional()];
function p15(o17) {
  return u17.apply(this, arguments);
}
function u17() {
  return u17 = t6(function(r33) {
    return e15(this, function(t39) {
      return [2, a9("followOA", a15, [r33], (n24 = t6(function(o17) {
        var r34;
        return e15(this, function(t40) {
          switch (t40.label) {
            case 0:
              return o10.isMp ? (r34 = o17.id, [4, e26(r34)]) : [3, 2];
            case 1:
              return [2, t40.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("followOA", {}))];
          }
        });
      }), function(o17) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), u17.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/unfollowOA.js
var r21;
var e27 = (r21 = t6(function(o17) {
  return e15(this, function(s30) {
    return [2, new Promise(function(s31, r33) {
      a8("UNFOLLOW_OA", { uid: o17, forceUpdate: true, zapp: a3 }, { success: function() {
        s31();
      }, fail: function(o18) {
        r33(o18);
      } }, {});
    })];
  });
}), function(o17) {
  return r21.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/unfollowOA.js
var u18 = [Me({ id: ke() }).optional()];
function l11(o17) {
  return p16.apply(this, arguments);
}
function p16() {
  return p16 = t6(function(r33) {
    return e15(this, function(n24) {
      return [2, a9("unfollowOA", u18, [r33], (l27 = t6(function(o17) {
        var r34;
        return e15(this, function(n25) {
          switch (n25.label) {
            case 0:
              return o10.isMp ? (r34 = o17.id, [4, e27(r34)]) : [3, 2];
            case 1:
              return [2, n25.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("unfollowOA", {}))];
          }
        });
      }), function(o17) {
        return l27.apply(this, arguments);
      }))];
      var l27;
    });
  }), p16.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getZMPShareInfo.js
var d9;
var i15 = (d9 = t6(function(e46, d17, i44, p49, h11, l27) {
  var m51, u48, w7, _6, f14, P3, j6, v7;
  return e15(this, function(c41) {
    switch (c41.label) {
      case 0:
        return w7 = new URL(location.href), _6 = new URLSearchParams(), f14 = "".concat(w7.pathname.replace(i, "")).concat(w7.search), l27 && (f14 = "/".concat(e46).concat("/" === l27[0] ? "" : "/").concat(l27)), !(P3 = new URL("".concat(A).concat(f14))).searchParams.has("env") && (null === (m51 = window.APP_ENV) || void 0 === m51 ? void 0 : m51.length) > 0 && P3.searchParams.set("env", window.APP_ENV), !P3.searchParams.has("version") && (null === (u48 = window.APP_VERSION) || void 0 === u48 ? void 0 : u48.length) > 0 && P3.searchParams.set("version", window.APP_VERSION), _6.append("title", i44), _6.append("description", p49 || ""), _6.append("thumbnail", h11 || ""), _6.append("url", P3.href), _6.append("accessToken", d17 || ""), j6 = { headers: { "Content-Type": "application/x-www-form-urlencoded" } }, [4, fetch(O.CREATE_SHARE_LINK, t9({ method: "POST", body: _6 }, j6))];
      case 1:
        return [4, c41.sent().json()];
      case 2:
        if (!(v7 = c41.sent()) || 0 !== v7.err) throw d6.error.cannotGetShareInfo();
        return P3.search = "".concat(P3.search, "&zshareId=").concat(v7.data.shareId), [2, e25(t9({}, v7.data), { shareableLink: P3.href })];
    }
  });
}), function(e46, r33, s30, a36, n24, o17) {
  return d9.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/common/apis/general/openShareSheet.js
var p17;
var h7 = (p17 = t6(function(e46, p49) {
  var h11, v7, f14, g8, _6, j6, U3, w7, b5, I6, k4;
  return e15(this, function(d17) {
    switch (d17.label) {
      case 0:
        switch (h11 = {}, v7 = e46, e46) {
          case "text":
            return [3, 1];
          case "image":
            return [3, 2];
          case "gif":
            return [3, 3];
          case "video":
            return [3, 4];
          case "link":
            return [3, 5];
          case "oa":
            return [3, 6];
          case "zmp":
            return [3, 7];
          case "zmp_deep_link":
            return [3, 10];
        }
        return [3, 13];
      case 1:
        return h11 = { text: null == p49 ? void 0 : p49.text, desc: (null == p49 ? void 0 : p49.desc) || (null == p49 ? void 0 : p49.description), autoParseLink: (null == p49 ? void 0 : p49.autoParseLink) ? 1 : 0 }, [3, 14];
      case 2:
        return _6 = [], j6 = f3(), U3 = (null === (g8 = s4()) || void 0 === g8 ? void 0 : g8.platformName) || "unknown", Number(j6) >= C[U3] && (v7 = "multi_image"), Array.isArray(null == p49 ? void 0 : p49.imageUrls) && (null == p49 ? void 0 : p49.imageUrls.length) > 0 ? _6 = null == p49 ? void 0 : p49.imageUrls : _6.push(null == p49 ? void 0 : p49.imageUrl), h11 = { photoUrls: _6, photoUrl: null == p49 ? void 0 : p49.imageUrl, caption: null == p49 ? void 0 : p49.caption }, [3, 14];
      case 3:
        return h11 = { photoUrl: null == p49 ? void 0 : p49.imageUrl, gifUrl: null == p49 ? void 0 : p49.gifUrl, width: null == p49 ? void 0 : p49.width, height: null == p49 ? void 0 : p49.height }, [3, 14];
      case 4:
        return h11 = { videoThumb: null == p49 ? void 0 : p49.videoThumb, videoUrl: null == p49 ? void 0 : p49.videoUrl, width: null == p49 ? void 0 : p49.width, height: null == p49 ? void 0 : p49.height }, [3, 14];
      case 5:
        return h11 = { link: null == p49 ? void 0 : p49.link, chatOnly: (null == p49 ? void 0 : p49.chatOnly) ? 1 : 0 }, [3, 14];
      case 6:
        return h11 = { uid: null == p49 ? void 0 : p49.id }, [3, 14];
      case 7:
      case 10:
        return [4, w2.getAccessToken()];
      case 8:
      case 11:
        return f14 = d17.sent(), [4, i15(I, f14 || "", (null == p49 ? void 0 : p49.title) || "", (null == p49 ? void 0 : p49.description) || "", (null == p49 ? void 0 : p49.thumbnail) || "", null == p49 ? void 0 : p49.path)];
      case 9:
        return w7 = d17.sent(), h11 = { data: { ZInstantAPIInfo: { url: "".concat(w7.zinstantApi), zinstantdata_id: "templateId=".concat(w7.ztemplateId, "&appId=").concat(I, "&shareId=").concat(w7.shareId) }, previewMsgText: { msg: { en: "Zalo Mini Program", vi: "Chương trình nhỏ" } }, enableReactions: 1 } }, [3, 14];
      case 12:
        return b5 = d17.sent().shareableLink, I6 = new URL(b5), k4 = I6.searchParams.getAll("zshareId"), I6.searchParams.delete("zshareId"), k4.length > 0 && I6.searchParams.append("zshareId", k4[0]), h11 = { link: I6.href, chatOnly: 0 }, [3, 14];
      case 13:
        return [3, 14];
      case 14:
        return [2, new Promise(function(e47, i44) {
          a8("OPEN_SHARESHEET", e25(t9({ type: u3[v7] }, h11), { zapp: a3 }), { success: function(r33) {
            e47({ status: null == r33 ? void 0 : r33.status, shareType: null == r33 ? void 0 : r33.share_type, numberOfUser: null == r33 ? void 0 : r33.uid_to_size });
          }, fail: function(e48) {
            i44(e48);
          } }, { timeout: false });
        })];
    }
  });
}), function(e46, r33) {
  return p17.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openShareSheet.js
var s9 = [ot.union([Me({ type: ot.enum(["text"]), data: Me({ text: ke(), description: ke().default("").optional(), autoParseLink: ot.boolean().default(false).optional() }) }), Me({ type: ot.enum(["image"]), data: Me({ imageUrl: ke().url().optional(), imageUrls: ot.array(ke().url()).max(10).optional() }) }), Me({ type: ot.enum(["link"]), data: Me({ link: ke().url(), chatOnly: ot.boolean().optional() }) }), Me({ type: ot.enum(["oa"]), data: Me({ id: ke() }) }), Me({ type: ot.enum(["gif"]), data: Me({ gifUrl: ke().url(), imageUrl: ke().url().optional(), width: we().min(0).optional(), height: we().min(0).optional() }) }), Me({ type: ot.enum(["video"]), data: Me({ videoThumb: ke().url(), videoUrl: ke().url(), width: we().min(0).optional(), height: we().min(0).optional() }) }), Me({ type: ot.enum(["zmp", "zmp_deep_link"]), data: Me({ title: ke().max(100), thumbnail: ke().url(), path: ke().optional(), description: ke().max(400).default("").optional() }) })])];
function u19(e46) {
  return d10.apply(this, arguments);
}
function d10() {
  return d10 = t6(function(t39) {
    return e15(this, function(o17) {
      return [2, a9("openShareSheet", s9, [t39], (n24 = t6(function(e46) {
        var t40, o18;
        return e15(this, function(n25) {
          switch (n25.label) {
            case 0:
              return o10.isMp ? (t40 = e46.type, o18 = e46.data, [4, h7(t40, o18)]) : [3, 2];
            case 1:
              return [2, n25.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ status: 2, shareType: 0, numberOfUser: 0 })] : [2, Promise.reject(o11("openShareSheet", {}))];
          }
        });
      }), function(e46) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), d10.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/requestCameraPermission.js
var s10 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, r33) {
      a8("REQUEST_PERMISSION_CAMERA", {}, { success: function(e46) {
        var r34, s30;
        (null === (r34 = s4()) || void 0 === r34 || null === (s30 = r34.platform) || void 0 === s30 ? void 0 : s30.isAndroid) ? o18({ userAllow: void 0 === e46.error_code || (null == e46 ? void 0 : e46.error_code) >= 0, message: void 0 === e46.error_code ? "User allowed" : null == e46 ? void 0 : e46.error_message }) : o18({ userAllow: (null == e46 ? void 0 : e46.error_code) >= 0, message: null == e46 ? void 0 : e46.error_message });
      }, fail: function(e46) {
        var s30, i44;
        (null === (s30 = s4()) || void 0 === s30 || null === (i44 = s30.platform) || void 0 === i44 ? void 0 : i44.isAndroid) && "number" == typeof (null == e46 ? void 0 : e46.code) ? o18({ userAllow: (null == e46 ? void 0 : e46.code) >= 0, message: null == e46 ? void 0 : e46.message }) : r33(e46);
      } }, { isMultiCallback: true });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/requestCameraPermission.js
function n16(r33) {
  return o14.apply(this, arguments);
}
function o14() {
  return (o14 = t6(function(n24) {
    return e15(this, function(o17) {
      return [2, a9("requestCameraPermission", [], [n24], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return [4, s10()];
            case 1:
              return [2, r33.sent()];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/createAndroidShortcut.js
var r22;
var s11 = (r22 = t6(function(n24, r33, s30) {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, e46) {
      a8("CREATE_SHORTCUT", { url: n24, title: r33, iconUrl: s30 }, { success: function(n25) {
        o18(n25);
      }, fail: function(n25) {
        e46(n25);
      } }, {});
    })];
  });
}), function(n24, t39, o17) {
  return r22.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/common/apis/general/getAppInfo.js
var n17 = t6(function() {
  var s30, n24, i44, m51 = arguments;
  return e15(this, function(e46) {
    switch (e46.label) {
      case 0:
        return s30 = m51.length > 0 && void 0 !== m51[0] ? m51[0] : I, n24 = m51.length > 1 && void 0 !== m51[1] ? m51[1] : 0, i44 = r15(), [4, d4(s30, n24, i44.version)];
      case 1:
        return [2, e46.sent()];
    }
  });
});

// ../../node_modules/zmp-sdk/apis/common/apis/general/openOutApp.js
var s12;
var t24 = (s12 = t6(function(n24) {
  return e15(this, function(r33) {
    return [2, new Promise(function(r34, s30) {
      a8("OPEN_OUTAPP", { url: n24 }, { success: function() {
        r34();
      }, fail: function(n25) {
        s30(n25);
      } }, {});
    })];
  });
}), function(n24) {
  return s12.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/createShortcut.js
var h8 = [Me({ params: ot.record(ke(), ke()).optional() }).optional()];
function g4(o17) {
  return v5.apply(this, arguments);
}
function v5() {
  return v5 = t6(function(t39) {
    return e15(this, function(r33) {
      return [2, a9("createShortcut", h8, [t39], (e46 = t6(function(o17) {
        var t40, r34, e47, m51, h11, g8, v7, _6;
        return e15(this, function(f14) {
          switch (f14.label) {
            case 0:
              return o10.isMp ? [4, n17(I, 0)] : [3, 6];
            case 1:
              return e47 = f14.sent(), m51 = null === (t40 = s4()) || void 0 === t40 || null === (r34 = t40.platform) || void 0 === r34 ? void 0 : r34.isIOS, h11 = e47.logoUrl || g2("og:image:url") || "", g8 = e47.name || document.title, (v7 = (null == o17 ? void 0 : o17.params) ? new URLSearchParams(null == o17 ? void 0 : o17.params) : new URLSearchParams()).append("utm_source", "zalo-shortcut"), v7.append("utm_medium", "default"), v7.append("utm_campaign", "default"), m51 ? (v7.append("appId", I), v7.append("appName", g8), v7.append("appIcon", h11), _6 = "https://mini.zalo.me/shortcut.html?".concat(v7.toString()), [4, t24(encodeURI(_6))]) : [3, 3];
            case 2:
              return f14.sent(), [3, 5];
            case 3:
              return [4, s11("".concat(A, "/").concat(I, "/?").concat(v7.toString()), g8, h11)];
            case 4:
              f14.sent(), f14.label = 5;
            case 5:
              return [2, d6.success()];
            case 6:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("createShortcut", {}))];
          }
        });
      }), function(o17) {
        return e46.apply(this, arguments);
      }))];
      var e46;
    });
  }), v5.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openBioAuthentication.js
var i16;
var l12 = (i16 = t6(function(e46, i44, l27) {
  return e15(this, function(n24) {
    return [2, new Promise(function(n25, r33) {
      a8("PROMPT_AUTHENTICATION", { secret_data: e46, app_id: a3, ui: { title: null == i44 ? void 0 : i44.title, sub_title: null == i44 ? void 0 : i44.subTitle, negative_text: null == i44 ? void 0 : i44.negativeButtonText }, require_fingerprint: l27 ? 1 : 0 }, { success: function(e47) {
        var o17, t39, i45, l28;
        n25({ code: null == e47 ? void 0 : e47.error_code, message: null == e47 ? void 0 : e47.error_message, data: { domain: null == e47 || null === (o17 = e47.data) || void 0 === o17 ? void 0 : o17.domain, code: null == e47 || null === (t39 = e47.data) || void 0 === t39 ? void 0 : t39.error_code, message: null == e47 || null === (i45 = e47.data) || void 0 === i45 ? void 0 : i45.error_message, payToken: null == e47 || null === (l28 = e47.data) || void 0 === l28 ? void 0 : l28.pay_token } });
      }, fail: function(e47) {
        r33(e47);
      } }, {});
    })];
  });
}), function(e46, o17, t39) {
  return i16.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openBioAuthentication.js
var c22 = [Me({ secretData: ke(), ui: Me({ title: ke().default("Xác thực").optional(), subTitle: ke().default("Sử dụng sinh trắc học của bạn để xác thực").optional(), negativeButtonText: ke().default("Đóng").optional() }).optional(), requireFingerprint: Ne().default(true).optional() }).optional()];
function p18(t39) {
  return l13.apply(this, arguments);
}
function l13() {
  return l13 = t6(function(e46) {
    return e15(this, function(o17) {
      return [2, a9("openBioAuthentication", c22, [e46], (n24 = t6(function(t39) {
        var e47, o18, n25;
        return e15(this, function(s30) {
          switch (s30.label) {
            case 0:
              return o10.isMp ? (e47 = t39.secretData, o18 = t39.ui, n25 = t39.requireFingerprint, [4, l12(e47, o18, n25)]) : [3, 2];
            case 1:
              return [2, s30.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ code: 0, message: "OK", data: { domain: "", code: 0, message: "", payToken: "" } })] : [2, Promise.reject(o11("openBioAuthentication", {}))];
          }
        });
      }), function(t39) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), l13.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/checkStateBioAuthentication.js
var e28 = t6(function() {
  return e15(this, function(t39) {
    return [2, new Promise(function(t40, n24) {
      a8("PROMPT_AUTHENTICATION_CHECK_STATE", {}, { success: function(o17) {
        t40(null == o17 ? void 0 : o17.bio_state);
      }, fail: function(t41) {
        n24(t41);
      } }, { actionName: "action.prompt.authentication.check_state" });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/checkStateBioAuthentication.js
function s13(t39) {
  return c23.apply(this, arguments);
}
function c23() {
  return (c23 = t6(function(s30) {
    return e15(this, function(c41) {
      return [2, a9("checkStateBioAuthentication", [], [s30], t6(function() {
        return e15(this, function(t39) {
          switch (t39.label) {
            case 0:
              return o10.isMp ? [4, e28()] : [3, 2];
            case 1:
              return [2, { bioState: t39.sent() }];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ bioState: "" })] : [2, Promise.reject(o11("checkStateBioAuthentication", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/showToast.js
var s14;
var r23 = (s14 = t6(function(t39) {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, s30) {
      a8("SHOW_TOAST", { toast: t39 }, { success: function() {
        o18();
      }, fail: function(t40) {
        s30(t40);
      } }, {});
    })];
  });
}), function(t39) {
  return s14.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/showToast.js
var a16 = [Me({ message: ke() }).optional()];
function p19(o17) {
  return u20.apply(this, arguments);
}
function u20() {
  return u20 = t6(function(s30) {
    return e15(this, function(r33) {
      return [2, a9("showToast", a16, [s30], (p49 = t6(function(o17) {
        return e15(this, function(s31) {
          switch (s31.label) {
            case 0:
              return o10.isMp ? [4, r23(o17.message)] : [3, 2];
            case 1:
              return [2, s31.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("showToast", {}))];
          }
        });
      }), function(o17) {
        return p49.apply(this, arguments);
      }))];
      var p49;
    });
  }), u20.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/hideKeyboard.js
var e29 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("HIDE_KEYBOARD", {}, { success: function() {
        o18();
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/hideKeyboard.js
function i17(r33) {
  return m19.apply(this, arguments);
}
function m19() {
  return (m19 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("hideKeyboard", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, e29()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("hideKeyboard", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openPhone.js
var s15;
var t25 = (s15 = t6(function(n24) {
  return e15(this, function(e46) {
    return [2, new Promise(function(e47, s30) {
      a8("OPEN_PHONE", { phoneNum: n24, phoneCode: n24 }, { success: function() {
        e47();
      }, fail: function(n25) {
        s30(n25);
      } }, {});
    })];
  });
}), function(n24) {
  return s15.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openPhone.js
var p20 = [Me({ phoneNumber: ke() })];
function u21(o17) {
  return c24.apply(this, arguments);
}
function c24() {
  return c24 = t6(function(e46) {
    return e15(this, function(r33) {
      return [2, a9("openPhone", p20, [e46], (u48 = t6(function(o17) {
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? [4, t25(o17.phoneNumber)] : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openPhone", {}))];
          }
        });
      }), function(o17) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), c24.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openSMS.js
var e30;
var s16 = (e30 = t6(function(n24, e46) {
  return e15(this, function(t39) {
    return [2, new Promise(function(t40, s30) {
      a8("OPEN_SMS", { content: e46, phoneCode: n24 }, { success: function() {
        t40();
      }, fail: function(n25) {
        s30(n25);
      } }, {});
    })];
  });
}), function(n24, o17) {
  return e30.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openSMS.js
var p21 = [Me({ content: ke(), phoneNumber: ke() })];
function u22(o17) {
  return c25.apply(this, arguments);
}
function c25() {
  return c25 = t6(function(r33) {
    return e15(this, function(e46) {
      return [2, a9("openSMS", p21, [r33], (u48 = t6(function(o17) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, s16(o17.phoneNumber, o17.content)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openSMS", {}))];
          }
        });
      }), function(o17) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), c25.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/viewOAQr.js
var r24;
var e31 = (r24 = t6(function(t39) {
  var r33, e46 = arguments;
  return e15(this, function(s30) {
    return r33 = e46.length > 1 && void 0 !== e46[1] ? e46[1] : "", [2, new Promise(function(s31, e47) {
      a8("VIEW_MYQR", { dpn: r33, uid: t39, zapp: a3 }, { success: function() {
        s31();
      }, fail: function(t40) {
        e47(t40);
      } }, { timeout: false });
    })];
  });
}), function(t39) {
  return r24.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/viewOAQr.js
var a17 = [Me({ id: ke(), displayName: ke().default("").optional() })];
function p22(r33) {
  return u23.apply(this, arguments);
}
function u23() {
  return u23 = t6(function(e46) {
    return e15(this, function(o17) {
      return [2, a9("viewOAQr", a17, [e46], (p49 = t6(function(r33) {
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? [4, e31(r33.id, r33.displayName)] : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("viewOAQr", {}))];
          }
        });
      }), function(r33) {
        return p49.apply(this, arguments);
      }))];
      var p49;
    });
  }), u23.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/keepScreen.js
var s17;
var t26 = (s17 = t6(function(n24) {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, s30) {
      a8("KEEP_SCREEN", { data: { keep_screen_on: n24 ? 1 : 0 } }, { success: function() {
        o18();
      }, fail: function(n25) {
        s30(n25);
      } }, {});
    })];
  });
}), function(n24) {
  return s17.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/keepScreen.js
var p23 = [Me({ keepScreenOn: Ne() })];
function c26(e46) {
  return u24.apply(this, arguments);
}
function u24() {
  return u24 = t6(function(r33) {
    return e15(this, function(n24) {
      return [2, a9("keepScreen", p23, [r33], (c41 = t6(function(e46) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, t26(e46.keepScreenOn)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("keepScreen", {}))];
          }
        });
      }), function(e46) {
        return c41.apply(this, arguments);
      }))];
      var c41;
    });
  }), u24.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/onKeepScreen.js
function i18(e46) {
  return m20.apply(this, arguments);
}
function m20() {
  return (m20 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("onKeepScreen", [], [i44], t6(function() {
        return e15(this, function(e46) {
          switch (e46.label) {
            case 0:
              return o10.isMp ? [4, t26(true)] : [3, 2];
            case 1:
              return [2, e46.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("onKeepScreen", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/offKeepScreen.js
function i19(e46) {
  return m21.apply(this, arguments);
}
function m21() {
  return (m21 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("offKeepScreen", [], [i44], t6(function() {
        return e15(this, function(e46) {
          switch (e46.label) {
            case 0:
              return o10.isMp ? [4, t26(false)] : [3, 2];
            case 1:
              return [2, e46.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("offKeepScreen", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/saveImageToGallery.js
var n18;
var i20 = (n18 = t6(function(s30, n24, i44) {
  var m51;
  return e15(this, function(t39) {
    return m51 = {}, s30 ? m51 = { imageBase64Data: s30 } : n24 && (m51 = { imageUrl: n24 }), [2, new Promise(function(s31, t40) {
      a8("SAVE_IMAGE_GALLERY", e25(t9({}, m51), { onProgress: i44 }), { success: function() {
        s31();
      }, fail: function(s32) {
        t40(s32);
      } }, {});
    })];
  });
}), function(s30, e46, r33) {
  return n18.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/saveImageToGallery.js
var m22 = [Me({ imageBase64Data: ke().optional(), imageUrl: ke().url().optional() }).optional()];
function l14(e46) {
  return p24.apply(this, arguments);
}
function p24() {
  return p24 = t6(function(r33) {
    return e15(this, function(o17) {
      return [2, a9("saveImageToGallery", m22, [r33], (l27 = t6(function(e46) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, i20(e46.imageBase64Data, e46.imageUrl, e46.onProgress)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("saveImageToGallery", {}))];
          }
        });
      }), function(e46) {
        return l27.apply(this, arguments);
      }))];
      var l27;
    });
  }), p24.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/saveVideoToGallery.js
var n19;
var t27 = (n19 = t6(function(s30, n24) {
  var t39;
  return e15(this, function(e46) {
    return t39 = { videoUrl: s30, onProgress: n24 }, [2, new Promise(function(s31, e47) {
      a8("SAVE_VIDEO_GALLERY", t9({}, t39), { success: function() {
        s31();
      }, fail: function(s32) {
        e47(s32);
      } }, {});
    })];
  });
}), function(s30, o17) {
  return n19.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/saveVideoToGallery.js
var m23 = [Me({ videoUrl: ke().url() }).optional()];
function a18(o17) {
  return u25.apply(this, arguments);
}
function u25() {
  return u25 = t6(function(r33) {
    return e15(this, function(e46) {
      return [2, a9("saveVideoToGallery", m23, [r33], (a36 = t6(function(o17) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, t27(o17.videoUrl, o17.onProgress)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("saveVideoToGallery", {}))];
          }
        });
      }), function(o17) {
        return a36.apply(this, arguments);
      }))];
      var a36;
    });
  }), u25.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openMiniApp.js
var a19;
var c27 = (a19 = t6(function(r33) {
  var a36, c41, i44, p49, l27, m51, f14 = arguments;
  return e15(this, function(o17) {
    switch (o17.label) {
      case 0:
        a36 = f14.length > 1 && void 0 !== f14[1] ? f14[1] : "/", c41 = f14.length > 2 && void 0 !== f14[2] ? f14[2] : {}, o17.label = 1;
      case 1:
        return o17.trys.push([1, 3, , 4]), [4, n17(r33, 0)];
      case 2:
        return i44 = o17.sent(), [3, 4];
      case 3:
        return o17.sent(), [3, 4];
      case 4:
        p49 = { appId: r33 }, i44 && (p49.appName = i44.name, p49.appAvtUrl = i44.logoUrl), l27 = "".concat(T, "/").concat(r33).concat(a36);
        try {
          m51 = new URL(l27), Object.entries(c41).forEach(function(r34) {
            var t39 = e6(r34, 2), s30 = t39[0], n24 = t39[1];
            m51.searchParams.append(s30, String(n24));
          }), l27 = m51.href;
        } catch (r34) {
        }
        return [2, new Promise(function(r34, e46) {
          a8("OPEN_MP", { mpUrl: l27, mpInfo: p49 }, { success: function() {
            r34();
          }, fail: function(r35) {
            e46(r35);
          } }, {});
        })];
    }
  });
}), function(r33) {
  return a19.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openMiniApp.js
var m24 = [Me({ appId: ke(), path: ke().optional(), params: ot.record(ke(), ke()).optional() })];
function u26(o17) {
  return c28.apply(this, arguments);
}
function c28() {
  return c28 = t6(function(r33) {
    return e15(this, function(n24) {
      return [2, a9("openMiniApp", m24, [r33], (t39 = t6(function(o17) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, c27(o17.appId, o17.path, o17.params)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openMiniApp", {}))];
          }
        });
      }), function(o17) {
        return t39.apply(this, arguments);
      }))];
      var t39;
    });
  }), c28.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/vibrate.js
var r25 = t6(function() {
  var e46, r33, s30 = arguments;
  return e15(this, function(t39) {
    return e46 = s30.length > 0 && void 0 !== s30[0] ? s30[0] : "oneShot", r33 = s30.length > 1 ? s30[1] : void 0, [2, new Promise(function(t40, s31) {
      a8("INTERACTIVE_VIBRATION", { type: p2[e46], vibrate_time: r33 }, { success: function() {
        t40();
      }, fail: function(e47) {
        s31(e47);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/vibrate.js
var u27 = [Me({ type: ot.enum(["oneShot"]).default("oneShot").optional(), milliseconds: we().optional() })];
function p25(o17) {
  return a20.apply(this, arguments);
}
function a20() {
  return a20 = t6(function(e46) {
    return e15(this, function(r33) {
      return [2, a9("vibrate", u27, [e46], (t39 = t6(function(o17) {
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? [4, r25(null == o17 ? void 0 : o17.type, null == o17 ? void 0 : o17.milliseconds)] : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("vibrate", {}))];
          }
        });
      }), function(o17) {
        return t39.apply(this, arguments);
      }))];
      var t39;
    });
  }), a20.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openWebview.js
var l15;
var r26 = (l15 = t6(function(t39, l27) {
  var r33, i44, u48, a36;
  return e15(this, function(n24) {
    return r33 = new URL(t39), i44 = r33.href.includes("vnpay"), u48 = r33.searchParams.get("typeInapp"), a36 = u.isEmpty(l27) ? void 0 : { modal_style: (null == l27 ? void 0 : l27.style) ? c3(null == l27 ? void 0 : l27.style) : "bottom_sheet", left_button: (null == l27 ? void 0 : l27.leftButton) ? c3(null == l27 ? void 0 : l27.leftButton) : "back" }, !i44 && u.isEmpty(u48) && r33.searchParams.set("typeInapp", "1"), [2, new Promise(function(t40, e46) {
      a8("OPEN_INAPP", { url: r33.href, style: a36 }, { success: function() {
        t40();
      }, fail: function(t41) {
        e46(t41);
      } }, {});
    })];
  });
}), function(t39, s30) {
  return l15.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openWebview.js
var p26 = [Me({ url: ke(), config: Me({ style: ot.enum(["bottomSheet", "normal"]).default("bottomSheet").optional(), leftButton: ot.enum(["back", "none"]).default("back").optional() }).optional() })];
function a21(o17) {
  return l16.apply(this, arguments);
}
function l16() {
  return l16 = t6(function(e46) {
    return e15(this, function(t39) {
      return [2, a9("openWebview", p26, [e46], (n24 = t6(function(o17) {
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? [4, r26(o17.url, o17.config)] : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openWebview", {}))];
          }
        });
      }), function(o17) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), l16.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getRouteParams.js
function t28() {
  var t39 = {}, a36 = window.location.href;
  try {
    var e46 = new URL(a36).searchParams.toString();
    e46 && (t39 = o7.getParamsAsObject(e46));
  } catch (r33) {
    throw new Error("Invalid URL");
  }
  return t39;
}

// ../../node_modules/zmp-sdk/apis/apis/getRouteParams.js
function t29() {
  return a9("getRouteParams", [], [], function() {
    return t28();
  });
}

// ../../node_modules/zmp-sdk/apis/apis/getAppInfo.js
function i21(r33) {
  return p27.apply(this, arguments);
}
function p27() {
  return (p27 = t6(function(i44) {
    return e15(this, function(p49) {
      return [2, a9("getAppInfo", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, n17()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ description: "", name: "", version: "", appUrl: "", qrCodeUrl: "" })] : [2, Promise.reject(o11("getAppInfo", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/sendDataToPreviousMiniApp.js
var r27;
var e32 = (r27 = t6(function(o17) {
  return e15(this, function(n24) {
    return [2, new Promise(function(n25, r33) {
      a8("WEBVIEW_SET_RESULT", { result: h4(o17) }, { success: function() {
        n25();
      }, fail: function(o18) {
        r33(o18);
      } }, {});
    })];
  });
}), function(o17) {
  return r27.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/sendDataToPreviousMiniApp.js
var p28 = [Me({ data: ot.any() })];
function m25(o17) {
  return u28.apply(this, arguments);
}
function u28() {
  return u28 = t6(function(r33) {
    return e15(this, function(e46) {
      return [2, a9("sendDataToPreviousMiniApp", p28, [r33], (m51 = t6(function(o17) {
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return o10.isMp ? [4, e32(o17.data)] : [3, 2];
            case 1:
              return [2, r34.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("sendDataToPreviousMiniApp", {}))];
          }
        });
      }), function(o17) {
        return m51.apply(this, arguments);
      }))];
      var m51;
    });
  }), u28.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getPhoneNumber.js
var i22 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, t39) {
      var i44, s30;
      a8("MP_GET_NUMBER", { forceUpdate: true }, { success: function(r33) {
        var e46 = {};
        if (null == r33 ? void 0 : r33.token) return e46.token = null == r33 ? void 0 : r33.token, o18(e46);
        t39(d6.error.clientNotSupport());
      }, fail: function(o19) {
        t39(o19);
      } }, { delay: (null === (i44 = s4()) || void 0 === i44 || null === (s30 = i44.platform) || void 0 === s30 ? void 0 : s30.isIOS) ? 400 : 0 });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/getPhoneNumber.js
function i23(e46) {
  return m26.apply(this, arguments);
}
function m26() {
  return (m26 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getPhoneNumber", [], [i44], t6(function() {
        return e15(this, function(e46) {
          switch (e46.label) {
            case 0:
              return o10.isMp ? [4, i22()] : [3, 2];
            case 1:
              return [2, e46.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ number: "" })] : [2, Promise.reject(o11("getPhoneNumber", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openProfilePicker.js
var i24 = t6(function() {
  var r33, i44 = arguments;
  return e15(this, function(o17) {
    return r33 = i44.length > 0 && void 0 !== i44[0] ? i44[0] : 1, [2, new Promise(function(o18, i45) {
      a8("MP_OPEN_PROFILE_PICKER", { maxProfile: r33 }, { success: function(r34) {
        Array.isArray(r34) ? o18(r34.map(function(r35) {
          return r35.id;
        })) : o18([]);
      }, fail: function(r34) {
        i45(r34);
      } }, { isMultiCallback: true });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/openProfilePicker.js
var m27 = [Me({ maxProfile: we().min(1).max(10).optional().default(1) })];
function c29(e46) {
  return p29.apply(this, arguments);
}
function p29() {
  return p29 = t6(function(r33) {
    return e15(this, function(o17) {
      return [2, a9("openProfilePicker", m27, [r33], (c41 = t6(function(e46) {
        var r34, o18, s30, m51, c42;
        return e15(this, function(d17) {
          switch (d17.label) {
            case 0:
              return o10.isMp ? [4, i24(null == e46 ? void 0 : e46.maxProfile)] : [3, 8];
            case 1:
              if (!((r34 = d17.sent()).length > 0)) return [3, 7];
              d17.label = 2;
            case 2:
              return d17.trys.push([2, 6, , 7]), [4, w2.getAccessToken()];
            case 3:
              return s30 = d17.sent(), [4, fetch(O.GET_LIST_USER_INFO, { method: "POST", headers: { "Content-Type": "application/json", access_token: s30 || "" }, body: JSON.stringify({ fields: "id,name,picture", user_ids: r34 }) })];
            case 4:
              return [4, d17.sent().json()];
            case 5:
              if (0 !== (m51 = d17.sent()).error || !m51.data) throw new Error(m51.message);
              return o18 = r34.map(function(e47) {
                var r35, o19, n24, t39, s31, a36, l27, u48, d18, c43, p49;
                return { id: (null === (r35 = m51.data[e47]) || void 0 === r35 || null === (o19 = r35.user_info) || void 0 === o19 ? void 0 : o19.user_id_by_app) || "", profile: { name: null === (n24 = m51.data[e47]) || void 0 === n24 || null === (t39 = n24.user_info) || void 0 === t39 ? void 0 : t39.name, avatar: null === (s31 = m51.data[e47]) || void 0 === s31 || null === (a36 = s31.user_info) || void 0 === a36 || null === (l27 = a36.picture) || void 0 === l27 || null === (u48 = l27.data) || void 0 === u48 ? void 0 : u48.url }, code: 0 !== (null === (d18 = m51.data[e47]) || void 0 === d18 ? void 0 : d18.error) ? -1 : 0, message: "Get profile: ".concat(u.isUndefined(null === (c43 = m51.data[e47]) || void 0 === c43 ? void 0 : c43.message) ? "Unknown error" : null === (p49 = m51.data[e47]) || void 0 === p49 ? void 0 : p49.message) };
              }), [3, 7];
            case 6:
              return c42 = d17.sent(), o18 = r34.map(function(e47) {
                return { id: e47, profile: { name: "", avatar: "" }, code: -1, message: "Can't get profile: ".concat(c42.message) };
              }), [3, 7];
            case 7:
              return [2, { users: o18 || [] }];
            case 8:
              return o10.isMpWeb ? [2, Promise.resolve({ users: [] })] : [2, Promise.reject(o11("openProfilePicker", {}))];
          }
        });
      }), function(e46) {
        return c41.apply(this, arguments);
      }))];
      var c41;
    });
  }), p29.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/connectWifi.js
var t30;
var i25 = (t30 = t6(function(s30, t39, i44) {
  var e46;
  return e15(this, function(r33) {
    return e46 = t39 && u.isString(t39) && t39.length > 0 ? "wpa_wpa2" : "open", [2, new Promise(function(o17, r34) {
      a8("MP_JOIN_WIFI", { ssid: s30, securityType: e46, password: t39, hiddenSsid: i44 }, { success: function(s31) {
        o17(s31);
      }, fail: function(s31) {
        r34(s31);
      } }, {});
    })];
  });
}), function(s30, n24, o17) {
  return t30.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/connectWifi.js
var p30 = [Me({ SSID: ke(), password: ke().optional(), hiddenSSID: Ne().optional() })];
function a22(o17) {
  return u29.apply(this, arguments);
}
function u29() {
  return u29 = t6(function(n24) {
    return e15(this, function(r33) {
      return [2, a9("connectWifi", p30, [n24], (t39 = t6(function(o17) {
        return e15(this, function(n25) {
          switch (n25.label) {
            case 0:
              return o10.isMp ? [4, i25(o17.SSID, o17.password, o17.hiddenSSID)] : [3, 2];
            case 1:
              return [2, n25.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("connectWifi", {}))];
          }
        });
      }), function(o17) {
        return t39.apply(this, arguments);
      }))];
      var t39;
    });
  }), u29.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openMediaPicker.js
var i26;
var l17 = (i26 = t6(function(e46, i44, l27, m51, u48, a36) {
  var c41, p49;
  return e15(this, function(r33) {
    return c41 = { type: 0, url: i44, max_size: l27 || _ }, p49 = { enable: 1, aspect_ratio: "1:1" }, [2, new Promise(function(o17, r34) {
      a8("PICK_MEDIA", { media_type: d2[e46], edit_view: t9({}, p49, m51), silent_request: u48, upload: c41, max_select_items: a36 }, { success: function(e47) {
        o17(null == e47 ? void 0 : e47.result_content);
      }, fail: function(e47) {
        r34(e47);
      } }, { isMultiCallback: true });
    })];
  });
}), function(e46, s30, t39, o17, r33, n24) {
  return i26.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/openMediaPicker.js
var c30 = [Me({ type: ot.enum(["video", "photo", "file", "zcamera", "zcamera_photo", "zcamera_video", "zcamera_scan"]), serverUploadUrl: ke().url(), maxItemSize: we().min(0).optional(), editView: Me({ enable: Ne().optional(), aspectRatio: ke().regex(/^d+:d+$/).optional() }).optional(), silentRequest: Ne().optional(), maxSelectItem: we().min(1).optional() })];
function u30(e46) {
  return f7.apply(this, arguments);
}
function f7() {
  return f7 = t6(function(o17) {
    return e15(this, function(t39) {
      return [2, a9("openMediaPicker", c30, [o17], (i44 = t6(function(e46) {
        var o18, t40, i45, r33, n24, l27;
        return e15(this, function(d17) {
          switch (d17.label) {
            case 0:
              return o10.isMp ? (r33 = {}, u.isUndefined(null === (o18 = e46.editView) || void 0 === o18 ? void 0 : o18.enable) || (null === (t40 = e46.editView) || void 0 === t40 ? void 0 : t40.enable) ? r33.enable = 1 : r33.enable = 0, r33.aspect_ratio = null === (i45 = e46.editView) || void 0 === i45 ? void 0 : i45.aspectRatio, n24 = true === e46.silentRequest ? 1 : 0, l27 = e46.maxSelectItem, [4, l17(e46.type, e46.serverUploadUrl, e46.maxItemSize, r33, n24, l27)]) : [3, 2];
            case 1:
              return [2, { data: d17.sent() }];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ data: "" })] : [2, Promise.reject(o11("openMediaPicker", {}))];
          }
        });
      }), function(e46) {
        return i44.apply(this, arguments);
      }))];
      var i44;
    });
  }), f7.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getShareableLink.js
var a23 = [Me({ title: ke().max(100), description: ke().max(400).default(""), thumbnail: ke().url().optional(), path: ke().optional() })];
function m28(t39) {
  return l18.apply(this, arguments);
}
function l18() {
  return l18 = t6(function(e46) {
    return e15(this, function(r33) {
      return [2, a9("getShareableLink", a23, [e46], (m51 = t6(function(t39) {
        var e47;
        return e15(this, function(r34) {
          switch (r34.label) {
            case 0:
              return [4, w2.getAccessToken()];
            case 1:
              return e47 = r34.sent(), [4, i15(window.APP_ID, e47 || "", t39.title, t39.description, t39.thumbnail, t39.path)];
            case 2:
              return [2, r34.sent().shareableLink];
          }
        });
      }), function(t39) {
        return m51.apply(this, arguments);
      }))];
      var m51;
    });
  }), l18.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/closeLoading.js
var o15;
var r28 = (o15 = t6(function(e46) {
  return e15(this, function(t39) {
    return [2, new Promise(function(t40, o17) {
      a8("MP_CLOSE_LOADINGVIEW", { at: (/* @__PURE__ */ new Date()).getTime(), when: e46, url: location.href }, { success: function(e47) {
        t40(e47);
      }, fail: function(e47) {
        o17(e47);
      } }, {});
    })];
  });
}), function(e46) {
  return o15.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/closeLoading.js
function i27(o17) {
  return c31.apply(this, arguments);
}
function c31() {
  return (c31 = t6(function(i44) {
    return e15(this, function(c41) {
      return [2, a9("closeLoading", [], [i44], t6(function() {
        return e15(this, function(o17) {
          switch (o17.label) {
            case 0:
              return o10.isMp ? [4, r28("user action")] : [3, 2];
            case 1:
              return [2, o17.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("closeLoading", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/requestUpdateZalo.js
function i28(o17) {
  return a24.apply(this, arguments);
}
function a24() {
  return (a24 = t6(function(i44) {
    return e15(this, function(a36) {
      return [2, a9("requestUpdateZalo", [], [i44], t6(function() {
        var o17, e46, i45;
        return e15(this, function(n24) {
          switch (n24.label) {
            case 0:
              return o10.isMp ? (i45 = (null === (o17 = s4()) || void 0 === o17 || null === (e46 = o17.platform) || void 0 === e46 ? void 0 : e46.isIOS) ? "https://apps.apple.com/vn/app/zalo/id579523206" : "https://play.google.com/store/apps/details?id=com.zing.zalo", [4, t24(i45)]) : [3, 2];
            case 1:
              return [2, n24.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("requestUpdateZalo", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/events.js
var r29 = f2.getInstance();
r29.addListener = function(e46, n24, m51) {
  var o17 = e46;
  return r29.addListener(o5[o17], n24, m51);
};

// ../../node_modules/zmp-sdk/apis/apis/onConfirmToExit.js
var m29 = [ot.function()];
function s18(o17) {
  return a9("onConfirmToExit", m29, [o17], function(o18) {
    var n24 = o18;
    o10.isMp && (r29.on(o5.AppClose, n24, '{"handle_h5": 1}'), B2.setNavigationBarLeftButton(void 0, 2));
  });
}

// ../../node_modules/zmp-sdk/apis/apis/offConfirmToExit.js
function m30() {
  return a9("offConfirmToExit", [], [], function() {
    o10.isMp && (r29.off(o5.AppClose), B2.setNavigationBarLeftButton(void 0, 0));
  });
}

// ../../node_modules/zmp-sdk/apis/apis/getDeviceId.js
function e33() {
  return a9("getDeviceId", [], [], function() {
    return o10.isMp && w2.getSync(N.DEVICE_ID) || "";
  });
}

// ../../node_modules/zmp-sdk/apis/apis/getDeviceIdAsync.js
function i29(t39) {
  return m31.apply(this, arguments);
}
function m31() {
  return (m31 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getDeviceIdAsync", [], [i44], t6(function() {
        return e15(this, function(t39) {
          switch (t39.label) {
            case 0:
              return o10.isMp ? [4, w2.jumpAndGetToken(N.DEVICE_ID)] : [3, 2];
            case 1:
              return [2, t39.sent() || ""];
            case 2:
              return [2, Promise.resolve("")];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getContext.js
function e34() {
  return a9("getContext", [], [], function() {
    if (o10.isMp) {
      var o17 = w2.getSync(N.CONTEXT_ID) || "";
      return { type: w2.getSync(N.CONTEXT_TYPE) || "", id: o17 };
    }
    return { type: "", id: "" };
  });
}

// ../../node_modules/zmp-sdk/apis/apis/getContextAsync.js
function i30(t39) {
  return m32.apply(this, arguments);
}
function m32() {
  return (m32 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getContextAsync", [], [i44], t6(function() {
        var t39;
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, w2.jumpAndGetToken(N.CONTEXT_ID)] : [3, 3];
            case 1:
              return t39 = r33.sent() || "", [4, w2.jumpAndGetToken(N.CONTEXT_TYPE)];
            case 2:
              return [2, { type: r33.sent() || "", id: t39 }];
            case 3:
              return [2, Promise.resolve({ type: "", id: "" })];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getAuthCode.js
function m33(e46) {
  return a25.apply(this, arguments);
}
function a25() {
  return (a25 = t6(function(m51) {
    return e15(this, function(a36) {
      return [2, a9("getAuthCode", [], [m51], t6(function() {
        var e46, t39;
        return e15(this, function(u48) {
          switch (u48.label) {
            case 0:
              return o10.isMp ? [4, w2.jumpAndGetToken(N.ZOAUTH, true)] : [3, 4];
            case 1:
              return e46 = u48.sent() || "", [4, w2.jumpAndGetToken(N.ZOAUTH_VRF)];
            case 2:
              return t39 = u48.sent() || "", [4, w2.verifyUserAuthorized()];
            case 3:
              if (u48.sent()) return [2, { authCode: e46, authCodeVerify: t39 }];
              throw u5([{ action: "getAuthCode", error: -104, message: "no user auth", data: {} }]), d6.error.loginFailed("User Authentication Required");
            case 4:
              return [2, Promise.resolve({ authCode: "", authCodeVerify: "" })];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getZPIToken.js
function u31(t39) {
  return p31.apply(this, arguments);
}
function p31() {
  return (p31 = t6(function(u48) {
    return e15(this, function(p49) {
      return [2, a9("getZPIToken", [], [u48], t6(function() {
        var t39, n24, u49, p50;
        return e15(this, function(s30) {
          switch (s30.label) {
            case 0:
              return o10.isMp ? [4, w2.jumpAndGetToken(N.UTOKEN_ZPI)] : [3, 5];
            case 1:
              return t39 = s30.sent() || "", [4, w2.jumpAndGetToken(N.GTOKEN_ZPI)];
            case 2:
              return n24 = s30.sent() || "", [4, w2.jumpAndGetToken(N.ZPP_ZPI)];
            case 3:
              return u49 = s30.sent() || "", [4, w2.jumpAndGetToken(N.ZPT_ZPI)];
            case 4:
              return p50 = s30.sent() || "", [2, { utoken: t39, gtoken: n24, zpp: u49, zpt: p50 }];
            case 5:
              return [2, Promise.resolve({ utoken: "", gtoken: "", zpp: "", zpt: "" })];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/setAccessToken.js
var e35 = [ke()];
function t31(o17) {
  return a9("setAccessToken", e35, [o17], function(o18) {
    var r33 = o18;
    w2.setAccessToken(r33);
  });
}

// ../../node_modules/zmp-sdk/apis/apis/openOutApp.js
var u32 = [Me({ url: ke().url() })];
function m34(r33) {
  return c32.apply(this, arguments);
}
function c32() {
  return c32 = t6(function(o17) {
    return e15(this, function(t39) {
      return [2, a9("openOutApp", u32, [o17], (m51 = t6(function(r33) {
        return e15(this, function(o18) {
          switch (o18.label) {
            case 0:
              return o10.isMp ? [4, t24(r33.url)] : [3, 2];
            case 1:
              return [2, o18.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openOutApp", {}))];
          }
        });
      }), function(r33) {
        return m51.apply(this, arguments);
      }))];
      var m51;
    });
  }), c32.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/chooseImage.js
var i31 = [Me({ count: we().min(1).optional().default(1), sourceType: ot.array(ot.enum(["album", "camera"])).max(2).default(["album", "camera"]).optional(), cameraType: ot.enum(["back", "front"]).default("front").optional() })];
function c33(e46) {
  return u33.apply(this, arguments);
}
function u33() {
  return u33 = t6(function(t39) {
    return e15(this, function(n24) {
      return [2, a9("chooseImage", i31, [t39], (o17 = t6(function(e46) {
        var t40, n25, o18, a36;
        return e15(this, function(r33) {
          return t40 = e46.sourceType || ["album", "camera"], n25 = e46.cameraType || "front", o18 = e46.count || 1, (a36 = document.createElement("input")).type = "file", a36.multiple = o18 > 1, a36.accept = "image/*", a36.style.display = "none", 1 === t40.length && t40.includes("camera") && (a36.accept = "front" === n25 ? "zcamera/photo-frontfacing" : "zcamera/photo-backfacing", a36.capture = "front" === n25 ? "user" : "environment"), [2, new Promise(function(e47, t41) {
            a36.addEventListener("change", function(n26) {
              var a37, r34 = (null == n26 || null === (a37 = n26.target) || void 0 === a37 ? void 0 : a37.files) || [];
              if (r34.length > o18) t41(new Error("You are only allowed to choose a maximum of ".concat(o18, " images at a time")));
              else {
                var i44 = { filePaths: [], tempFiles: [] };
                Array.from(r34).forEach(function(e48) {
                  i44.filePaths.push(URL.createObjectURL(e48)), i44.tempFiles.push({ path: URL.createObjectURL(e48), size: e48.size });
                }), e47(i44);
              }
            }), document.body.appendChild(a36), a36.click();
          })];
        });
      }), function(e46) {
        return o17.apply(this, arguments);
      }))];
      var o17;
    });
  }), u33.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getLocation.js
var n20 = t6(function() {
  var o17, n24, i44 = arguments;
  return e15(this, function(r33) {
    return o17 = i44.length > 0 && void 0 !== i44[0] && i44[0], n24 = i44.length > 1 && void 0 !== i44[1] ? i44[1] : "Cho phép Zalo truy cập vào vị trí của thiết bị này?", [2, new Promise(function(r34, i45) {
      a8("GET_LOCATION", { silent_request: o17, permission_description: n24, forceUpdate: true }, { success: function(o18) {
        var t39 = {};
        if (null == o18 ? void 0 : o18.token) return t39.token = null == o18 ? void 0 : o18.token, r34(t39);
        i45(d6.error.clientNotSupport());
      }, fail: function(o18) {
        i45(o18);
      } }, { isMultiCallback: true, timeout: false });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/getLocation.js
function i32(o17) {
  return m35.apply(this, arguments);
}
function m35() {
  return (m35 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getLocation", [], [i44], t6(function() {
        return e15(this, function(o17) {
          switch (o17.label) {
            case 0:
              return o10.isMp ? [4, n20()] : [3, 2];
            case 1:
              return [2, o17.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ token: "" })] : [2, Promise.reject(o11("getLocation", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/onCallbackData.js
function a26(a36) {
  f2.getInstance().on(o5.OnDataCallback, function(t39) {
    a36({ data: t39 });
  });
}

// ../../node_modules/zmp-sdk/apis/apis/onCallbackData.js
var t32 = [ot.function().args(Me({ data: ot.any() })).returns(ot.void())];
function i33(o17) {
  return a9("onCallbackData", t32, [o17], function() {
    a26(o17);
  });
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/payment/checkTransaction.js
var c34;
var p32 = (c34 = t6(function(r33) {
  var c41, p49, d17, u48, g8, f14, h11, j6, l27, I6, b5;
  return e15(this, function(i44) {
    switch (i44.label) {
      case 0:
        return r33 || (r33 = t28()), "object" == typeof r33 && ((p49 = r33.zmpOrderId || r33.orderId) || (c41 = r33.appTransID || r33.apptransid || r33.transId)), "string" == typeof r33 && (d17 = o7.getParameterByName("orderId", r33), u48 = o7.getParameterByName("appTransID", r33) || o7.getParameterByName("apptransid", r33), c41 = d17 || u48), g8 = { headers: { authorization: "Bearer " } }, [4, w2.getAccessToken().catch(function() {
          throw d6.error.loginRequired;
        })];
      case 1:
        return f14 = i44.sent(), g8.headers.authorization = "Bearer ".concat(f14), h11 = new URL(m.GET_TRANSACTION), j6 = { orderId: p49 || "", transId: c41 || "", appId: I }, h11.search = new URLSearchParams(j6).toString(), [4, fetch(h11.toString(), g8)];
      case 2:
        return [4, i44.sent().json()];
      case 3:
        if (!(l27 = i44.sent()).data) throw d6.error.badRequest(l27.msg);
        return I6 = l27.data || {}, b5 = { err: l27.err, msg: l27.msg }, 0 !== Object.keys(I6).length && I6.constructor === Object && Object.assign(b5, t9({ msg: I6.resultMessage }, I6)), [2, b5];
    }
  });
}), function(r33) {
  return c34.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/common/apis/general/payment/createOrder.js
var y5;
var g5 = (y5 = t6(function(y8, g8, w7, I6, b5, _6, C5) {
  var P3, R3, v7, O4, S5, T4, N4, k4, A5, D4, E5, U3, z2, J3, q2, x2, B3, L3, V3, W2;
  return e15(this, function(F4) {
    switch (F4.label) {
      case 0:
        P3 = I, (R3 = new URLSearchParams()).append("amount", y8.toString()), R3.append("item", encodeURIComponent(JSON.stringify(g8))), R3.append("appId", P3), R3.append("mac", I6), w7 && R3.append("desc", encodeURIComponent(w7)), b5 && (v7 = b5, "object" == typeof b5 && (v7 = JSON.stringify(b5)), R3.append("extradata", encodeURIComponent(v7))), _6 && (O4 = _6, "object" == typeof _6 && (O4 = JSON.stringify(_6)), R3.append("payload", encodeURIComponent(O4))), C5 && ("string" == typeof C5 ? (R3.append("method", C5), R3.append("isCustom", "false")) : "object" == typeof C5 && (R3.append("method", C5.id), R3.append("isCustom", JSON.stringify(C5.isCustom)))), S5 = { headers: { "Content-Type": "application/x-www-form-urlencoded", authorization: "Bearer " } }, F4.label = 1;
      case 1:
        return F4.trys.push([1, 6, , 7]), [4, w2.getAccessToken().catch(function() {
          throw d6.error.loginRequired;
        })];
      case 2:
        return T4 = F4.sent(), S5.headers.authorization = "Bearer ".concat(T4), [4, fetch(m.CREATE_ORDER, e25(t9({ method: "POST" }, S5), { body: R3 }))];
      case 3:
        return [4, F4.sent().json()];
      case 4:
        if (!(N4 = F4.sent()).data || 0 !== N4.err) throw d6.error.badRequest(N4.msg);
        return k4 = N4.data.id, A5 = N4.data.jwt, D4 = N4.data.messageToken || "", E5 = window.APP_VERSION, U3 = window.APP_ENV, z2 = { miniAppId: P3, orderId: k4 }, J3 = N4.data.method, q2 = N4.data.isCustom, A5 && (z2.token = A5), E5 && (z2.version = E5), U3 && (z2.env = U3), J3 && (z2.method = J3), q2 && (z2.isCustom = q2), x2 = o7.encode(z2), B3 = new URL("?".concat(x2), U).toString(), L3 = f3(), V3 = l3() && L3 < 742 ? "normal" : "bottomSheet", r11.enablePaymentDone = f2.getInstance().eventNames().includes(o5.PaymentDone), [4, r26(B3, { style: V3 }).then(function() {
          var o17 = false;
          f2.getInstance().once(o5.WebviewClosed, function() {
            setTimeout(t6(function() {
              var e46;
              return e15(this, function(r33) {
                switch (r33.label) {
                  case 0:
                    return o17 ? [3, 2] : (f2.getInstance().emit(o5.PaymentDone, k4, { orderId: k4 }), [4, p32({ zmpOrderId: k4 })]);
                  case 1:
                    (e46 = r33.sent()).err >= 0 && !e46.isCustom && f2.getInstance().emit(o5.PaymentClose, e46.err, e25(t9({}, e46), { zmpOrderId: k4 })), r33.label = 2;
                  case 2:
                    return [2];
                }
              });
            }), 500);
          }), f2.getInstance().once(o5.OpenApp, function() {
            o17 = true;
          }), f2.getInstance().once(o5.PaymentDone, function() {
            o17 = true, r11.enablePaymentDone = false;
          });
        })];
      case 5:
        return F4.sent(), [2, { orderId: k4, messageToken: D4 }];
      case 6:
        throw W2 = F4.sent(), console.log("Internal error"), W2;
      case 7:
        return [2];
    }
  });
}), function(e46, t39, n24, o17, r33, s30, a36) {
  return y5.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/payment/createOrder.js
var u34 = [Me({ amount: ot.union([ke(), we()]), item: ot.array(ot.record(ke(), ot.any())), desc: ot.string(), mac: ot.string(), extradata: ot.any().optional(), payload: ot.any().optional(), method: ot.union([ot.string(), ot.object({ id: ot.string(), isCustom: ot.boolean() })]).optional() })];
function d11(r33) {
  return p33.apply(this, arguments);
}
function p33() {
  return p33 = t6(function(e46) {
    return e15(this, function(o17) {
      return [2, a9("createOrder", u34, [e46], (t39 = t6(function(r33) {
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? [4, g5(r33.amount, r33.item, r33.desc, r33.mac, r33.extradata, r33.payload, r33.method)] : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ orderId: "", messageToken: "" })] : [2, Promise.reject(o11("createOrder", {}))];
          }
        });
      }), function(r33) {
        return t39.apply(this, arguments);
      }))];
      var t39;
    });
  }), p33.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/showPaymentConfirm.js
var e36 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("MP_CONFIRM_REQUEST_PAYMENT", {}, { success: function() {
        o18();
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/common/apis/general/payment/purchase.js
var m36;
var p34 = (m36 = t6(function(e46, m51, p49, i44, l27) {
  var u48, h11, f14, j6, w7, _6, g8, b5, y8, T4, C5, I6;
  return e15(this, function(d17) {
    switch (d17.label) {
      case 0:
        u48 = I, (h11 = new URLSearchParams()).append("amount", e46.toString()), h11.append("appId", u48), m51 && h11.append("desc", encodeURIComponent(m51)), p49 && h11.append("method", p49), i44 && h11.append("transId", i44), l27 && h11.append("mac", l27), f14 = { headers: { "Content-Type": "application/x-www-form-urlencoded", authorization: "Bearer " } }, d17.label = 1;
      case 1:
        return d17.trys.push([1, 10, , 11]), [4, w2.getAccessToken().catch(function() {
          throw d6.error.loginRequired;
        })];
      case 2:
        return w7 = d17.sent(), f14.headers.authorization = "Bearer ".concat(w7), console.log("_purchase"), [4, w2.jumpAndGetToken()];
      case 3:
        if (d17.sent(), _6 = null === (j6 = w2.miniProgramConfig) || void 0 === j6 ? void 0 : j6.showConfirmToPayment, console.log("showConfirmToPayment", _6), !_6) return [3, 7];
        d17.label = 4;
      case 4:
        return d17.trys.push([4, 6, , 7]), [4, e36()];
      case 5:
        return d17.sent(), [3, 7];
      case 6:
        throw g8 = d17.sent(), console.error("Failed:", g8), g8;
      case 7:
        return [4, fetch(m.PURCHASE, e25(t9({ method: "POST" }, f14), { body: h11 }))];
      case 8:
        return [4, d17.sent().json()];
      case 9:
        if (!(b5 = d17.sent()).data || 0 !== b5.err) throw d6.error.badRequest(b5.msg);
        return y8 = b5.data.id, T4 = b5.data.messageToken || "", C5 = b5.data.transId || "", [2, { orderId: y8, messageToken: T4, transId: C5 }];
      case 10:
        throw I6 = d17.sent(), console.log("Internal error"), I6;
      case 11:
        return [2];
    }
  });
}), function(e46, r33, o17, s30, t39) {
  return m36.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/payment/purchase.js
var a27 = [Me({ amount: ot.union([ot.string(), ot.number()]), desc: ot.string(), method: ot.string(), transId: ot.string().optional(), mac: ot.string().optional() })];
function u35(r33) {
  return c35.apply(this, arguments);
}
function c35() {
  return c35 = t6(function(n24) {
    return e15(this, function(o17) {
      return [2, a9("purchase", a27, [n24], (u48 = t6(function(r33) {
        return e15(this, function(n25) {
          switch (n25.label) {
            case 0:
              return o10.isMp ? [4, p34(r33.amount, r33.desc, r33.method, null == r33 ? void 0 : r33.transId, null == r33 ? void 0 : r33.mac)] : [3, 2];
            case 1:
              return [2, n25.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ orderId: "", messageToken: "", transId: "" })] : [2, Promise.reject(o11("purchase", {}))];
          }
        });
      }), function(r33) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), c35.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/payment/selectPaymentMethod.js
var s19;
var t33 = (s19 = t6(function(n24, s30) {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, t39) {
      var r33 = n24 && (null == n24 ? void 0 : n24.length) > 0;
      a8("MP_SELECT_PAYMENT_METHOD", { isBinding: r33, channels: n24, selectedMethod: s30, forceUpdate: true }, { success: function(n25) {
        console.log(n25), o18(n25);
      }, fail: function(n25) {
        t39(n25);
      } }, {});
    })];
  });
}), function(n24, e46) {
  return s19.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/payment/selectPaymentMethod.js
var l19 = [Me({ channels: Ae(Me({ method: ke(), subMethod: ke().optional(), subInfo: ke().optional() })).optional(), selectedMethod: Me({ method: ke(), subMethod: ke().optional() }).optional() }).optional()];
function p35(e46) {
  return c36.apply(this, arguments);
}
function c36() {
  return c36 = t6(function(o17) {
    return e15(this, function(t39) {
      return [2, a9("selectPaymentMethod", l19, [o17], (n24 = t6(function(e46) {
        var o18, t40;
        return e15(this, function(n25) {
          switch (n25.label) {
            case 0:
              return o10.isMp ? (o18 = e46.channels, t40 = e46.selectedMethod, [4, t33(o18, t40)]) : [3, 2];
            case 1:
              return [2, n25.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ method: "" })] : [2, Promise.reject(o11("selectPaymentMethod", {}))];
          }
        });
      }), function(e46) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), c36.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/payment/checkTransaction.js
var m37 = [Me({ data: ot.union([ke(), ot.record(ke(), ke().nullish())]) })];
function u36(r33) {
  return p36.apply(this, arguments);
}
function p36() {
  return p36 = t6(function(t39) {
    return e15(this, function(e46) {
      return [2, a9("checkTransaction", m37, [t39], (n24 = t6(function(r33) {
        return e15(this, function(t40) {
          switch (t40.label) {
            case 0:
              return o10.isMp ? [4, p32(r33.data)] : [3, 2];
            case 1:
              return [2, t40.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ msg: "Success", orderId: "", resultCode: 1, transTime: (/* @__PURE__ */ new Date()).getTime().toString(), createdAt: (/* @__PURE__ */ new Date()).getTime().toString(), transId: "" })] : [2, Promise.reject(o11("checkTransaction", {}))];
          }
        });
      }), function(r33) {
        return n24.apply(this, arguments);
      }))];
      var n24;
    });
  }), p36.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/payment/createOrderIAP.js
var u37;
var f8 = (u37 = t6(function(e46, u48, f14, _6, E5, h11) {
  var I6, j6, R3, A5, w7, T4, v7, P3, D4, y8, b5, C5, S5, g8;
  return e15(this, function(l27) {
    switch (l27.label) {
      case 0:
        w7 = I, (T4 = new URLSearchParams()).append("payType", u48 || m2.SUBSCRIPTION), v7 = s2.UNKNOW, f14 === c.IMMEDIATE_AND_CHARGE_FULL_PRICE && (v7 = s2.IMMEDIATE_AND_CHARGE_FULL_PRICE), f14 === c.DEFERRED && (v7 = s2.DEFERRED), T4.append("utm", E5 || "default"), T4.append("merchantTransId", h11 || ""), T4.append("prorationMode", v7.toString()), T4.append("appId", w7), _6 && T4.append("method", _6), P3 = "", (null === (I6 = s4()) || void 0 === I6 || null === (j6 = I6.platform) || void 0 === j6 ? void 0 : j6.isIOS) ? P3 = "ios" : (null === (R3 = s4()) || void 0 === R3 || null === (A5 = R3.platform) || void 0 === A5 ? void 0 : A5.isAndroid) && (P3 = "android"), T4.append("os", P3), T4.append("productId", e46), D4 = { headers: { "Content-Type": "application/x-www-form-urlencoded", authorization: "Bearer " } }, l27.label = 1;
      case 1:
        return l27.trys.push([1, 5, , 6]), [4, w2.getAccessToken().catch(function() {
          throw d6.error.loginRequired;
        })];
      case 2:
        return y8 = l27.sent(), D4.headers.authorization = "Bearer ".concat(y8), [4, fetch(m.CREATE_IAP_ORDER, e25(t9({ method: "POST" }, D4), { body: T4 }))];
      case 3:
        return [4, l27.sent().json()];
      case 4:
        if (!(b5 = l27.sent()).data || !b5.data.store || 0 !== b5.err) throw d6.error.createIAPOrderFailed(b5.err);
        return C5 = b5.data.store, S5 = b5.data.orderId, [2, new Promise(function(e47, o17) {
          a8("IAP_REQUESTPAYMENT", t9({}, C5), { success: function() {
            e47({ orderId: S5 });
          }, fail: function(e48) {
            o17(e48);
          } });
        })];
      case 5:
        throw g8 = l27.sent(), console.log("Internal error"), g8;
      case 6:
        return [2];
    }
  });
}), function(e46, r33, o17, t39, s30, n24) {
  return u37.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/payment/createOrderIAP.js
var p37 = [Me({ productId: ke(), payType: ot.enum(["ONETIME", "SUBSCRIPTION"]).optional(), prorationMode: ot.enum(["DEFERRED", "IMMEDIATE_AND_CHARGE_FULL_PRICE"]).default("DEFERRED").optional(), utm: ke().optional(), method: ot.enum(["IAP", "IAP_SANDBOX"]).optional(), merchantTransId: ke().optional() })];
function u38(r33) {
  return d12.apply(this, arguments);
}
function d12() {
  return d12 = t6(function(o17) {
    return e15(this, function(e46) {
      return [2, a9("createOrderIAP", p37, [o17], (t39 = t6(function(r33) {
        return e15(this, function(o18) {
          switch (o18.label) {
            case 0:
              return o10.isMp ? [4, f8(r33.productId, r33.payType, r33.prorationMode, r33.method, r33.utm, r33.merchantTransId)] : [3, 2];
            case 1:
              return [2, o18.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ orderId: "" })] : [2, Promise.reject(o11("createOrderIAP", {}))];
          }
        });
      }), function(r33) {
        return t39.apply(this, arguments);
      }))];
      var t39;
    });
  }), d12.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/setAndroidBottomNavigationBar.js
var m38 = [Me({ hide: Ne().default(false).optional() }).optional()];
function p38(o17) {
  return u39.apply(this, arguments);
}
function u39() {
  return u39 = t6(function(t39) {
    return e15(this, function(r33) {
      return [2, a9("setAndroidBottomNavigationBar", m38, [t39], (p49 = t6(function(o17) {
        return e15(this, function(t40) {
          switch (t40.label) {
            case 0:
              return o10.isMp ? [4, B2.setAndroidBottomNavigationBar(o17.hide)] : [3, 2];
            case 1:
              return [2, t40.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("setAndroidBottomNavigationBar", {}))];
          }
        });
      }), function(o17) {
        return p49.apply(this, arguments);
      }))];
      var p49;
    });
  }), u39.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/setIOSBottomSafeArea.js
var a28 = [Me({ hide: Ne().default(false).optional() }).optional()];
function p39(o17) {
  return u40.apply(this, arguments);
}
function u40() {
  return u40 = t6(function(e46) {
    return e15(this, function(t39) {
      return [2, a9("setIOSBottomSafeArea", a28, [e46], (p49 = t6(function(o17) {
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? [4, B2.setIOSBottomSafeArea(o17.hide)] : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("setIOSBottomSafeArea", {}))];
          }
        });
      }), function(o17) {
        return p49.apply(this, arguments);
      }))];
      var p49;
    });
  }), u40.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/setStatusBar.js
var m39 = [Me({ type: ot.enum(["normal", "hidden", "transparent"]).default("normal").optional(), color: ot.string().optional() }).optional()];
function p40(r33) {
  return u41.apply(this, arguments);
}
function u41() {
  return u41 = t6(function(o17) {
    return e15(this, function(t39) {
      return [2, a9("setStatusBar", m39, [o17], (p49 = t6(function(r33) {
        return e15(this, function(o18) {
          switch (o18.label) {
            case 0:
              return o10.isMp ? [4, B2.configHeader({ statusBarType: r33.type, headerColor: r33.color })] : [3, 2];
            case 1:
              return [2, o18.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("setStatusBar", {}))];
          }
        });
      }), function(r33) {
        return p49.apply(this, arguments);
      }))];
      var p49;
    });
  }), u41.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/configAppView.js
var d13 = [Me({ headerTextColor: ot.enum(["black", "white"]).optional(), headerColor: ot.string().optional(), actionBar: Me({ hide: ot.boolean().optional(), title: ot.string().optional(), leftButton: ot.enum(["back", "none"]).optional(), textAlign: ot.enum(["left", "center"]).default("left").optional() }).optional(), statusBarType: ot.enum(["normal", "hidden", "transparent"]).optional(), hideAndroidBottomNavigationBar: ot.boolean().optional(), hideIOSSafeAreaBottom: ot.boolean().optional() })];
function u42(o17) {
  return c37.apply(this, arguments);
}
function c37() {
  return c37 = t6(function(t39) {
    return e15(this, function(n24) {
      return [2, a9("configAppView", d13, [t39], (u48 = t6(function(o17) {
        var t40, n25, r33, d17, u49, c41, p49;
        return e15(this, function(s30) {
          switch (s30.label) {
            case 0:
              return o10.isMp ? u.isUndefined(null === (t40 = o17.actionBar) || void 0 === t40 ? void 0 : t40.leftButton) ? [3, 2] : [4, B2.setNavigationBarLeftButton(null === (c41 = o17.actionBar) || void 0 === c41 ? void 0 : c41.leftButton)] : [3, 13];
            case 1:
              s30.sent(), s30.label = 2;
            case 2:
              return u.isUndefined(null === (n25 = o17.actionBar) || void 0 === n25 ? void 0 : n25.title) || !(null === (r33 = o17.actionBar) || void 0 === r33 ? void 0 : r33.title) ? [3, 4] : [4, B2.setNavigationBarTitle(null === (p49 = o17.actionBar) || void 0 === p49 ? void 0 : p49.title)];
            case 3:
              s30.sent(), s30.label = 4;
            case 4:
              return [4, B2.configHeader({ hideActionBar: null === (d17 = o17.actionBar) || void 0 === d17 ? void 0 : d17.hide, statusBarType: o17.statusBarType, headerTextColor: o17.headerTextColor, headerColor: o17.headerColor, textAlign: null === (u49 = o17.actionBar) || void 0 === u49 ? void 0 : u49.textAlign })];
            case 5:
              s30.sent(), s30.label = 6;
            case 6:
              return s30.trys.push([6, 11, , 12]), "boolean" != typeof o17.hideAndroidBottomNavigationBar ? [3, 8] : [4, B2.setAndroidBottomNavigationBar(o17.hideAndroidBottomNavigationBar)];
            case 7:
              s30.sent(), s30.label = 8;
            case 8:
              return "boolean" != typeof o17.hideIOSSafeAreaBottom ? [3, 10] : [4, B2.setIOSBottomSafeArea(o17.hideIOSSafeAreaBottom)];
            case 9:
              s30.sent(), s30.label = 10;
            case 10:
              return [3, 12];
            case 11:
              return s30.sent(), [3, 12];
            case 12:
              return [2];
            case 13:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("configAppView", {}))];
          }
        });
      }), function(o17) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), c37.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/minimizeApp.js
var e37 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("MA_MENU_MINIMIZE", {}, { success: function(s30) {
        o18(s30);
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/minimizeApp.js
function s20(r33) {
  return m40.apply(this, arguments);
}
function m40() {
  return (m40 = t6(function(s30) {
    return e15(this, function(m51) {
      return [2, a9("minimizeApp", [], [s30], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, e37()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("minimizeApp", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openPermissionSetting.js
var e38 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("MA_MENU_PERMISSION", {}, { success: function(s30) {
        o18(s30);
      }, fail: function(o19) {
        n24(o19);
      } }, {}), o18();
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/openPermissionSetting.js
function i34(e46) {
  return m41.apply(this, arguments);
}
function m41() {
  return (m41 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("openPermissionSetting", [], [i44], t6(function() {
        return e15(this, function(e46) {
          switch (e46.label) {
            case 0:
              return o10.isMp ? [4, e38()] : [3, 2];
            case 1:
              return [2, e46.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openPermissionSetting", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/favoriteApp.js
var n21 = t6(function() {
  return e15(this, function(e46) {
    return [2, new Promise(function(e47, s30) {
      a8("MP_ADD_MYFAVORITES", { timestamp: (/* @__PURE__ */ new Date()).getTime() }, { success: function(t39) {
        e47(t39);
      }, fail: function(e48) {
        s30(e48);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/favoriteApp.js
function i35(r33) {
  return p41.apply(this, arguments);
}
function p41() {
  return (p41 = t6(function(i44) {
    return e15(this, function(p49) {
      return [2, a9("favoriteApp", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, n21()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("favoriteApp", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/openGroupList.js
var e39 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("OPEN_GROUPLIST", {}, { success: function(s30) {
        o18(s30);
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/openGroupList.js
function i36(r33) {
  return p42.apply(this, arguments);
}
function p42() {
  return (p42 = t6(function(i44) {
    return e15(this, function(p49) {
      return [2, a9("openGroupList", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, e39()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("openGroupList", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/requestSendNotification.js
var t34 = t6(function() {
  var e46;
  return e15(this, function(n24) {
    switch (n24.label) {
      case 0:
        return [4, w2.getAccessToken()];
      case 1:
        return e46 = n24.sent(), [2, new Promise(function(o17, n25) {
          a8("MP_SEND_NOTIFICATION", { accessToken: e46 }, { success: function(e47) {
            o17(e47);
          }, fail: function(e47) {
            n25(e47);
          } }, { delay: 500 });
        })];
    }
  });
});

// ../../node_modules/zmp-sdk/apis/apis/requestSendNotification.js
function s21(t39) {
  return m42.apply(this, arguments);
}
function m42() {
  return (m42 = t6(function(s30) {
    return e15(this, function(m51) {
      return [2, a9("requestSendNotification", [], [s30], t6(function() {
        return e15(this, function(t39) {
          switch (t39.label) {
            case 0:
              return o10.isMp ? [4, t34()] : [3, 2];
            case 1:
              return [2, t39.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("requestSendNotification", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/addRating.js
var e40 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, n24) {
      a8("MP_ADD_RATING", {}, { success: function(s30) {
        o18(s30);
      }, fail: function(o19) {
        n24(o19);
      } }, {});
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/addRating.js
function i37(r33) {
  return m43.apply(this, arguments);
}
function m43() {
  return (m43 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("addRating", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, e40()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({})] : [2, Promise.reject(o11("addRating", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/interactOA.js
var o16;
var a29;
var c38 = (o16 = t6(function(e46, t39, o17) {
  var a36, c41;
  return e15(this, function(s30) {
    switch (s30.label) {
      case 0:
        return (a36 = new URLSearchParams()).append("appId", t39), a36.append("accessToken", o17), a36.append("oaId", e46), c41 = { headers: { "Content-Type": "application/x-www-form-urlencoded" } }, [4, fetch(O.CHECK_INTERACT_OA, t9({ method: "POST", body: a36 }, c41))];
      case 1:
        return [4, s30.sent().json()];
      case 2:
        return [2, s30.sent()];
    }
  });
}), function(e46, n24, t39) {
  return o16.apply(this, arguments);
});
var i38 = (a29 = t6(function(e46) {
  return e15(this, function(n24) {
    return [2, new Promise(function(n25, r33) {
      a8("MP_INTERACT_OA", { oaId: e46, forceUpdate: true }, { success: function(e47) {
        n25(e47);
      }, fail: function(e47) {
        r33(e47);
      } }, {});
    })];
  });
}), function(e46) {
  return a29.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/interactOA.js
var l20 = [Me({ oaId: ke() })];
function f9(r33) {
  return d14.apply(this, arguments);
}
function d14() {
  return d14 = t6(function(t39) {
    return e15(this, function(o17) {
      return [2, a9("interactOA", l20, [t39], (f14 = t6(function(r33) {
        var t40, o18, n24;
        return e15(this, function(p49) {
          switch (p49.label) {
            case 0:
              return o10.isMp ? (t40 = r33.oaId, [4, w2.getAccessToken()]) : [3, 5];
            case 1:
              return !(o18 = p49.sent()) || u.isEmpty(o18) ? [3, 3] : [4, c38(t40, I, o18)];
            case 2:
              if (0 === (null == (n24 = p49.sent()) ? void 0 : n24.err)) return [2, Promise.resolve({})];
              p49.label = 3;
            case 3:
              return [4, i38(t40)];
            case 4:
              return [2, p49.sent()];
            case 5:
              return o10.isMpWeb ? [2, Promise.resolve({})] : [2, Promise.reject(o11("interactOA", {}))];
          }
        });
      }), function(r33) {
        return f14.apply(this, arguments);
      }))];
      var f14;
    });
  }), d14.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/checkIsAllowedInteractWithOA.js
var l21 = [Me({ oaId: ke() })];
function p43(r33) {
  return d15.apply(this, arguments);
}
function d15() {
  return d15 = t6(function(t39) {
    return e15(this, function(o17) {
      return [2, a9("isAllowedInteractWithOA", l21, [t39], (p49 = t6(function(r33) {
        var t40, o18, n24;
        return e15(this, function(u48) {
          switch (u48.label) {
            case 0:
              return o10.isMp ? (t40 = r33.oaId, [4, w2.getAccessToken()]) : [3, 4];
            case 1:
              return !(o18 = u48.sent()) || u.isEmpty(o18) ? [3, 3] : [4, c38(t40, I, o18)];
            case 2:
              return n24 = u48.sent(), [2, Promise.resolve({ status: 0 === (null == n24 ? void 0 : n24.err) })];
            case 3:
              return [2, Promise.resolve({ status: false })];
            case 4:
              return o10.isMpWeb ? [2, Promise.resolve({ status: false })] : [2, Promise.reject(o11("IsAllowedInteractWithOA", {}))];
          }
        });
      }), function(r33) {
        return p49.apply(this, arguments);
      }))];
      var p49;
    });
  }), d15.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/getSetting.js
function i39(t39) {
  return m44.apply(this, arguments);
}
function m44() {
  return (m44 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getSetting", [], [i44], t6(function() {
        return e15(this, function(t39) {
          switch (t39.label) {
            case 0:
              return o10.isMp ? [4, m5()] : [3, 2];
            case 1:
              return [2, t39.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({ authSetting: {} })] : [2, Promise.reject(o11("getSetting", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/authorize.js
var c39 = ot.enum(v);
var l22 = [Me({ scopes: ot.array(c39).max(2).optional() }).optional()];
function f10(o17) {
  return j5.apply(this, arguments);
}
function j5() {
  return j5 = t6(function(n24) {
    return e15(this, function(i44) {
      return [2, a9("authorize", l22, [n24], (u48 = t6(function(o17) {
        var e46;
        return e15(this, function(n25) {
          switch (n25.label) {
            case 0:
              return o10.isMp ? (e46 = null == o17 ? void 0 : o17.scopes, m4(724, 573) ? [2, Promise.reject(d6.error.clientNotSupport())] : [4, r14(null != e46 ? e46 : void 0)]) : [3, 2];
            case 1:
              return [2, n25.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({})] : [2, Promise.reject(o11("authorize", {}))];
          }
        });
      }), function(o17) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), j5.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/checkZaloCameraPermission.js
var s22 = t6(function() {
  return e15(this, function(s30) {
    return [2, new Promise((o17 = t6(function(r33, s31) {
      var o18;
      return e15(this, function(n24) {
        switch (n24.label) {
          case 0:
            return u.isUndefined(ZaloJavaScriptInterface) || "function" != typeof ZaloJavaScriptInterface.checkCameraPermission ? [3, 2] : [4, ZaloJavaScriptInterface.checkCameraPermission()];
          case 1:
            return o18 = n24.sent(), [2, r33({ userAllow: "grant" === o18 })];
          case 2:
            return s31(d6.error.clientNotSupport()), [2];
        }
      });
    }), function(r33, e46) {
      return o17.apply(this, arguments);
    }))];
    var o17;
  });
});

// ../../node_modules/zmp-sdk/apis/apis/checkZaloCameraPermission.js
function t35(t39) {
  return a9("checkZaloCameraPermission", [], [t39], t6(function() {
    return e15(this, function(r33) {
      switch (r33.label) {
        case 0:
          return [4, s22()];
        case 1:
          return [2, r33.sent()];
      }
    });
  }));
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getUserID.js
var t36 = t6(function() {
  return e15(this, function(t39) {
    return [2, new Promise((s30 = t6(function(r33, t40) {
      var s31, o17, i44;
      return e15(this, function(n24) {
        switch (n24.label) {
          case 0:
            return n24.trys.push([0, 2, , 3]), [4, w2.jumpAndGetToken()];
          case 1:
            return n24.sent(), o17 = null === (s31 = w2.miniProgramConfig) || void 0 === s31 ? void 0 : s31.userId, r33(o17), [3, 3];
          case 2:
            return i44 = n24.sent(), t40(i44), [3, 3];
          case 3:
            return [2];
        }
      });
    }), function(r33, e46) {
      return s30.apply(this, arguments);
    }))];
    var s30;
  });
});

// ../../node_modules/zmp-sdk/apis/apis/getUserID.js
function i40(r33) {
  return m45.apply(this, arguments);
}
function m45() {
  return (m45 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getUserID", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, t36()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve(void 0)] : [2, Promise.reject(o11("getUserID", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/getIDToken.js
var t37 = t6(function() {
  return e15(this, function(t39) {
    return [2, new Promise((s30 = t6(function(r33, t40) {
      var s31, o17, i44;
      return e15(this, function(n24) {
        switch (n24.label) {
          case 0:
            return n24.trys.push([0, 2, , 3]), [4, w2.jumpAndGetToken()];
          case 1:
            return n24.sent(), o17 = null === (s31 = w2.miniProgramConfig) || void 0 === s31 ? void 0 : s31.idToken, r33(o17), [3, 3];
          case 2:
            return i44 = n24.sent(), t40(i44), [3, 3];
          case 3:
            return [2];
        }
      });
    }), function(r33, e46) {
      return s30.apply(this, arguments);
    }))];
    var s30;
  });
});

// ../../node_modules/zmp-sdk/apis/apis/getIDToken.js
function i41(e46) {
  return m46.apply(this, arguments);
}
function m46() {
  return (m46 = t6(function(i44) {
    return e15(this, function(m51) {
      return [2, a9("getIDToken", [], [i44], t6(function() {
        return e15(this, function(e46) {
          switch (e46.label) {
            case 0:
              return o10.isMp ? [4, t37()] : [3, 2];
            case 1:
              return [2, e46.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve(void 0)] : [2, Promise.reject(o11("getIDToken", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/media/zmaCamera.js
var l23 = { format: n.JPEG, quality: t2.NORMAL, mirrored: false, useVideoSourceSize: false };
var h9 = { width: 1024, height: 768, facingMode: e2.FRONT, mirrored: false, audio: false, video: true };
var v6 = function() {
  function o17(e46) {
    var n24, r33, c41, d17, u48, l27 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null, v8 = this;
    a2(this, o17), this._videoEnabled = true, this._audioEnabled = null, this.tick = function() {
      if (v8.haveVideoStream() && v8._userRequestFrameCallback) {
        var e47 = v8.getCanvas();
        if (v8.drawFrame(), e47) {
          var t39 = { data: e47.toDataURL(n.PNG), width: e47.width, height: e47.height };
          f2.getInstance().emit(a.OnFrameCallback, 0, t39), requestAnimationFrame(v8.tick);
        }
      }
    }, this._videoElement = e46, this._mediaConstraints = null != l27 ? l27 : null, this._facingMode = null !== (n24 = null == l27 ? void 0 : l27.facingMode) && void 0 !== n24 ? n24 : h9.facingMode, this._cameraList = [], this._stream = null, this._selectedDeviceId = null !== (r33 = null == l27 ? void 0 : l27.deviceId) && void 0 !== r33 ? r33 : "", this._canvasElement = null, this._ctx = null, this._hasUserMedia = false, this._mirror = null !== (c41 = null == l27 ? void 0 : l27.mirrored) && void 0 !== c41 ? c41 : h9.mirrored, this._userRequestFrameCallback = false, this._audioEnabled = null !== (d17 = null == l27 ? void 0 : l27.audio) && void 0 !== d17 ? d17 : h9.audio, this._videoEnabled = null !== (u48 = null == l27 ? void 0 : l27.video) && void 0 !== u48 ? u48 : h9.video;
  }
  var v7 = o17.prototype;
  return v7.on = function(e46, t39) {
    f2.getInstance().on(e46, function(e47) {
      t39(e47);
    }), e46 === a.OnFrameCallback && (this._userRequestFrameCallback = true, this.tick());
  }, v7.off = function(e46) {
    f2.getInstance().off(e46), e46 === a.OnFrameCallback && (this._userRequestFrameCallback = false);
  }, v7.getQualityNumber = function(e46) {
    switch (e46) {
      case "high":
        return 0.9;
      case "normal":
      default:
        return 0.6;
      case "low":
        return 0.2;
    }
  }, v7.getFacingMode = function() {
    return this._facingMode;
  }, v7.getCameraList = function() {
    return this._cameraList;
  }, v7.getCameraCount = function() {
    return this._cameraList.length;
  }, v7.getSelectedDeviceId = function() {
    return this._selectedDeviceId;
  }, v7.setDeviceId = function(t39) {
    var i44 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return i44._selectedDeviceId = t39, i44._hasUserMedia ? [4, i44.stream()] : [3, 2];
          case 1:
            e46.sent(), e46.label = 2;
          case 2:
            return [2];
        }
      });
    })();
  }, v7.getVideoInputs = function(e46) {
    var t39 = this;
    return this._cameraList = [], e46.forEach(function(e47) {
      "videoinput" === e47.kind && t39._cameraList.push(e47);
    }), this._cameraList;
  }, v7.updateMediaConstraints = function(t39) {
    var i44 = this;
    return t6(function() {
      var e46, a36, r33, s30, o18, c41;
      return e15(this, function(d17) {
        switch (d17.label) {
          case 0:
            return u.isEmpty(t39) ? [2] : JSON.stringify(t39) !== JSON.stringify(i44._mediaConstraints) ? (i44._mediaConstraints = t39, i44._facingMode = null !== (e46 = null == t39 ? void 0 : t39.facingMode) && void 0 !== e46 ? e46 : i44._facingMode, i44._selectedDeviceId = null !== (a36 = null == t39 ? void 0 : t39.deviceId) && void 0 !== a36 ? a36 : i44._selectedDeviceId, i44._mirror = null !== (r33 = null == t39 ? void 0 : t39.mirrored) && void 0 !== r33 ? r33 : i44._mirror, i44._audioEnabled = null !== (s30 = null == t39 ? void 0 : t39.audio) && void 0 !== s30 ? s30 : i44._audioEnabled, i44._videoEnabled = null !== (o18 = null == t39 ? void 0 : t39.video) && void 0 !== o18 ? o18 : i44._videoEnabled, i44._selectedDeviceId = null !== (c41 = null == t39 ? void 0 : t39.deviceId) && void 0 !== c41 ? c41 : i44._selectedDeviceId, i44._hasUserMedia ? [4, i44.stream()] : [3, 2]) : [2];
          case 1:
            d17.sent(), d17.label = 2;
          case 2:
            return [2];
        }
      });
    })();
  }, v7.getCurrentMediaConstraints = function() {
    var e46 = this._mediaConstraints, t39 = {};
    this._videoElement.width = (null == e46 ? void 0 : e46.width) || this._videoElement.width || h9.width, this._videoElement.height = (null == e46 ? void 0 : e46.height) || this._videoElement.height || h9.height;
    var i44 = false !== this._videoEnabled, n24 = false !== this._audioEnabled;
    return i44 && ("" == this._selectedDeviceId && null !== this._facingMode ? t39.facingMode = this._facingMode : t39.deviceId = { exact: this._selectedDeviceId }, t39.width = { exact: this._videoElement.width }, t39.height = { exact: this._videoElement.height }), { video: !!i44 && t39, audio: n24 };
  }, v7.selectCamera = function() {
    var e46 = true, t39 = false, i44 = void 0;
    try {
      for (var n24, a36 = this._cameraList[Symbol.iterator](); !(e46 = (n24 = a36.next()).done); e46 = true) {
        var s30 = n24.value;
        if (this._facingMode == e2.FRONT && s30.label.toLowerCase().includes("front") || this._facingMode == e2.BACK && s30.label.toLowerCase().includes("back")) {
          this._selectedDeviceId = s30.deviceId;
          break;
        }
      }
    } catch (e47) {
      t39 = true, i44 = e47;
    } finally {
      try {
        e46 || null == a36.return || a36.return();
      } finally {
        if (t39) throw i44;
      }
    }
  }, v7.flip = function() {
    var t39 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return t39._facingMode = t39._facingMode == e2.FRONT ? e2.BACK : e2.FRONT, t39._videoElement.style.transform = "", t39.selectCamera(), t39._hasUserMedia ? [4, t39.stream()] : [3, 2];
          case 1:
            e46.sent(), e46.label = 2;
          case 2:
            return [2];
        }
      });
    })();
  }, v7.start = function() {
    var t39 = this;
    return t6(function() {
      var n24, r33;
      return e15(this, function(s30) {
        return n24 = f2.getInstance(), r33 = function(e46) {
          try {
            t39._hasUserMedia && t39.stop();
          } catch (e47) {
          }
        }, n24.off(o5.AppPaused, r33), n24.on(o5.AppPaused, r33), [2, new Promise((o18 = t6(function(e46, n25) {
          var r34;
          return e15(this, function(s31) {
            switch (s31.label) {
              case 0:
                return s31.trys.push([0, 2, , 3]), [4, t39.stream()];
              case 1:
                return s31.sent(), f2.getInstance().emit(a.OnStartCallback, 0), e46(t39._selectedDeviceId), [3, 3];
              case 2:
                return r34 = s31.sent(), n25(r34), [3, 3];
              case 3:
                return [2];
            }
          });
        }), function(e46, t40) {
          return o18.apply(this, arguments);
        }))];
        var o18;
      });
    })();
  }, v7.info = function() {
    var t39 = this;
    return t6(function() {
      return e15(this, function(e46) {
        return [2, new Promise(function(e47, i44) {
          navigator.mediaDevices.enumerateDevices().then(function(i45) {
            t39.getVideoInputs(i45), e47(t39._cameraList);
          }).catch(function(e48) {
            i44(e48);
          });
        })];
      });
    })();
  }, v7.stream = function() {
    var t39 = this;
    return t6(function() {
      return e15(this, function(i44) {
        return [2, new Promise(function(i45, n24) {
          try {
            t39._stop();
            var a36 = t39.getCurrentMediaConstraints();
            if (false === a36.video && !a36.audio) return void i45(t39._facingMode);
            navigator.mediaDevices.getUserMedia(a36).then((r33 = t6(function(e46) {
              return e15(this, function(n25) {
                switch (n25.label) {
                  case 0:
                    return [4, t39.info()];
                  case 1:
                    return n25.sent(), t39.selectCamera(), t39.handleUserMedia(null, e46), i45(t39._facingMode), [2];
                }
              });
            }), function(e46) {
              return r33.apply(this, arguments);
            })).catch(function(e46) {
              t39.handleUserMedia(e46), n24(e46);
            });
          } catch (e46) {
            n24(e46);
          }
          var r33;
        })];
      });
    })();
  }, v7.handleUserMedia = function(e46, t39) {
    var i44 = this;
    if (!e46 && t39) {
      this._stream = t39;
      try {
        this._videoElement && (this._videoElement.srcObject = t39, (this._facingMode == e2.FRONT || this._mirror) && (this._videoElement.style.transform = "scale(-1,1)"), this._videoElement.onloadedmetadata = function() {
          i44._userRequestFrameCallback && i44.tick();
        }, this._videoElement.play().then(function() {
          i44._hasUserMedia = true;
        }).catch(function(e47) {
          i44._hasUserMedia = false;
        }));
      } catch (e47) {
        this._hasUserMedia = false;
      }
    } else this._hasUserMedia = false;
  }, v7.updateTrackEnable = function(t39, i44) {
    var n24 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            if (t39 === o.VIDEO) {
              if (n24._videoEnabled === i44) return [2];
              n24._videoEnabled = i44;
            } else {
              if (n24._audioEnabled === i44) return [2];
              n24._audioEnabled = i44;
            }
            return [4, n24.stream()];
          case 1:
            return e46.sent(), [2];
        }
      });
    })();
  }, v7.pause = function(t39) {
    var i44 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return [4, i44.updateTrackEnable(t39, false)];
          case 1:
            return e46.sent(), [2];
        }
      });
    })();
  }, v7.resume = function(t39) {
    var i44 = this;
    return t6(function() {
      return e15(this, function(e46) {
        switch (e46.label) {
          case 0:
            return [4, i44.updateTrackEnable(t39, true)];
          case 1:
            return e46.sent(), [2];
        }
      });
    })();
  }, v7.isUsing = function() {
    return this._hasUserMedia;
  }, v7.stop = function() {
    this._stop(), f2.getInstance().emit(a.OnStopCallback, 0);
  }, v7._stop = function() {
    o17.stopMediaStream(this._stream), this._hasUserMedia = false;
  }, v7.setFacingMode = function(e46) {
    this._facingMode = e46;
  }, v7.setMirror = function(e46) {
    this._mirror = e46, this._videoElement && (this._videoElement.style.transform = e46 ? "scale(-1,1)" : "");
  }, v7.takePhoto = function(e46) {
    if (!this._hasUserMedia) return null;
    var t39 = e46 || {}, i44 = this.getCanvas(t39);
    if (this.drawFrame(t39), i44) {
      var n24 = t39.format || l23.format;
      return { data: i44.toDataURL(n24, this.getQualityNumber(t39.quality)), width: i44.width, height: i44.height };
    }
    return null;
  }, v7.haveVideoStream = function() {
    var e46 = this._videoElement;
    return e46 && e46.videoHeight > 0 && this._hasUserMedia;
  }, v7.getCanvas = function(e46) {
    var t39 = this._videoElement, i44 = e46 || {};
    if (!this.haveVideoStream()) return null;
    if (!this._ctx) {
      var n24 = t39.videoWidth, a36 = t39.videoHeight;
      if (!i44.useVideoSourceSize) {
        var r33 = n24 / a36;
        a36 = (n24 = i44.minScreenshotWidth || t39.clientWidth) / r33, i44.minScreenshotHeight && a36 < i44.minScreenshotHeight && (n24 = (a36 = i44.minScreenshotHeight) * r33);
      }
      this._canvasElement = document.createElement("canvas"), this._canvasElement.width = (null == i44 ? void 0 : i44.width) || n24, this._canvasElement.height = (null == i44 ? void 0 : i44.height) || a36, this._ctx = this._canvasElement.getContext("2d");
    }
    return this._canvasElement;
  }, v7.drawFrame = function(e46) {
    var t39 = this._videoElement, i44 = e46 || {};
    if (t39 && this._ctx && this._canvasElement) {
      var n24 = this._canvasElement, a36 = this._ctx;
      n24.width = (null == i44 ? void 0 : i44.width) || n24.width, n24.height = (null == i44 ? void 0 : i44.height) || n24.height, ((null == i44 ? void 0 : i44.mirrored) || this._mirror || this._facingMode === e2.FRONT) && (a36.translate(n24.width, 0), a36.scale(-1, 1)), a36.drawImage(t39, 0, 0, n24.width, n24.height), ((null == i44 ? void 0 : i44.mirrored) || this._mirror || this._facingMode === e2.FRONT) && (a36.scale(-1, 1), a36.translate(-n24.width, 0));
    }
  }, o17.stopMediaStream = function(e46) {
    e46 && (e46.getVideoTracks && e46.getAudioTracks ? (e46.getVideoTracks().map(function(t39) {
      e46.removeTrack(t39), t39.stop();
    }), e46.getAudioTracks().map(function(t39) {
      e46.removeTrack(t39), t39.stop();
    })) : e46.stop());
  }, o17;
}();

// ../../node_modules/zmp-sdk/apis/apis/createCameraContext.js
var t38 = Me({ play: ot.function(), pause: ot.function() }).refine(function(o17) {
  return "play" in o17 && "pause" in o17;
}, { message: "Invalid HTMLVideoElement" });
var a30 = [Me({ videoElement: t38, mediaConstraints: ot.object({ width: ot.number().min(100).optional(), height: ot.number().min(100).optional(), facingMode: ot.string().optional(), audio: ot.boolean().optional(), video: ot.boolean().optional(), deviceId: ot.string().optional(), mirrored: ot.boolean().optional() }).nullish() }).optional()];
function r30(o17) {
  return a9("createCameraContext", a30, [o17], function(o18) {
    return new v6(o18.videoElement, o18.mediaConstraints);
  });
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/adtimaAd.js
var n22 = function() {
  function n24() {
    var o17, t40;
    a2(this, n24), this.sdkLoaded = false, this.queue = [];
    var s30 = window.AdtimaZMA = { config: { cateId: null === (o17 = window.zmpGlobal) || void 0 === o17 || null === (t40 = o17.appInfo) || void 0 === t40 ? void 0 : t40.cateType, appId: window.APP_ID }, queue: [], load: function(e46, o18) {
      s30.queue = a5(s30.queue).concat([[e46, o18]]);
    } };
  }
  var t39 = n24.prototype;
  return t39.loadSDK = function() {
    var e46 = this, i44 = document.createElement("script");
    i44.src = "https://adtima-static.zascdn.me/resource/js/zma/sdk.js?q=".concat(Math.floor((/* @__PURE__ */ new Date()).getTime() / 864e5)), i44.onload = function() {
      e46.sdkLoaded = true, e46.processQueue();
    }, i44.onerror = function() {
      e46.sdkLoaded = true;
    }, document.body.appendChild(i44);
  }, t39.load = function(e46, i44) {
    var n25;
    u.isFunction(null === (n25 = window.AdtimaZMA) || void 0 === n25 ? void 0 : n25.load) && window.AdtimaZMA.load(e46, i44);
  }, t39.display = function(e46) {
    var i44;
    this.sdkLoaded ? u.isFunction(null === (i44 = window.AdtimaZMA) || void 0 === i44 ? void 0 : i44.display) && window.AdtimaZMA.display(e46) : this.queue.push(this.display.bind(this, e46));
  }, t39.refresh = function(e46) {
    var i44;
    this.sdkLoaded ? u.isFunction(null === (i44 = window.AdtimaZMA) || void 0 === i44 ? void 0 : i44.refresh) && window.AdtimaZMA.refresh(e46) : this.queue.push(this.refresh.bind(this, e46));
  }, t39.processQueue = function() {
    for (; this.queue.length > 0; ) {
      var e46 = this.queue.shift();
      e46 && e46();
    }
  }, n24.getInstance = function() {
    return n24.instance || (n24.instance = new n24()), n24.instance;
  }, n24;
}();

// ../../node_modules/zmp-sdk/apis/apis/adtimaAd/setup.js
function e41(t39) {
  return s23.apply(this, arguments);
}
function s23() {
  return (s23 = t6(function(t39) {
    return e15(this, function(n24) {
      return [2, a9("setupAd", [], [t39], function() {
        return n22.getInstance().loadSDK();
      })];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/adtimaAd/load.js
var e42 = [Me({ ids: ot.union([ot.string(), ot.array(ot.string())]), config: Me({ display: ot.boolean().optional(), onClose: ot.function().optional() }).optional() })];
function a31(o17) {
  return l24.apply(this, arguments);
}
function l24() {
  return l24 = t6(function(n24) {
    return e15(this, function(t39) {
      return [2, a9("loadAd", e42, [n24], (a36 = t6(function(o17) {
        return e15(this, function(n25) {
          return n22.getInstance().load(o17.ids, o17.config), [2];
        });
      }), function(o17) {
        return a36.apply(this, arguments);
      }))];
      var a36;
    });
  }), l24.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/adtimaAd/display.js
var e43 = [Me({ id: ot.string() })];
function m47(t39) {
  return u43.apply(this, arguments);
}
function u43() {
  return u43 = t6(function(r33) {
    return e15(this, function(n24) {
      return [2, a9("displayAd", e43, [r33], (m51 = t6(function(t39) {
        return e15(this, function(r34) {
          return n22.getInstance().display(t39.id), [2];
        });
      }), function(t39) {
        return m51.apply(this, arguments);
      }))];
      var m51;
    });
  }), u43.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/adtimaAd/refresh.js
var s24 = [Me({ id: ot.string().optional() }).optional()];
function m48(r33) {
  return u44.apply(this, arguments);
}
function u44() {
  return u44 = t6(function(t39) {
    return e15(this, function(o17) {
      return [2, a9("refreshAd", s24, [t39], (m51 = t6(function(r33) {
        return e15(this, function(t40) {
          return n22.getInstance().refresh(r33.id), [2];
        });
      }), function(r33) {
        return m51.apply(this, arguments);
      }))];
      var m51;
    });
  }), u44.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/showOAWidget.js
var h10 = [Me({ id: ke(), guidingText: ke().optional(), color: ke().optional(), onStatusChange: ot.function().optional(), onError: ot.function().optional() }).catchall(ot.any())];
function f11(o17) {
  return g6.apply(this, arguments);
}
function g6() {
  return g6 = t6(function(n24) {
    return e15(this, function(r33) {
      return [2, a9("showOAWidget", h10, [n24], (i44 = t6(function(n25) {
        var r34, i45, c41, h11, f14, g8, j6, w7;
        return e15(this, function(y8) {
          if (o10.isMp) {
            try {
              r34 = document.getElementById(n25.id), i45 = n25.onStatusChange, c41 = n25.guidingText, h11 = n25.color, f14 = t(n25, ["onStatusChange", "guidingText", "color"]), r34 && (g8 = document.createElement("iframe"), j6 = "ifOAF-".concat(n25.id, "-").concat(Date.now()), (w7 = new URL(g.OA)).searchParams.set("id", j6), w7.searchParams.set("appId", window.APP_ID), c41 && w7.searchParams.set("guidingText", c41), h11 && w7.searchParams.set("color", h11), Object.entries(f14).forEach(function(o17) {
                var e46 = e6(o17, 2), n26 = e46[0], r35 = e46[1];
                r35 && w7.searchParams.set("data-".concat(n26), String(r35));
              }), g8.id = j6, g8.src = w7.href, g8.style.width = "100%", g8.style.height = "100%", g8.style.border = "none", r34.innerHTML = g8.outerHTML, window.addEventListener("message", function() {
                var e46 = t6(function(o17) {
                  var e47, t39, n26, r35, s30, a36, c42, d17;
                  return e15(this, function(p49) {
                    switch (p49.label) {
                      case 0:
                        return e47 = o17.data, o17.origin.startsWith(k) ? (t39 = document.getElementById(j6), n26 = e47.type, (r35 = new URL(e47.href)).origin !== w7.origin || r35.pathname !== w7.pathname || e47.id !== j6 ? [3, 3] : "followOA" !== n26 && "getOAInfo" !== n26 ? [3, 2] : [4, w2.jumpAndGetToken()]) : [3, 3];
                      case 1:
                        return p49.sent(), c42 = null === (s30 = w2.miniProgramConfig) || void 0 === s30 ? void 0 : s30.jwt, null == t39 || null === (a36 = t39.contentWindow) || void 0 === a36 || a36.postMessage({ type: n26, payload: { jwt: c42 } }, k), [3, 3];
                      case 2:
                        "oaInfo" === n26 ? null == i45 || i45(null === (d17 = e47.payload) || void 0 === d17 ? void 0 : d17.followed) : "openOA" === n26 ? i13(e47.payload.id, "oa") : "heightChange" === n26 && (t39.style.height = "".concat(e47.payload, "px")), p49.label = 3;
                      case 3:
                        return [2];
                    }
                  });
                });
                return function(o17) {
                  return e46.apply(this, arguments);
                };
              }(), false));
            } catch (o17) {
              console.log(o17);
            }
            return [2, Promise.resolve()];
          }
          return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("showOAWidget", {}))];
        });
      }), function(o17) {
        return i44.apply(this, arguments);
      }))];
      var i44;
    });
  }), g6.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/showFunctionButtonWidget.js
var p44 = [ot.object({ id: ot.string(), type: ot.string(), text: ot.string(), color: ot.string().optional(), disabled: ot.boolean().optional(), onDataReceived: ot.function().optional(), onError: ot.function().optional() }).catchall(ot.any())];
function f12(t39) {
  return y6.apply(this, arguments);
}
function y6() {
  return y6 = t6(function(r33) {
    return e15(this, function(f14) {
      return [2, a9("showFunctionButtonWidget", p44, [r33], (y8 = t6(function(r34) {
        var a36, p49, f15, y9, h11, j6, v7;
        return e15(this, function(g8) {
          if (o10.isMp) {
            try {
              a36 = document.getElementById(r34.id), p49 = r34.onDataReceived, f15 = r34.onError, y9 = t(r34, ["onDataReceived", "onError"]), a36 && (h11 = document.createElement("iframe"), j6 = "ifFBW-".concat(r34.id, "-").concat(Date.now()), (v7 = new URL(g.FUNCTION_BUTTON)).searchParams.set("id", j6), v7.searchParams.set("appId", window.APP_ID), Object.entries(y9).forEach(function(t39) {
                var o17 = e6(t39, 2), n24 = o17[0], r35 = o17[1];
                r35 && v7.searchParams.set("data-".concat(n24), String(r35));
              }), h11.id = j6, h11.src = v7.href, h11.style.width = "100%", h11.style.height = "100%", h11.style.border = "none", Object.entries(y9).forEach(function(t39) {
                var o17 = e6(t39, 2), n24 = o17[0], r35 = o17[1];
                r35 && h11.setAttribute("data-".concat(n24), String(r35));
              }), a36.innerHTML = h11.outerHTML, window.addEventListener("message", function() {
                var o17 = t6(function(t39) {
                  var o18, e46, r35, i44, s30, a37, d17, h12, g9, w7, _6, b5, E5;
                  return e15(this, function(m51) {
                    switch (m51.label) {
                      case 0:
                        return o18 = t39.data, t39.origin.startsWith(k) ? (e46 = document.getElementById(j6), r35 = o18.type, (i44 = new URL(o18.href)).origin !== v7.origin || i44.pathname !== v7.pathname || o18.id !== j6 ? [3, 4] : "buttonClicked" !== r35 ? [3, 3] : [4, w2.jumpAndGetToken()]) : [3, 4];
                      case 1:
                        return m51.sent(), h12 = null === (s30 = w2.miniProgramConfig) || void 0 === s30 ? void 0 : s30.jwt, g9 = null === (a37 = w2.miniProgramConfig) || void 0 === a37 ? void 0 : a37.encryptKey, [4, c6()];
                      case 2:
                        return w7 = m51.sent(), null == e46 || null === (d17 = e46.contentWindow) || void 0 === d17 || d17.postMessage({ type: r35, payload: { jwt: h12, encryptKey: g9, accessToken: w7 } }, k), [3, 4];
                      case 3:
                        "onSuccess" === r35 ? (null == (_6 = o18.payload) ? void 0 : _6.type) === y9.type && (b5 = { callAction: function(t40, o19) {
                          var n24;
                          null == e46 || null === (n24 = e46.contentWindow) || void 0 === n24 || n24.postMessage({ type: "context.callAction", payload: { actionName: t40, actionData: o19 } }, k);
                        } }, null == p49 || p49.apply(void 0, a5(Object.values(_6)).concat([b5]))) : "onError" === r35 && (null == (E5 = o18.payload) ? void 0 : E5.type) === y9.type && (null == f15 || f15(null == E5 ? void 0 : E5.data)), m51.label = 4;
                      case 4:
                        return [2];
                    }
                  });
                });
                return function(t39) {
                  return o17.apply(this, arguments);
                };
              }(), false));
            } catch (t39) {
              console.log(t39);
            }
            return [2, Promise.resolve()];
          }
          return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("showFunctionButtonWidget", {}))];
        });
      }), function(t39) {
        return y8.apply(this, arguments);
      }))];
      var y8;
    });
  }), y6.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/scanNFC.js
var i42;
var u45 = (i42 = t6(function(d17, i44) {
  var u48;
  return e15(this, function(o17) {
    return u48 = {}, "cccd" === d17 && (u48 = { mrz: null == i44 ? void 0 : i44.mrz }), [2, new Promise(function(o18, i45) {
      a8("SCAN_NFC", { type: l2[d17], data: u48 }, { success: function(l27) {
        if ("cccd" === d17) {
          var n24, i46, u49, g8, e46, t39, s30, r33, v7, c41, m51, a36, f14, p49, A5, C5, _6, h11, j6, y8, R3, b5 = { dg1: null !== (n24 = l27.dg1) && void 0 !== n24 ? n24 : "", dg2: null !== (i46 = l27.dg2) && void 0 !== i46 ? i46 : "", dg3: null !== (u49 = l27.dg3) && void 0 !== u49 ? u49 : "", dg4: null !== (g8 = l27.dg4) && void 0 !== g8 ? g8 : "", dg5: null !== (e46 = l27.dg5) && void 0 !== e46 ? e46 : "", dg6: null !== (t39 = l27.dg6) && void 0 !== t39 ? t39 : "", dg7: null !== (s30 = l27.dg7) && void 0 !== s30 ? s30 : "", dg8: null !== (r33 = l27.dg8) && void 0 !== r33 ? r33 : "", dg9: null !== (v7 = l27.dg9) && void 0 !== v7 ? v7 : "", dg10: null !== (c41 = l27.dg10) && void 0 !== c41 ? c41 : "", dg11: null !== (m51 = l27.dg11) && void 0 !== m51 ? m51 : "", dg12: null !== (a36 = l27.dg12) && void 0 !== a36 ? a36 : "", dg13: null !== (f14 = l27.dg13) && void 0 !== f14 ? f14 : "", dg14: null !== (p49 = l27.dg14) && void 0 !== p49 ? p49 : "", dg15: null !== (A5 = l27.dg15) && void 0 !== A5 ? A5 : "", dg16: null !== (C5 = l27.dg16) && void 0 !== C5 ? C5 : "", com: null !== (_6 = l27.com) && void 0 !== _6 ? _6 : "", sod: null !== (h11 = l27.sod) && void 0 !== h11 ? h11 : "", challenge: null !== (j6 = l27.challenge) && void 0 !== j6 ? j6 : "", aAResult: null !== (y8 = l27.aAResult) && void 0 !== y8 ? y8 : "", eACCAResult: null !== (R3 = l27.eACCAResult) && void 0 !== R3 ? R3 : "" };
          o18(b5);
        } else o18({});
      }, fail: function(d18) {
        i45(d18);
      } }, { timeout: false });
    })];
  });
}), function(d17, l27) {
  return i42.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/scanNFC.js
var u46 = [Me({ type: ot.enum(["cccd"]), data: Me({ mrz: ke().max(90) }) })];
function l25(r33) {
  return f13.apply(this, arguments);
}
function f13() {
  return f13 = t6(function(o17) {
    return e15(this, function(t39) {
      return [2, a9("scanNFC", u46, [o17], (e46 = t6(function(r33) {
        var o18, t40;
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? m4(754, 620) ? [2, Promise.reject(d6.error.clientNotSupport())] : (o18 = r33.type, t40 = r33.data, [4, u45(o18, t40)]) : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({})] : [2, Promise.reject(o11("scanNFC", {}))];
          }
        });
      }), function(r33) {
        return e46.apply(this, arguments);
      }))];
      var e46;
    });
  }), f13.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/common/apis/general/checkNFC.js
var e44 = t6(function() {
  return e15(this, function(o17) {
    return [2, new Promise(function(o18, t39) {
      a8("CHECK_NFC", {}, { success: function(s30) {
        o18(s30);
      }, fail: function(o19) {
        t39(o19);
      } }, { timeout: false });
    })];
  });
});

// ../../node_modules/zmp-sdk/apis/apis/checkNFC.js
function i43(r33) {
  return c40.apply(this, arguments);
}
function c40() {
  return (c40 = t6(function(i44) {
    return e15(this, function(c41) {
      return [2, a9("checkNFC", [], [i44], t6(function() {
        return e15(this, function(r33) {
          switch (r33.label) {
            case 0:
              return o10.isMp ? [4, e44()] : [3, 2];
            case 1:
              return [2, r33.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve({})] : [2, Promise.reject(o11("checkNFC", {}))];
          }
        });
      }))];
    });
  })).apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/native-storage/getItem.js
var s25 = [ot.string().trim().min(1)];
function p45(n24) {
  return a9("getItem", s25, [n24], function() {
    for (var t39 = arguments.length, n25 = new Array(t39), s30 = 0; s30 < t39; s30++) n25[s30] = arguments[s30];
    if (o10.isMp || o10.isMpWeb) {
      if (m4(757, 625)) throw d6.error.clientNotSupport();
      return F3.getItem(n25[0]);
    }
    throw o11("getItem", {});
  });
}

// ../../node_modules/zmp-sdk/apis/apis/native-storage/setItem.js
var s26 = [ot.string().trim().min(1), ot.string().trim()];
function p46(m51, p49) {
  return a9("setItem", s26, [m51, p49], function() {
    for (var t39 = arguments.length, m52 = new Array(t39), s30 = 0; s30 < t39; s30++) m52[s30] = arguments[s30];
    if (o10.isMp || o10.isMpWeb) {
      if (m4(757, 625)) throw d6.error.clientNotSupport();
      return F3.setItem(m52[0], m52[1]);
    }
    throw o11("setItem", {});
  });
}

// ../../node_modules/zmp-sdk/apis/apis/native-storage/clear.js
function n23() {
  return a9("clear", [], [], function() {
    if (o10.isMp || o10.isMpWeb) {
      if (m4(757, 625)) throw d6.error.clientNotSupport();
      return F3.clearStorageSync();
    }
    throw o11("clear", {});
  });
}

// ../../node_modules/zmp-sdk/apis/apis/native-storage/removeItem.js
var s27 = [ot.string().trim().min(1)];
function p47(t39) {
  return a9("removeItem", s27, [t39], function() {
    for (var m51 = arguments.length, t40 = new Array(m51), s30 = 0; s30 < m51; s30++) t40[s30] = arguments[s30];
    if (o10.isMp || o10.isMpWeb) {
      if (m4(757, 625)) throw d6.error.clientNotSupport();
      return F3.removeItem(t40[0]);
    }
    throw o11("removeItem", {});
  });
}

// ../../node_modules/zmp-sdk/apis/apis/native-storage/getStorageInfoSync.js
function m49() {
  return a9("getStorageInfoSync", [], [], function() {
    if (o10.isMp || o10.isMpWeb) {
      if (m4(757, 625)) throw d6.error.clientNotSupport();
      return F3.getStorageInfoSync();
    }
    throw o11("getStorageInfoSync", {});
  });
}

// ../../node_modules/zmp-sdk/apis/apis/native-storage/index.js
var I5 = { getItem: p45, setItem: p46, clear: n23, removeItem: p47, getStorageInfo: m49 };

// ../../node_modules/zmp-sdk/apis/apis/payment/index.js
var a32 = { purchase: u35, createOrder: d11, checkTransaction: u36, createOrderIAP: u38, selectPaymentMethod: p35 };
var m50 = a32;

// ../../node_modules/zmp-sdk/apis/common/apis/general/saveFile.js
var s28;
var e45 = (s28 = t6(function(n24, s30) {
  return e15(this, function(r33) {
    return [2, new Promise(function(r34, e46) {
      a8("SAVE_FILE", { fileUrl: n24, onProgress: s30 }, { success: function(n25) {
        r34();
      }, fail: function(n25) {
        e46(n25);
      } }, {});
    })];
  });
}), function(n24, o17) {
  return s28.apply(this, arguments);
});

// ../../node_modules/zmp-sdk/apis/apis/downloadFile.js
var l26 = [Me({ url: ot.string().url() }).optional()];
function u47(r33) {
  return a33.apply(this, arguments);
}
function a33() {
  return a33 = t6(function(o17) {
    return e15(this, function(e46) {
      return [2, a9("downloadFile", l26, [o17], (u48 = t6(function(r33) {
        var o18;
        return e15(this, function(e47) {
          switch (e47.label) {
            case 0:
              return o10.isMp ? (o18 = r33.url, [4, e45(o18, r33.onProgress)]) : [3, 2];
            case 1:
              return [2, e47.sent()];
            case 2:
              return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("downloadFile", {}))];
          }
        });
      }), function(r33) {
        return u48.apply(this, arguments);
      }))];
      var u48;
    });
  }), a33.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/apis/trackingManager/logEvent.js
var p48 = ot.enum(["APP_INITIALIZING", "APP_READY"]);
var d16 = ot.any();
function a34(m51, a36) {
  return a9("logEvent", [p48, d16], [m51, a36], function(t39, m52) {
    if (o10.isMp || o10.isMpWeb) {
      var p49, d17, a37, c41 = e25(t9({}, "object" == typeof m52 && null !== m52 ? m52 : {}), { duration: Math.round(performance.now()), timestamp: Date.now() });
      return null === window || void 0 === window || null === (p49 = window.zmpGlobal) || void 0 === p49 || null === (d17 = p49.eventManager) || void 0 === d17 || null === (a37 = d17.logEventForClient) || void 0 === a37 || a37.call(d17, { type: S.DEV_TRACKING, eventCode: "APP_INITIALIZING" === t39 ? t4.APP_INITIALIZING : t4.APP_READY }), u5([{ action: "zmp.app.lifecycle.tracking", error: 0, message: "", data: t9({ eventType: t39 }, c41) }]);
    }
    throw o11("logEvent", {});
  });
}

// ../../node_modules/zmp-sdk/apis/apis/trackingManager/index.js
var r31 = { logEvent: a34 };

// ../../node_modules/zmp-sdk/apis/apis/showCustomOAWidget.js
var g7 = [Me({ id: ke(), textColor: ot.string().optional(), fontSize: ot.string().optional(), buttonHeight: ot.string().optional(), buttonColor: ot.string().optional(), buttonTextColor: ot.string().optional(), buttonRadius: ot.string().optional(), buttonText: ot.string().optional(), checkboxBGColor: ot.string().optional(), checkboxCheckmarkColor: ot.string().optional(), onClick: ot.function().optional(), onStatusChange: ot.function().optional(), onError: ot.function().optional() }).catchall(ot.any())];
function C4(o17) {
  return y7.apply(this, arguments);
}
function y7() {
  return y7 = t6(function(r33) {
    return e15(this, function(i44) {
      return [2, a9("showCustomOAWidget", g7, [r33], (a36 = t6(function(r34) {
        var i45, a37, c41, g8, C5, y8, j6, x2, k4, w7, _6, v7, T4, A5, O4, E5, S5, P3, I6, M5, R3;
        return e15(this, function(B3) {
          if (o10.isMp) {
            try {
              i45 = document.getElementById(r34.id), a37 = r34.textColor, c41 = r34.fontSize, g8 = r34.buttonHeight, C5 = r34.buttonColor, y8 = r34.buttonTextColor, j6 = r34.buttonRadius, x2 = r34.buttonText, k4 = r34.checkboxBGColor, w7 = r34.checkboxCheckmarkColor, _6 = r34.onStatusChange, v7 = r34.onClick, T4 = r34.onError, A5 = t(r34, ["textColor", "fontSize", "buttonHeight", "buttonColor", "buttonTextColor", "buttonRadius", "buttonText", "checkboxBGColor", "checkboxCheckmarkColor", "onStatusChange", "onClick", "onError"]), i45 && (O4 = document.createElement("iframe"), E5 = "ifCOAF-".concat(r34.id, "-").concat(Date.now()), S5 = new URL(g.CUSTOM_OA), P3 = { id: E5, appId: window.APP_ID, textColor: a37, fontSize: c41, buttonHeight: g8, buttonColor: C5, buttonTextColor: y8, buttonRadius: j6, buttonText: x2, checkboxBGColor: k4, checkboxCheckmarkColor: w7 }, Object.entries(A5).forEach(function(o17) {
                var t39 = e6(o17, 2), e46 = t39[0], r35 = t39[1];
                r35 && (P3["data-".concat(e46)] = String(r35));
              }), I6 = JSON.stringify(P3), M5 = J(I6), R3 = encodeURIComponent(M5), N2(S5.searchParams, "data", R3), O4.id = E5, O4.src = S5.href, O4.style.width = "100%", O4.style.height = "100%", O4.style.border = "none", i45.innerHTML = O4.outerHTML, window.addEventListener("message", function() {
                var t39 = t6(function(o17) {
                  var t40, n24, r35, i46, a38, s30, l27, c42, p49;
                  return e15(this, function(d17) {
                    switch (d17.label) {
                      case 0:
                        return t40 = o17.data, o17.origin.startsWith(k) ? (n24 = document.getElementById(E5), r35 = t40.type, (i46 = new URL(t40.href)).origin !== S5.origin || i46.pathname !== S5.pathname || t40.id !== E5 ? [3, 3] : "followOA" !== r35 && "getOAInfo" !== r35 ? [3, 2] : [4, w2.jumpAndGetToken()]) : [3, 3];
                      case 1:
                        return d17.sent(), l27 = null === (a38 = w2.miniProgramConfig) || void 0 === a38 ? void 0 : a38.jwt, null == n24 || null === (s30 = n24.contentWindow) || void 0 === s30 || s30.postMessage({ type: r35, payload: { jwt: l27 } }, k), [3, 3];
                      case 2:
                        "oaInfo" === r35 ? null == _6 || _6(null === (c42 = t40.payload) || void 0 === c42 ? void 0 : c42.followed) : "openOA" === r35 ? i13(t40.payload.id, "oa") : "heightChange" === r35 ? n24.style.height = "".concat(t40.payload, "px") : "onClick" === r35 ? (p49 = { callAction: function(o18, t41) {
                          var e46;
                          null == n24 || null === (e46 = n24.contentWindow) || void 0 === e46 || e46.postMessage({ type: "context.callAction", payload: { actionName: o18, actionData: t41 } }, k);
                        } }, null == v7 || v7.apply(void 0, a5(Object.values(t40.payload)).concat([p49]))) : "onError" === r35 && (null == T4 || T4(t40.payload)), d17.label = 3;
                      case 3:
                        return [2];
                    }
                  });
                });
                return function(o17) {
                  return t39.apply(this, arguments);
                };
              }(), false));
            } catch (o17) {
              console.log(o17);
            }
            return [2, Promise.resolve()];
          }
          return o10.isMpWeb ? [2, Promise.resolve()] : [2, Promise.reject(o11("showCustomOAWidget", {}))];
        });
      }), function(o17) {
        return a36.apply(this, arguments);
      }))];
      var a36;
    });
  }), y7.apply(this, arguments);
}

// ../../node_modules/zmp-sdk/apis/index.js
window.ZaloMiniAppSDK = { getVersion: t16 }, m6();

// ../../node_modules/zmp-sdk/apis/index.es.js
var r32;
var s29;
var a35 = t(apis_exports, ["createOrder", "checkTransaction", "createOrderIAP", "selectPaymentMethod", "purchase"]);
(null == (s29 = null === (r32 = s4()) || void 0 === r32 ? void 0 : r32.appEnv) ? void 0 : s29.isMp) && (window.ZaloMiniAppSDK = {}, Object.assign(window.ZaloMiniAppSDK, apis_exports));
export {
  h2 as AndroidBottomNavigationBarType,
  n5 as AppError,
  a as CameraEvents,
  t8 as ChatType,
  m50 as CheckoutSDK,
  o5 as EventName,
  o5 as Events,
  e2 as FacingMode,
  m2 as IAPPayType,
  w as IOSSafeAreaBottomType,
  a6 as JumpStatus,
  d2 as MediaPickerType,
  e7 as NetworkType,
  r7 as OrientationType,
  a32 as Payment,
  n as PhotoFormat,
  t2 as PhotoQuality,
  n9 as PlatformType,
  c2 as PostFeedType,
  i2 as ProfileType,
  s2 as ProrationMode,
  l2 as ScanNFCType,
  u3 as ShareSheetType,
  f as StatusBarType,
  o as StreamType,
  _2 as TextAlignType,
  p2 as VibrateType,
  v6 as ZMACameraImp,
  i37 as addRating,
  f10 as authorize,
  i43 as checkNFC,
  s13 as checkStateBioAuthentication,
  u36 as checkTransaction,
  t35 as checkZaloCameraPermission,
  c33 as chooseImage,
  c12 as clearStorage,
  i11 as closeApp,
  i27 as closeLoading,
  u42 as configAppView,
  a22 as connectWifi,
  r30 as createCameraContext,
  d11 as createOrder,
  u38 as createOrderIAP,
  g4 as createShortcut,
  a35 as default,
  m47 as displayAd,
  u47 as downloadFile,
  r29 as events,
  i35 as favoriteApp,
  p15 as followOA,
  c6 as getAccessToken,
  i21 as getAppInfo,
  m33 as getAuthCode,
  i10 as getBeacons,
  e34 as getContext,
  i30 as getContextAsync,
  e33 as getDeviceId,
  i29 as getDeviceIdAsync,
  i41 as getIDToken,
  i32 as getLocation,
  a12 as getNetworkType,
  i23 as getPhoneNumber,
  t29 as getRouteParams,
  i39 as getSetting,
  m28 as getShareableLink,
  u11 as getStorage,
  i6 as getStorageInfo,
  r16 as getSystemInfo,
  i40 as getUserID,
  u13 as getUserInfo,
  t16 as getVersion,
  u31 as getZPIToken,
  i17 as hideKeyboard,
  f9 as interactOA,
  f9 as interactOa,
  p43 as isAllowedInteractWithOA,
  c26 as keepScreen,
  a31 as loadAd,
  m6 as login,
  s20 as minimizeApp,
  I5 as nativeStorage,
  m30 as offConfirmToExit,
  i19 as offKeepScreen,
  i33 as onCallbackData,
  s18 as onConfirmToExit,
  i18 as onKeepScreen,
  s8 as onNetworkStatusChange,
  p18 as openBioAuthentication,
  u14 as openChat,
  i36 as openGroupList,
  u30 as openMediaPicker,
  u26 as openMiniApp,
  m34 as openOutApp,
  i34 as openPermissionSetting,
  u21 as openPhone,
  u16 as openPostFeed,
  m16 as openProfile,
  c29 as openProfilePicker,
  u22 as openSMS,
  u19 as openShareSheet,
  a21 as openWebview,
  u35 as purchase,
  m48 as refreshAd,
  u12 as removeStorage,
  n16 as requestCameraPermission,
  s21 as requestSendNotification,
  i28 as requestUpdateZalo,
  l14 as saveImageToGallery,
  a18 as saveVideoToGallery,
  l25 as scanNFC,
  i12 as scanQRCode,
  p35 as selectPaymentMethod,
  m25 as sendDataToPreviousMiniApp,
  t31 as setAccessToken,
  p38 as setAndroidBottomNavigationBar,
  p39 as setIOSBottomSafeArea,
  m10 as setNavigationBarColor,
  u9 as setNavigationBarLeftButton,
  l7 as setNavigationBarTitle,
  p40 as setStatusBar,
  u10 as setStorage,
  e41 as setupAd,
  C4 as showCustomOAWidget,
  f12 as showFunctionButtonWidget,
  f11 as showOAWidget,
  p19 as showToast,
  p13 as startBeaconDiscovery,
  i8 as stopBeaconDiscovery,
  r31 as trackingManager,
  l11 as unfollowOA,
  p25 as vibrate,
  p22 as viewOAQr
};
//# sourceMappingURL=zmp-sdk.js.map
