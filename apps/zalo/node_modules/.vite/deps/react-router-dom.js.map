{"version": 3, "sources": ["../../react-router/lib/context.ts", "../../react-router/lib/hooks.tsx", "../../react-router/lib/deprecations.ts", "../../react-router/lib/components.tsx", "../../react-router/index.ts", "../../react-router-dom/dom.ts", "../../react-router-dom/index.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport type {\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  AgnosticRouteMatch,\n  History,\n  LazyRouteFunction,\n  Location,\n  Action as NavigationType,\n  RelativeRoutingType,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n  lazy?: LazyRouteFunction<RouteObject>;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject\n  // Omit `future` since those can be pulled from the `router`\n  // `NavigationContext` needs future since it doesn't have a `router` in all cases\n  extends Omit<NavigationContextObject, \"future\"> {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n  viewTransition?: boolean;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n  future: {\n    v7_relativeSplatPath: boolean;\n  };\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n  isDataRoute: boolean;\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n  isDataRoute: false,\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  Blocker,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RevalidationState,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  IDLE_BLOCKER,\n  Action as NavigationType,\n  UNSAFE_convertRouteMatchToUiMatch as convertRouteMatchToUiMatch,\n  UNSAFE_decodePath as decodePath,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  DataRouteMatch,\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, decodePath(pathname)),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\nconst navigateEffectWarning =\n  `You should call navigate() in a React.useEffect(), not when ` +\n  `your component is first rendered.`;\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(\n  cb: Parameters<typeof React.useLayoutEffect>[0]\n) {\n  let isStatic = React.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  let { isDataRoute } = React.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\n\nfunction useNavigateUnstable(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let dataRouterContext = React.useContext(DataRouterContext);\n  let { basename, future, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our history listener yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history (but only if we're not in a data router,\n      // otherwise it'll prepend the basename inside of the router).\n      // If this is a root navigation, then we navigate to the raw basename\n      // which allows the basename to have full control over the presence of a\n      // trailing slash on root links\n      if (dataRouterContext == null && basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [\n      basename,\n      navigator,\n      routePathnamesJson,\n      locationPathname,\n      dataRouterContext,\n    ]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { future } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let routePathnamesJson = JSON.stringify(\n    getResolveToMatches(matches, future.v7_relativeSplatPath)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nexport function useRoutesImpl(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string,\n  dataRouterState?: RemixRouter[\"state\"],\n  future?: RemixRouter[\"future\"]\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined ||\n        matches[matches.length - 1].route.Component !== undefined ||\n        matches[matches.length - 1].route.lazy !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" ` +\n        `does not have an element or Component. This means it will render an <Outlet /> with a ` +\n        `null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterState,\n    future\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    console.error(\n      \"Error handled by React Router default ErrorBoundary:\",\n      error\n    );\n\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own <code style={codeStyles}>ErrorBoundary</code> or{\" \"}\n          <code style={codeStyles}>errorElement</code> prop on your route.\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\nconst defaultErrorElement = <DefaultErrorComponent />;\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  revalidation: RevalidationState;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (\n      state.location !== props.location ||\n      (state.revalidation !== \"idle\" && props.revalidation === \"idle\")\n    ) {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error !== undefined ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    (match.route.errorElement || match.route.ErrorBoundary)\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState: RemixRouter[\"state\"] | null = null,\n  future: RemixRouter[\"future\"] | null = null\n): React.ReactElement | null {\n  if (matches == null) {\n    if (!dataRouterState) {\n      return null;\n    }\n\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else if (\n      future?.v7_partialHydration &&\n      parentMatches.length === 0 &&\n      !dataRouterState.initialized &&\n      dataRouterState.matches.length > 0\n    ) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id] !== undefined\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for errors on route IDs: ${Object.keys(\n        errors\n      ).join(\",\")}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n\n      if (match.route.id) {\n        let { loaderData, errors } = dataRouterState;\n        let needsToRunLoader =\n          match.route.loader &&\n          loaderData[match.route.id] === undefined &&\n          (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error: any;\n    let shouldRenderHydrateFallback = false;\n    let errorElement: React.ReactNode | null = null;\n    let hydrateFallbackElement: React.ReactNode | null = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\n            \"route-fallback\",\n            false,\n            \"No `HydrateFallback` element provided to render during initial hydration\"\n          );\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children: React.ReactNode;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = <match.route.Component />;\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return (\n        <RenderedRoute\n          match={match}\n          routeContext={{\n            outlet,\n            matches,\n            isDataRoute: dataRouterState != null,\n          }}\n          children={children}\n        />\n      );\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState &&\n      (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        revalidation={dataRouterState.revalidation}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches, isDataRoute: true }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n}\n\nenum DataRouterStateHook {\n  UseBlocker = \"useBlocker\",\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n  UseNavigateStable = \"useNavigate\",\n  UseRouteId = \"useRouteId\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nexport function useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return React.useMemo(\n    () => ({\n      revalidate: dataRouterContext.router.revalidate,\n      state: state.revalidation,\n    }),\n    [dataRouterContext.router.revalidate, state.revalidation]\n  );\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches(): UIMatch[] {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () => matches.map((m) => convertRouteMatchToUiMatch(m, loaderData)),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router, basename } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n\n  let [blockerKey, setBlockerKey] = React.useState(\"\");\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (arg) => {\n      if (typeof shouldBlock !== \"function\") {\n        return !!shouldBlock;\n      }\n      if (basename === \"/\") {\n        return shouldBlock(arg);\n      }\n\n      // If they provided us a function and we've got an active basename, strip\n      // it from the locations we expose to the user to match the behavior of\n      // useLocation\n      let { currentLocation, nextLocation, historyAction } = arg;\n      return shouldBlock({\n        currentLocation: {\n          ...currentLocation,\n          pathname:\n            stripBasename(currentLocation.pathname, basename) ||\n            currentLocation.pathname,\n        },\n        nextLocation: {\n          ...nextLocation,\n          pathname:\n            stripBasename(nextLocation.pathname, basename) ||\n            nextLocation.pathname,\n        },\n        historyAction,\n      });\n    },\n    [basename, shouldBlock]\n  );\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  React.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  React.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey)\n    ? state.blockers.get(blockerKey)!\n    : IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable(): NavigateFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n\n  let activeRef = React.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(activeRef.current, navigateEffectWarning);\n\n      // Short circuit here since if this happens on first render the navigate\n      // is useless because we haven't wired up our router subscriber yet\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        router.navigate(to);\n      } else {\n        router.navigate(to, { fromRouteId: id, ...options });\n      }\n    },\n    [router, id]\n  );\n\n  return navigate;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import type { FutureConfig as RouterFutureConfig } from \"@remix-run/router\";\nimport type { FutureConfig as RenderFutureConfig } from \"./components\";\n\nconst alreadyWarned: { [key: string]: boolean } = {};\n\nexport function warnOnce(key: string, message: string): void {\n  if (__DEV__ && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\n\nconst logDeprecation = (flag: string, msg: string, link: string) =>\n  warnOnce(\n    flag,\n    `⚠️ React Router Future Flag Warning: ${msg}. ` +\n      `You can use the \\`${flag}\\` future flag to opt-in early. ` +\n      `For more information, see ${link}.`\n  );\n\nexport function logV6DeprecationWarnings(\n  renderFuture: Partial<RenderFutureConfig> | undefined,\n  routerFuture?: Omit<RouterFutureConfig, \"v7_prependBasename\">\n) {\n  if (renderFuture?.v7_startTransition === undefined) {\n    logDeprecation(\n      \"v7_startTransition\",\n      \"React Router will begin wrapping state updates in `React.startTransition` in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\"\n    );\n  }\n\n  if (\n    renderFuture?.v7_relativeSplatPath === undefined &&\n    (!routerFuture || routerFuture.v7_relativeSplatPath === undefined)\n  ) {\n    logDeprecation(\n      \"v7_relativeSplatPath\",\n      \"Relative route resolution within Splat routes is changing in v7\",\n      \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\"\n    );\n  }\n\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\n        \"v7_fetcherPersist\",\n        \"The persistence behavior of fetchers is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\"\n      );\n    }\n\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\n        \"v7_normalizeFormMethod\",\n        \"Casing of `formMethod` fields is being normalized to uppercase in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\"\n      );\n    }\n\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\n        \"v7_partialHydration\",\n        \"`RouterProvider` hydration behavior is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\"\n      );\n    }\n\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\n        \"v7_skipActionErrorRevalidation\",\n        \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\",\n        \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\"\n      );\n    }\n  }\n}\n", "import type {\n  InitialEntry,\n  LazyRouteFunction,\n  Location,\n  MemoryHistory,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  RouterState,\n  RouterSubscriber,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  UNSAFE_getResolveToMatches as getResolveToMatches,\n  UNSAFE_invariant as invariant,\n  parsePath,\n  resolveTo,\n  stripBasename,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\nimport * as React from \"react\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./context\";\nimport {\n  AwaitContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./context\";\nimport {\n  _renderMatches,\n  useAsyncValue,\n  useInRouterContext,\n  useLocation,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  useRoutesImpl,\n} from \"./hooks\";\nimport { logV6DeprecationWarnings } from \"./deprecations\";\n\nexport interface FutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_startTransition: boolean;\n}\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n  // Only accept future flags relevant to rendering behavior\n  // routing flags should be accessed via router.future\n  future?: Partial<Pick<FutureConfig, \"v7_startTransition\">>;\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let { v7_startTransition } = future || {};\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (newState: RouterState) => {\n      if (v7_startTransition && startTransitionImpl) {\n        startTransitionImpl(() => setStateImpl(newState));\n      } else {\n        setStateImpl(newState);\n      }\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [router, future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={basename}\n            location={state.location}\n            navigationType={state.historyAction}\n            navigator={navigator}\n            future={{\n              v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n            }}\n          >\n            {state.initialized || router.future.v7_partialHydration ? (\n              <DataRoutes\n                routes={router.routes}\n                future={router.future}\n                state={state}\n              />\n            ) : (\n              fallbackElement\n            )}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  future?: Partial<FutureConfig>;\n}\n\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n  future,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  let { future, static: isStatic } = React.useContext(NavigationContext);\n\n  warning(\n    !isStatic,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = resolveTo(\n    to,\n    getResolveToMatches(matches, future.v7_relativeSplatPath),\n    locationPathname,\n    relative === \"path\"\n  );\n  let jsonPath = JSON.stringify(path);\n\n  React.useEffect(\n    () => navigate(JSON.parse(jsonPath), { replace, state, relative }),\n    [navigate, jsonPath, relative, replace, state]\n  );\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<NonIndexRouteObject>;\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  lazy?: LazyRouteFunction<IndexRouteObject>;\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  hydrateFallbackElement?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n  Component?: React.ComponentType | null;\n  HydrateFallback?: React.ComponentType | null;\n  ErrorBoundary?: React.ComponentType | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n  future?: Partial<Pick<FutureConfig, \"v7_relativeSplatPath\">>;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n  future,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({\n      basename,\n      navigator,\n      static: staticProp,\n      future: {\n        v7_relativeSplatPath: false,\n        ...future,\n      },\n    }),\n    [basename, future, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let locationContext = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key,\n      },\n      navigationType,\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n\n  warning(\n    locationContext != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (locationContext == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider children={children} value={locationContext} />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        \"_error\" in promise\n          ? AwaitRenderStatus.error\n          : \"_data\" in promise\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    let treePath = [...parentPath, index];\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, treePath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary:\n        element.props.ErrorBoundary != null ||\n        element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n", "import * as React from \"react\";\nimport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AgnosticPatchRoutesOnNavigationFunction,\n  AgnosticPatchRoutesOnNavigationFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  HydrationState,\n  InitialEntry,\n  JsonFunction,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  RedirectFunction,\n  RelativeRoutingType,\n  Router as RemixRouter,\n  FutureConfig as RouterFutureConfig,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  resolvePath,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  FutureConfig,\n  IndexRouteProps,\n  LayoutRouteProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  PathRouteProps,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n} from \"./lib/components\";\nimport {\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createRoutesFromChildren,\n  renderMatches,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  NavigateOptions,\n  Navigator,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteId,\n  useRouteLoaderData,\n  useRoutes,\n  useRoutesImpl,\n} from \"./lib/hooks\";\nimport { logV6DeprecationWarnings } from \"./lib/deprecations\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LazyRouteFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  Pathname,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n  Blocker,\n  BlockerFunction,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useBlocker,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport type PatchRoutesOnNavigationFunctionArgs =\n  AgnosticPatchRoutesOnNavigationFunctionArgs<RouteObject, RouteMatch>;\n\nexport type PatchRoutesOnNavigationFunction =\n  AgnosticPatchRoutesOnNavigationFunction<RouteObject, RouteMatch>;\n\nfunction mapRouteProperties(route: RouteObject) {\n  let updates: Partial<RouteObject> & { hasErrorBoundary: boolean } = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null,\n  };\n\n  if (route.Component) {\n    if (__DEV__) {\n      if (route.element) {\n        warning(\n          false,\n          \"You should not include both `Component` and `element` on your route - \" +\n            \"`Component` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      element: React.createElement(route.Component),\n      Component: undefined,\n    });\n  }\n\n  if (route.HydrateFallback) {\n    if (__DEV__) {\n      if (route.hydrateFallbackElement) {\n        warning(\n          false,\n          \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" +\n            \"`HydrateFallback` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: React.createElement(route.HydrateFallback),\n      HydrateFallback: undefined,\n    });\n  }\n\n  if (route.ErrorBoundary) {\n    if (__DEV__) {\n      if (route.errorElement) {\n        warning(\n          false,\n          \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" +\n            \"`ErrorBoundary` will be used.\"\n        );\n      }\n    }\n    Object.assign(updates, {\n      errorElement: React.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined,\n    });\n  }\n\n  return updates;\n}\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n    dataStrategy?: DataStrategyFunction;\n    patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  LocationContext as UNSAFE_LocationContext,\n  NavigationContext as UNSAFE_NavigationContext,\n  RouteContext as UNSAFE_RouteContext,\n  mapRouteProperties as UNSAFE_mapRouteProperties,\n  useRouteId as UNSAFE_useRouteId,\n  useRoutesImpl as UNSAFE_useRoutesImpl,\n  logV6DeprecationWarnings as UNSAFE_logV6DeprecationWarnings,\n};\n", "import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFO,IAAMA,oBACLC,oBAA8C,IAAI;AAC1D,IAAAC,MAAa;AACXF,oBAAkBG,cAAc;AAClC;AAEO,IAAMC,yBAA+BH,oBAE1C,IAAI;AACN,IAAAC,MAAa;AACXE,yBAAuBD,cAAc;AACvC;AAEO,IAAME,eAAqBJ,oBAAqC,IAAI;AAC3E,IAAAC,MAAa;AACXG,eAAaF,cAAc;AAC7B;AAsCO,IAAMG,oBAA0BL,oBACrC,IACF;AAEA,IAAAC,MAAa;AACXI,oBAAkBH,cAAc;AAClC;AAOO,IAAMI,kBAAwBN,oBACnC,IACF;AAEA,IAAAC,MAAa;AACXK,kBAAgBJ,cAAc;AAChC;IAQaK,eAAqBP,oBAAkC;EAClEQ,QAAQ;EACRC,SAAS,CAAA;EACTC,aAAa;AACf,CAAC;AAED,IAAAT,MAAa;AACXM,eAAaL,cAAc;AAC7B;AAEO,IAAMS,oBAA0BX,oBAAmB,IAAI;AAE9D,IAAAC,MAAa;AACXU,oBAAkBT,cAAc;AAClC;ACvHO,SAASU,QACdC,IAAMC,OAEE;AAAA,MADR;IAAEC;EAA6C,IAACD,UAAA,SAAG,CAAA,IAAEA;AAErD,GACEE,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEC;IAAUC;EAAU,IAAUC,iBAAWf,iBAAiB;AAChE,MAAI;IAAEgB;IAAMC;IAAUC;EAAO,IAAIC,gBAAgBX,IAAI;IAAEE;EAAS,CAAC;AAEjE,MAAIU,iBAAiBH;AAMrB,MAAIJ,aAAa,KAAK;AACpBO,qBACEH,aAAa,MAAMJ,WAAWQ,UAAU,CAACR,UAAUI,QAAQ,CAAC;EAChE;AAEA,SAAOH,UAAUQ,WAAW;IAAEL,UAAUG;IAAgBF;IAAQF;EAAK,CAAC;AACxE;AAOO,SAASL,qBAA8B;AAC5C,SAAaI,iBAAWd,eAAe,KAAK;AAC9C;AAYO,SAASsB,cAAwB;AACtC,GACEZ,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,SAAaG,iBAAWd,eAAe,EAAEuB;AAC3C;AAQO,SAASC,oBAAoC;AAClD,SAAaV,iBAAWd,eAAe,EAAEyB;AAC3C;AASO,SAASC,SAGdC,SAA+D;AAC/D,GACEjB,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEK;MAAaM,YAAW;AAC9B,SAAaM,cACX,MAAMC,UAA0BF,SAASG,WAAWd,QAAQ,CAAC,GAC7D,CAACA,UAAUW,OAAO,CACpB;AACF;AAUA,IAAMI,wBACJ;AAIF,SAASC,0BACPC,IACA;AACA,MAAIC,WAAiBpB,iBAAWf,iBAAiB,EAAEoC;AACnD,MAAI,CAACD,UAAU;AAIbE,IAAMC,sBAAgBJ,EAAE;EAC1B;AACF;AAQO,SAASK,cAAgC;AAC9C,MAAI;IAAElC;EAAY,IAAUU,iBAAWb,YAAY;AAGnD,SAAOG,cAAcmC,kBAAiB,IAAKC,oBAAmB;AAChE;AAEA,SAASA,sBAAwC;AAC/C,GACE9B,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI8B,oBAA0B3B,iBAAWrB,iBAAiB;AAC1D,MAAI;IAAEmB;IAAU8B;IAAQ7B;EAAU,IAAUC,iBAAWf,iBAAiB;AACxE,MAAI;IAAEI;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAEhD,MAAIsB,qBAAqBC,KAAKC,UAC5BC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,CAC1D;AAEA,MAAIC,YAAkBC,aAAO,KAAK;AAClClB,4BAA0B,MAAM;AAC9BiB,cAAUE,UAAU;EACtB,CAAC;AAED,MAAIC,WAAmCC,kBACrC,SAAC9C,IAAiB+C,SAAkC;AAAA,QAAlCA,YAAwB,QAAA;AAAxBA,gBAA2B,CAAA;IAAE;AAC7C3D,WAAA4D,QAAQN,UAAUE,SAASpB,qBAAqB,IAAC;AAIjD,QAAI,CAACkB,UAAUE,QAAS;AAExB,QAAI,OAAO5C,OAAO,UAAU;AAC1BM,gBAAU2C,GAAGjD,EAAE;AACf;IACF;AAEA,QAAIkD,OAAOC,UACTnD,IACAsC,KAAKc,MAAMf,kBAAkB,GAC7BD,kBACAW,QAAQ7C,aAAa,MACvB;AAQA,QAAIgC,qBAAqB,QAAQ7B,aAAa,KAAK;AACjD6C,WAAKzC,WACHyC,KAAKzC,aAAa,MACdJ,WACAQ,UAAU,CAACR,UAAU6C,KAAKzC,QAAQ,CAAC;IAC3C;AAEA,KAAC,CAAC,CAACsC,QAAQM,UAAU/C,UAAU+C,UAAU/C,UAAUgD,MACjDJ,MACAH,QAAQQ,OACRR,OACF;EACF,GACA,CACE1C,UACAC,WACA+B,oBACAD,kBACAF,iBAAiB,CAErB;AAEA,SAAOW;AACT;AAEA,IAAMW,gBAAsBrE,oBAAuB,IAAI;AAOhD,SAASsE,mBAA+C;AAC7D,SAAalD,iBAAWiD,aAAa;AACvC;AAQO,SAASE,UAAUC,SAA8C;AACtE,MAAIhE,SAAeY,iBAAWb,YAAY,EAAEC;AAC5C,MAAIA,QAAQ;AACV,WACEiE,oBAACJ,cAAcK,UAAQ;MAACC,OAAOH;IAAQ,GAAEhE,MAA+B;EAE5E;AACA,SAAOA;AACT;AAQO,SAASoE,YAId;AACA,MAAI;IAAEnE;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAIsE,aAAapE,QAAQA,QAAQqE,SAAS,CAAC;AAC3C,SAAOD,aAAcA,WAAWE,SAAiB,CAAA;AACnD;AAOO,SAASvD,gBACdX,IAAMmE,QAEA;AAAA,MADN;IAAEjE;EAA6C,IAACiE,WAAA,SAAG,CAAA,IAAEA;AAErD,MAAI;IAAEhC;EAAO,IAAU5B,iBAAWf,iBAAiB;AACnD,MAAI;IAAEI;EAAQ,IAAUW,iBAAWb,YAAY;AAC/C,MAAI;IAAEe,UAAU2B;MAAqBrB,YAAW;AAChD,MAAIsB,qBAAqBC,KAAKC,UAC5BC,oBAAoB5C,SAASuC,OAAOM,oBAAoB,CAC1D;AAEA,SAAapB,cACX,MACE8B,UACEnD,IACAsC,KAAKc,MAAMf,kBAAkB,GAC7BD,kBACAlC,aAAa,MACf,GACF,CAACF,IAAIqC,oBAAoBD,kBAAkBlC,QAAQ,CACrD;AACF;AAUO,SAASkE,UACdC,QACAC,aAC2B;AAC3B,SAAOC,cAAcF,QAAQC,WAAW;AAC1C;AAGO,SAASC,cACdF,QACAC,aACAE,iBACArC,QAC2B;AAC3B,GACEhC,mBAAkB,IAAEf,OADtBgB;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEE;EAAU,IAAUC,iBAAWf,iBAAiB;AACtD,MAAI;IAAEI,SAAS6E;EAAc,IAAUlE,iBAAWb,YAAY;AAC9D,MAAIsE,aAAaS,cAAcA,cAAcR,SAAS,CAAC;AACvD,MAAIS,eAAeV,aAAaA,WAAWE,SAAS,CAAA;AACpD,MAAIS,iBAAiBX,aAAaA,WAAWvD,WAAW;AACxD,MAAImE,qBAAqBZ,aAAaA,WAAWa,eAAe;AAChE,MAAIC,cAAcd,cAAcA,WAAWe;AAE3C,MAAA3F,MAAa;AAqBX,QAAI4F,aAAcF,eAAeA,YAAY5B,QAAS;AACtD+B,gBACEN,gBACA,CAACG,eAAeE,WAAWE,SAAS,GAAG,GACvC,oEAAA,MACMP,iBAAuCK,2BAAAA,aAAwB,kBAAA;;KAI1BA,2CAAAA,aAAU,oBAC1CA,YAAAA,eAAe,MAAM,MAASA,aAAU,QAAI,MACzD;EACF;AAEA,MAAIG,sBAAsBpE,YAAW;AAErC,MAAIC;AACJ,MAAIsD,aAAa;AAAA,QAAAc;AACf,QAAIC,oBACF,OAAOf,gBAAgB,WAAWgB,UAAUhB,WAAW,IAAIA;AAE7D,MACEM,uBAAuB,SAAGQ,wBACxBC,kBAAkB5E,aAAQ,OAAA,SAA1B2E,sBAA4BG,WAAWX,kBAAkB,MAACxF,OAF9DgB,UAAS,OAGP,8KACmF,iEAClBwE,qBAAkB,SAAI,mBACpES,kBAAkB5E,WAAQ,sCAAuC,IANtFL,UAAS,KAAA,IAAA;AASTY,eAAWqE;EACb,OAAO;AACLrE,eAAWmE;EACb;AAEA,MAAI1E,WAAWO,SAASP,YAAY;AAEpC,MAAI+E,oBAAoB/E;AACxB,MAAImE,uBAAuB,KAAK;AAe9B,QAAIa,iBAAiBb,mBAAmBvB,QAAQ,OAAO,EAAE,EAAEqC,MAAM,GAAG;AACpE,QAAIC,WAAWlF,SAAS4C,QAAQ,OAAO,EAAE,EAAEqC,MAAM,GAAG;AACpDF,wBAAoB,MAAMG,SAASC,MAAMH,eAAexB,MAAM,EAAE4B,KAAK,GAAG;EAC1E;AAEA,MAAIjG,UAAUkG,YAAYzB,QAAQ;IAAE5D,UAAU+E;EAAkB,CAAC;AAEjE,MAAApG,MAAa;AACXA,WAAA4D,QACE8B,eAAelF,WAAW,MAAI,iCACCoB,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAI,IACpF,IAAC;AAEDpB,WAAA4D,QACEpD,WAAW,QACTA,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMgB,YAAYC,UAC9CpG,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMkB,cAAcD,UAChDpG,QAAQA,QAAQqE,SAAS,CAAC,EAAEc,MAAMmB,SAASF,QAC7C,qCAAmChF,SAASP,WAAWO,SAASN,SAASM,SAASR,OAAI,6IAGxF,IAAC;EACH;AAEA,MAAI2F,kBAAkBC,eACpBxG,WACEA,QAAQyG,IAAKC,WACXC,OAAOC,OAAO,CAAA,GAAIF,OAAO;IACvBpC,QAAQqC,OAAOC,OAAO,CAAA,GAAI9B,cAAc4B,MAAMpC,MAAM;IACpDzD,UAAUI,UAAU;MAClB+D;;MAEAtE,UAAUmG,iBACNnG,UAAUmG,eAAeH,MAAM7F,QAAQ,EAAEA,WACzC6F,MAAM7F;IAAQ,CACnB;IACDoE,cACEyB,MAAMzB,iBAAiB,MACnBD,qBACA/D,UAAU;MACR+D;;MAEAtE,UAAUmG,iBACNnG,UAAUmG,eAAeH,MAAMzB,YAAY,EAAEpE,WAC7C6F,MAAMzB;IAAY,CACvB;GACR,CACH,GACFJ,eACAD,iBACArC,MACF;AAKA,MAAImC,eAAe6B,iBAAiB;AAClC,WACEvC,oBAACnE,gBAAgBoE,UAAQ;MACvBC,OAAO;QACL9C,UAAQ0F,SAAA;UACNjG,UAAU;UACVC,QAAQ;UACRF,MAAM;UACN+C,OAAO;UACPoD,KAAK;QAAS,GACX3F,QAAQ;QAEbE,gBAAgB0F,OAAeC;MACjC;IAAE,GAEDV,eACuB;EAE9B;AAEA,SAAOA;AACT;AAEA,SAASW,wBAAwB;AAC/B,MAAIC,QAAQC,cAAa;AACzB,MAAIC,UAAUC,qBAAqBH,KAAK,IACjCA,MAAMI,SAAUJ,MAAAA,MAAMK,aACzBL,iBAAiBM,QACjBN,MAAME,UACN3E,KAAKC,UAAUwE,KAAK;AACxB,MAAIO,QAAQP,iBAAiBM,QAAQN,MAAMO,QAAQ;AACnD,MAAIC,YAAY;AAChB,MAAIC,YAAY;IAAEC,SAAS;IAAUC,iBAAiBH;;AACtD,MAAII,aAAa;IAAEF,SAAS;IAAWC,iBAAiBH;;AAExD,MAAIK,UAAU;AACd,MAAAxI,MAAa;AACXyI,YAAQd,MACN,wDACAA,KACF;AAEAa,cACEhE,oBAAAkE,gBACEjG,MAAA+B,oBAAA,KAAA,MAAG,qBAAsB,GACzBA,oBAAA,KAAA,MAAG,gGAEqBA,oBAAA,QAAA;MAAMmE,OAAOJ;OAAY,eAAmB,GAAI,OAAC,KACvE/D,oBAAA,QAAA;MAAMmE,OAAOJ;IAAW,GAAC,cAAkB,GAC1C,sBAAA,CACH;EAEN;AAEA,SACE/D,oBAAAkE,gBAAA,MACElE,oBAAI,MAAA,MAAA,+BAAiC,GACrCA,oBAAA,MAAA;IAAImE,OAAO;MAAEC,WAAW;IAAS;EAAE,GAAEf,OAAY,GAChDK,QAAQ1D,oBAAA,OAAA;IAAKmE,OAAOP;EAAU,GAAEF,KAAW,IAAI,MAC/CM,OACD;AAEN;AAEA,IAAMK,sBAAsBrE,oBAACkD,uBAAqB,IAAE;AAgB7C,IAAMoB,sBAAN,cAAwCjC,gBAG7C;EACAkC,YAAYC,OAAiC;AAC3C,UAAMA,KAAK;AACX,SAAK7E,QAAQ;MACXvC,UAAUoH,MAAMpH;MAChBqH,cAAcD,MAAMC;MACpBtB,OAAOqB,MAAMrB;;EAEjB;EAEA,OAAOuB,yBAAyBvB,OAAY;AAC1C,WAAO;MAAEA;;EACX;EAEA,OAAOwB,yBACLH,OACA7E,OACA;AASA,QACEA,MAAMvC,aAAaoH,MAAMpH,YACxBuC,MAAM8E,iBAAiB,UAAUD,MAAMC,iBAAiB,QACzD;AACA,aAAO;QACLtB,OAAOqB,MAAMrB;QACb/F,UAAUoH,MAAMpH;QAChBqH,cAAcD,MAAMC;;IAExB;AAMA,WAAO;MACLtB,OAAOqB,MAAMrB,UAAUf,SAAYoC,MAAMrB,QAAQxD,MAAMwD;MACvD/F,UAAUuC,MAAMvC;MAChBqH,cAAcD,MAAMC,gBAAgB9E,MAAM8E;;EAE9C;EAEAG,kBAAkBzB,OAAY0B,WAAgB;AAC5CZ,YAAQd,MACN,yDACAA,OACA0B,SACF;EACF;EAEAC,SAAS;AACP,WAAO,KAAKnF,MAAMwD,UAAUf,SAC1BpC,oBAAClE,aAAamE,UAAQ;MAACC,OAAO,KAAKsE,MAAMO;IAAa,GACpD/E,oBAAC9D,kBAAkB+D,UAAQ;MACzBC,OAAO,KAAKP,MAAMwD;MAClB6B,UAAU,KAAKR,MAAMS;IAAU,CAChC,CACoB,IAEvB,KAAKT,MAAMQ;EAEf;AACF;AAQA,SAASE,cAAaC,MAAwD;AAAA,MAAvD;IAAEJ;IAAcrC;IAAOsC;EAA6B,IAACG;AAC1E,MAAI7G,oBAA0B3B,iBAAWrB,iBAAiB;AAI1D,MACEgD,qBACAA,kBAAkBN,UAClBM,kBAAkB8G,kBACjB1C,MAAMvB,MAAMkE,gBAAgB3C,MAAMvB,MAAMmE,gBACzC;AACAhH,sBAAkB8G,cAAcG,6BAA6B7C,MAAMvB,MAAMqE;EAC3E;AAEA,SACExF,oBAAClE,aAAamE,UAAQ;IAACC,OAAO6E;EAAa,GACxCC,QACoB;AAE3B;AAEO,SAASxC,eACdxG,SACA6E,eACAD,iBACArC,QAC2B;AAAA,MAAAkH;AAAA,MAH3B5E,kBAA2B,QAAA;AAA3BA,oBAA8B,CAAA;EAAE;AAAA,MAChCD,oBAA4C,QAAA;AAA5CA,sBAA+C;EAAI;AAAA,MACnDrC,WAAoC,QAAA;AAApCA,aAAuC;EAAI;AAE3C,MAAIvC,WAAW,MAAM;AAAA,QAAA0J;AACnB,QAAI,CAAC9E,iBAAiB;AACpB,aAAO;IACT;AAEA,QAAIA,gBAAgB+E,QAAQ;AAG1B3J,gBAAU4E,gBAAgB5E;IAC5B,YACE0J,UAAAnH,WAAAmH,QAAAA,QAAQE,uBACR/E,cAAcR,WAAW,KACzB,CAACO,gBAAgBiF,eACjBjF,gBAAgB5E,QAAQqE,SAAS,GACjC;AAOArE,gBAAU4E,gBAAgB5E;IAC5B,OAAO;AACL,aAAO;IACT;EACF;AAEA,MAAIuG,kBAAkBvG;AAGtB,MAAI2J,UAAMF,mBAAG7E,oBAAA6E,OAAAA,SAAAA,iBAAiBE;AAC9B,MAAIA,UAAU,MAAM;AAClB,QAAIG,aAAavD,gBAAgBwD,UAC9BC,OAAMA,EAAE7E,MAAMqE,OAAMG,UAAM,OAAA,SAANA,OAASK,EAAE7E,MAAMqE,EAAE,OAAMpD,MAChD;AACA,MACE0D,cAAc,KAACtK,OADjBgB,UAAS,OAAA,8DAEqDmG,OAAOsD,KACjEN,MACF,EAAE1D,KAAK,GAAG,CAAC,IAJbzF,UAAS,KAAA,IAAA;AAMT+F,sBAAkBA,gBAAgBP,MAChC,GACAkE,KAAKC,IAAI5D,gBAAgBlC,QAAQyF,aAAa,CAAC,CACjD;EACF;AAIA,MAAIM,iBAAiB;AACrB,MAAIC,gBAAgB;AACpB,MAAIzF,mBAAmBrC,UAAUA,OAAOqH,qBAAqB;AAC3D,aAASU,IAAI,GAAGA,IAAI/D,gBAAgBlC,QAAQiG,KAAK;AAC/C,UAAI5D,QAAQH,gBAAgB+D,CAAC;AAE7B,UAAI5D,MAAMvB,MAAMoF,mBAAmB7D,MAAMvB,MAAMqF,wBAAwB;AACrEH,wBAAgBC;MAClB;AAEA,UAAI5D,MAAMvB,MAAMqE,IAAI;AAClB,YAAI;UAAEiB;UAAYd,QAAAA;QAAO,IAAI/E;AAC7B,YAAI8F,mBACFhE,MAAMvB,MAAMwF,UACZF,WAAW/D,MAAMvB,MAAMqE,EAAE,MAAMpD,WAC9B,CAACuD,WAAUA,QAAOjD,MAAMvB,MAAMqE,EAAE,MAAMpD;AACzC,YAAIM,MAAMvB,MAAMmB,QAAQoE,kBAAkB;AAIxCN,2BAAiB;AACjB,cAAIC,iBAAiB,GAAG;AACtB9D,8BAAkBA,gBAAgBP,MAAM,GAAGqE,gBAAgB,CAAC;UAC9D,OAAO;AACL9D,8BAAkB,CAACA,gBAAgB,CAAC,CAAC;UACvC;AACA;QACF;MACF;IACF;EACF;AAEA,SAAOA,gBAAgBqE,YAAY,CAAC7K,QAAQ2G,OAAOmE,UAAU;AAE3D,QAAI1D;AACJ,QAAI2D,8BAA8B;AAClC,QAAIzB,eAAuC;AAC3C,QAAImB,yBAAiD;AACrD,QAAI5F,iBAAiB;AACnBuC,cAAQwC,UAAUjD,MAAMvB,MAAMqE,KAAKG,OAAOjD,MAAMvB,MAAMqE,EAAE,IAAIpD;AAC5DiD,qBAAe3C,MAAMvB,MAAMkE,gBAAgBhB;AAE3C,UAAI+B,gBAAgB;AAClB,YAAIC,gBAAgB,KAAKQ,UAAU,GAAG;AACpCxF,sBACE,kBACA,OACA,0EACF;AACAyF,wCAA8B;AAC9BN,mCAAyB;QAC3B,WAAWH,kBAAkBQ,OAAO;AAClCC,wCAA8B;AAC9BN,mCAAyB9D,MAAMvB,MAAMqF,0BAA0B;QACjE;MACF;IACF;AAEA,QAAIxK,WAAU6E,cAAckG,OAAOxE,gBAAgBP,MAAM,GAAG6E,QAAQ,CAAC,CAAC;AACtE,QAAIG,cAAcA,MAAM;AACtB,UAAIhC;AACJ,UAAI7B,OAAO;AACT6B,mBAAWK;iBACFyB,6BAA6B;AACtC9B,mBAAWwB;MACb,WAAW9D,MAAMvB,MAAMkB,WAAW;AAOhC2C,mBAAWhF,oBAAC0C,MAAMvB,MAAMkB,WAAS,IAAE;MACrC,WAAWK,MAAMvB,MAAMgB,SAAS;AAC9B6C,mBAAWtC,MAAMvB,MAAMgB;MACzB,OAAO;AACL6C,mBAAWjJ;MACb;AACA,aACEiE,oBAACkF,eAAa;QACZxC;QACAqC,cAAc;UACZhJ;UACAC,SAAAA;UACAC,aAAa2E,mBAAmB;;QAElCoE;MAAmB,CACpB;;AAML,WAAOpE,oBACJ8B,MAAMvB,MAAMmE,iBAAiB5C,MAAMvB,MAAMkE,gBAAgBwB,UAAU,KACpE7G,oBAACsE,qBAAmB;MAClBlH,UAAUwD,gBAAgBxD;MAC1BqH,cAAc7D,gBAAgB6D;MAC9BQ,WAAWI;MACXlC;MACA6B,UAAUgC,YAAW;MACrBjC,cAAc;QAAEhJ,QAAQ;QAAMC,SAAAA;QAASC,aAAa;MAAK;IAAE,CAC5D,IAED+K,YAAW;KAEZ,IAAiC;AACtC;AAAC,IAEIC,iBAAc,SAAdA,iBAAc;AAAdA,EAAAA,gBAAc,YAAA,IAAA;AAAdA,EAAAA,gBAAc,gBAAA,IAAA;AAAdA,EAAAA,gBAAc,mBAAA,IAAA;AAAA,SAAdA;AAAc,EAAdA,kBAAc,CAAA,CAAA;AAAA,IAMdC,sBAAmB,SAAnBA,sBAAmB;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,eAAA,IAAA;AAAnBA,EAAAA,qBAAmB,oBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAnBA,EAAAA,qBAAmB,gBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,mBAAA,IAAA;AAAnBA,EAAAA,qBAAmB,YAAA,IAAA;AAAA,SAAnBA;AAAmB,EAAnBA,uBAAmB,CAAA,CAAA;AAaxB,SAASC,0BACPC,UACA;AACA,SAAUA,WAAQ;AACpB;AAEA,SAASC,qBAAqBD,UAA0B;AACtD,MAAIE,MAAY3K,iBAAWrB,iBAAiB;AAC5C,GAAUgM,MAAG9L,OAAbgB,UAAS,OAAM2K,0BAA0BC,QAAQ,CAAC,IAAlD5K,UAAS,KAAA,IAAA;AACT,SAAO8K;AACT;AAEA,SAASC,mBAAmBH,UAA+B;AACzD,MAAIzH,QAAchD,iBAAWjB,sBAAsB;AACnD,GAAUiE,QAAKnE,OAAfgB,UAAS,OAAQ2K,0BAA0BC,QAAQ,CAAC,IAApD5K,UAAS,KAAA,IAAA;AACT,SAAOmD;AACT;AAEA,SAAS6H,gBAAgBJ,UAA+B;AACtD,MAAIjG,QAAcxE,iBAAWb,YAAY;AACzC,GAAUqF,QAAK3F,OAAfgB,UAAS,OAAQ2K,0BAA0BC,QAAQ,CAAC,IAApD5K,UAAS,KAAA,IAAA;AACT,SAAO2E;AACT;AAGA,SAASsG,kBAAkBL,UAA+B;AACxD,MAAIjG,QAAQqG,gBAAgBJ,QAAQ;AACpC,MAAIM,YAAYvG,MAAMnF,QAAQmF,MAAMnF,QAAQqE,SAAS,CAAC;AACtD,GACEqH,UAAUvG,MAAMqE,KAAEhK,OADpBgB,UAEK4K,OAAAA,WAAQ,wDAAA,IAFb5K,UAAS,KAAA,IAAA;AAIT,SAAOkL,UAAUvG,MAAMqE;AACzB;AAKO,SAASmC,aAAa;AAC3B,SAAOF,kBAAkBP,oBAAoBU,UAAU;AACzD;AAMO,SAASC,gBAAgB;AAC9B,MAAIlI,QAAQ4H,mBAAmBL,oBAAoBY,aAAa;AAChE,SAAOnI,MAAMoI;AACf;AAMO,SAASC,iBAAiB;AAC/B,MAAI1J,oBAAoB+I,qBAAqBJ,eAAegB,cAAc;AAC1E,MAAItI,QAAQ4H,mBAAmBL,oBAAoBe,cAAc;AACjE,SAAaxK,cACX,OAAO;IACLyK,YAAY5J,kBAAkB6J,OAAOD;IACrCvI,OAAOA,MAAM8E;EACf,IACA,CAACnG,kBAAkB6J,OAAOD,YAAYvI,MAAM8E,YAAY,CAC1D;AACF;AAMO,SAAS2D,aAAwB;AACtC,MAAI;IAAEpM;IAASyK;EAAW,IAAIc,mBAC5BL,oBAAoBmB,UACtB;AACA,SAAa5K,cACX,MAAMzB,QAAQyG,IAAKuD,OAAMsC,2BAA2BtC,GAAGS,UAAU,CAAC,GAClE,CAACzK,SAASyK,UAAU,CACtB;AACF;AAKO,SAAS8B,gBAAyB;AACvC,MAAI5I,QAAQ4H,mBAAmBL,oBAAoBsB,aAAa;AAChE,MAAIC,UAAUhB,kBAAkBP,oBAAoBsB,aAAa;AAEjE,MAAI7I,MAAMgG,UAAUhG,MAAMgG,OAAO8C,OAAO,KAAK,MAAM;AACjDxE,YAAQd,MACuDsF,6DAAAA,UAAO,GACtE;AACA,WAAOrG;EACT;AACA,SAAOzC,MAAM8G,WAAWgC,OAAO;AACjC;AAKO,SAASC,mBAAmBD,SAA0B;AAC3D,MAAI9I,QAAQ4H,mBAAmBL,oBAAoByB,kBAAkB;AACrE,SAAOhJ,MAAM8G,WAAWgC,OAAO;AACjC;AAKO,SAASG,gBAAyB;AACvC,MAAIjJ,QAAQ4H,mBAAmBL,oBAAoB2B,aAAa;AAChE,MAAIJ,UAAUhB,kBAAkBP,oBAAoBsB,aAAa;AACjE,SAAO7I,MAAMmJ,aAAanJ,MAAMmJ,WAAWL,OAAO,IAAIrG;AACxD;AAOO,SAASgB,gBAAyB;AAAA,MAAA2F;AACvC,MAAI5F,QAAcxG,iBAAWT,iBAAiB;AAC9C,MAAIyD,QAAQ4H,mBAAmBL,oBAAoB8B,aAAa;AAChE,MAAIP,UAAUhB,kBAAkBP,oBAAoB8B,aAAa;AAIjE,MAAI7F,UAAUf,QAAW;AACvB,WAAOe;EACT;AAGA,UAAA4F,gBAAOpJ,MAAMgG,WAANoD,OAAAA,SAAAA,cAAeN,OAAO;AAC/B;AAKO,SAASQ,gBAAyB;AACvC,MAAI/I,QAAcvD,iBAAWhB,YAAY;AACzC,SAAOuE,SAAK,OAAA,SAALA,MAAOgJ;AAChB;AAKO,SAASC,gBAAyB;AACvC,MAAIjJ,QAAcvD,iBAAWhB,YAAY;AACzC,SAAOuE,SAAK,OAAA,SAALA,MAAOkJ;AAChB;AAEA,IAAIC,YAAY;AAQT,SAASC,WAAWC,aAAiD;AAC1E,MAAI;IAAEpB;IAAQ1L;EAAS,IAAI4K,qBAAqBJ,eAAeuC,UAAU;AACzE,MAAI7J,QAAQ4H,mBAAmBL,oBAAoBsC,UAAU;AAE7D,MAAI,CAACC,YAAYC,aAAa,IAAUC,eAAS,EAAE;AACnD,MAAIC,kBAAwB1K,kBACzB2K,SAAQ;AACP,QAAI,OAAON,gBAAgB,YAAY;AACrC,aAAO,CAAC,CAACA;IACX;AACA,QAAI9M,aAAa,KAAK;AACpB,aAAO8M,YAAYM,GAAG;IACxB;AAKA,QAAI;MAAEC;MAAiBC;MAAcC;IAAc,IAAIH;AACvD,WAAON,YAAY;MACjBO,iBAAehH,SAAA,CAAA,GACVgH,iBAAe;QAClBjN,UACEoN,cAAcH,gBAAgBjN,UAAUJ,QAAQ,KAChDqN,gBAAgBjN;OACnB;MACDkN,cAAYjH,SAAA,CAAA,GACPiH,cAAY;QACflN,UACEoN,cAAcF,aAAalN,UAAUJ,QAAQ,KAC7CsN,aAAalN;OAChB;MACDmN;IACF,CAAC;EACH,GACA,CAACvN,UAAU8M,WAAW,CACxB;AAIAtL,EAAMiM,gBAAU,MAAM;AACpB,QAAInH,MAAMoH,OAAO,EAAEd,SAAS;AAC5BK,kBAAc3G,GAAG;AACjB,WAAO,MAAMoF,OAAOiC,cAAcrH,GAAG;EACvC,GAAG,CAACoF,MAAM,CAAC;AAMXlK,EAAMiM,gBAAU,MAAM;AACpB,QAAIT,eAAe,IAAI;AACrBtB,aAAOkC,WAAWZ,YAAYG,eAAe;IAC/C;KACC,CAACzB,QAAQsB,YAAYG,eAAe,CAAC;AAIxC,SAAOH,cAAc9J,MAAM2K,SAASC,IAAId,UAAU,IAC9C9J,MAAM2K,SAASE,IAAIf,UAAU,IAC7BgB;AACN;AAMA,SAASrM,oBAAsC;AAC7C,MAAI;IAAE+J;EAAO,IAAId,qBAAqBJ,eAAeyD,iBAAiB;AACtE,MAAIlF,KAAKiC,kBAAkBP,oBAAoBwD,iBAAiB;AAEhE,MAAI5L,YAAkBC,aAAO,KAAK;AAClClB,4BAA0B,MAAM;AAC9BiB,cAAUE,UAAU;EACtB,CAAC;AAED,MAAIC,WAAmCC,kBACrC,SAAC9C,IAAiB+C,SAAkC;AAAA,QAAlCA,YAAwB,QAAA;AAAxBA,gBAA2B,CAAA;IAAE;AAC7C3D,WAAA4D,QAAQN,UAAUE,SAASpB,qBAAqB,IAAC;AAIjD,QAAI,CAACkB,UAAUE,QAAS;AAExB,QAAI,OAAO5C,OAAO,UAAU;AAC1B+L,aAAOlJ,SAAS7C,EAAE;IACpB,OAAO;AACL+L,aAAOlJ,SAAS7C,IAAE0G,SAAA;QAAI6H,aAAanF;SAAOrG,OAAO,CAAE;IACrD;EACF,GACA,CAACgJ,QAAQ3C,EAAE,CACb;AAEA,SAAOvG;AACT;AAEA,IAAM2L,kBAAyC,CAAA;AAE/C,SAASvJ,YAAY0B,KAAa8H,MAAexH,SAAiB;AAChE,MAAI,CAACwH,QAAQ,CAACD,gBAAc7H,GAAG,GAAG;AAChC6H,oBAAc7H,GAAG,IAAI;AACrBvH,WAAA4D,QAAQ,OAAOiE,OAAO,IAAC;EACzB;AACF;AC9lCA,IAAMuH,gBAA4C,CAAA;AAE3C,SAASE,SAAS/H,KAAaM,SAAuB;AAC3D,MAAe,CAACuH,cAAcvH,OAAO,GAAG;AACtCuH,kBAAcvH,OAAO,IAAI;AACzBY,YAAQ8G,KAAK1H,OAAO;EACtB;AACF;AAEA,IAAM2H,iBAAiBA,CAACC,MAAcC,KAAaC,SACjDL,SACEG,MACA,0CAAwCC,MAAG,QAAA,sBACpBD,OAAsC,sCAAA,+BAC9BE,OAAI,IACrC;AAEK,SAASC,yBACdC,cACAC,cACA;AACA,OAAID,gBAAAA,OAAAA,SAAAA,aAAcE,wBAAuBnJ,QAAW;AAClD4I,mBACE,sBACA,mFACA,gEACF;EACF;AAEA,OACEK,gBAAY,OAAA,SAAZA,aAAcxM,0BAAyBuD,WACtC,CAACkJ,gBAAgBA,aAAazM,yBAAyBuD,SACxD;AACA4I,mBACE,wBACA,mEACA,kEACF;EACF;AAEA,MAAIM,cAAc;AAChB,QAAIA,aAAaE,sBAAsBpJ,QAAW;AAChD4I,qBACE,qBACA,0DACA,+DACF;IACF;AAEA,QAAIM,aAAaG,2BAA2BrJ,QAAW;AACrD4I,qBACE,0BACA,wEACA,oEACF;IACF;AAEA,QAAIM,aAAa1F,wBAAwBxD,QAAW;AAClD4I,qBACE,uBACA,yDACA,iEACF;IACF;AAEA,QAAIM,aAAaI,mCAAmCtJ,QAAW;AAC7D4I,qBACE,kCACA,gFACA,4EACF;IACF;EACF;AACF;ACWA,IAAMW,mBAAmB;AACzB,IAAMC,sBAAsB3N,MAAM0N,gBAAgB;AAuI3C,SAASE,aAAYC,OAMc;AAAA,MANb;IAC3BC;IACAC;IACAC;IACAC;IACAC;EACiB,IAACL;AAClB,MAAIM,aAAmBC,aAAM;AAC7B,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAUC,oBAAoB;MACvCN;MACAC;MACAM,UAAU;IACZ,CAAC;EACH;AAEA,MAAIC,UAAUL,WAAWE;AACzB,MAAI,CAACI,OAAOC,YAAY,IAAUC,eAAS;IACzCC,QAAQJ,QAAQI;IAChBC,UAAUL,QAAQK;EACpB,CAAC;AACD,MAAI;IAAEC;EAAmB,IAAIZ,UAAU,CAAA;AACvC,MAAIa,WAAiBC,kBAClBC,cAA6D;AAC5DH,0BAAsBI,sBAClBA,oBAAoB,MAAMR,aAAaO,QAAQ,CAAC,IAChDP,aAAaO,QAAQ;EAC3B,GACA,CAACP,cAAcI,kBAAkB,CACnC;AAEAK,EAAMC,sBAAgB,MAAMZ,QAAQa,OAAON,QAAQ,GAAG,CAACP,SAASO,QAAQ,CAAC;AAEzEI,EAAMG,gBAAU,MAAMC,yBAAyBrB,MAAM,GAAG,CAACA,MAAM,CAAC;AAEhE,SACEsB,oBAACC,QAAM;IACL3B;IACAC;IACAc,UAAUJ,MAAMI;IAChBa,gBAAgBjB,MAAMG;IACtBe,WAAWnB;IACXN;EAAe,CAChB;AAEL;AAkBO,SAAS0B,SAAQC,OAKA;AAAA,MALC;IACvBC;IACAC,SAAAA;IACAtB;IACAuB;EACa,IAACH;AACd,GACEI,mBAAkB,IAAEC,OADtBC;IAEE;;;IACA;EAAA,IAHFA,UAAS,KAAA,IAAA;AAOT,MAAI;IAAEjC;IAAQkC,QAAQC;EAAS,IAAUC,iBAAWC,iBAAiB;AAErEL,SAAAM,QACE,CAACH,UACD,uNAGF,IAAC;AAED,MAAI;IAAEI;EAAQ,IAAUH,iBAAWI,YAAY;AAC/C,MAAI;IAAEC,UAAUC;MAAqBC,YAAW;AAChD,MAAIC,WAAWC,YAAW;AAI1B,MAAIC,OAAOC,UACTnB,IACAoB,oBAAoBT,SAASvC,OAAOiD,oBAAoB,GACxDP,kBACAZ,aAAa,MACf;AACA,MAAIoB,WAAWC,KAAKC,UAAUN,IAAI;AAElC7B,EAAMG,gBACJ,MAAMwB,SAASO,KAAKE,MAAMH,QAAQ,GAAG;IAAErB,SAAAA;IAAStB;IAAOuB;EAAS,CAAC,GACjE,CAACc,UAAUM,UAAUpB,UAAUD,UAAStB,KAAK,CAC/C;AAEA,SAAO;AACT;AAWO,SAAS+C,OAAOC,OAA+C;AACpE,SAAOC,UAAUD,MAAME,OAAO;AAChC;AAmDO,SAASC,MAAMC,QAA+C;AAE5D3B,SADPC,UAAS,OAEP,sIACoE,IAHtEA,UAAS,KAAA;AAKX;AAqBO,SAASV,OAAMqC,OAQqB;AAAA,MARpB;IACrBhE,UAAUiE,eAAe;IACzBhE,WAAW;IACXc,UAAUmD;IACVtC,iBAAiBuC,OAAeC;IAChCvC;IACAS,QAAQ+B,aAAa;IACrBjE;EACW,IAAC4D;AACZ,GACE,CAAC7B,mBAAkB,IAAEC,OADvBC,UAEE,OAAA,wGACqD,IAHvDA,UAAS,KAAA,IAAA;AAQT,MAAIrC,WAAWiE,aAAahC,QAAQ,QAAQ,GAAG;AAC/C,MAAIqC,oBAA0BC,cAC5B,OAAO;IACLvE;IACA6B;IACAS,QAAQ+B;IACRjE,QAAMoE,SAAA;MACJnB,sBAAsB;IAAK,GACxBjD,MAAM;MAGb,CAACJ,UAAUI,QAAQyB,WAAWwC,UAAU,CAC1C;AAEA,MAAI,OAAOH,iBAAiB,UAAU;AACpCA,mBAAeO,UAAUP,YAAY;EACvC;AAEA,MAAI;IACFrB,WAAW;IACX6B,SAAS;IACTC,OAAO;IACPhE,QAAQ;IACRiE,MAAM;EACR,IAAIV;AAEJ,MAAIW,kBAAwBN,cAAQ,MAAM;AACxC,QAAIO,mBAAmBC,cAAclC,UAAU7C,QAAQ;AAEvD,QAAI8E,oBAAoB,MAAM;AAC5B,aAAO;IACT;AAEA,WAAO;MACL/D,UAAU;QACR8B,UAAUiC;QACVJ;QACAC;QACAhE;QACAiE;;MAEFhD;;EAEJ,GAAG,CAAC5B,UAAU6C,UAAU6B,QAAQC,MAAMhE,OAAOiE,KAAKhD,cAAc,CAAC;AAEjEQ,SAAAM,QACEmC,mBAAmB,MACnB,uBAAqB7E,WAAQ,sCAAA,MACvB6C,WAAW6B,SAASC,OAA2C,2CAAA,kDAEvE,IAAC;AAED,MAAIE,mBAAmB,MAAM;AAC3B,WAAO;EACT;AAEA,SACEnD,oBAACe,kBAAkBuC,UAAQ;IAACC,OAAOX;EAAkB,GACnD5C,oBAACwD,gBAAgBF,UAAQ;IAAC/E;IAAoBgF,OAAOJ;EAAgB,CAAE,CAC7C;AAEhC;AAaO,SAASM,OAAMC,OAGqB;AAAA,MAHpB;IACrBnF;IACAc;EACW,IAACqE;AACZ,SAAOC,UAAUC,yBAAyBrF,QAAQ,GAAGc,QAAQ;AAC/D;AAgBO,SAASwE,MAAKC,OAAkD;AAAA,MAAjD;IAAEvF;IAAUwF;IAAcC;EAAoB,IAACF;AACnE,SACE9D,oBAACiE,oBAAkB;IAACD;IAAkBD;KACpC/D,oBAACkE,cAAc3F,MAAAA,QAAuB,CACpB;AAExB;AAAC,IAWI4F,oBAAiB,SAAjBA,oBAAiB;AAAjBA,EAAAA,mBAAAA,mBAAiB,SAAA,IAAA,CAAA,IAAA;AAAjBA,EAAAA,mBAAAA,mBAAiB,SAAA,IAAA,CAAA,IAAA;AAAjBA,EAAAA,mBAAAA,mBAAiB,OAAA,IAAA,CAAA,IAAA;AAAA,SAAjBA;AAAiB,EAAjBA,qBAAiB,CAAA,CAAA;AAMtB,IAAMC,sBAAsB,IAAIC,QAAQ,MAAM;AAAA,CAAE;AAEhD,IAAMJ,qBAAN,cAAuCK,gBAGrC;EACAC,YAAYtC,OAAgC;AAC1C,UAAMA,KAAK;AACX,SAAKhD,QAAQ;MAAEuF,OAAO;;EACxB;EAEA,OAAOC,yBAAyBD,OAAY;AAC1C,WAAO;MAAEA;;EACX;EAEAE,kBAAkBF,OAAYG,WAAgB;AAC5CC,YAAQJ,MACN,oDACAA,OACAG,SACF;EACF;EAEAE,SAAS;AACP,QAAI;MAAEtG;MAAUwF;MAAcC;QAAY,KAAK/B;AAE/C,QAAI6C,UAAiC;AACrC,QAAIC,SAA4BZ,kBAAkBa;AAElD,QAAI,EAAEhB,mBAAmBK,UAAU;AAEjCU,eAASZ,kBAAkBc;AAC3BH,gBAAUT,QAAQL,QAAO;AACzBkB,aAAOC,eAAeL,SAAS,YAAY;QAAEM,KAAKA,MAAM;MAAK,CAAC;AAC9DF,aAAOC,eAAeL,SAAS,SAAS;QAAEM,KAAKA,MAAMpB;MAAQ,CAAC;IAChE,WAAW,KAAK/E,MAAMuF,OAAO;AAE3BO,eAASZ,kBAAkBK;AAC3B,UAAIa,cAAc,KAAKpG,MAAMuF;AAC7BM,gBAAUT,QAAQiB,OAAM,EAAGC,MAAM,MAAM;MAAA,CAAE;AACzCL,aAAOC,eAAeL,SAAS,YAAY;QAAEM,KAAKA,MAAM;MAAK,CAAC;AAC9DF,aAAOC,eAAeL,SAAS,UAAU;QAAEM,KAAKA,MAAMC;MAAY,CAAC;IACrE,WAAYrB,QAA2BwB,UAAU;AAE/CV,gBAAUd;AACVe,eACE,YAAYD,UACRX,kBAAkBK,QAClB,WAAWM,UACXX,kBAAkBc,UAClBd,kBAAkBa;IAC1B,OAAO;AAELD,eAASZ,kBAAkBa;AAC3BE,aAAOC,eAAenB,SAAS,YAAY;QAAEoB,KAAKA,MAAM;MAAK,CAAC;AAC9DN,gBAAUd,QAAQyB,KACfC,UACCR,OAAOC,eAAenB,SAAS,SAAS;QAAEoB,KAAKA,MAAMM;OAAM,GAC5DlB,WACCU,OAAOC,eAAenB,SAAS,UAAU;QAAEoB,KAAKA,MAAMZ;MAAM,CAAC,CACjE;IACF;AAEA,QACEO,WAAWZ,kBAAkBK,SAC7BM,QAAQa,kBAAkBC,sBAC1B;AAEA,YAAMxB;IACR;AAEA,QAAIW,WAAWZ,kBAAkBK,SAAS,CAACT,cAAc;AAEvD,YAAMe,QAAQa;IAChB;AAEA,QAAIZ,WAAWZ,kBAAkBK,OAAO;AAEtC,aAAOxE,oBAAC6F,aAAavC,UAAQ;QAACC,OAAOuB;QAASvG,UAAUwF;MAAa,CAAE;IACzE;AAEA,QAAIgB,WAAWZ,kBAAkBc,SAAS;AAExC,aAAOjF,oBAAC6F,aAAavC,UAAQ;QAACC,OAAOuB;QAASvG;MAAmB,CAAE;IACrE;AAGA,UAAMuG;EACR;AACF;AAMA,SAASZ,aAAY4B,OAIlB;AAAA,MAJmB;IACpBvH;EAGF,IAACuH;AACC,MAAIJ,OAAOK,cAAa;AACxB,MAAIC,WAAW,OAAOzH,aAAa,aAAaA,SAASmH,IAAI,IAAInH;AACjE,SAAOyB,oBAAAiG,gBAAGD,MAAAA,QAAW;AACvB;AAaO,SAASpC,yBACdrF,UACA2H,YACe;AAAA,MADfA,eAAoB,QAAA;AAApBA,iBAAuB,CAAA;EAAE;AAEzB,MAAIC,SAAwB,CAAA;AAE5BxG,EAAMyG,eAASC,QAAQ9H,UAAU,CAAC+H,SAASC,UAAU;AACnD,QAAI,CAAOC,qBAAeF,OAAO,GAAG;AAGlC;IACF;AAEA,QAAIG,WAAW,CAAC,GAAGP,YAAYK,KAAK;AAEpC,QAAID,QAAQI,SAAeT,gBAAU;AAEnCE,aAAOQ,KAAKC,MACVT,QACAvC,yBAAyB0C,QAAQrE,MAAM1D,UAAUkI,QAAQ,CAC3D;AACA;IACF;AAEA,MACEH,QAAQI,SAAStE,SAAK1B,OADxBC,UAGI,OAAA,OAAA,OAAO2F,QAAQI,SAAS,WAAWJ,QAAQI,OAAOJ,QAAQI,KAAKG,QAAI,wGAAA,IAHvElG,UAAS,KAAA,IAAA;AAOT,MACE,CAAC2F,QAAQrE,MAAMsE,SAAS,CAACD,QAAQrE,MAAM1D,YAAQmC,OADjDC,UAAS,OAEP,0CAA0C,IAF5CA,UAAS,KAAA,IAAA;AAKT,QAAImG,QAAqB;MACvBC,IAAIT,QAAQrE,MAAM8E,MAAMN,SAASO,KAAK,GAAG;MACzCC,eAAeX,QAAQrE,MAAMgF;MAC7BX,SAASA,QAAQrE,MAAMqE;MACvBhC,WAAWgC,QAAQrE,MAAMqC;MACzBiC,OAAOD,QAAQrE,MAAMsE;MACrB/E,MAAM8E,QAAQrE,MAAMT;MACpB0F,QAAQZ,QAAQrE,MAAMiF;MACtB9H,QAAQkH,QAAQrE,MAAM7C;MACtB2E,cAAcuC,QAAQrE,MAAM8B;MAC5BoD,eAAeb,QAAQrE,MAAMkF;MAC7BC,kBACEd,QAAQrE,MAAMkF,iBAAiB,QAC/Bb,QAAQrE,MAAM8B,gBAAgB;MAChCsD,kBAAkBf,QAAQrE,MAAMoF;MAChCC,QAAQhB,QAAQrE,MAAMqF;MACtBC,MAAMjB,QAAQrE,MAAMsF;;AAGtB,QAAIjB,QAAQrE,MAAM1D,UAAU;AAC1BuI,YAAMvI,WAAWqF,yBACf0C,QAAQrE,MAAM1D,UACdkI,QACF;IACF;AAEAN,WAAOQ,KAAKG,KAAK;EACnB,CAAC;AAED,SAAOX;AACT;AAKO,SAASqB,cACdvG,SAC2B;AAC3B,SAAOwG,eAAexG,OAAO;AAC/B;ACtfA,SAASyG,mBAAmBZ,OAAoB;AAC9C,MAAIa,UAAgE;;;IAGlEP,kBAAkBN,MAAMK,iBAAiB,QAAQL,MAAM/C,gBAAgB;;AAGzE,MAAI+C,MAAMxC,WAAW;AACnB,QAAA5D,MAAa;AACX,UAAIoG,MAAMR,SAAS;AACjB5F,eAAAM,QACE,OACA,iGAEF,IAAC;MACH;IACF;AACAkE,WAAO0C,OAAOD,SAAS;MACrBrB,SAAetG,oBAAc8G,MAAMxC,SAAS;MAC5CA,WAAWuD;IACb,CAAC;EACH;AAEA,MAAIf,MAAMgB,iBAAiB;AACzB,QAAApH,MAAa;AACX,UAAIoG,MAAMiB,wBAAwB;AAChCrH,eAAAM,QACE,OACA,4HAEF,IAAC;MACH;IACF;AACAkE,WAAO0C,OAAOD,SAAS;MACrBI,wBAA8B/H,oBAAc8G,MAAMgB,eAAe;MACjEA,iBAAiBD;IACnB,CAAC;EACH;AAEA,MAAIf,MAAMK,eAAe;AACvB,QAAAzG,MAAa;AACX,UAAIoG,MAAM/C,cAAc;AACtBrD,eAAAM,QACE,OACA,8GAEF,IAAC;MACH;IACF;AACAkE,WAAO0C,OAAOD,SAAS;MACrB5D,cAAoB/D,oBAAc8G,MAAMK,aAAa;MACrDA,eAAeU;IACjB,CAAC;EACH;AAEA,SAAOF;AACT;AAEO,SAASK,mBACd7B,QACA8B,MASa;AACb,SAAOC,aAAa;IAClB5J,UAAU2J,QAAAA,OAAAA,SAAAA,KAAM3J;IAChBI,QAAMoE,SAAA,CAAA,GACDmF,QAAAA,OAAAA,SAAAA,KAAMvJ,QAAM;MACfyJ,oBAAoB;KACrB;IACDnJ,SAASF,oBAAoB;MAC3BN,gBAAgByJ,QAAAA,OAAAA,SAAAA,KAAMzJ;MACtBC,cAAcwJ,QAAAA,OAAAA,SAAAA,KAAMxJ;IACtB,CAAC;IACD2J,eAAeH,QAAAA,OAAAA,SAAAA,KAAMG;IACrBjC;IACAuB;IACAW,cAAcJ,QAAAA,OAAAA,SAAAA,KAAMI;IACpBC,yBAAyBL,QAAAA,OAAAA,SAAAA,KAAMK;EACjC,CAAC,EAAEC,WAAU;AACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClUO,IAAMC,gBAAgC;AAC7C,IAAMC,iBAA8B;AAE9B,SAAUC,cAAcC,QAAW;AACvC,SAAOA,UAAU,QAAQ,OAAOA,OAAOC,YAAY;AACrD;AAEM,SAAUC,gBAAgBF,QAAW;AACzC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUC,cAAcJ,QAAW;AACvC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUE,eAAeL,QAAW;AACxC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAOA,SAASG,gBAAgBC,OAAwB;AAC/C,SAAO,CAAC,EAAEA,MAAMC,WAAWD,MAAME,UAAUF,MAAMG,WAAWH,MAAMI;AACpE;AAEgB,SAAAC,uBACdL,OACAM,QAAe;AAEf,SACEN,MAAMO,WAAW;GAChB,CAACD,UAAUA,WAAW;EACvB,CAACP,gBAAgBC,KAAK;AAE1B;AA+BgB,SAAAQ,mBACdC,MAA8B;AAAA,MAA9BA,SAAA,QAAA;AAAAA,WAA4B;EAAE;AAE9B,SAAO,IAAIC,gBACT,OAAOD,SAAS,YAChBE,MAAMC,QAAQH,IAAI,KAClBA,gBAAgBC,kBACZD,OACAI,OAAOC,KAAKL,IAAI,EAAEM,OAAO,CAACC,OAAMC,QAAO;AACrC,QAAIC,QAAQT,KAAKQ,GAAG;AACpB,WAAOD,MAAKG,OACVR,MAAMC,QAAQM,KAAK,IAAIA,MAAME,IAAKC,OAAM,CAACJ,KAAKI,CAAC,CAAC,IAAI,CAAC,CAACJ,KAAKC,KAAK,CAAC,CAAC;KAEnE,CAAA,CAAyB,CAAC;AAErC;AAEgB,SAAAI,2BACdC,gBACAC,qBAA2C;AAE3C,MAAIC,eAAejB,mBAAmBe,cAAc;AAEpD,MAAIC,qBAAqB;AAMvBA,wBAAoBE,QAAQ,CAACC,GAAGV,QAAO;AACrC,UAAI,CAACQ,aAAaG,IAAIX,GAAG,GAAG;AAC1BO,4BAAoBK,OAAOZ,GAAG,EAAES,QAASR,WAAS;AAChDO,uBAAaK,OAAOb,KAAKC,KAAK;QAChC,CAAC;MACF;IACH,CAAC;EACF;AAED,SAAOO;AACT;AAoBA,IAAIM,6BAA6C;AAEjD,SAASC,+BAA4B;AACnC,MAAID,+BAA+B,MAAM;AACvC,QAAI;AACF,UAAIE;QACFC,SAASC,cAAc,MAAM;;QAE7B;MAAC;AAEHJ,mCAA6B;aACtBK,GAAG;AACVL,mCAA6B;IAC9B;EACF;AACD,SAAOA;AACT;AAgFA,IAAMM,wBAA0C,oBAAIC,IAAI,CACtD,qCACA,uBACA,YAAY,CACb;AAED,SAASC,eAAeC,SAAsB;AAC5C,MAAIA,WAAW,QAAQ,CAACH,sBAAsBT,IAAIY,OAAsB,GAAG;AACzEC,WAAAC,QACE,OACA,MAAIF,UACsBjD,+DAAAA,0BAAAA,iBAAc,IAAG,IAC5C;AAED,WAAO;EACR;AACD,SAAOiD;AACT;AAEgB,SAAAG,sBACdrC,QACAsC,UAAgB;AAQhB,MAAIC;AACJ,MAAIC;AACJ,MAAIN;AACJ,MAAIO;AACJ,MAAIC;AAEJ,MAAInD,cAAcS,MAAM,GAAG;AAIzB,QAAI2C,OAAO3C,OAAO4C,aAAa,QAAQ;AACvCJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAChDC,aAASvC,OAAO4C,aAAa,QAAQ,KAAK5D;AAC1CkD,cAAUD,eAAejC,OAAO4C,aAAa,SAAS,CAAC,KAAK3D;AAE5DwD,eAAW,IAAId,SAAS3B,MAAM;aAE9BX,gBAAgBW,MAAM,KACrBR,eAAeQ,MAAM,MACnBA,OAAO8C,SAAS,YAAY9C,OAAO8C,SAAS,UAC/C;AACA,QAAIC,OAAO/C,OAAO+C;AAElB,QAAIA,QAAQ,MAAM;AAChB,YAAM,IAAIC,MAAK,oEACuD;IAEvE;AAOD,QAAIL,OAAO3C,OAAO4C,aAAa,YAAY,KAAKG,KAAKH,aAAa,QAAQ;AAC1EJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAEhDC,aACEvC,OAAO4C,aAAa,YAAY,KAChCG,KAAKH,aAAa,QAAQ,KAC1B5D;AACFkD,cACED,eAAejC,OAAO4C,aAAa,aAAa,CAAC,KACjDX,eAAec,KAAKH,aAAa,SAAS,CAAC,KAC3C3D;AAGFwD,eAAW,IAAId,SAASoB,MAAM/C,MAAM;AAMpC,QAAI,CAAC0B,6BAA4B,GAAI;AACnC,UAAI;QAAEuB;QAAMH;QAAMlC;MAAK,IAAKZ;AAC5B,UAAI8C,SAAS,SAAS;AACpB,YAAII,SAASD,OAAUA,OAAI,MAAM;AACjCR,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;AACjCT,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;iBACxBD,MAAM;AACfR,iBAASjB,OAAOyB,MAAMrC,KAAK;MAC5B;IACF;EACF,WAAU1B,cAAcc,MAAM,GAAG;AAChC,UAAM,IAAIgD,MACR,oFAC+B;EAElC,OAAM;AACLT,aAASvD;AACTwD,aAAS;AACTN,cAAUjD;AACVyD,WAAO1C;EACR;AAGD,MAAIyC,YAAYP,YAAY,cAAc;AACxCQ,WAAOD;AACPA,eAAWU;EACZ;AAED,SAAO;IAAEX;IAAQD,QAAQA,OAAOjD,YAAW;IAAI4C;IAASO;IAAUC;;AACpE;;;;AC/FA,IAAAU,uBAAA;AAEA,IAAI;AACFC,SAAOC,uBAAuBF;AAC/B,SAAQtB,GAAG;AACV;AAgBc,SAAAyB,oBACdC,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBpB,UAAUmB,QAAAA,OAAAA,SAAAA,KAAMnB;IAChBqB,QAAMC,UAAA,CAAA,GACDH,QAAAA,OAAAA,SAAAA,KAAME,QAAM;MACfE,oBAAoB;KACrB;IACDC,SAASC,qBAAqB;MAAEV,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;IAAM,CAAE;IACtDW,gBAAeP,QAAAA,OAAAA,SAAAA,KAAMO,kBAAiBC,mBAAkB;IACxDT;;IAEAU,cAAcT,QAAAA,OAAAA,SAAAA,KAAMS;IACpBC,yBAAyBV,QAAAA,OAAAA,SAAAA,KAAMU;IAC/Bd,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;GACf,EAAEe,WAAU;AACf;AAEgB,SAAAC,iBACdb,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBpB,UAAUmB,QAAAA,OAAAA,SAAAA,KAAMnB;IAChBqB,QAAMC,UAAA,CAAA,GACDH,QAAAA,OAAAA,SAAAA,KAAME,QAAM;MACfE,oBAAoB;KACrB;IACDC,SAASQ,kBAAkB;MAAEjB,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;IAAM,CAAE;IACnDW,gBAAeP,QAAAA,OAAAA,SAAAA,KAAMO,kBAAiBC,mBAAkB;IACxDT;;IAEAU,cAAcT,QAAAA,OAAAA,SAAAA,KAAMS;IACpBC,yBAAyBV,QAAAA,OAAAA,SAAAA,KAAMU;IAC/Bd,QAAQI,QAAAA,OAAAA,SAAAA,KAAMJ;GACf,EAAEe,WAAU;AACf;AAEA,SAASH,qBAAkB;AAAA,MAAAM;AACzB,MAAIC,SAAKD,UAAGlB,WAAAkB,OAAAA,SAAAA,QAAQE;AACpB,MAAID,SAASA,MAAME,QAAQ;AACzBF,YAAKZ,UAAA,CAAA,GACAY,OAAK;MACRE,QAAQC,kBAAkBH,MAAME,MAAM;KACvC;EACF;AACD,SAAOF;AACT;AAEA,SAASG,kBACPD,QAAsC;AAEtC,MAAI,CAACA,OAAQ,QAAO;AACpB,MAAIE,UAAUrE,OAAOqE,QAAQF,MAAM;AACnC,MAAIG,aAA6C,CAAA;AACjD,WAAS,CAAClE,KAAKmE,GAAG,KAAKF,SAAS;AAG9B,QAAIE,OAAOA,IAAIC,WAAW,sBAAsB;AAC9CF,iBAAWlE,GAAG,IAAI,IAAIqE,kBACpBF,IAAIG,QACJH,IAAII,YACJJ,IAAIK,MACJL,IAAIM,aAAa,IAAI;eAEdN,OAAOA,IAAIC,WAAW,SAAS;AAExC,UAAID,IAAIO,WAAW;AACjB,YAAIC,mBAAmBjC,OAAOyB,IAAIO,SAAS;AAC3C,YAAI,OAAOC,qBAAqB,YAAY;AAC1C,cAAI;AAEF,gBAAIC,QAAQ,IAAID,iBAAiBR,IAAIU,OAAO;AAG5CD,kBAAME,QAAQ;AACdZ,uBAAWlE,GAAG,IAAI4E;mBACXzD,GAAG;UACV;QAEH;MACF;AAED,UAAI+C,WAAWlE,GAAG,KAAK,MAAM;AAC3B,YAAI4E,QAAQ,IAAIvC,MAAM8B,IAAIU,OAAO;AAGjCD,cAAME,QAAQ;AACdZ,mBAAWlE,GAAG,IAAI4E;MACnB;IACF,OAAM;AACLV,iBAAWlE,GAAG,IAAImE;IACnB;EACF;AACD,SAAOD;AACT;AAmBA,IAAMa,wBAA8BC,qBAA2C;EAC7EC,iBAAiB;AAClB,CAAA;AACD,IAAAzD,MAAa;AACXuD,wBAAsBG,cAAc;AACrC;AAOKC,IAAAA,kBAAwBH,qBAAqC,oBAAII,IAAG,CAAE;AAC5E,IAAA5D,MAAa;AACX2D,kBAAgBD,cAAc;AAC/B;AA+BD,IAAMG,oBAAmB;AACzB,IAAMC,uBAAsBC,OAAMF,iBAAgB;AAClD,IAAMG,aAAa;AACnB,IAAMC,gBAAgBC,SAASF,UAAU;AACzC,IAAMG,SAAS;AACf,IAAMC,YAAYL,OAAMI,MAAM;AAE9B,SAASE,oBAAoBC,IAAc;AACzC,MAAIR,sBAAqB;AACvBA,IAAAA,qBAAoBQ,EAAE;EACvB,OAAM;AACLA,OAAE;EACH;AACH;AAEA,SAASC,cAAcD,IAAc;AACnC,MAAIL,eAAe;AACjBA,kBAAcK,EAAE;EACjB,OAAM;AACLA,OAAE;EACH;AACH;AASA,IAAME,WAAN,MAAc;EAOZC,cAAA;AANA,SAAM3B,SAAwC;AAO5C,SAAK4B,UAAU,IAAIC,QAAQ,CAACC,SAASC,WAAU;AAC7C,WAAKD,UAAWnG,WAAS;AACvB,YAAI,KAAKqE,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACd8B,kBAAQnG,KAAK;QACd;;AAEH,WAAKoG,SAAUC,YAAU;AACvB,YAAI,KAAKhC,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACd+B,iBAAOC,MAAM;QACd;;IAEL,CAAC;EACH;AACD;AAKK,SAAUC,eAAcC,MAIR;AAAA,MAJS;IAC7BC;IACAC;IACA1D;EACoB,IAAAwD;AACpB,MAAI,CAAC3C,OAAO8C,YAAY,IAAUC,gBAASF,OAAO7C,KAAK;AACvD,MAAI,CAACgD,cAAcC,eAAe,IAAUF,gBAAQ;AACpD,MAAI,CAACG,WAAWC,YAAY,IAAUJ,gBAAsC;IAC1E3B,iBAAiB;EAClB,CAAA;AACD,MAAI,CAACgC,WAAWC,YAAY,IAAUN,gBAAQ;AAC9C,MAAI,CAACO,YAAYC,aAAa,IAAUR,gBAAQ;AAChD,MAAI,CAACS,cAAcC,eAAe,IAAUV,gBAAQ;AAKpD,MAAIW,cAAoBC,cAAyB,oBAAIpC,IAAG,CAAE;AAC1D,MAAI;IAAEqC;EAAkB,IAAKzE,UAAU,CAAA;AAEvC,MAAI0E,uBAA6BC,mBAC9B7B,QAAkB;AACjB,QAAI2B,oBAAoB;AACtB5B,0BAAoBC,EAAE;IACvB,OAAM;AACLA,SAAE;IACH;EACH,GACA,CAAC2B,kBAAkB,CAAC;AAGtB,MAAIG,WAAiBD,mBACnB,CACEE,UAAqBC,UAMnB;AAAA,QALF;MACEC;MACAC;MACAC;IACD,IAAAH;AAEDD,aAASK,SAASzH,QAAQ,CAAC0H,SAASnI,QAAO;AACzC,UAAImI,QAAQ3D,SAAShC,QAAW;AAC9B+E,oBAAYa,QAAQC,IAAIrI,KAAKmI,QAAQ3D,IAAI;MAC1C;IACH,CAAC;AACDuD,oBAAgBtH,QAAST,SAAQuH,YAAYa,QAAQE,OAAOtI,GAAG,CAAC;AAEhE,QAAIuI,8BACF7B,OAAOhE,UAAU,QACjBgE,OAAOhE,OAAOzB,YAAY,QAC1B,OAAOyF,OAAOhE,OAAOzB,SAASuH,wBAAwB;AAIxD,QAAI,CAACP,sBAAsBM,6BAA6B;AACtD,UAAIP,WAAW;AACbjC,sBAAc,MAAMY,aAAakB,QAAQ,CAAC;MAC3C,OAAM;AACLH,6BAAqB,MAAMf,aAAakB,QAAQ,CAAC;MAClD;AACD;IACD;AAGD,QAAIG,WAAW;AAEbjC,oBAAc,MAAK;AAEjB,YAAIoB,YAAY;AACdF,uBAAaA,UAAUb,QAAO;AAC9Be,qBAAWsB,eAAc;QAC1B;AACDzB,qBAAa;UACX/B,iBAAiB;UACjB+C,WAAW;UACXU,iBAAiBT,mBAAmBS;UACpCC,cAAcV,mBAAmBU;QAClC,CAAA;MACH,CAAC;AAGD,UAAIC,IAAIlC,OAAOhE,OAAQzB,SAASuH,oBAAoB,MAAK;AACvDzC,sBAAc,MAAMY,aAAakB,QAAQ,CAAC;MAC5C,CAAC;AAGDe,QAAEC,SAASC,QAAQ,MAAK;AACtB/C,sBAAc,MAAK;AACjBmB,uBAAa1E,MAAS;AACtB4E,wBAAc5E,MAAS;AACvBsE,0BAAgBtE,MAAS;AACzBwE,uBAAa;YAAE/B,iBAAiB;UAAK,CAAE;QACzC,CAAC;MACH,CAAC;AAEDc,oBAAc,MAAMqB,cAAcwB,CAAC,CAAC;AACpC;IACD;AAGD,QAAIzB,YAAY;AAGdF,mBAAaA,UAAUb,QAAO;AAC9Be,iBAAWsB,eAAc;AACzBnB,sBAAgB;QACdzD,OAAOgE;QACPa,iBAAiBT,mBAAmBS;QACpCC,cAAcV,mBAAmBU;MAClC,CAAA;IACF,OAAM;AAEL7B,sBAAgBe,QAAQ;AACxBb,mBAAa;QACX/B,iBAAiB;QACjB+C,WAAW;QACXU,iBAAiBT,mBAAmBS;QACpCC,cAAcV,mBAAmBU;MAClC,CAAA;IACF;EACH,GACA,CAACjC,OAAOhE,QAAQyE,YAAYF,WAAWM,aAAaG,oBAAoB,CAAC;AAK3EnC,EAAMwD,uBAAgB,MAAMrC,OAAOsC,UAAUpB,QAAQ,GAAG,CAAClB,QAAQkB,QAAQ,CAAC;AAI1ErC,EAAM0D,iBAAU,MAAK;AACnB,QAAIlC,UAAU9B,mBAAmB,CAAC8B,UAAUiB,WAAW;AACrDd,mBAAa,IAAIlB,SAAQ,CAAQ;IAClC;EACH,GAAG,CAACe,SAAS,CAAC;AAKdxB,EAAM0D,iBAAU,MAAK;AACnB,QAAIhC,aAAaJ,gBAAgBH,OAAOhE,QAAQ;AAC9C,UAAImF,WAAWhB;AACf,UAAIqC,gBAAgBjC,UAAUf;AAC9B,UAAIiB,cAAaT,OAAOhE,OAAOzB,SAASuH,oBAAoB,YAAW;AACrEd,6BAAqB,MAAMf,aAAakB,QAAQ,CAAC;AACjD,cAAMqB;MACR,CAAC;AACD/B,MAAAA,YAAW0B,SAASC,QAAQ,MAAK;AAC/B5B,qBAAa1E,MAAS;AACtB4E,sBAAc5E,MAAS;AACvBsE,wBAAgBtE,MAAS;AACzBwE,qBAAa;UAAE/B,iBAAiB;QAAK,CAAE;MACzC,CAAC;AACDmC,oBAAcD,WAAU;IACzB;EACH,GAAG,CAACO,sBAAsBb,cAAcI,WAAWP,OAAOhE,MAAM,CAAC;AAIjE6C,EAAM0D,iBAAU,MAAK;AACnB,QACEhC,aACAJ,gBACAhD,MAAMsF,SAASnJ,QAAQ6G,aAAasC,SAASnJ,KAC7C;AACAiH,gBAAUb,QAAO;IAClB;EACH,GAAG,CAACa,WAAWE,YAAYtD,MAAMsF,UAAUtC,YAAY,CAAC;AAIxDtB,EAAM0D,iBAAU,MAAK;AACnB,QAAI,CAAClC,UAAU9B,mBAAmBoC,cAAc;AAC9CP,sBAAgBO,aAAaxD,KAAK;AAClCmD,mBAAa;QACX/B,iBAAiB;QACjB+C,WAAW;QACXU,iBAAiBrB,aAAaqB;QAC9BC,cAActB,aAAasB;MAC5B,CAAA;AACDrB,sBAAgB9E,MAAS;IAC1B;KACA,CAACuE,UAAU9B,iBAAiBoC,YAAY,CAAC;AAE5C9B,EAAM0D,iBAAU,MAAK;AACnBzH,WAAAC,QACEgF,mBAAmB,QAAQ,CAACC,OAAO1D,OAAOoG,qBAC1C,8HACoE,IACrE;KAGA,CAAA,CAAE;AAEL,MAAIC,YAAkBC,eAAQ,MAAgB;AAC5C,WAAO;MACLC,YAAY7C,OAAO6C;MACnBC,gBAAgB9C,OAAO8C;MACvBC,IAAKC,OAAMhD,OAAOiD,SAASD,CAAC;MAC5BE,MAAMA,CAACC,IAAIhG,QAAOf,SAChB4D,OAAOiD,SAASE,IAAI;QAClBhG,OAAAA;QACAiG,oBAAoBhH,QAAAA,OAAAA,SAAAA,KAAMgH;OAC3B;MACHC,SAASA,CAACF,IAAIhG,QAAOf,SACnB4D,OAAOiD,SAASE,IAAI;QAClBE,SAAS;QACTlG,OAAAA;QACAiG,oBAAoBhH,QAAAA,OAAAA,SAAAA,KAAMgH;OAC3B;;EAEP,GAAG,CAACpD,MAAM,CAAC;AAEX,MAAI/E,WAAW+E,OAAO/E,YAAY;AAElC,MAAIqI,oBAA0BV,eAC5B,OAAO;IACL5C;IACA2C;IACAY,QAAQ;IACRtI;MAEF,CAAC+E,QAAQ2C,WAAW1H,QAAQ,CAAC;AAG/B,MAAIuI,eAAqBZ,eACvB,OAAO;IACLa,sBAAsBzD,OAAO1D,OAAOmH;MAEtC,CAACzD,OAAO1D,OAAOmH,oBAAoB,CAAC;AAGtC5E,EAAM0D,iBACJ,MAAMmB,yBAAyBpH,QAAQ0D,OAAO1D,MAAM,GACpD,CAACA,QAAQ0D,OAAO1D,MAAM,CAAC;AASzB,SACE9B,qBAAAmJ,iBAAA,MACEnJ,qBAACoJ,kBAAkBC,UAAS;IAAAtK,OAAO+J;KACjC9I,qBAACsJ,uBAAuBD,UAAS;IAAAtK,OAAO4D;KACrC3C,qBAAAiE,gBAAgBoF,UAAQ;IAACtK,OAAOsH,YAAYa;KAC3ClH,qBAAC6D,sBAAsBwF,UAAS;IAAAtK,OAAO8G;EAAS,GAC9C7F,qBAACuJ,QAAM;IACL9I;IACAwH,UAAUtF,MAAMsF;IAChBuB,gBAAgB7G,MAAM8G;IACtBtB;IACArG,QAAQkH;EAEP,GAAArG,MAAM+G,eAAelE,OAAO1D,OAAOoG,sBAClClI,qBAAC2J,oBACC;IAAAhI,QAAQ6D,OAAO7D;IACfG,QAAQ0D,OAAO1D;IACfa;GAAY,IAGd4C,eACD,CACM,CACsB,CACR,CACK,GAEnC,IAAI;AAGX;AAGA,IAAMoE,qBAA2B9K,YAAK+K,UAAU;AAEhD,SAASA,WAAUC,OAQlB;AAAA,MARmB;IAClBlI;IACAG;IACAa;EAKD,IAAAkH;AACC,SAAOC,cAAcnI,QAAQL,QAAWqB,OAAOb,MAAM;AACvD;AAYM,SAAUiI,cAAaC,OAKR;AAAA,MALS;IAC5BvJ;IACAwJ;IACAnI;IACAN,QAAAA;EACmB,IAAAwI;AACnB,MAAIE,aAAmB5D,cAAM;AAC7B,MAAI4D,WAAWhD,WAAW,MAAM;AAC9BgD,eAAWhD,UAAUhF,qBAAqB;MAAEV,QAAAA;MAAQ2I,UAAU;IAAI,CAAE;EACrE;AAED,MAAIlI,UAAUiI,WAAWhD;AACzB,MAAI,CAACvE,OAAO8C,YAAY,IAAUC,gBAAS;IACzC/E,QAAQsB,QAAQtB;IAChBsH,UAAUhG,QAAQgG;EACnB,CAAA;AACD,MAAI;IAAE1B;EAAkB,IAAKzE,UAAU,CAAA;AACvC,MAAI4E,WAAiBD,mBAClBE,cAA4D;AAC3DJ,0BAAsBnC,uBAClBA,qBAAoB,MAAMqB,aAAakB,QAAQ,CAAC,IAChDlB,aAAakB,QAAQ;EAC3B,GACA,CAAClB,cAAcc,kBAAkB,CAAC;AAGpClC,EAAMwD,uBAAgB,MAAM5F,QAAQmI,OAAO1D,QAAQ,GAAG,CAACzE,SAASyE,QAAQ,CAAC;AAEzErC,EAAM0D,iBAAU,MAAMmB,yBAAyBpH,MAAM,GAAG,CAACA,MAAM,CAAC;AAEhE,SACE9B,qBAACuJ,QAAM;IACL9I;IACAwJ;IACAhC,UAAUtF,MAAMsF;IAChBuB,gBAAgB7G,MAAMhC;IACtBwH,WAAWlG;IACXH;EAAc,CAAA;AAGpB;AAaM,SAAUuI,WAAUC,OAKR;AAAA,MALS;IACzB7J;IACAwJ;IACAnI;IACAN,QAAAA;EACgB,IAAA8I;AAChB,MAAIJ,aAAmB5D,cAAM;AAC7B,MAAI4D,WAAWhD,WAAW,MAAM;AAC9BgD,eAAWhD,UAAUzE,kBAAkB;MAAEjB,QAAAA;MAAQ2I,UAAU;IAAI,CAAE;EAClE;AAED,MAAIlI,UAAUiI,WAAWhD;AACzB,MAAI,CAACvE,OAAO8C,YAAY,IAAUC,gBAAS;IACzC/E,QAAQsB,QAAQtB;IAChBsH,UAAUhG,QAAQgG;EACnB,CAAA;AACD,MAAI;IAAE1B;EAAkB,IAAKzE,UAAU,CAAA;AACvC,MAAI4E,WAAiBD,mBAClBE,cAA4D;AAC3DJ,0BAAsBnC,uBAClBA,qBAAoB,MAAMqB,aAAakB,QAAQ,CAAC,IAChDlB,aAAakB,QAAQ;EAC3B,GACA,CAAClB,cAAcc,kBAAkB,CAAC;AAGpClC,EAAMwD,uBAAgB,MAAM5F,QAAQmI,OAAO1D,QAAQ,GAAG,CAACzE,SAASyE,QAAQ,CAAC;AAEzErC,EAAM0D,iBAAU,MAAMmB,yBAAyBpH,MAAM,GAAG,CAACA,MAAM,CAAC;AAEhE,SACE9B,qBAACuJ,QAAM;IACL9I;IACAwJ;IACAhC,UAAUtF,MAAMsF;IAChBuB,gBAAgB7G,MAAMhC;IACtBwH,WAAWlG;IACXH;EAAc,CAAA;AAGpB;AAeA,SAASyI,cAAaC,OAKD;AAAA,MALE;IACrB/J;IACAwJ;IACAnI;IACAG;EACmB,IAAAuI;AACnB,MAAI,CAAC7H,OAAO8C,YAAY,IAAUC,gBAAS;IACzC/E,QAAQsB,QAAQtB;IAChBsH,UAAUhG,QAAQgG;EACnB,CAAA;AACD,MAAI;IAAE1B;EAAkB,IAAKzE,UAAU,CAAA;AACvC,MAAI4E,WAAiBD,mBAClBE,cAA4D;AAC3DJ,0BAAsBnC,uBAClBA,qBAAoB,MAAMqB,aAAakB,QAAQ,CAAC,IAChDlB,aAAakB,QAAQ;EAC3B,GACA,CAAClB,cAAcc,kBAAkB,CAAC;AAGpClC,EAAMwD,uBAAgB,MAAM5F,QAAQmI,OAAO1D,QAAQ,GAAG,CAACzE,SAASyE,QAAQ,CAAC;AAEzErC,EAAM0D,iBAAU,MAAMmB,yBAAyBpH,MAAM,GAAG,CAACA,MAAM,CAAC;AAEhE,SACE9B,qBAACuJ,QAAM;IACL9I;IACAwJ;IACAhC,UAAUtF,MAAMsF;IAChBuB,gBAAgB7G,MAAMhC;IACtBwH,WAAWlG;IACXH;EAAc,CAAA;AAGpB;AAEA,IAAAxB,MAAa;AACXiK,gBAAcvG,cAAc;AAC7B;AAeD,IAAMyG,YACJ,OAAOjJ,WAAW,eAClB,OAAOA,OAAOzB,aAAa,eAC3B,OAAOyB,OAAOzB,SAASC,kBAAkB;AAE3C,IAAM0K,qBAAqB;AAKdC,IAAAA,OAAaC,kBACxB,SAASC,YAAWC,OAalBC,KAAG;AAAA,MAZH;IACEC;IACAC;IACAC;IACArC,SAAAA;IACAlG;IACAxE;IACAwK;IACAC;IACAuC;EACO,IACRL,OADIM,OAAIC,8BAAAP,OAAAQ,SAAA;AAIT,MAAI;IAAE7K;EAAQ,IAAW8K,kBAAWC,iBAAiB;AAGrD,MAAIC;AACJ,MAAIC,aAAa;AAEjB,MAAI,OAAO/C,OAAO,YAAY+B,mBAAmBiB,KAAKhD,EAAE,GAAG;AAEzD8C,mBAAe9C;AAGf,QAAI8B,WAAW;AACb,UAAI;AACF,YAAImB,aAAa,IAAIC,IAAIrK,OAAOyG,SAAS6D,IAAI;AAC7C,YAAIC,YAAYpD,GAAGqD,WAAW,IAAI,IAC9B,IAAIH,IAAID,WAAWK,WAAWtD,EAAE,IAChC,IAAIkD,IAAIlD,EAAE;AACd,YAAIuD,OAAOlL,cAAc+K,UAAUI,UAAU1L,QAAQ;AAErD,YAAIsL,UAAUK,WAAWR,WAAWQ,UAAUF,QAAQ,MAAM;AAE1DvD,eAAKuD,OAAOH,UAAUM,SAASN,UAAUO;QAC1C,OAAM;AACLZ,uBAAa;QACd;eACMzL,GAAG;AAEVK,eAAAC,QACE,OACA,eAAaoI,KAAE,wGACsC,IACtD;MACF;IACF;EACF;AAGD,MAAImD,OAAOS,QAAQ5D,IAAI;IAAEsC;EAAU,CAAA;AAEnC,MAAIuB,kBAAkBC,oBAAoB9D,IAAI;IAC5CE,SAAAA;IACAlG;IACAxE;IACAyK;IACAqC;IACAE;EACD,CAAA;AACD,WAASuB,YACP7O,OAAsD;AAEtD,QAAImN,QAASA,SAAQnN,KAAK;AAC1B,QAAI,CAACA,MAAM8O,kBAAkB;AAC3BH,sBAAgB3O,KAAK;IACtB;EACH;AAEA;;IAEEmC,qBAAA,KAAA+B,UAAA,CAAA,GACMqJ,MAAI;MACRU,MAAML,gBAAgBK;MACtBd,SAASU,cAAcR,iBAAiBF,UAAU0B;MAClD3B;MACA5M;KAAc,CAAA;;AAGpB,CAAC;AAGH,IAAAmC,MAAa;AACXqK,OAAK3G,cAAc;AACpB;AAsBY4I,IAAAA,UAAgBhC,kBAC3B,SAASiC,eAAcC,OAYrB/B,KAAG;AAAA,MAXH;IACE,gBAAgBgC,kBAAkB;IAClCC,gBAAgB;IAChBC,WAAWC,gBAAgB;IAC3BC,MAAM;IACNC,OAAOC;IACP1E;IACAwC;IACAlB;EAED,IAAA6C,OADI1B,OAAIC,8BAAAyB,OAAAQ,UAAA;AAIT,MAAIpB,OAAOqB,gBAAgB5E,IAAI;IAAEsC,UAAUG,KAAKH;EAAQ,CAAE;AAC1D,MAAIhD,WAAWuF,YAAW;AAC1B,MAAIC,cAAoBlC,kBAAWjC,sBAAsB;AACzD,MAAI;IAAEnB;IAAW1H;EAAU,IAAS8K,kBAAWC,iBAAiB;AAChE,MAAIzH,kBACF0J,eAAe;;EAGfC,uBAAuBxB,IAAI,KAC3Bf,mBAAmB;AAErB,MAAIwC,aAAaxF,UAAUG,iBACvBH,UAAUG,eAAe4D,IAAI,EAAEC,WAC/BD,KAAKC;AACT,MAAIyB,mBAAmB3F,SAASkE;AAChC,MAAI0B,uBACFJ,eAAeA,YAAYK,cAAcL,YAAYK,WAAW7F,WAC5DwF,YAAYK,WAAW7F,SAASkE,WAChC;AAEN,MAAI,CAACa,eAAe;AAClBY,uBAAmBA,iBAAiBnQ,YAAW;AAC/CoQ,2BAAuBA,uBACnBA,qBAAqBpQ,YAAW,IAChC;AACJkQ,iBAAaA,WAAWlQ,YAAW;EACpC;AAED,MAAIoQ,wBAAwBpN,UAAU;AACpCoN,2BACE7M,cAAc6M,sBAAsBpN,QAAQ,KAAKoN;EACpD;AAOD,QAAME,mBACJJ,eAAe,OAAOA,WAAWK,SAAS,GAAG,IACzCL,WAAWM,SAAS,IACpBN,WAAWM;AACjB,MAAIC,WACFN,qBAAqBD,cACpB,CAACR,OACAS,iBAAiB5B,WAAW2B,UAAU,KACtCC,iBAAiBO,OAAOJ,gBAAgB,MAAM;AAElD,MAAIK,YACFP,wBAAwB,SACvBA,yBAAyBF,cACvB,CAACR,OACAU,qBAAqB7B,WAAW2B,UAAU,KAC1CE,qBAAqBM,OAAOR,WAAWM,MAAM,MAAM;AAEzD,MAAII,cAAc;IAChBH;IACAE;IACArK;;AAGF,MAAIuK,cAAcJ,WAAWnB,kBAAkBzL;AAE/C,MAAI2L;AACJ,MAAI,OAAOC,kBAAkB,YAAY;AACvCD,gBAAYC,cAAcmB,WAAW;EACtC,OAAM;AAMLpB,gBAAY,CACVC,eACAgB,WAAW,WAAW,MACtBE,YAAY,YAAY,MACxBrK,kBAAkB,kBAAkB,IAAI,EAEvCwK,OAAOC,OAAO,EACdC,KAAK,GAAG;EACZ;AAED,MAAIrB,QACF,OAAOC,cAAc,aAAaA,UAAUgB,WAAW,IAAIhB;AAE7D,SACEhJ,qBAACsG,MAAI5I,UAAA,CAAA,GACCqJ,MAAI;IACM,gBAAAkD;IACdrB;IACAlC;IACAqC;IACAzE;IACAwC;GAEC,GAAA,OAAOlB,aAAa,aAAaA,SAASoE,WAAW,IAAIpE,QAAQ;AAGxE,CAAC;AAGH,IAAA3J,MAAa;AACXsM,UAAQ5I,cAAc;AACvB;AAsGM,IAAM0K,OAAa9D,kBACxB,CAAA+D,OAeEC,iBACE;AAAA,MAfF;IACEC;IACApG;IACAyC;IACArC,SAAAA;IACAlG;IACAjC,SAASvD;IACTwD;IACAmO;IACA7D;IACArC;IACAuC;MAEDwD,OADII,QAAK1D,8BAAAsD,OAAAK,UAAA;AAIV,MAAIC,SAASC,UAAS;AACtB,MAAIC,aAAaC,cAAczO,QAAQ;IAAEsK;EAAU,CAAA;AACnD,MAAIoE,aACF3O,OAAOjD,YAAW,MAAO,QAAQ,QAAQ;AAE3C,MAAI6R,gBAA0DzR,WAAS;AACrEiR,gBAAYA,SAASjR,KAAK;AAC1B,QAAIA,MAAM8O,iBAAkB;AAC5B9O,UAAM0R,eAAc;AAEpB,QAAIC,YAAa3R,MAAqC4R,YACnDD;AAEH,QAAIE,gBACDF,aAAAA,OAAAA,SAAAA,UAAWzO,aAAa,YAAY,MACrCL;AAEFuO,WAAOO,aAAa3R,MAAM8R,eAAe;MACvCd;MACAnO,QAAQgP;MACRjH;MACAI,SAAAA;MACAlG;MACAsI;MACArC;MACAuC;IACD,CAAA;;AAGH,SACEnL,qBAAA,QAAA+B,UAAA;IACEgJ,KAAK6D;IACLlO,QAAQ2O;IACR1O,QAAQwO;IACRL,UAAU5D,iBAAiB4D,WAAWQ;KAClCP,KAAK,CAAA;AAGf,CAAC;AAGH,IAAAzO,MAAa;AACXoO,OAAK1K,cAAc;AACpB;SAWe4L,kBAAiBC,QAGR;AAAA,MAHS;IAChCC;IACAC;EACuB,IAAAF;AACvBG,uBAAqB;IAAEF;IAAQC;EAAU,CAAE;AAC3C,SAAO;AACT;AAEA,IAAAzP,MAAa;AACXsP,oBAAkB5L,cAAc;AACjC;AAOD,IAAKiM;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAA,sBAAA,IAAA;AACAA,EAAAA,gBAAA,WAAA,IAAA;AACAA,EAAAA,gBAAA,kBAAA,IAAA;AACAA,EAAAA,gBAAA,YAAA,IAAA;AACAA,EAAAA,gBAAA,wBAAA,IAAA;AACF,GANKA,oBAAAA,kBAMJ,CAAA,EAAA;AAED,IAAKC;CAAL,SAAKA,sBAAmB;AACtBA,EAAAA,qBAAA,YAAA,IAAA;AACAA,EAAAA,qBAAA,aAAA,IAAA;AACAA,EAAAA,qBAAA,sBAAA,IAAA;AACF,GAJKA,yBAAAA,uBAIJ,CAAA,EAAA;AAID,SAASC,2BACPC,UAA8C;AAE9C,SAAUA,WAAQ;AACpB;AAEA,SAASC,sBAAqBD,UAAwB;AACpD,MAAIE,MAAY/E,kBAAWnC,iBAAiB;AAC5C,GAAUkH,MAAGhQ,OAAbiQ,UAAS,OAAMJ,2BAA0BC,QAAQ,CAAC,IAAlDG,UAAS,KAAA,IAAA;AACT,SAAOD;AACT;AAEA,SAASE,oBAAmBJ,UAA6B;AACvD,MAAIzN,QAAc4I,kBAAWjC,sBAAsB;AACnD,GAAU3G,QAAKrC,OAAfiQ,UAAS,OAAQJ,2BAA0BC,QAAQ,CAAC,IAApDG,UAAS,KAAA,IAAA;AACT,SAAO5N;AACT;AASM,SAAU8J,oBACd9D,IAAM8H,OAeA;AAAA,MAdN;IACEtS;IACA0K,SAAS6H;IACT/N;IACAiG;IACAqC;IACAE;yBAQE,CAAA,IAAEsF;AAEN,MAAIhI,WAAWkI,YAAW;AAC1B,MAAI1I,WAAWuF,YAAW;AAC1B,MAAItB,OAAOqB,gBAAgB5E,IAAI;IAAEsC;EAAU,CAAA;AAE3C,SAAaxE,mBACV5I,WAA0C;AACzC,QAAIK,uBAAuBL,OAAOM,MAAM,GAAG;AACzCN,YAAM0R,eAAc;AAIpB,UAAI1G,WACF6H,gBAAgBpP,SACZoP,cACAE,WAAW3I,QAAQ,MAAM2I,WAAW1E,IAAI;AAE9CzD,eAASE,IAAI;QACXE,SAAAA;QACAlG;QACAiG;QACAqC;QACAE;MACD,CAAA;IACF;KAEH,CACElD,UACAQ,UACAyD,MACAwE,aACA/N,OACAxE,QACAwK,IACAC,oBACAqC,UACAE,cAAc,CACf;AAEL;AAMM,SAAU0F,gBACdC,aAAiC;AAEjCxQ,SAAAC,QACE,OAAOhC,oBAAoB,aAC3B,yOAG+C,IAChD;AAED,MAAIwS,yBAA+BzK,cAAOjI,mBAAmByS,WAAW,CAAC;AACzE,MAAIE,wBAA8B1K,cAAO,KAAK;AAE9C,MAAI2B,WAAWuF,YAAW;AAC1B,MAAIlO,eAAqB8I,eACvB;;;;IAIEjJ,2BACE8I,SAASoE,QACT2E,sBAAsB9J,UAAU,OAAO6J,uBAAuB7J,OAAO;KAEzE,CAACe,SAASoE,MAAM,CAAC;AAGnB,MAAI5D,WAAWkI,YAAW;AAC1B,MAAIM,kBAAwBxK,mBAC1B,CAACyK,UAAUC,oBAAmB;AAC5B,UAAMC,kBAAkB/S,mBACtB,OAAO6S,aAAa,aAAaA,SAAS5R,YAAY,IAAI4R,QAAQ;AAEpEF,0BAAsB9J,UAAU;AAChCuB,aAAS,MAAM2I,iBAAiBD,eAAe;EACjD,GACA,CAAC1I,UAAUnJ,YAAY,CAAC;AAG1B,SAAO,CAACA,cAAc2R,eAAe;AACvC;AA2CA,SAASI,+BAA4B;AACnC,MAAI,OAAOtR,aAAa,aAAa;AACnC,UAAM,IAAIoB,MACR,+GACgE;EAEnE;AACH;AAEA,IAAImQ,YAAY;AAChB,IAAIC,qBAAqBA,MAAA,OAAWC,OAAO,EAAEF,SAAS,IAAK;SAM3CpC,YAAS;AACvB,MAAI;IAAE1J;EAAM,IAAK6K,sBAAqBJ,gBAAewB,SAAS;AAC9D,MAAI;IAAEhR;EAAQ,IAAW8K,kBAAWC,iBAAiB;AACrD,MAAIkG,iBAAiBC,WAAU;AAE/B,SAAalL,mBACX,SAACtI,QAAQyT,SAAgB;AAAA,QAAhBA,YAAO,QAAA;AAAPA,gBAAU,CAAA;IAAE;AACnBP,iCAA4B;AAE5B,QAAI;MAAE1Q;MAAQD;MAAQL;MAASO;MAAUC;IAAI,IAAKL,sBAChDrC,QACAsC,QAAQ;AAGV,QAAImR,QAAQnJ,aAAa,OAAO;AAC9B,UAAI3J,MAAM8S,QAAQ/C,cAAc0C,mBAAkB;AAClD/L,aAAOqM,MAAM/S,KAAK4S,gBAAgBE,QAAQjR,UAAUA,QAAQ;QAC1DiI,oBAAoBgJ,QAAQhJ;QAC5BhI;QACAC;QACAwO,YAAYuC,QAAQlR,UAAWA;QAC/BoR,aAAaF,QAAQvR,WAAYA;QACjCyG,WAAW8K,QAAQ9K;MACpB,CAAA;IACF,OAAM;AACLtB,aAAOiD,SAASmJ,QAAQjR,UAAUA,QAAQ;QACxCiI,oBAAoBgJ,QAAQhJ;QAC5BhI;QACAC;QACAwO,YAAYuC,QAAQlR,UAAWA;QAC/BoR,aAAaF,QAAQvR,WAAYA;QACjCwI,SAAS+I,QAAQ/I;QACjBlG,OAAOiP,QAAQjP;QACfoP,aAAaL;QACb5K,WAAW8K,QAAQ9K;QACnBqE,gBAAgByG,QAAQzG;MACzB,CAAA;IACF;KAEH,CAAC3F,QAAQ/E,UAAUiR,cAAc,CAAC;AAEtC;AAIM,SAAUtC,cACdzO,QAAeqR,QACsC;AAAA,MAArD;IAAE/G;0BAAiD,CAAA,IAAE+G;AAErD,MAAI;IAAEvR;EAAQ,IAAW8K,kBAAWC,iBAAiB;AACrD,MAAIyG,eAAqB1G,kBAAW2G,YAAY;AAChD,GAAUD,eAAY3R,OAAtBiQ,UAAS,OAAe,kDAAkD,IAA1EA,UAAS,KAAA,IAAA;AAET,MAAI,CAAC4B,KAAK,IAAIF,aAAaG,QAAQC,MAAM,EAAE;AAG3C,MAAInG,OAAInK,UAAQwL,CAAAA,GAAAA,gBAAgB5M,SAASA,SAAS,KAAK;IAAEsK;EAAQ,CAAE,CAAC;AAKpE,MAAIhD,WAAWuF,YAAW;AAC1B,MAAI7M,UAAU,MAAM;AAGlBuL,SAAKG,SAASpE,SAASoE;AAKvB,QAAIiG,SAAS,IAAI/T,gBAAgB2N,KAAKG,MAAM;AAC5C,QAAIkG,cAAcD,OAAO5S,OAAO,OAAO;AACvC,QAAI8S,qBAAqBD,YAAYE,KAAMvT,OAAMA,MAAM,EAAE;AACzD,QAAIsT,oBAAoB;AACtBF,aAAOlL,OAAO,OAAO;AACrBmL,kBAAYhE,OAAQrP,OAAMA,CAAC,EAAEK,QAASL,OAAMoT,OAAO3S,OAAO,SAAST,CAAC,CAAC;AACrE,UAAIwT,KAAKJ,OAAOK,SAAQ;AACxBzG,WAAKG,SAASqG,KAASA,MAAAA,KAAO;IAC/B;EACF;AAED,OAAK,CAAC/R,UAAUA,WAAW,QAAQwR,MAAMS,MAAMC,OAAO;AACpD3G,SAAKG,SAASH,KAAKG,SACfH,KAAKG,OAAOxD,QAAQ,OAAO,SAAS,IACpC;EACL;AAMD,MAAIpI,aAAa,KAAK;AACpByL,SAAKC,WACHD,KAAKC,aAAa,MAAM1L,WAAWqS,UAAU,CAACrS,UAAUyL,KAAKC,QAAQ,CAAC;EACzE;AAED,SAAOyE,WAAW1E,IAAI;AACxB;SAgBgB6G,WAAUC,QAEF;AAAA,MAAAC;AAAA,MAFgB;IACtCnU;0BACoB,CAAA,IAAEkU;AACtB,MAAI;IAAExN;EAAM,IAAK6K,sBAAqBJ,gBAAeiD,UAAU;AAC/D,MAAIvQ,QAAQ6N,oBAAmBN,qBAAoBgD,UAAU;AAC7D,MAAI7M,cAAoBkF,kBAAWtH,eAAe;AAClD,MAAI2O,QAAcrH,kBAAW2G,YAAY;AACzC,MAAIiB,WAAOF,iBAAGL,MAAMR,QAAQQ,MAAMR,QAAQnE,SAAS,CAAC,MAAC,OAAA,SAAvCgF,eAAyCL,MAAMQ;AAE7D,GAAU/M,cAAW/F,OAArBiQ,UAAS,OAAA,kDAAA,IAATA,UAAS,KAAA,IAAA;AACT,GAAUqC,QAAKtS,OAAfiQ,UAAS,OAAA,+CAAA,IAATA,UAAS,KAAA,IAAA;AACT,IACE4C,WAAW,QAAI7S,OADjBiQ,UAAS,OAAA,kEAAA,IAATA,UAAS,KAAA,IAAA;AAQT,MAAI8C,aAAa3O,YAAYA,UAAS,IAAK;AAC3C,MAAI,CAACmK,YAAYyE,aAAa,IAAU5N,gBAAiB5G,OAAOuU,UAAU;AAC1E,MAAIvU,OAAOA,QAAQ+P,YAAY;AAC7ByE,kBAAcxU,GAAG;EAClB,WAAU,CAAC+P,YAAY;AAEtByE,kBAAc/B,mBAAkB,CAAE;EACnC;AAGDlN,EAAM0D,iBAAU,MAAK;AACnBvC,WAAO+N,WAAW1E,UAAU;AAC5B,WAAO,MAAK;AAIVrJ,aAAOgO,cAAc3E,UAAU;;EAEnC,GAAG,CAACrJ,QAAQqJ,UAAU,CAAC;AAGvB,MAAI4E,OAAahN,mBACf,CAACqF,MAAclK,SAAkC;AAC/C,KAAUuR,UAAO7S,OAAjBiQ,UAAS,OAAU,yCAAyC,IAA5DA,UAAS,KAAA,IAAA;AACT/K,WAAOqM,MAAMhD,YAAYsE,SAASrH,MAAMlK,IAAI;KAE9C,CAACiN,YAAYsE,SAAS3N,MAAM,CAAC;AAG/B,MAAIkO,aAAaxE,UAAS;AAC1B,MAAID,SAAexI,mBACjB,CAACtI,QAAQyD,SAAQ;AACf8R,eAAWvV,QAAM4D,UAAA,CAAA,GACZH,MAAI;MACP6G,UAAU;MACVoG;IAAU,CAAA,CACX;EACH,GACA,CAACA,YAAY6E,UAAU,CAAC;AAG1B,MAAIC,cAAoBvL,eAAQ,MAAK;AACnC,QAAIuL,eAAoB/I,kBACtB,CAACmE,OAAOhE,QAAO;AACb,aACG/K,qBAAA0O,MAAI3M,UAAA,CAAA,GAAKgN,OAAK;QAAEtG,UAAU;QAAOoG;QAAwB9D;MAAQ,CAAA,CAAA;IAEtE,CAAC;AAEH,QAAAzK,MAAa;AACXqT,MAAAA,aAAY3P,cAAc;IAC3B;AACD,WAAO2P;EACT,GAAG,CAAC9E,UAAU,CAAC;AAGf,MAAI5H,UAAUtE,MAAMqE,SAAS4M,IAAI/E,UAAU,KAAKgF;AAChD,MAAIvQ,OAAO+C,YAAYuN,IAAI/E,UAAU;AACrC,MAAIiF,wBAA8B1L,eAChC,MAAArG,UAAA;IACE2M,MAAMiF;IACN1E;IACAwE;EAAI,GACDxM,SAAO;IACV3D;EAAI,CAAA,GAEN,CAACqQ,aAAa1E,QAAQwE,MAAMxM,SAAS3D,IAAI,CAAC;AAG5C,SAAOwQ;AACT;SAMgBC,cAAW;AACzB,MAAIpR,QAAQ6N,oBAAmBN,qBAAoB8D,WAAW;AAC9D,SAAOxV,MAAMyV,KAAKtR,MAAMqE,SAASjE,QAAO,CAAE,EAAE9D,IAAIiV,YAAA;AAAA,QAAC,CAACpV,KAAKmI,OAAO,IAACiN;AAAA,WAAAnS,UAAA,CAAA,GAC1DkF,SAAO;MACVnI;IAAG,CAAA;EAAA,CACH;AACJ;AAEA,IAAMqV,iCAAiC;AACvC,IAAIC,uBAA+C,CAAA;AAKnD,SAASpE,qBAAoBqE,QAMvB;AAAA,MANwB;IAC5BvE;IACAC;0BAIE,CAAA,IAAEsE;AACJ,MAAI;IAAE7O;EAAM,IAAK6K,sBAAqBJ,gBAAeqE,oBAAoB;AACzE,MAAI;IAAEC;IAAuB3L;EAAoB,IAAG4H,oBAClDN,qBAAoBoE,oBAAoB;AAE1C,MAAI;IAAE7T;EAAQ,IAAW8K,kBAAWC,iBAAiB;AACrD,MAAIvD,WAAWuF,YAAW;AAC1B,MAAI4E,UAAUoC,WAAU;AACxB,MAAI1G,aAAa2G,cAAa;AAG9BpQ,EAAM0D,iBAAU,MAAK;AACnBvG,WAAOS,QAAQyS,oBAAoB;AACnC,WAAO,MAAK;AACVlT,aAAOS,QAAQyS,oBAAoB;;KAEpC,CAAA,CAAE;AAGLC,cACQlO,mBAAY,MAAK;AACrB,QAAIqH,WAAWnL,UAAU,QAAQ;AAC/B,UAAI7D,OAAOgR,SAASA,OAAO7H,UAAUmK,OAAO,IAAI,SAASnK,SAASnJ;AAClEsV,2BAAqBtV,GAAG,IAAI0C,OAAOoT;IACpC;AACD,QAAI;AACFC,qBAAeC,QACb/E,cAAcoE,gCACdY,KAAKC,UAAUZ,oBAAoB,CAAC;aAE/B1Q,OAAO;AACdpD,aAAAC,QACE,OAAK,sGAC+FmD,QAAK,IAAI,IAC9G;IACF;AACDlC,WAAOS,QAAQyS,oBAAoB;EACrC,GAAG,CAAC3E,YAAYD,QAAQhC,WAAWnL,OAAOsF,UAAUmK,OAAO,CAAC,CAAC;AAI/D,MAAI,OAAOrS,aAAa,aAAa;AAEnCsE,IAAMwD,uBAAgB,MAAK;AACzB,UAAI;AACF,YAAIoN,mBAAmBJ,eAAeK,QACpCnF,cAAcoE,8BAA8B;AAE9C,YAAIc,kBAAkB;AACpBb,iCAAuBW,KAAKI,MAAMF,gBAAgB;QACnD;eACMhV,GAAG;MACV;IAEJ,GAAG,CAAC8P,UAAU,CAAC;AAIf1L,IAAMwD,uBAAgB,MAAK;AACzB,UAAIuN,wBACFtF,UAAUrP,aAAa,MACnB,CAACwH,WAAUmK,aACTtC;;QACE/N,UAAA,CAAA,GAEKkG,WAAQ;UACXkE,UACEnL,cAAciH,UAASkE,UAAU1L,QAAQ,KACzCwH,UAASkE;SAEbiG;QAAAA;MAAO,IAEXtC;AACN,UAAIuF,2BAA2B7P,UAAAA,OAAAA,SAAAA,OAAQ8P,wBACrClB,sBACA,MAAM5S,OAAOoT,SACbQ,qBAAqB;AAEvB,aAAO,MAAMC,4BAA4BA,yBAAwB;OAChE,CAAC7P,QAAQ/E,UAAUqP,MAAM,CAAC;AAI7BzL,IAAMwD,uBAAgB,MAAK;AAEzB,UAAI0M,0BAA0B,OAAO;AACnC;MACD;AAGD,UAAI,OAAOA,0BAA0B,UAAU;AAC7C/S,eAAO+T,SAAS,GAAGhB,qBAAqB;AACxC;MACD;AAGD,UAAItM,SAASqE,MAAM;AACjB,YAAIkJ,KAAKzV,SAAS0V,eAChBC,mBAAmBzN,SAASqE,KAAK+F,MAAM,CAAC,CAAC,CAAC;AAE5C,YAAImD,IAAI;AACNA,aAAGG,eAAc;AACjB;QACD;MACF;AAGD,UAAI/M,uBAAuB,MAAM;AAC/B;MACD;AAGDpH,aAAO+T,SAAS,GAAG,CAAC;OACnB,CAACtN,UAAUsM,uBAAuB3L,kBAAkB,CAAC;EACzD;AACH;AAYgB,SAAAgN,gBACdC,UACAjE,SAA+B;AAE/B,MAAI;IAAEkE;EAAO,IAAKlE,WAAW,CAAA;AAC7BvN,EAAM0D,iBAAU,MAAK;AACnB,QAAInG,OAAOkU,WAAW,OAAO;MAAEA;IAAS,IAAGxU;AAC3CE,WAAOuU,iBAAiB,gBAAgBF,UAAUjU,IAAI;AACtD,WAAO,MAAK;AACVJ,aAAOwU,oBAAoB,gBAAgBH,UAAUjU,IAAI;;EAE7D,GAAG,CAACiU,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASnB,YACPkB,UACAjE,SAA+B;AAE/B,MAAI;IAAEkE;EAAO,IAAKlE,WAAW,CAAA;AAC7BvN,EAAM0D,iBAAU,MAAK;AACnB,QAAInG,OAAOkU,WAAW,OAAO;MAAEA;IAAS,IAAGxU;AAC3CE,WAAOuU,iBAAiB,YAAYF,UAAUjU,IAAI;AAClD,WAAO,MAAK;AACVJ,aAAOwU,oBAAoB,YAAYH,UAAUjU,IAAI;;EAEzD,GAAG,CAACiU,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASG,UAASC,QAMjB;AAAA,MANkB;IACjBC;IACAxS;EAID,IAAAuS;AACC,MAAIE,UAAUC,WAAWF,IAAI;AAE7B9R,EAAM0D,iBAAU,MAAK;AACnB,QAAIqO,QAAQzT,UAAU,WAAW;AAC/B,UAAI2T,UAAU9U,OAAO+U,QAAQ5S,OAAO;AACpC,UAAI2S,SAAS;AAIXE,mBAAWJ,QAAQE,SAAS,CAAC;MAC9B,OAAM;AACLF,gBAAQK,MAAK;MACd;IACF;EACH,GAAG,CAACL,SAASzS,OAAO,CAAC;AAErBU,EAAM0D,iBAAU,MAAK;AACnB,QAAIqO,QAAQzT,UAAU,aAAa,CAACwT,MAAM;AACxCC,cAAQK,MAAK;IACd;EACH,GAAG,CAACL,SAASD,IAAI,CAAC;AACpB;AAYA,SAASzI,uBACP/E,IACA/G,MAA6C;AAAA,MAA7CA,SAAAA,QAAAA;AAAAA,WAA2C,CAAA;EAAE;AAE7C,MAAIiE,YAAkB0F,kBAAW1H,qBAAqB;AAEtD,IACEgC,aAAa,QAAIvF,OADnBiQ,UAEE,OAAA,wJACqE,IAHvEA,UAAS,KAAA,IAAA;AAMT,MAAI;IAAE9P;EAAQ,IAAK4P,sBACjBJ,gBAAevC,sBAAsB;AAEvC,MAAIxB,OAAOqB,gBAAgB5E,IAAI;IAAEsC,UAAUrJ,KAAKqJ;EAAQ,CAAE;AAC1D,MAAI,CAACpF,UAAU9B,iBAAiB;AAC9B,WAAO;EACR;AAED,MAAI2S,cACF1V,cAAc6E,UAAU2B,gBAAgB2E,UAAU1L,QAAQ,KAC1DoF,UAAU2B,gBAAgB2E;AAC5B,MAAIwK,WACF3V,cAAc6E,UAAU4B,aAAa0E,UAAU1L,QAAQ,KACvDoF,UAAU4B,aAAa0E;AAezB,SACEyK,UAAU1K,KAAKC,UAAUwK,QAAQ,KAAK,QACtCC,UAAU1K,KAAKC,UAAUuK,WAAW,KAAK;AAE7C;", "names": ["DataRouterContext", "createContext", "process", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "isDataRoute", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "decodePath", "navigateEffectWarning", "useIsomorphicLayoutEffect", "cb", "isStatic", "static", "React", "useLayoutEffect", "useNavigate", "useNavigateStable", "useNavigateUnstable", "dataRouterContext", "future", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getResolveToMatches", "v7_relativeSplatPath", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "createElement", "Provider", "value", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "useRoutesImpl", "dataRouterState", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "pathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "parentSegments", "split", "segments", "slice", "join", "matchRoutes", "element", "undefined", "Component", "lazy", "renderedMatches", "_renderMatches", "map", "match", "Object", "assign", "encodeLocation", "_extends", "key", "NavigationType", "Pop", "DefaultErrorComponent", "error", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "console", "Fragment", "style", "fontStyle", "defaultErrorElement", "RenderErrorBoundary", "constructor", "props", "revalidation", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "staticContext", "errorElement", "Error<PERSON>ou<PERSON><PERSON>", "_deepestRenderedBoundaryId", "id", "_dataRouterState", "_future", "errors", "v7_partialHydration", "initialized", "errorIndex", "findIndex", "m", "keys", "Math", "min", "renderFallback", "fallbackIndex", "i", "HydrateFallback", "hydrateFallbackElement", "loaderData", "needsToRunLoader", "loader", "reduceRight", "index", "shouldRenderHydrateFallback", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useRouteId", "UseRouteId", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "useMatches", "UseMatches", "convertRouteMatchToUiMatch", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "set<PERSON><PERSON>er<PERSON>ey", "useState", "blockerFunction", "arg", "currentLocation", "nextLocation", "historyAction", "stripBasename", "useEffect", "String", "deleteBlocker", "get<PERSON><PERSON>er", "blockers", "has", "get", "IDLE_BLOCKER", "UseNavigateStable", "fromRouteId", "alreadyWarned", "cond", "warnOnce", "warn", "logDeprecation", "flag", "msg", "link", "logV6DeprecationWarnings", "renderFuture", "routerFuture", "v7_startTransition", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_skipActionErrorRevalidation", "START_TRANSITION", "startTransitionImpl", "MemoryRouter", "_ref3", "basename", "children", "initialEntries", "initialIndex", "future", "historyRef", "useRef", "current", "createMemoryHistory", "v5Compat", "history", "state", "setStateImpl", "useState", "action", "location", "v7_startTransition", "setState", "useCallback", "newState", "startTransitionImpl", "React", "useLayoutEffect", "listen", "useEffect", "logV6DeprecationWarnings", "createElement", "Router", "navigationType", "navigator", "Navigate", "_ref4", "to", "replace", "relative", "useInRouterContext", "process", "invariant", "static", "isStatic", "useContext", "NavigationContext", "warning", "matches", "RouteContext", "pathname", "locationPathname", "useLocation", "navigate", "useNavigate", "path", "resolveTo", "getResolveToMatches", "v7_relativeSplatPath", "jsonPath", "JSON", "stringify", "parse", "Outlet", "props", "useOutlet", "context", "Route", "_props", "_ref5", "basenameProp", "locationProp", "NavigationType", "Pop", "staticProp", "navigationContext", "useMemo", "_extends", "parsePath", "search", "hash", "key", "locationContext", "trailingPathname", "stripBasename", "Provider", "value", "LocationContext", "Routes", "_ref6", "useRoutes", "createRoutesFromChildren", "Await", "_ref7", "errorElement", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "Component", "constructor", "error", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "promise", "status", "pending", "success", "Object", "defineProperty", "get", "renderError", "reject", "catch", "_tracked", "then", "data", "_error", "Aborted<PERSON>eferredError", "AwaitContext", "_ref8", "useAsyncValue", "to<PERSON><PERSON>", "Fragment", "parentPath", "routes", "Children", "for<PERSON>ach", "element", "index", "isValidElement", "treePath", "type", "push", "apply", "name", "route", "id", "join", "caseSensitive", "loader", "Error<PERSON>ou<PERSON><PERSON>", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "lazy", "renderMatches", "_renderMatches", "mapRouteProperties", "updates", "assign", "undefined", "HydrateFallback", "hydrateFallbackElement", "createMemoryRouter", "opts", "createRouter", "v7_prependBasename", "hydrationData", "dataStrategy", "patchRoutesOnNavigation", "initialize", "defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "dataStrategy", "patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "createContext", "isTransitioning", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "React", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "fetcher", "current", "set", "delete", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "logV6DeprecationWarnings", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "DataRoutes", "_ref3", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "HistoryRouter", "_ref6", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "useRouteId", "options", "fetch", "formEncType", "fromRouteId", "_temp2", "routeContext", "RouteContext", "match", "matches", "slice", "params", "indexValues", "hasNakedIndexParam", "some", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "_temp3", "_route$matches", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "fetcherWithComponents", "useFetchers", "UseFetchers", "from", "_ref11", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp4", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref12", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"]}