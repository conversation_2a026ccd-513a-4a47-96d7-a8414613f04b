{"name": "react-router", "version": "6.30.1", "description": "Declarative routing for React", "keywords": ["react", "router", "route", "routing", "history", "link"], "repository": {"type": "git", "url": "https://github.com/remix-run/react-router", "directory": "packages/react-router"}, "license": "MIT", "author": "Remix Software <<EMAIL>>", "sideEffects": false, "main": "./dist/main.js", "unpkg": "./dist/umd/react-router.production.min.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"@remix-run/router": "1.23.0"}, "devDependencies": {"react": "^18.2.0", "react-router-dom": "6.30.1"}, "peerDependencies": {"react": ">=16.8"}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md"], "engines": {"node": ">=14.0.0"}}