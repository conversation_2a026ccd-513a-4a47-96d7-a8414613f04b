<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TapTap - Zalo Mini App</title>
  </head>
  <body>
    <div id="app"></div>
    <script>
      // Polyfill for process global in browser
      if (typeof process === 'undefined') {
        window.process = { env: {} };
      }
    </script>
    <script type="module" src="/src/app.tsx"></script>
  </body>
</html>
