import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react({
    // Ensure React 18 compatibility
    jsxRuntime: 'automatic'
  })],
  define: {
    // Define process.env for browser compatibility
    'process.env': '{}',
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'process.env.VITE_API_BASE_URL': JSON.stringify(process.env.VITE_API_BASE_URL),
    'process.env.VITE_PLATFORM': JSON.stringify(process.env.VITE_PLATFORM || 'zalo'),
    'process.env.VITE_ZALO_APP_ID': JSON.stringify(process.env.VITE_ZALO_APP_ID),
    'process.env.VITE_OA_ID': JSON.stringify(process.env.VITE_OA_ID),
    // Add process polyfill for Vite client
    'process': JSON.stringify({ env: {} }),
    'global': 'globalThis',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../../shared'),
      '@taptap/shared': path.resolve(__dirname, '../../shared/index'),
    },
    dedupe: ['react', 'react-dom'],
  },
  server: {
    port: 9080,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          zmp: ['zmp-ui', 'zmp-sdk'],
        },
      },
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'zmp-ui', 'zmp-sdk'],
  },
})
