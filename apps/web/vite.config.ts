import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  define: {
    // Define process.env for browser compatibility
    'process.env': '{}',
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    'process.env.VITE_API_BASE_URL': JSON.stringify(process.env.VITE_API_BASE_URL),
    'process.env.VITE_PLATFORM': JSON.stringify(process.env.VITE_PLATFORM || 'web'),
    // Add process polyfill for Vite client
    'process': JSON.stringify({ env: {} }),
    'global': 'globalThis',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../../shared'),
      '@taptap/shared': path.resolve(__dirname, '../../shared/index'),
    },
  },
  server: {
    port: 9070,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      external: [
        // Mark zmp-sdk as external for web builds since it's Zalo-specific
        'zmp-sdk',
        'zmp-ui'
      ],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
        },
        globals: {
          'zmp-sdk': 'ZMPSdk',
          'zmp-ui': 'ZMPUi'
        }
      },
    },
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
  },
})
