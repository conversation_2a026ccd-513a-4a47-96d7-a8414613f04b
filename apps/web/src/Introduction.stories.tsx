import type { Meta } from '@storybook/react';

const meta: Meta = {
  title: 'Introduction',
  parameters: {
    layout: 'centered',
    docs: {
      page: () => (
        <div className="max-w-4xl mx-auto p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            TapTap Component Library
          </h1>
          
          <div className="prose prose-lg max-w-none">
            <p className="text-xl text-gray-600 mb-8">
              Welcome to the TapTap component library! This Storybook contains all the 
              reusable UI components used across our multi-platform application.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h2 className="text-xl font-semibold text-blue-900 mb-3">
                🚀 Getting Started
              </h2>
              <ul className="text-blue-800 space-y-2">
                <li>• Browse components in the sidebar</li>
                <li>• Interact with component properties in the Controls panel</li>
                <li>• View component documentation in the Docs tab</li>
                <li>• Test responsive behavior with the viewport toolbar</li>
              </ul>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  📱 Multi-Platform Support
                </h3>
                <p className="text-gray-600">
                  Components are designed to work seamlessly across Web and 
                  Zalo Mini App platforms with consistent behavior and styling.
                </p>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  🎨 Design System
                </h3>
                <p className="text-gray-600">
                  Built with Tailwind CSS using a consistent color palette, 
                  typography scale, and spacing system for unified design.
                </p>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  ⚡ TypeScript
                </h3>
                <p className="text-gray-600">
                  All components are fully typed with TypeScript for better 
                  development experience and type safety.
                </p>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  ♿ Accessible
                </h3>
                <p className="text-gray-600">
                  Components follow accessibility best practices with proper 
                  ARIA labels, keyboard navigation, and screen reader support.
                </p>
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-3">
                📚 Component Categories
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Shared Components</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Button</li>
                    <li>• Input</li>
                    <li>• Card</li>
                    <li>• Modal</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Web Components</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Layout</li>
                    <li>• Navigation</li>
                    <li>• LoadingSpinner</li>
                    <li>• ErrorBoundary</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Zalo Components</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• ZaloButton</li>
                    <li>• ZaloHeader</li>
                    <li>• ZaloTabBar</li>
                    <li>• ZaloModal</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  },
};

export default meta;

export const Welcome = {
  name: 'Welcome to TapTap Storybook',
};
