import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import App from './App'
import './styles/tailwind.css'

// Error boundary for development
if (process.env.NODE_ENV === 'development') {
  // Add error boundary in development
  const originalError = console.error;
  console.error = (...args) => {
    originalError(...args);
    // You can add error reporting here
  };
}

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
)

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>
)
