import React from 'react';
import { Button } from '@taptap/shared';

export const HomePage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to TapTap</h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Your gaming hub for discovering new games and connecting with friends.
        </p>
        <div className="space-y-4">
          <Button 
            onClick={() => alert('Explore games!')}
            className="btn btn-primary px-8 py-3 text-lg"
          >
            Explore Games
          </Button>
        </div>
      </div>
      
      <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="card text-center">
          <h3 className="text-xl font-semibold mb-2">Discover Games</h3>
          <p className="text-gray-600">Find your next favorite game from thousands of options.</p>
        </div>
        <div className="card text-center">
          <h3 className="text-xl font-semibold mb-2">Connect with Friends</h3>
          <p className="text-gray-600">Share your gaming experiences and compete with friends.</p>
        </div>
        <div className="card text-center">
          <h3 className="text-xl font-semibold mb-2">Track Progress</h3>
          <p className="text-gray-600">Monitor your gaming achievements and statistics.</p>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
