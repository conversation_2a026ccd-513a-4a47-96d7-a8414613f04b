import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  className = '' 
}) => {
  const sizeClasses = {
    small: 'w-5 h-5',
    medium: 'w-10 h-10',
    large: 'w-16 h-16'
  };

  return (
    <div className={`mx-auto ${sizeClasses[size]} ${className}`}>
      <div className="animate-spin rounded-full border-4 border-primary-200 border-t-primary-600 h-full w-full"></div>
    </div>
  );
};
