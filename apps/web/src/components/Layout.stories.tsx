import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Layout } from './Layout';

const meta: Meta<typeof Layout> = {
  title: 'Web/Layout',
  component: Layout,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'The main layout component for the web application with header, navigation, and content areas.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const SampleContent = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Welcome to TapTap</h2>
      <p className="text-gray-600 mb-4">
        This is a sample content area within the layout component. The layout provides
        a consistent structure with header navigation and responsive design.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <div key={item} className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900">Card {item}</h3>
            <p className="text-sm text-gray-600 mt-1">Sample card content</p>
          </div>
        ))}
      </div>
    </div>
    
    <div className="bg-blue-50 rounded-lg p-6">
      <h3 className="text-lg font-medium text-blue-900 mb-2">Features</h3>
      <ul className="space-y-2 text-blue-800">
        <li>• Responsive design that works on all devices</li>
        <li>• Consistent navigation across the application</li>
        <li>• Modern and clean UI design</li>
        <li>• Accessible and user-friendly interface</li>
      </ul>
    </div>
  </div>
);

export const Default: Story = {
  args: {
    children: <SampleContent />,
  },
};

export const WithLongContent: Story = {
  args: {
    children: (
      <div className="space-y-6">
        <SampleContent />
        <div className="bg-gray-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Long Content Example</h2>
          <div className="space-y-4 text-gray-600">
            {Array.from({ length: 20 }, (_, i) => (
              <p key={i}>
                This is paragraph {i + 1} of long content to demonstrate how the layout
                handles scrollable content. The layout should maintain its structure
                while allowing the content area to scroll naturally.
              </p>
            ))}
          </div>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: 'Layout with long content to demonstrate scrolling behavior.',
      },
    },
  },
};

export const MobileView: Story = {
  args: {
    children: <SampleContent />,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'Layout optimized for mobile viewing with responsive navigation.',
      },
    },
  },
};

export const TabletView: Story = {
  args: {
    children: <SampleContent />,
  },
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
    docs: {
      description: {
        story: 'Layout on tablet-sized screens showing responsive behavior.',
      },
    },
  },
};
