import type { <PERSON>a, StoryObj } from '@storybook/react';
import { LoadingSpinner } from './LoadingSpinner';

const meta: Meta<typeof LoadingSpinner> = {
  title: 'Web/LoadingSpinner',
  component: LoadingSpinner,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A loading spinner component for indicating loading states in the application.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const InCard: Story = {
  render: () => (
    <div className="bg-white rounded-lg shadow-sm border p-8 w-64">
      <h3 className="text-lg font-medium text-gray-900 mb-4 text-center">
        Loading Content
      </h3>
      <LoadingSpinner />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Loading spinner displayed within a card component.',
      },
    },
  },
};

export const FullPage: Story = {
  render: () => (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner />
        <p className="text-gray-600 mt-4">Loading application...</p>
      </div>
    </div>
  ),
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Full page loading state with spinner and message.',
      },
    },
  },
};

export const MultipleSpinners: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-8 p-8">
      <div className="text-center">
        <LoadingSpinner />
        <p className="text-sm text-gray-600 mt-2">Loading...</p>
      </div>
      <div className="text-center">
        <LoadingSpinner />
        <p className="text-sm text-gray-600 mt-2">Processing...</p>
      </div>
      <div className="text-center">
        <LoadingSpinner />
        <p className="text-sm text-gray-600 mt-2">Saving...</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Multiple spinners showing different loading states.',
      },
    },
  },
};
