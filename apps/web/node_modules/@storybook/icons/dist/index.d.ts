import * as React from 'react';
import React__default from 'react';

declare const iconList: {
    name: string;
    icons: string[];
}[];

interface IconProps extends React__default.SVGAttributes<SVGElement> {
    children?: never;
    color?: string;
    size?: number;
}

declare const PhotoIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ComponentIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GridIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const OutlineIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PhotoDragIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PhotoStabilizeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CameraStabilizeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GridAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SearchIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ZoomIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ZoomOutIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ZoomResetIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const EyeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const EyeCloseIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LightningIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LightningOffIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MirrorIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GrowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ContrastIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SwitchAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ContrastIgnoredIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PaintBrushIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RulerIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CameraIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const VideoIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SpeakerIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PlayIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PlayBackIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PlayNextIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RewindIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FastForwardIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StopAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SunIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MoonIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StopAltHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PlayHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PlayAllHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StopIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SideBySideIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StackedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BookIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DocumentIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CopyIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CategoryIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FolderIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PrintIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GraphLineIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CalendarIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GraphBarIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AlignLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AlignRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FilterIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DocChartIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DocListIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DragIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MenuIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MarkupIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BoldIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ItalicIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PaperClipIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ListOrderedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ListUnorderedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ParagraphIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MarkdownIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RepoIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CommitIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BranchIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PullRequestIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MergeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AppleIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LinuxIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UbuntuIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const WindowsIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChromeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StorybookIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AzureDevOpsIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BitbucketIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChromaticIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ComponentDrivenIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DiscordIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FacebookIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FigmaIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GDriveIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GithubIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GitlabIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GoogleIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GraphqlIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MediumIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ReduxIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TwitterIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const YoutubeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const VSCodeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LinkedinIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const XIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BrowserIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TabletIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MobileIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const WatchIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SidebarIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SidebarAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SidebarAltToggleIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SidebarToggleIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BottomBarIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BottomBarToggleIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CPUIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DatabaseIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const MemoryIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StructureIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BoxIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PowerIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const EditIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CogIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const NutIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const WrenchIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const EllipsisIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const WandIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SweepIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CheckIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FormIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BatchDenyIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BatchAcceptIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ControlsIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PlusIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CloseAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CrossIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TrashIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PinAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UnpinIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AddIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SubtractIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CloseIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DeleteIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PassedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChangedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FailedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ClearIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CommentIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CommentAddIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RequestChangeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CommentsIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChatIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LockIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UnlockIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const KeyIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const OutboxIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CreditIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ButtonIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TypeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PointerDefaultIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PointerHandIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CommandIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SaveIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const InfoIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const QuestionIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SupportIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AlertIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AlertAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const EmailIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PhoneIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LinkIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LinkBrokenIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BellIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RSSIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ShareAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ShareIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const JumpToIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CircleHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CircleIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BookmarkHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BookmarkIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DiamondIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const HeartHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const HeartIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StarHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StarIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CertificateIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const VerifiedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ThumbsUpIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ShieldIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BasketIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BeakerIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const HourglassIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FlagIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CloudHollowIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CloudIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StickerIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StatusFailIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StatusIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StatusWarnIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const StatusPassIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronUpIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronDownIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronSmallUpIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronSmallDownIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronSmallLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ChevronSmallRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowUpIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowDownIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowTopLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowTopRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowBottomLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowBottomRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowSolidUpIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowSolidDownIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowSolidLeftIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ArrowSolidRightIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ExpandAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CollapseIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ExpandIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UnfoldIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TransferIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RedirectIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UndoIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ReplyIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const SyncIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UploadIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DownloadIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const BackIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ProceedIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const RefreshIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const GlobeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const CompassIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const LocationIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const PinIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TimeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DashboardIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const TimerIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const HomeIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AdminIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const DirectionIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UserIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UserAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UserAddIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const UsersIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const ProfileIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FaceHappyIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FaceNeutralIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const FaceSadIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AccessibilityIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AccessibilityAltIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

declare const AccessibilityIgnoredIcon: React.ForwardRefExoticComponent<IconProps & React.RefAttributes<SVGSVGElement>>;

export { AccessibilityAltIcon, AccessibilityIcon, AccessibilityIgnoredIcon, AddIcon, AdminIcon, AlertAltIcon, AlertIcon, AlignLeftIcon, AlignRightIcon, AppleIcon, ArrowBottomLeftIcon, ArrowBottomRightIcon, ArrowDownIcon, ArrowLeftIcon, ArrowRightIcon, ArrowSolidDownIcon, ArrowSolidLeftIcon, ArrowSolidRightIcon, ArrowSolidUpIcon, ArrowTopLeftIcon, ArrowTopRightIcon, ArrowUpIcon, AzureDevOpsIcon, BackIcon, BasketIcon, BatchAcceptIcon, BatchDenyIcon, BeakerIcon, BellIcon, BitbucketIcon, BoldIcon, BookIcon, BookmarkHollowIcon, BookmarkIcon, BottomBarIcon, BottomBarToggleIcon, BoxIcon, BranchIcon, BrowserIcon, ButtonIcon, CPUIcon, CalendarIcon, CameraIcon, CameraStabilizeIcon, CategoryIcon, CertificateIcon, ChangedIcon, ChatIcon, CheckIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, ChevronSmallDownIcon, ChevronSmallLeftIcon, ChevronSmallRightIcon, ChevronSmallUpIcon, ChevronUpIcon, ChromaticIcon, ChromeIcon, CircleHollowIcon, CircleIcon, ClearIcon, CloseAltIcon, CloseIcon, CloudHollowIcon, CloudIcon, CogIcon, CollapseIcon, CommandIcon, CommentAddIcon, CommentIcon, CommentsIcon, CommitIcon, CompassIcon, ComponentDrivenIcon, ComponentIcon, ContrastIcon, ContrastIgnoredIcon, ControlsIcon, CopyIcon, CreditIcon, CrossIcon, DashboardIcon, DatabaseIcon, DeleteIcon, DiamondIcon, DirectionIcon, DiscordIcon, DocChartIcon, DocListIcon, DocumentIcon, DownloadIcon, DragIcon, EditIcon, EllipsisIcon, EmailIcon, ExpandAltIcon, ExpandIcon, EyeCloseIcon, EyeIcon, FaceHappyIcon, FaceNeutralIcon, FaceSadIcon, FacebookIcon, FailedIcon, FastForwardIcon, FigmaIcon, FilterIcon, FlagIcon, FolderIcon, FormIcon, GDriveIcon, GithubIcon, GitlabIcon, GlobeIcon, GoogleIcon, GraphBarIcon, GraphLineIcon, GraphqlIcon, GridAltIcon, GridIcon, GrowIcon, HeartHollowIcon, HeartIcon, HomeIcon, HourglassIcon, InfoIcon, ItalicIcon, JumpToIcon, KeyIcon, LightningIcon, LightningOffIcon, LinkBrokenIcon, LinkIcon, LinkedinIcon, LinuxIcon, ListOrderedIcon, ListUnorderedIcon, LocationIcon, LockIcon, MarkdownIcon, MarkupIcon, MediumIcon, MemoryIcon, MenuIcon, MergeIcon, MirrorIcon, MobileIcon, MoonIcon, NutIcon, OutboxIcon, OutlineIcon, PaintBrushIcon, PaperClipIcon, ParagraphIcon, PassedIcon, PhoneIcon, PhotoDragIcon, PhotoIcon, PhotoStabilizeIcon, PinAltIcon, PinIcon, PlayAllHollowIcon, PlayBackIcon, PlayHollowIcon, PlayIcon, PlayNextIcon, PlusIcon, PointerDefaultIcon, PointerHandIcon, PowerIcon, PrintIcon, ProceedIcon, ProfileIcon, PullRequestIcon, QuestionIcon, RSSIcon, RedirectIcon, ReduxIcon, RefreshIcon, ReplyIcon, RepoIcon, RequestChangeIcon, RewindIcon, RulerIcon, SaveIcon, SearchIcon, ShareAltIcon, ShareIcon, ShieldIcon, SideBySideIcon, SidebarAltIcon, SidebarAltToggleIcon, SidebarIcon, SidebarToggleIcon, SpeakerIcon, StackedIcon, StarHollowIcon, StarIcon, StatusFailIcon, StatusIcon, StatusPassIcon, StatusWarnIcon, StickerIcon, StopAltHollowIcon, StopAltIcon, StopIcon, StorybookIcon, StructureIcon, SubtractIcon, SunIcon, SupportIcon, SweepIcon, SwitchAltIcon, SyncIcon, TabletIcon, ThumbsUpIcon, TimeIcon, TimerIcon, TransferIcon, TrashIcon, TwitterIcon, TypeIcon, UbuntuIcon, UndoIcon, UnfoldIcon, UnlockIcon, UnpinIcon, UploadIcon, UserAddIcon, UserAltIcon, UserIcon, UsersIcon, VSCodeIcon, VerifiedIcon, VideoIcon, WandIcon, WatchIcon, WindowsIcon, WrenchIcon, XIcon, YoutubeIcon, ZoomIcon, ZoomOutIcon, ZoomResetIcon, iconList };
