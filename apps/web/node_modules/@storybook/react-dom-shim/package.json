{"name": "@storybook/react-dom-shim", "version": "8.6.14", "description": "", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/react-dom-shim", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/react-dom-shim"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/react-18.d.ts", "import": "./dist/react-18.mjs", "require": "./dist/react-18.js", "node": "./dist/react-18.js"}, "./dist/react-16": {"types": "./dist/react-16.d.ts", "import": "./dist/react-16.mjs", "require": "./dist/react-16.js", "node": "./dist/react-16.js"}, "./dist/preset": {"types": "./dist/preset.d.ts", "require": "./dist/preset.js"}, "./package.json": "./package.json"}, "main": "dist/react-18.js", "module": "dist/react-18.mjs", "types": "dist/react-18.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts"}, "devDependencies": {"typescript": "^5.7.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta", "storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/preset.ts", "./src/react-16.tsx", "./src/react-18.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}