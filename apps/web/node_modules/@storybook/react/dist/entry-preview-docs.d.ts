import { ArgTypesExtractor } from 'storybook/internal/docs-tools';
import { LegacyStoryFn, DecoratorFunction, ArgTypesEnhancer } from 'storybook/internal/types';
import { R as ReactRenderer } from './types-5617c98e.js';
import 'react';

declare const applyDecorators: (storyFn: LegacyStoryFn<ReactRenderer>, decorators: DecoratorFunction<ReactRenderer>[]) => LegacyStoryFn<ReactRenderer>;

declare const parameters: {
    docs: {
        story: {
            inline: boolean;
        };
        extractArgTypes: ArgTypesExtractor;
        extractComponentDescription: (component?: any) => string;
    };
};
declare const decorators: DecoratorFunction<ReactRenderer>[];
declare const argTypesEnhancers: ArgTypesEnhancer<ReactRenderer>[];

export { applyDecorators, argTypesEnhancers, decorators, parameters };
