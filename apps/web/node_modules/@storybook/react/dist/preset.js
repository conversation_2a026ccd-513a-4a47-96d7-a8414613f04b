"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var preset_exports={};__export(preset_exports,{addons:()=>addons,previewAnnotations:()=>previewAnnotations,resolvedReact:()=>resolvedReact});module.exports=__toCommonJS(preset_exports);var import_node_path=require("path"),addons=[require.resolve("@storybook/react-dom-shim/dist/preset")],previewAnnotations=async(input=[],options)=>{let docsConfig=await options.presets.apply("docs",{},options),features=await options.presets.apply("features",{},options),docsEnabled=Object.keys(docsConfig).length>0;return[].concat(input).concat([(0,import_node_path.join)(__dirname,"entry-preview.mjs")]).concat(docsEnabled?[(0,import_node_path.join)(__dirname,"entry-preview-docs.mjs")]:[]).concat(features?.experimentalRSC?[(0,import_node_path.join)(__dirname,"entry-preview-rsc.mjs")]:[])},resolvedReact=async existing=>{try{return{...existing,react:(0,import_node_path.dirname)(require.resolve("react/package.json")),reactDom:(0,import_node_path.dirname)(require.resolve("react-dom/package.json"))}}catch{return existing}};0&&(module.exports={addons,previewAnnotations,resolvedReact});
