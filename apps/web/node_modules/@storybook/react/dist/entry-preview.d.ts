import { D as Decorator } from './public-types-f2c70f25.js';
import { ArgsStoryFn, RenderContext, BaseAnnotations } from 'storybook/internal/types';
import { R as ReactRenderer } from './types-5617c98e.js';
import 'react';

declare const render: ArgsStoryFn<ReactRenderer>;

declare function renderToCanvas({ storyContext, unboundStoryFn, showMain, showException, forceRemount, }: RenderContext<ReactRenderer>, canvasElement: ReactRenderer['canvasElement']): Promise<() => Promise<void>>;

declare const mount: BaseAnnotations<ReactRenderer>['mount'];

declare const parameters: {
    renderer: string;
};

declare const decorators: Decorator[];
declare const beforeAll: () => Promise<void>;

export { beforeAll, decorators, mount, parameters, render, renderToCanvas };
