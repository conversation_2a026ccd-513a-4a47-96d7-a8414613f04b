{"name": "@storybook/addon-links", "version": "8.6.14", "description": "Link stories together to build demos and prototypes with your UI components", "keywords": ["storybook-addons", "organize"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/links", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/links"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./react": {"types": "./dist/react/index.d.ts", "import": "./dist/react/index.mjs", "require": "./dist/react/index.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./manager": "./dist/manager.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "preview": ["dist/preview.d.ts"], "react": ["dist/react/index.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "ts-dedent": "^2.0.0"}, "devDependencies": {"typescript": "^5.7.3"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta", "storybook": "^8.6.14"}, "peerDependenciesMeta": {"react": {"optional": true}}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/react/index.ts", "./src/index.ts"], "managerEntries": ["./src/manager.ts"], "previewEntries": ["./src/preview.ts"], "post": "./scripts/fix-preview-api-reference.ts"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Links", "icon": "https://user-images.githubusercontent.com/263385/101991673-48355c80-3c7c-11eb-9b6e-b627c96a75f6.png", "unsupportedFrameworks": ["react-native"]}}