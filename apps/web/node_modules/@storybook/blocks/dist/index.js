var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __esm=(fn,res)=>function(){return fn&&(res=(0,fn[__getOwnPropNames(fn)[0]])(fn=0)),res};var __commonJS=(cb,mod)=>function(){return mod||(0,cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __export=(target,all)=>{for(var name2 in all)__defProp(target,name2,{get:all[name2],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key2 of __getOwnPropNames(from))!__hasOwnProp.call(to,key2)&&key2!==except&&__defProp(to,key2,{get:()=>from[key2],enumerable:!(desc=__getOwnPropDesc(from,key2))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);function uniq(arr){return Array.from(new Set(arr))}var init_uniq=__esm({"../../node_modules/es-toolkit/dist/array/uniq.mjs"(){}});function pickBy(obj,shouldPick){let result2={},objEntries=Object.entries(obj);for(let i3=0;i3<objEntries.length;i3++){let[key2,value3]=objEntries[i3];shouldPick(value3,key2)&&(result2[key2]=value3)}return result2}var init_pickBy=__esm({"../../node_modules/es-toolkit/dist/object/pickBy.mjs"(){}});function isTypedArray(x3){return ArrayBuffer.isView(x3)&&!(x3 instanceof DataView)}var init_isTypedArray=__esm({"../../node_modules/es-toolkit/dist/predicate/isTypedArray.mjs"(){}});function isPrimitive(value3){return value3==null||typeof value3!="object"&&typeof value3!="function"}var init_isPrimitive=__esm({"../../node_modules/es-toolkit/dist/predicate/isPrimitive.mjs"(){}});function cloneDeep(obj){return cloneDeepImpl(obj)}function cloneDeepImpl(obj,stack=new Map){if(isPrimitive(obj))return obj;if(stack.has(obj))return stack.get(obj);if(Array.isArray(obj)){let result2=new Array(obj.length);stack.set(obj,result2);for(let i3=0;i3<obj.length;i3++)result2[i3]=cloneDeepImpl(obj[i3],stack);return Object.prototype.hasOwnProperty.call(obj,"index")&&(result2.index=obj.index),Object.prototype.hasOwnProperty.call(obj,"input")&&(result2.input=obj.input),result2}if(obj instanceof Date)return new Date(obj.getTime());if(obj instanceof RegExp){let result2=new RegExp(obj.source,obj.flags);return result2.lastIndex=obj.lastIndex,result2}if(obj instanceof Map){let result2=new Map;stack.set(obj,result2);for(let[key2,value3]of obj.entries())result2.set(key2,cloneDeepImpl(value3,stack));return result2}if(obj instanceof Set){let result2=new Set;stack.set(obj,result2);for(let value3 of obj.values())result2.add(cloneDeepImpl(value3,stack));return result2}if(typeof Buffer<"u"&&Buffer.isBuffer(obj))return obj.subarray();if(isTypedArray(obj)){let result2=new(Object.getPrototypeOf(obj)).constructor(obj.length);stack.set(obj,result2);for(let i3=0;i3<obj.length;i3++)result2[i3]=cloneDeepImpl(obj[i3],stack);return result2}if(obj instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&obj instanceof SharedArrayBuffer)return obj.slice(0);if(obj instanceof DataView){let result2=new DataView(obj.buffer.slice(0),obj.byteOffset,obj.byteLength);return stack.set(obj,result2),copyProperties(result2,obj,stack),result2}if(typeof File<"u"&&obj instanceof File){let result2=new File([obj],obj.name,{type:obj.type});return stack.set(obj,result2),copyProperties(result2,obj,stack),result2}if(obj instanceof Blob){let result2=new Blob([obj],{type:obj.type});return stack.set(obj,result2),copyProperties(result2,obj,stack),result2}if(obj instanceof Error){let result2=new obj.constructor;return stack.set(obj,result2),result2.message=obj.message,result2.name=obj.name,result2.stack=obj.stack,result2.cause=obj.cause,copyProperties(result2,obj,stack),result2}if(typeof obj=="object"&&obj!==null){let result2={};return stack.set(obj,result2),copyProperties(result2,obj,stack),result2}return obj}function copyProperties(target,source2,stack){let keys=Object.keys(source2);for(let i3=0;i3<keys.length;i3++){let key2=keys[i3],descriptor=Object.getOwnPropertyDescriptor(source2,key2);(descriptor?.writable||descriptor?.set)&&(target[key2]=cloneDeepImpl(source2[key2],stack))}}var init_cloneDeep=__esm({"../../node_modules/es-toolkit/dist/object/cloneDeep.mjs"(){init_isPrimitive();init_isTypedArray()}});var stringTag,numberTag,booleanTag,argumentsTag,init_tags=__esm({"../../node_modules/es-toolkit/dist/compat/_internal/tags.mjs"(){stringTag="[object String]",numberTag="[object Number]",booleanTag="[object Boolean]",argumentsTag="[object Arguments]"}});function cloneDeep2(obj){if(typeof obj!="object")return cloneDeep(obj);switch(Object.prototype.toString.call(obj)){case numberTag:case stringTag:case booleanTag:{let result2=new obj.constructor(obj?.valueOf());return copyProperties(result2,obj),result2}case argumentsTag:{let result2={};return copyProperties(result2,obj),result2.length=obj.length,result2[Symbol.iterator]=obj[Symbol.iterator],result2}default:return cloneDeep(obj)}}var init_cloneDeep2=__esm({"../../node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs"(){init_cloneDeep();init_tags()}});function debounce(func,debounceMs,{signal,edges}={}){let pendingThis,pendingArgs=null,leading=edges!=null&&edges.includes("leading"),trailing=edges==null||edges.includes("trailing"),invoke=()=>{pendingArgs!==null&&(func.apply(pendingThis,pendingArgs),pendingThis=void 0,pendingArgs=null)},onTimerEnd=()=>{trailing&&invoke(),cancel()},timeoutId=null,schedule=()=>{timeoutId!=null&&clearTimeout(timeoutId),timeoutId=setTimeout(()=>{timeoutId=null,onTimerEnd()},debounceMs)},cancelTimer=()=>{timeoutId!==null&&(clearTimeout(timeoutId),timeoutId=null)},cancel=()=>{cancelTimer(),pendingThis=void 0,pendingArgs=null},flush=()=>{cancelTimer(),invoke()},debounced=function(...args2){if(signal?.aborted)return;pendingThis=this,pendingArgs=args2;let isFirstCall=timeoutId==null;schedule(),leading&&isFirstCall&&invoke()};return debounced.schedule=schedule,debounced.cancel=cancel,debounced.flush=flush,signal?.addEventListener("abort",cancel,{once:!0}),debounced}var init_debounce=__esm({"../../node_modules/es-toolkit/dist/function/debounce.mjs"(){}});function debounce2(func,debounceMs=0,options2={}){typeof options2!="object"&&(options2={});let{signal,leading=!1,trailing=!0,maxWait}=options2,edges=Array(2);leading&&(edges[0]="leading"),trailing&&(edges[1]="trailing");let result2,pendingAt=null,_debounced=debounce(function(...args2){result2=func.apply(this,args2),pendingAt=null},debounceMs,{signal,edges}),debounced=function(...args2){if(maxWait!=null){if(pendingAt===null)pendingAt=Date.now();else if(Date.now()-pendingAt>=maxWait)return result2=func.apply(this,args2),pendingAt=Date.now(),_debounced.cancel(),_debounced.schedule(),result2}return _debounced.apply(this,args2),result2},flush=()=>(_debounced.flush(),result2);return debounced.cancel=_debounced.cancel,debounced.flush=flush,debounced}var init_debounce2=__esm({"../../node_modules/es-toolkit/dist/compat/function/debounce.mjs"(){init_debounce()}});var init_compat=__esm({"../../node_modules/es-toolkit/dist/compat/index.mjs"(){init_uniq();init_pickBy();init_debounce2();init_cloneDeep2()}});var getControlId,getControlSetterButtonId,init_helpers=__esm({"src/controls/helpers.ts"(){getControlId=value3=>`control-${value3.replace(/\s+/g,"-")}`,getControlSetterButtonId=value3=>`set-${value3.replace(/\s+/g,"-")}`}});var require_color_name=__commonJS({"../../node_modules/color-name/index.js"(exports2,module2){"use strict";module2.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}});var require_conversions=__commonJS({"../../node_modules/color-convert/conversions.js"(exports2,module2){var cssKeywords=require_color_name(),reverseKeywords={};for(let key2 of Object.keys(cssKeywords))reverseKeywords[cssKeywords[key2]]=key2;var convert3={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};module2.exports=convert3;for(let model of Object.keys(convert3)){if(!("channels"in convert3[model]))throw new Error("missing channels property: "+model);if(!("labels"in convert3[model]))throw new Error("missing channel labels property: "+model);if(convert3[model].labels.length!==convert3[model].channels)throw new Error("channel and label counts mismatch: "+model);let{channels,labels}=convert3[model];delete convert3[model].channels,delete convert3[model].labels,Object.defineProperty(convert3[model],"channels",{value:channels}),Object.defineProperty(convert3[model],"labels",{value:labels})}convert3.rgb.hsl=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,min=Math.min(r3,g3,b3),max=Math.max(r3,g3,b3),delta=max-min,h3,s3;max===min?h3=0:r3===max?h3=(g3-b3)/delta:g3===max?h3=2+(b3-r3)/delta:b3===max&&(h3=4+(r3-g3)/delta),h3=Math.min(h3*60,360),h3<0&&(h3+=360);let l3=(min+max)/2;return max===min?s3=0:l3<=.5?s3=delta/(max+min):s3=delta/(2-max-min),[h3,s3*100,l3*100]};convert3.rgb.hsv=function(rgb2){let rdif,gdif,bdif,h3,s3,r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,v3=Math.max(r3,g3,b3),diff=v3-Math.min(r3,g3,b3),diffc=function(c3){return(v3-c3)/6/diff+1/2};return diff===0?(h3=0,s3=0):(s3=diff/v3,rdif=diffc(r3),gdif=diffc(g3),bdif=diffc(b3),r3===v3?h3=bdif-gdif:g3===v3?h3=1/3+rdif-bdif:b3===v3&&(h3=2/3+gdif-rdif),h3<0?h3+=1:h3>1&&(h3-=1)),[h3*360,s3*100,v3*100]};convert3.rgb.hwb=function(rgb2){let r3=rgb2[0],g3=rgb2[1],b3=rgb2[2],h3=convert3.rgb.hsl(rgb2)[0],w3=1/255*Math.min(r3,Math.min(g3,b3));return b3=1-1/255*Math.max(r3,Math.max(g3,b3)),[h3,w3*100,b3*100]};convert3.rgb.cmyk=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,k3=Math.min(1-r3,1-g3,1-b3),c3=(1-r3-k3)/(1-k3)||0,m3=(1-g3-k3)/(1-k3)||0,y3=(1-b3-k3)/(1-k3)||0;return[c3*100,m3*100,y3*100,k3*100]};function comparativeDistance(x3,y3){return(x3[0]-y3[0])**2+(x3[1]-y3[1])**2+(x3[2]-y3[2])**2}convert3.rgb.keyword=function(rgb2){let reversed=reverseKeywords[rgb2];if(reversed)return reversed;let currentClosestDistance=1/0,currentClosestKeyword;for(let keyword of Object.keys(cssKeywords)){let value3=cssKeywords[keyword],distance=comparativeDistance(rgb2,value3);distance<currentClosestDistance&&(currentClosestDistance=distance,currentClosestKeyword=keyword)}return currentClosestKeyword};convert3.keyword.rgb=function(keyword){return cssKeywords[keyword]};convert3.rgb.xyz=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255;r3=r3>.04045?((r3+.055)/1.055)**2.4:r3/12.92,g3=g3>.04045?((g3+.055)/1.055)**2.4:g3/12.92,b3=b3>.04045?((b3+.055)/1.055)**2.4:b3/12.92;let x3=r3*.4124+g3*.3576+b3*.1805,y3=r3*.2126+g3*.7152+b3*.0722,z3=r3*.0193+g3*.1192+b3*.9505;return[x3*100,y3*100,z3*100]};convert3.rgb.lab=function(rgb2){let xyz=convert3.rgb.xyz(rgb2),x3=xyz[0],y3=xyz[1],z3=xyz[2];x3/=95.047,y3/=100,z3/=108.883,x3=x3>.008856?x3**(1/3):7.787*x3+16/116,y3=y3>.008856?y3**(1/3):7.787*y3+16/116,z3=z3>.008856?z3**(1/3):7.787*z3+16/116;let l3=116*y3-16,a3=500*(x3-y3),b3=200*(y3-z3);return[l3,a3,b3]};convert3.hsl.rgb=function(hsl2){let h3=hsl2[0]/360,s3=hsl2[1]/100,l3=hsl2[2]/100,t22,t3,val;if(s3===0)return val=l3*255,[val,val,val];l3<.5?t22=l3*(1+s3):t22=l3+s3-l3*s3;let t1=2*l3-t22,rgb2=[0,0,0];for(let i3=0;i3<3;i3++)t3=h3+1/3*-(i3-1),t3<0&&t3++,t3>1&&t3--,6*t3<1?val=t1+(t22-t1)*6*t3:2*t3<1?val=t22:3*t3<2?val=t1+(t22-t1)*(2/3-t3)*6:val=t1,rgb2[i3]=val*255;return rgb2};convert3.hsl.hsv=function(hsl2){let h3=hsl2[0],s3=hsl2[1]/100,l3=hsl2[2]/100,smin=s3,lmin=Math.max(l3,.01);l3*=2,s3*=l3<=1?l3:2-l3,smin*=lmin<=1?lmin:2-lmin;let v3=(l3+s3)/2,sv=l3===0?2*smin/(lmin+smin):2*s3/(l3+s3);return[h3,sv*100,v3*100]};convert3.hsv.rgb=function(hsv){let h3=hsv[0]/60,s3=hsv[1]/100,v3=hsv[2]/100,hi=Math.floor(h3)%6,f4=h3-Math.floor(h3),p3=255*v3*(1-s3),q3=255*v3*(1-s3*f4),t3=255*v3*(1-s3*(1-f4));switch(v3*=255,hi){case 0:return[v3,t3,p3];case 1:return[q3,v3,p3];case 2:return[p3,v3,t3];case 3:return[p3,q3,v3];case 4:return[t3,p3,v3];case 5:return[v3,p3,q3]}};convert3.hsv.hsl=function(hsv){let h3=hsv[0],s3=hsv[1]/100,v3=hsv[2]/100,vmin=Math.max(v3,.01),sl,l3;l3=(2-s3)*v3;let lmin=(2-s3)*vmin;return sl=s3*vmin,sl/=lmin<=1?lmin:2-lmin,sl=sl||0,l3/=2,[h3,sl*100,l3*100]};convert3.hwb.rgb=function(hwb){let h3=hwb[0]/360,wh=hwb[1]/100,bl=hwb[2]/100,ratio=wh+bl,f4;ratio>1&&(wh/=ratio,bl/=ratio);let i3=Math.floor(6*h3),v3=1-bl;f4=6*h3-i3,(i3&1)!==0&&(f4=1-f4);let n3=wh+f4*(v3-wh),r3,g3,b3;switch(i3){default:case 6:case 0:r3=v3,g3=n3,b3=wh;break;case 1:r3=n3,g3=v3,b3=wh;break;case 2:r3=wh,g3=v3,b3=n3;break;case 3:r3=wh,g3=n3,b3=v3;break;case 4:r3=n3,g3=wh,b3=v3;break;case 5:r3=v3,g3=wh,b3=n3;break}return[r3*255,g3*255,b3*255]};convert3.cmyk.rgb=function(cmyk){let c3=cmyk[0]/100,m3=cmyk[1]/100,y3=cmyk[2]/100,k3=cmyk[3]/100,r3=1-Math.min(1,c3*(1-k3)+k3),g3=1-Math.min(1,m3*(1-k3)+k3),b3=1-Math.min(1,y3*(1-k3)+k3);return[r3*255,g3*255,b3*255]};convert3.xyz.rgb=function(xyz){let x3=xyz[0]/100,y3=xyz[1]/100,z3=xyz[2]/100,r3,g3,b3;return r3=x3*3.2406+y3*-1.5372+z3*-.4986,g3=x3*-.9689+y3*1.8758+z3*.0415,b3=x3*.0557+y3*-.204+z3*1.057,r3=r3>.0031308?1.055*r3**(1/2.4)-.055:r3*12.92,g3=g3>.0031308?1.055*g3**(1/2.4)-.055:g3*12.92,b3=b3>.0031308?1.055*b3**(1/2.4)-.055:b3*12.92,r3=Math.min(Math.max(0,r3),1),g3=Math.min(Math.max(0,g3),1),b3=Math.min(Math.max(0,b3),1),[r3*255,g3*255,b3*255]};convert3.xyz.lab=function(xyz){let x3=xyz[0],y3=xyz[1],z3=xyz[2];x3/=95.047,y3/=100,z3/=108.883,x3=x3>.008856?x3**(1/3):7.787*x3+16/116,y3=y3>.008856?y3**(1/3):7.787*y3+16/116,z3=z3>.008856?z3**(1/3):7.787*z3+16/116;let l3=116*y3-16,a3=500*(x3-y3),b3=200*(y3-z3);return[l3,a3,b3]};convert3.lab.xyz=function(lab){let l3=lab[0],a3=lab[1],b3=lab[2],x3,y3,z3;y3=(l3+16)/116,x3=a3/500+y3,z3=y3-b3/200;let y22=y3**3,x22=x3**3,z22=z3**3;return y3=y22>.008856?y22:(y3-16/116)/7.787,x3=x22>.008856?x22:(x3-16/116)/7.787,z3=z22>.008856?z22:(z3-16/116)/7.787,x3*=95.047,y3*=100,z3*=108.883,[x3,y3,z3]};convert3.lab.lch=function(lab){let l3=lab[0],a3=lab[1],b3=lab[2],h3;h3=Math.atan2(b3,a3)*360/2/Math.PI,h3<0&&(h3+=360);let c3=Math.sqrt(a3*a3+b3*b3);return[l3,c3,h3]};convert3.lch.lab=function(lch){let l3=lch[0],c3=lch[1],hr=lch[2]/360*2*Math.PI,a3=c3*Math.cos(hr),b3=c3*Math.sin(hr);return[l3,a3,b3]};convert3.rgb.ansi16=function(args2,saturation=null){let[r3,g3,b3]=args2,value3=saturation===null?convert3.rgb.hsv(args2)[2]:saturation;if(value3=Math.round(value3/50),value3===0)return 30;let ansi=30+(Math.round(b3/255)<<2|Math.round(g3/255)<<1|Math.round(r3/255));return value3===2&&(ansi+=60),ansi};convert3.hsv.ansi16=function(args2){return convert3.rgb.ansi16(convert3.hsv.rgb(args2),args2[2])};convert3.rgb.ansi256=function(args2){let r3=args2[0],g3=args2[1],b3=args2[2];return r3===g3&&g3===b3?r3<8?16:r3>248?231:Math.round((r3-8)/247*24)+232:16+36*Math.round(r3/255*5)+6*Math.round(g3/255*5)+Math.round(b3/255*5)};convert3.ansi16.rgb=function(args2){let color=args2%10;if(color===0||color===7)return args2>50&&(color+=3.5),color=color/10.5*255,[color,color,color];let mult=(~~(args2>50)+1)*.5,r3=(color&1)*mult*255,g3=(color>>1&1)*mult*255,b3=(color>>2&1)*mult*255;return[r3,g3,b3]};convert3.ansi256.rgb=function(args2){if(args2>=232){let c3=(args2-232)*10+8;return[c3,c3,c3]}args2-=16;let rem,r3=Math.floor(args2/36)/5*255,g3=Math.floor((rem=args2%36)/6)/5*255,b3=rem%6/5*255;return[r3,g3,b3]};convert3.rgb.hex=function(args2){let string=(((Math.round(args2[0])&255)<<16)+((Math.round(args2[1])&255)<<8)+(Math.round(args2[2])&255)).toString(16).toUpperCase();return"000000".substring(string.length)+string};convert3.hex.rgb=function(args2){let match=args2.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!match)return[0,0,0];let colorString=match[0];match[0].length===3&&(colorString=colorString.split("").map(char=>char+char).join(""));let integer=parseInt(colorString,16),r3=integer>>16&255,g3=integer>>8&255,b3=integer&255;return[r3,g3,b3]};convert3.rgb.hcg=function(rgb2){let r3=rgb2[0]/255,g3=rgb2[1]/255,b3=rgb2[2]/255,max=Math.max(Math.max(r3,g3),b3),min=Math.min(Math.min(r3,g3),b3),chroma=max-min,grayscale,hue;return chroma<1?grayscale=min/(1-chroma):grayscale=0,chroma<=0?hue=0:max===r3?hue=(g3-b3)/chroma%6:max===g3?hue=2+(b3-r3)/chroma:hue=4+(r3-g3)/chroma,hue/=6,hue%=1,[hue*360,chroma*100,grayscale*100]};convert3.hsl.hcg=function(hsl2){let s3=hsl2[1]/100,l3=hsl2[2]/100,c3=l3<.5?2*s3*l3:2*s3*(1-l3),f4=0;return c3<1&&(f4=(l3-.5*c3)/(1-c3)),[hsl2[0],c3*100,f4*100]};convert3.hsv.hcg=function(hsv){let s3=hsv[1]/100,v3=hsv[2]/100,c3=s3*v3,f4=0;return c3<1&&(f4=(v3-c3)/(1-c3)),[hsv[0],c3*100,f4*100]};convert3.hcg.rgb=function(hcg){let h3=hcg[0]/360,c3=hcg[1]/100,g3=hcg[2]/100;if(c3===0)return[g3*255,g3*255,g3*255];let pure=[0,0,0],hi=h3%1*6,v3=hi%1,w3=1-v3,mg=0;switch(Math.floor(hi)){case 0:pure[0]=1,pure[1]=v3,pure[2]=0;break;case 1:pure[0]=w3,pure[1]=1,pure[2]=0;break;case 2:pure[0]=0,pure[1]=1,pure[2]=v3;break;case 3:pure[0]=0,pure[1]=w3,pure[2]=1;break;case 4:pure[0]=v3,pure[1]=0,pure[2]=1;break;default:pure[0]=1,pure[1]=0,pure[2]=w3}return mg=(1-c3)*g3,[(c3*pure[0]+mg)*255,(c3*pure[1]+mg)*255,(c3*pure[2]+mg)*255]};convert3.hcg.hsv=function(hcg){let c3=hcg[1]/100,g3=hcg[2]/100,v3=c3+g3*(1-c3),f4=0;return v3>0&&(f4=c3/v3),[hcg[0],f4*100,v3*100]};convert3.hcg.hsl=function(hcg){let c3=hcg[1]/100,l3=hcg[2]/100*(1-c3)+.5*c3,s3=0;return l3>0&&l3<.5?s3=c3/(2*l3):l3>=.5&&l3<1&&(s3=c3/(2*(1-l3))),[hcg[0],s3*100,l3*100]};convert3.hcg.hwb=function(hcg){let c3=hcg[1]/100,g3=hcg[2]/100,v3=c3+g3*(1-c3);return[hcg[0],(v3-c3)*100,(1-v3)*100]};convert3.hwb.hcg=function(hwb){let w3=hwb[1]/100,v3=1-hwb[2]/100,c3=v3-w3,g3=0;return c3<1&&(g3=(v3-c3)/(1-c3)),[hwb[0],c3*100,g3*100]};convert3.apple.rgb=function(apple){return[apple[0]/65535*255,apple[1]/65535*255,apple[2]/65535*255]};convert3.rgb.apple=function(rgb2){return[rgb2[0]/255*65535,rgb2[1]/255*65535,rgb2[2]/255*65535]};convert3.gray.rgb=function(args2){return[args2[0]/100*255,args2[0]/100*255,args2[0]/100*255]};convert3.gray.hsl=function(args2){return[0,0,args2[0]]};convert3.gray.hsv=convert3.gray.hsl;convert3.gray.hwb=function(gray){return[0,100,gray[0]]};convert3.gray.cmyk=function(gray){return[0,0,0,gray[0]]};convert3.gray.lab=function(gray){return[gray[0],0,0]};convert3.gray.hex=function(gray){let val=Math.round(gray[0]/100*255)&255,string=((val<<16)+(val<<8)+val).toString(16).toUpperCase();return"000000".substring(string.length)+string};convert3.rgb.gray=function(rgb2){return[(rgb2[0]+rgb2[1]+rgb2[2])/3/255*100]}}});var require_route=__commonJS({"../../node_modules/color-convert/route.js"(exports2,module2){var conversions=require_conversions();function buildGraph(){let graph={},models=Object.keys(conversions);for(let len=models.length,i3=0;i3<len;i3++)graph[models[i3]]={distance:-1,parent:null};return graph}function deriveBFS(fromModel){let graph=buildGraph(),queue=[fromModel];for(graph[fromModel].distance=0;queue.length;){let current=queue.pop(),adjacents=Object.keys(conversions[current]);for(let len=adjacents.length,i3=0;i3<len;i3++){let adjacent=adjacents[i3],node=graph[adjacent];node.distance===-1&&(node.distance=graph[current].distance+1,node.parent=current,queue.unshift(adjacent))}}return graph}function link(from,to){return function(args2){return to(from(args2))}}function wrapConversion(toModel,graph){let path=[graph[toModel].parent,toModel],fn=conversions[graph[toModel].parent][toModel],cur=graph[toModel].parent;for(;graph[cur].parent;)path.unshift(graph[cur].parent),fn=link(conversions[graph[cur].parent][cur],fn),cur=graph[cur].parent;return fn.conversion=path,fn}module2.exports=function(fromModel){let graph=deriveBFS(fromModel),conversion={},models=Object.keys(graph);for(let len=models.length,i3=0;i3<len;i3++){let toModel=models[i3];graph[toModel].parent!==null&&(conversion[toModel]=wrapConversion(toModel,graph))}return conversion}}});var require_color_convert=__commonJS({"../../node_modules/color-convert/index.js"(exports2,module2){var conversions=require_conversions(),route=require_route(),convert3={},models=Object.keys(conversions);function wrapRaw(fn){let wrappedFn=function(...args2){let arg0=args2[0];return arg0==null?arg0:(arg0.length>1&&(args2=arg0),fn(args2))};return"conversion"in fn&&(wrappedFn.conversion=fn.conversion),wrappedFn}function wrapRounded(fn){let wrappedFn=function(...args2){let arg0=args2[0];if(arg0==null)return arg0;arg0.length>1&&(args2=arg0);let result2=fn(args2);if(typeof result2=="object")for(let len=result2.length,i3=0;i3<len;i3++)result2[i3]=Math.round(result2[i3]);return result2};return"conversion"in fn&&(wrappedFn.conversion=fn.conversion),wrappedFn}models.forEach(fromModel=>{convert3[fromModel]={},Object.defineProperty(convert3[fromModel],"channels",{value:conversions[fromModel].channels}),Object.defineProperty(convert3[fromModel],"labels",{value:conversions[fromModel].labels});let routes=route(fromModel);Object.keys(routes).forEach(toModel=>{let fn=routes[toModel];convert3[fromModel][toModel]=wrapRounded(fn),convert3[fromModel][toModel].raw=wrapRaw(fn)})});module2.exports=convert3}});function u2(){return(u2=Object.assign||function(e3){for(var r3=1;r3<arguments.length;r3++){var t3=arguments[r3];for(var n3 in t3)Object.prototype.hasOwnProperty.call(t3,n3)&&(e3[n3]=t3[n3])}return e3}).apply(this,arguments)}function c2(e3,r3){if(e3==null)return{};var t3,n3,o3={},a3=Object.keys(e3);for(n3=0;n3<a3.length;n3++)r3.indexOf(t3=a3[n3])>=0||(o3[t3]=e3[t3]);return o3}function i2(e3){var t3=(0,import_react22.useRef)(e3),n3=(0,import_react22.useRef)(function(e4){t3.current&&t3.current(e4)});return t3.current=e3,n3.current}function Y2(e3,t3,l3){var u3=i2(l3),c3=(0,import_react22.useState)(function(){return e3.toHsva(t3)}),s3=c3[0],f4=c3[1],v3=(0,import_react22.useRef)({color:t3,hsva:s3});(0,import_react22.useEffect)(function(){if(!e3.equal(t3,v3.current.color)){var r3=e3.toHsva(t3);v3.current={hsva:r3,color:t3},f4(r3)}},[t3,e3]),(0,import_react22.useEffect)(function(){var r3;F2(s3,v3.current.hsva)||e3.equal(r3=e3.fromHsva(s3),v3.current.color)||(v3.current={hsva:s3,color:r3},u3(r3))},[s3,e3,u3]);var d3=(0,import_react22.useCallback)(function(e4){f4(function(r3){return Object.assign({},r3,e4)})},[]);return[s3,d3]}var import_react22,s2,f3,v2,d2,h2,m2,g2,p2,b2,_2,x2,C2,E2,H2,N2,w2,y2,q2,k2,I2,z2,D2,K2,L2,S2,T2,F2,P2,X2,R2,V2,$2,J2,Q2,U2,W2,Z2,ee2,re2,le2,ue2,Ee2,He2,init_dist=__esm({"../../node_modules/react-colorful/dist/index.mjs"(){import_react22=__toESM(require("react"),1);s2=function(e3,r3,t3){return r3===void 0&&(r3=0),t3===void 0&&(t3=1),e3>t3?t3:e3<r3?r3:e3},f3=function(e3){return"touches"in e3},v2=function(e3){return e3&&e3.ownerDocument.defaultView||self},d2=function(e3,r3,t3){var n3=e3.getBoundingClientRect(),o3=f3(r3)?function(e4,r4){for(var t4=0;t4<e4.length;t4++)if(e4[t4].identifier===r4)return e4[t4];return e4[0]}(r3.touches,t3):r3;return{left:s2((o3.pageX-(n3.left+v2(e3).pageXOffset))/n3.width),top:s2((o3.pageY-(n3.top+v2(e3).pageYOffset))/n3.height)}},h2=function(e3){!f3(e3)&&e3.preventDefault()},m2=import_react22.default.memo(function(o3){var a3=o3.onMove,l3=o3.onKey,s3=c2(o3,["onMove","onKey"]),m3=(0,import_react22.useRef)(null),g3=i2(a3),p3=i2(l3),b3=(0,import_react22.useRef)(null),_3=(0,import_react22.useRef)(!1),x3=(0,import_react22.useMemo)(function(){var e3=function(e4){h2(e4),(f3(e4)?e4.touches.length>0:e4.buttons>0)&&m3.current?g3(d2(m3.current,e4,b3.current)):t3(!1)},r3=function(){return t3(!1)};function t3(t4){var n3=_3.current,o4=v2(m3.current),a4=t4?o4.addEventListener:o4.removeEventListener;a4(n3?"touchmove":"mousemove",e3),a4(n3?"touchend":"mouseup",r3)}return[function(e4){var r4=e4.nativeEvent,n3=m3.current;if(n3&&(h2(r4),!function(e5,r5){return r5&&!f3(e5)}(r4,_3.current)&&n3)){if(f3(r4)){_3.current=!0;var o4=r4.changedTouches||[];o4.length&&(b3.current=o4[0].identifier)}n3.focus(),g3(d2(n3,r4,b3.current)),t3(!0)}},function(e4){var r4=e4.which||e4.keyCode;r4<37||r4>40||(e4.preventDefault(),p3({left:r4===39?.05:r4===37?-.05:0,top:r4===40?.05:r4===38?-.05:0}))},t3]},[p3,g3]),C3=x3[0],E3=x3[1],H4=x3[2];return(0,import_react22.useEffect)(function(){return H4},[H4]),import_react22.default.createElement("div",u2({},s3,{onTouchStart:C3,onMouseDown:C3,className:"react-colorful__interactive",ref:m3,onKeyDown:E3,tabIndex:0,role:"slider"}))}),g2=function(e3){return e3.filter(Boolean).join(" ")},p2=function(r3){var t3=r3.color,n3=r3.left,o3=r3.top,a3=o3===void 0?.5:o3,l3=g2(["react-colorful__pointer",r3.className]);return import_react22.default.createElement("div",{className:l3,style:{top:100*a3+"%",left:100*n3+"%"}},import_react22.default.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t3}}))},b2=function(e3,r3,t3){return r3===void 0&&(r3=0),t3===void 0&&(t3=Math.pow(10,r3)),Math.round(t3*e3)/t3},_2={grad:.9,turn:360,rad:360/(2*Math.PI)},x2=function(e3){return L2(C2(e3))},C2=function(e3){return e3[0]==="#"&&(e3=e3.substring(1)),e3.length<6?{r:parseInt(e3[0]+e3[0],16),g:parseInt(e3[1]+e3[1],16),b:parseInt(e3[2]+e3[2],16),a:e3.length===4?b2(parseInt(e3[3]+e3[3],16)/255,2):1}:{r:parseInt(e3.substring(0,2),16),g:parseInt(e3.substring(2,4),16),b:parseInt(e3.substring(4,6),16),a:e3.length===8?b2(parseInt(e3.substring(6,8),16)/255,2):1}},E2=function(e3,r3){return r3===void 0&&(r3="deg"),Number(e3)*(_2[r3]||1)},H2=function(e3){var r3=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e3);return r3?N2({h:E2(r3[1],r3[2]),s:Number(r3[3]),l:Number(r3[4]),a:r3[5]===void 0?1:Number(r3[5])/(r3[6]?100:1)}):{h:0,s:0,v:0,a:1}},N2=function(e3){var r3=e3.s,t3=e3.l;return{h:e3.h,s:(r3*=(t3<50?t3:100-t3)/100)>0?2*r3/(t3+r3)*100:0,v:t3+r3,a:e3.a}},w2=function(e3){return K2(I2(e3))},y2=function(e3){var r3=e3.s,t3=e3.v,n3=e3.a,o3=(200-r3)*t3/100;return{h:b2(e3.h),s:b2(o3>0&&o3<200?r3*t3/100/(o3<=100?o3:200-o3)*100:0),l:b2(o3/2),a:b2(n3,2)}},q2=function(e3){var r3=y2(e3);return"hsl("+r3.h+", "+r3.s+"%, "+r3.l+"%)"},k2=function(e3){var r3=y2(e3);return"hsla("+r3.h+", "+r3.s+"%, "+r3.l+"%, "+r3.a+")"},I2=function(e3){var r3=e3.h,t3=e3.s,n3=e3.v,o3=e3.a;r3=r3/360*6,t3/=100,n3/=100;var a3=Math.floor(r3),l3=n3*(1-t3),u3=n3*(1-(r3-a3)*t3),c3=n3*(1-(1-r3+a3)*t3),i3=a3%6;return{r:b2(255*[n3,u3,l3,l3,c3,n3][i3]),g:b2(255*[c3,n3,n3,u3,l3,l3][i3]),b:b2(255*[l3,l3,c3,n3,n3,u3][i3]),a:b2(o3,2)}},z2=function(e3){var r3=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e3);return r3?L2({r:Number(r3[1])/(r3[2]?100/255:1),g:Number(r3[3])/(r3[4]?100/255:1),b:Number(r3[5])/(r3[6]?100/255:1),a:r3[7]===void 0?1:Number(r3[7])/(r3[8]?100:1)}):{h:0,s:0,v:0,a:1}},D2=function(e3){var r3=e3.toString(16);return r3.length<2?"0"+r3:r3},K2=function(e3){var r3=e3.r,t3=e3.g,n3=e3.b,o3=e3.a,a3=o3<1?D2(b2(255*o3)):"";return"#"+D2(r3)+D2(t3)+D2(n3)+a3},L2=function(e3){var r3=e3.r,t3=e3.g,n3=e3.b,o3=e3.a,a3=Math.max(r3,t3,n3),l3=a3-Math.min(r3,t3,n3),u3=l3?a3===r3?(t3-n3)/l3:a3===t3?2+(n3-r3)/l3:4+(r3-t3)/l3:0;return{h:b2(60*(u3<0?u3+6:u3)),s:b2(a3?l3/a3*100:0),v:b2(a3/255*100),a:o3}},S2=import_react22.default.memo(function(r3){var t3=r3.hue,n3=r3.onChange,o3=g2(["react-colorful__hue",r3.className]);return import_react22.default.createElement("div",{className:o3},import_react22.default.createElement(m2,{onMove:function(e3){n3({h:360*e3.left})},onKey:function(e3){n3({h:s2(t3+360*e3.left,0,360)})},"aria-label":"Hue","aria-valuenow":b2(t3),"aria-valuemax":"360","aria-valuemin":"0"},import_react22.default.createElement(p2,{className:"react-colorful__hue-pointer",left:t3/360,color:q2({h:t3,s:100,v:100,a:1})})))}),T2=import_react22.default.memo(function(r3){var t3=r3.hsva,n3=r3.onChange,o3={backgroundColor:q2({h:t3.h,s:100,v:100,a:1})};return import_react22.default.createElement("div",{className:"react-colorful__saturation",style:o3},import_react22.default.createElement(m2,{onMove:function(e3){n3({s:100*e3.left,v:100-100*e3.top})},onKey:function(e3){n3({s:s2(t3.s+100*e3.left,0,100),v:s2(t3.v-100*e3.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+b2(t3.s)+"%, Brightness "+b2(t3.v)+"%"},import_react22.default.createElement(p2,{className:"react-colorful__saturation-pointer",top:1-t3.v/100,left:t3.s/100,color:q2(t3)})))}),F2=function(e3,r3){if(e3===r3)return!0;for(var t3 in e3)if(e3[t3]!==r3[t3])return!1;return!0},P2=function(e3,r3){return e3.replace(/\s/g,"")===r3.replace(/\s/g,"")},X2=function(e3,r3){return e3.toLowerCase()===r3.toLowerCase()||F2(C2(e3),C2(r3))};V2=typeof window<"u"?import_react22.useLayoutEffect:import_react22.useEffect,$2=function(){return R2||(typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0)},J2=new Map,Q2=function(e3){V2(function(){var r3=e3.current?e3.current.ownerDocument:document;if(r3!==void 0&&!J2.has(r3)){var t3=r3.createElement("style");t3.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,J2.set(r3,t3);var n3=$2();n3&&t3.setAttribute("nonce",n3),r3.head.appendChild(t3)}},[])},U2=function(t3){var n3=t3.className,o3=t3.colorModel,a3=t3.color,l3=a3===void 0?o3.defaultColor:a3,i3=t3.onChange,s3=c2(t3,["className","colorModel","color","onChange"]),f4=(0,import_react22.useRef)(null);Q2(f4);var v3=Y2(o3,l3,i3),d3=v3[0],h3=v3[1],m3=g2(["react-colorful",n3]);return import_react22.default.createElement("div",u2({},s3,{ref:f4,className:m3}),import_react22.default.createElement(T2,{hsva:d3,onChange:h3}),import_react22.default.createElement(S2,{hue:d3.h,onChange:h3,className:"react-colorful__last-control"}))},W2={defaultColor:"000",toHsva:x2,fromHsva:function(e3){return w2({h:e3.h,s:e3.s,v:e3.v,a:1})},equal:X2},Z2=function(r3){return import_react22.default.createElement(U2,u2({},r3,{colorModel:W2}))},ee2=function(r3){var t3=r3.className,n3=r3.hsva,o3=r3.onChange,a3={backgroundImage:"linear-gradient(90deg, "+k2(Object.assign({},n3,{a:0}))+", "+k2(Object.assign({},n3,{a:1}))+")"},l3=g2(["react-colorful__alpha",t3]),u3=b2(100*n3.a);return import_react22.default.createElement("div",{className:l3},import_react22.default.createElement("div",{className:"react-colorful__alpha-gradient",style:a3}),import_react22.default.createElement(m2,{onMove:function(e3){o3({a:e3.left})},onKey:function(e3){o3({a:s2(n3.a+e3.left)})},"aria-label":"Alpha","aria-valuetext":u3+"%","aria-valuenow":u3,"aria-valuemin":"0","aria-valuemax":"100"},import_react22.default.createElement(p2,{className:"react-colorful__alpha-pointer",left:n3.a,color:k2(n3)})))},re2=function(t3){var n3=t3.className,o3=t3.colorModel,a3=t3.color,l3=a3===void 0?o3.defaultColor:a3,i3=t3.onChange,s3=c2(t3,["className","colorModel","color","onChange"]),f4=(0,import_react22.useRef)(null);Q2(f4);var v3=Y2(o3,l3,i3),d3=v3[0],h3=v3[1],m3=g2(["react-colorful",n3]);return import_react22.default.createElement("div",u2({},s3,{ref:f4,className:m3}),import_react22.default.createElement(T2,{hsva:d3,onChange:h3}),import_react22.default.createElement(S2,{hue:d3.h,onChange:h3}),import_react22.default.createElement(ee2,{hsva:d3,onChange:h3,className:"react-colorful__last-control"}))},le2={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:H2,fromHsva:k2,equal:P2},ue2=function(r3){return import_react22.default.createElement(re2,u2({},r3,{colorModel:le2}))},Ee2={defaultColor:"rgba(0, 0, 0, 1)",toHsva:z2,fromHsva:function(e3){var r3=I2(e3);return"rgba("+r3.r+", "+r3.g+", "+r3.b+", "+r3.a+")"},equal:P2},He2=function(r3){return import_react22.default.createElement(re2,u2({},r3,{colorModel:Ee2}))}}});var Color_exports={};__export(Color_exports,{ColorControl:()=>ColorControl,default:()=>Color_default});var import_react23,import_components13,import_theming17,import_icons4,import_color_convert,Wrapper8,PickerTooltip,TooltipContent,Note,Swatches,SwatchColor,swatchBackground,Swatch,Input2,ToggleIcon,ColorSpace,COLOR_SPACES,COLOR_REGEXP,RGB_REGEXP,HSL_REGEXP,HEX_REGEXP,SHORTHEX_REGEXP,ColorPicker,fallbackColor,stringToArgs,parseValue,getRealValue,useColorInput,id,usePresets,ColorControl,Color_default,init_Color=__esm({"src/controls/Color.tsx"(){import_react23=__toESM(require("react")),import_components13=require("storybook/internal/components"),import_theming17=require("storybook/internal/theming"),import_icons4=require("@storybook/icons"),import_color_convert=__toESM(require_color_convert());init_compat();init_dist();init_helpers();Wrapper8=import_theming17.styled.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),PickerTooltip=(0,import_theming17.styled)(import_components13.WithTooltip)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),TooltipContent=import_theming17.styled.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),Note=(0,import_theming17.styled)(import_components13.TooltipNote)(({theme})=>({fontFamily:theme.typography.fonts.base})),Swatches=import_theming17.styled.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),SwatchColor=import_theming17.styled.div(({theme,active})=>({width:16,height:16,boxShadow:active?`${theme.appBorderColor} 0 0 0 1px inset, ${theme.textMutedColor}50 0 0 0 4px`:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:theme.appBorderRadius})),swatchBackground=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,Swatch=({value:value3,style,...props})=>{let backgroundImage=`linear-gradient(${value3}, ${value3}), ${swatchBackground}, linear-gradient(#fff, #fff)`;return import_react23.default.createElement(SwatchColor,{...props,style:{...style,backgroundImage}})},Input2=(0,import_theming17.styled)(import_components13.Form.Input)(({theme,readOnly})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:theme.typography.fonts.base})),ToggleIcon=(0,import_theming17.styled)(import_icons4.MarkupIcon)(({theme})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:theme.input.color})),ColorSpace=(ColorSpace2=>(ColorSpace2.RGB="rgb",ColorSpace2.HSL="hsl",ColorSpace2.HEX="hex",ColorSpace2))(ColorSpace||{}),COLOR_SPACES=Object.values(ColorSpace),COLOR_REGEXP=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,RGB_REGEXP=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,HSL_REGEXP=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,HEX_REGEXP=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,SHORTHEX_REGEXP=/^\s*#?([0-9a-f]{3})\s*$/i,ColorPicker={hex:Z2,rgb:He2,hsl:ue2},fallbackColor={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},stringToArgs=value3=>{let match=value3?.match(COLOR_REGEXP);if(!match)return[0,0,0,1];let[,x3,y3,z3,a3=1]=match;return[x3,y3,z3,a3].map(Number)},parseValue=value3=>{if(!value3)return;let valid=!0;if(RGB_REGEXP.test(value3)){let[r3,g3,b3,a3]=stringToArgs(value3),[h3,s3,l3]=import_color_convert.default.rgb.hsl([r3,g3,b3])||[0,0,0];return{valid,value:value3,keyword:import_color_convert.default.rgb.keyword([r3,g3,b3]),colorSpace:"rgb",rgb:value3,hsl:`hsla(${h3}, ${s3}%, ${l3}%, ${a3})`,hex:`#${import_color_convert.default.rgb.hex([r3,g3,b3]).toLowerCase()}`}}if(HSL_REGEXP.test(value3)){let[h3,s3,l3,a3]=stringToArgs(value3),[r3,g3,b3]=import_color_convert.default.hsl.rgb([h3,s3,l3])||[0,0,0];return{valid,value:value3,keyword:import_color_convert.default.hsl.keyword([h3,s3,l3]),colorSpace:"hsl",rgb:`rgba(${r3}, ${g3}, ${b3}, ${a3})`,hsl:value3,hex:`#${import_color_convert.default.hsl.hex([h3,s3,l3]).toLowerCase()}`}}let plain=value3.replace("#",""),rgb2=import_color_convert.default.keyword.rgb(plain)||import_color_convert.default.hex.rgb(plain),hsl2=import_color_convert.default.rgb.hsl(rgb2),mapped=value3;if(/[^#a-f0-9]/i.test(value3)?mapped=plain:HEX_REGEXP.test(value3)&&(mapped=`#${plain}`),mapped.startsWith("#"))valid=HEX_REGEXP.test(mapped);else try{import_color_convert.default.keyword.hex(mapped)}catch{valid=!1}return{valid,value:mapped,keyword:import_color_convert.default.rgb.keyword(rgb2),colorSpace:"hex",rgb:`rgba(${rgb2[0]}, ${rgb2[1]}, ${rgb2[2]}, 1)`,hsl:`hsla(${hsl2[0]}, ${hsl2[1]}%, ${hsl2[2]}%, 1)`,hex:mapped}},getRealValue=(value3,color,colorSpace)=>{if(!value3||!color?.valid)return fallbackColor[colorSpace];if(colorSpace!=="hex")return color?.[colorSpace]||fallbackColor[colorSpace];if(!color.hex.startsWith("#"))try{return`#${import_color_convert.default.keyword.hex(color.hex)}`}catch{return fallbackColor.hex}let short=color.hex.match(SHORTHEX_REGEXP);if(!short)return HEX_REGEXP.test(color.hex)?color.hex:fallbackColor.hex;let[r3,g3,b3]=short[1].split("");return`#${r3}${r3}${g3}${g3}${b3}${b3}`},useColorInput=(initialValue,onChange)=>{let[value3,setValue]=(0,import_react23.useState)(initialValue||""),[color,setColor]=(0,import_react23.useState)(()=>parseValue(value3)),[colorSpace,setColorSpace]=(0,import_react23.useState)(color?.colorSpace||"hex");(0,import_react23.useEffect)(()=>{let nextValue=initialValue||"",nextColor=parseValue(nextValue);setValue(nextValue),setColor(nextColor),setColorSpace(nextColor?.colorSpace||"hex")},[initialValue]);let realValue=(0,import_react23.useMemo)(()=>getRealValue(value3,color,colorSpace).toLowerCase(),[value3,color,colorSpace]),updateValue=(0,import_react23.useCallback)(update=>{let parsed=parseValue(update),v3=parsed?.value||update||"";setValue(v3),v3===""&&(setColor(void 0),onChange(void 0)),parsed&&(setColor(parsed),setColorSpace(parsed.colorSpace),onChange(parsed.value))},[onChange]),cycleColorSpace=(0,import_react23.useCallback)(()=>{let next=COLOR_SPACES.indexOf(colorSpace)+1;next>=COLOR_SPACES.length&&(next=0),setColorSpace(COLOR_SPACES[next]);let update=color?.[COLOR_SPACES[next]]||"";setValue(update),onChange(update)},[color,colorSpace,onChange]);return{value:value3,realValue,updateValue,color,colorSpace,cycleColorSpace}},id=value3=>value3.replace(/\s*/,"").toLowerCase(),usePresets=(presetColors,currentColor,colorSpace)=>{let[selectedColors,setSelectedColors]=(0,import_react23.useState)(currentColor?.valid?[currentColor]:[]);(0,import_react23.useEffect)(()=>{currentColor===void 0&&setSelectedColors([])},[currentColor]);let presets=(0,import_react23.useMemo)(()=>(presetColors||[]).map(preset=>typeof preset=="string"?parseValue(preset):preset.title?{...parseValue(preset.color),keyword:preset.title}:parseValue(preset.color)).concat(selectedColors).filter(Boolean).slice(-27),[presetColors,selectedColors]),addPreset=(0,import_react23.useCallback)(color=>{color?.valid&&(presets.some(preset=>id(preset[colorSpace])===id(color[colorSpace]))||setSelectedColors(arr=>arr.concat(color)))},[colorSpace,presets]);return{presets,addPreset}},ColorControl=({name:name2,value:initialValue,onChange,onFocus,onBlur,presetColors,startOpen=!1,argType})=>{let debouncedOnChange=(0,import_react23.useCallback)(debounce2(onChange,200),[onChange]),{value:value3,realValue,updateValue,color,colorSpace,cycleColorSpace}=useColorInput(initialValue,debouncedOnChange),{presets,addPreset}=usePresets(presetColors,color,colorSpace),Picker=ColorPicker[colorSpace],readonly=!!argType?.table?.readonly;return import_react23.default.createElement(Wrapper8,{"aria-readonly":readonly},import_react23.default.createElement(PickerTooltip,{startOpen,trigger:readonly?[null]:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>addPreset(color),tooltip:import_react23.default.createElement(TooltipContent,null,import_react23.default.createElement(Picker,{color:realValue==="transparent"?"#000000":realValue,onChange:updateValue,onFocus,onBlur}),presets.length>0&&import_react23.default.createElement(Swatches,null,presets.map((preset,index)=>import_react23.default.createElement(import_components13.WithTooltip,{key:`${preset.value}-${index}`,hasChrome:!1,tooltip:import_react23.default.createElement(Note,{note:preset.keyword||preset.value})},import_react23.default.createElement(Swatch,{value:preset[colorSpace],active:color&&id(preset[colorSpace])===id(color[colorSpace]),onClick:()=>updateValue(preset.value)})))))},import_react23.default.createElement(Swatch,{value:realValue,style:{margin:4}})),import_react23.default.createElement(Input2,{id:getControlId(name2),value:value3,onChange:e3=>updateValue(e3.target.value),onFocus:e3=>e3.target.select(),readOnly:readonly,placeholder:"Choose color..."}),value3?import_react23.default.createElement(ToggleIcon,{onClick:cycleColorSpace}):null)},Color_default=ColorControl}});var require_similar=__commonJS({"../../node_modules/map-or-similar/src/similar.js"(exports2,module2){function Similar(){return this.list=[],this.lastItem=void 0,this.size=0,this}Similar.prototype.get=function(key2){var index;if(this.lastItem&&this.isEqual(this.lastItem.key,key2))return this.lastItem.val;if(index=this.indexOf(key2),index>=0)return this.lastItem=this.list[index],this.list[index].val};Similar.prototype.set=function(key2,val){var index;return this.lastItem&&this.isEqual(this.lastItem.key,key2)?(this.lastItem.val=val,this):(index=this.indexOf(key2),index>=0?(this.lastItem=this.list[index],this.list[index].val=val,this):(this.lastItem={key:key2,val},this.list.push(this.lastItem),this.size++,this))};Similar.prototype.delete=function(key2){var index;if(this.lastItem&&this.isEqual(this.lastItem.key,key2)&&(this.lastItem=void 0),index=this.indexOf(key2),index>=0)return this.size--,this.list.splice(index,1)[0]};Similar.prototype.has=function(key2){var index;return this.lastItem&&this.isEqual(this.lastItem.key,key2)?!0:(index=this.indexOf(key2),index>=0?(this.lastItem=this.list[index],!0):!1)};Similar.prototype.forEach=function(callback,thisArg){var i3;for(i3=0;i3<this.size;i3++)callback.call(thisArg||this,this.list[i3].val,this.list[i3].key,this)};Similar.prototype.indexOf=function(key2){var i3;for(i3=0;i3<this.size;i3++)if(this.isEqual(this.list[i3].key,key2))return i3;return-1};Similar.prototype.isEqual=function(val1,val2){return val1===val2||val1!==val1&&val2!==val2};module2.exports=Similar}});var require_map_or_similar=__commonJS({"../../node_modules/map-or-similar/src/map-or-similar.js"(exports2,module2){module2.exports=function(forceSimilar){if(typeof Map!="function"||forceSimilar){var Similar=require_similar();return new Similar}else return new Map}}});var require_memoizerific=__commonJS({"../../node_modules/memoizerific/src/memoizerific.js"(exports2,module2){var MapOrSimilar=require_map_or_similar();module2.exports=function(limit){var cache=new MapOrSimilar(process.env.FORCE_SIMILAR_INSTEAD_OF_MAP==="true"),lru=[];return function(fn){var memoizerific=function(){var currentCache=cache,newMap,fnResult,argsLengthMinusOne=arguments.length-1,lruPath=Array(argsLengthMinusOne+1),isMemoized=!0,i3;if((memoizerific.numArgs||memoizerific.numArgs===0)&&memoizerific.numArgs!==argsLengthMinusOne+1)throw new Error("Memoizerific functions should always be called with the same number of arguments");for(i3=0;i3<argsLengthMinusOne;i3++){if(lruPath[i3]={cacheItem:currentCache,arg:arguments[i3]},currentCache.has(arguments[i3])){currentCache=currentCache.get(arguments[i3]);continue}isMemoized=!1,newMap=new MapOrSimilar(process.env.FORCE_SIMILAR_INSTEAD_OF_MAP==="true"),currentCache.set(arguments[i3],newMap),currentCache=newMap}return isMemoized&&(currentCache.has(arguments[argsLengthMinusOne])?fnResult=currentCache.get(arguments[argsLengthMinusOne]):isMemoized=!1),isMemoized||(fnResult=fn.apply(null,arguments),currentCache.set(arguments[argsLengthMinusOne],fnResult)),limit>0&&(lruPath[argsLengthMinusOne]={cacheItem:currentCache,arg:arguments[argsLengthMinusOne]},isMemoized?moveToMostRecentLru(lru,lruPath):lru.push(lruPath),lru.length>limit&&removeCachedResult(lru.shift())),memoizerific.wasMemoized=isMemoized,memoizerific.numArgs=argsLengthMinusOne+1,fnResult};return memoizerific.limit=limit,memoizerific.wasMemoized=!1,memoizerific.cache=cache,memoizerific.lru=lru,memoizerific}};function moveToMostRecentLru(lru,lruPath){var lruLen=lru.length,lruPathLen=lruPath.length,isMatch,i3,ii;for(i3=0;i3<lruLen;i3++){for(isMatch=!0,ii=0;ii<lruPathLen;ii++)if(!isEqual(lru[i3][ii].arg,lruPath[ii].arg)){isMatch=!1;break}if(isMatch)break}lru.push(lru.splice(i3,1)[0])}function removeCachedResult(removedLru){var removedLruLen=removedLru.length,currentLru=removedLru[removedLruLen-1],tmp,i3;for(currentLru.cacheItem.delete(currentLru.arg),i3=removedLruLen-2;i3>=0&&(currentLru=removedLru[i3],tmp=currentLru.cacheItem.get(currentLru.arg),!tmp||!tmp.size);i3--)currentLru.cacheItem.delete(currentLru.arg)}function isEqual(val1,val2){return val1===val2||val1!==val1&&val2!==val2}}});var require_default_options=__commonJS({"../../node_modules/tocbot/src/js/default-options.js"(exports2,module2){module2.exports={tocSelector:".js-toc",contentSelector:".js-toc-content",headingSelector:"h1, h2, h3",ignoreSelector:".js-toc-ignore",hasInnerContainers:!1,linkClass:"toc-link",extraLinkClasses:"",activeLinkClass:"is-active-link",listClass:"toc-list",extraListClasses:"",isCollapsedClass:"is-collapsed",collapsibleClass:"is-collapsible",listItemClass:"toc-list-item",activeListItemClass:"is-active-li",collapseDepth:0,scrollSmooth:!0,scrollSmoothDuration:420,scrollSmoothOffset:0,scrollEndCallback:function(e3){},headingsOffset:1,throttleTimeout:50,positionFixedSelector:null,positionFixedClass:"is-position-fixed",fixedSidebarOffset:"auto",includeHtml:!1,includeTitleTags:!1,onClick:function(e3){},orderedList:!0,scrollContainer:null,skipRendering:!1,headingLabelCallback:!1,ignoreHiddenElements:!1,headingObjectCallback:null,basePath:"",disableTocScrollSync:!1,tocScrollOffset:0}}});var require_build_html=__commonJS({"../../node_modules/tocbot/src/js/build-html.js"(exports2,module2){module2.exports=function(options2){var forEach=[].forEach,some=[].some,body=document.body,tocElement,currentlyHighlighting=!0,SPACE_CHAR=" ";function createEl(d3,container){var link=container.appendChild(createLink(d3));if(d3.children.length){var list=createList(d3.isCollapsed);d3.children.forEach(function(child){createEl(child,list)}),link.appendChild(list)}}function render(parent,data){var collapsed=!1,container=createList(collapsed);if(data.forEach(function(d3){createEl(d3,container)}),tocElement=parent||tocElement,tocElement!==null)return tocElement.firstChild&&tocElement.removeChild(tocElement.firstChild),data.length===0?tocElement:tocElement.appendChild(container)}function createLink(data){var item=document.createElement("li"),a3=document.createElement("a");return options2.listItemClass&&item.setAttribute("class",options2.listItemClass),options2.onClick&&(a3.onclick=options2.onClick),options2.includeTitleTags&&a3.setAttribute("title",data.textContent),options2.includeHtml&&data.childNodes.length?forEach.call(data.childNodes,function(node){a3.appendChild(node.cloneNode(!0))}):a3.textContent=data.textContent,a3.setAttribute("href",options2.basePath+"#"+data.id),a3.setAttribute("class",options2.linkClass+SPACE_CHAR+"node-name--"+data.nodeName+SPACE_CHAR+options2.extraLinkClasses),item.appendChild(a3),item}function createList(isCollapsed){var listElement=options2.orderedList?"ol":"ul",list=document.createElement(listElement),classes=options2.listClass+SPACE_CHAR+options2.extraListClasses;return isCollapsed&&(classes=classes+SPACE_CHAR+options2.collapsibleClass,classes=classes+SPACE_CHAR+options2.isCollapsedClass),list.setAttribute("class",classes),list}function updateFixedSidebarClass(){if(options2.scrollContainer&&document.querySelector(options2.scrollContainer)){var top;top=document.querySelector(options2.scrollContainer).scrollTop}else top=document.documentElement.scrollTop||body.scrollTop;var posFixedEl=document.querySelector(options2.positionFixedSelector);options2.fixedSidebarOffset==="auto"&&(options2.fixedSidebarOffset=tocElement.offsetTop),top>options2.fixedSidebarOffset?posFixedEl.className.indexOf(options2.positionFixedClass)===-1&&(posFixedEl.className+=SPACE_CHAR+options2.positionFixedClass):posFixedEl.className=posFixedEl.className.replace(SPACE_CHAR+options2.positionFixedClass,"")}function getHeadingTopPos(obj){var position=0;return obj!==null&&(position=obj.offsetTop,options2.hasInnerContainers&&(position+=getHeadingTopPos(obj.offsetParent))),position}function updateClassname(obj,className){return obj&&obj.className!==className&&(obj.className=className),obj}function updateToc(headingsArray){if(options2.scrollContainer&&document.querySelector(options2.scrollContainer)){var top;top=document.querySelector(options2.scrollContainer).scrollTop}else top=document.documentElement.scrollTop||body.scrollTop;options2.positionFixedSelector&&updateFixedSidebarClass();var headings=headingsArray,topHeader;if(currentlyHighlighting&&tocElement!==null&&headings.length>0){some.call(headings,function(heading,i3){if(getHeadingTopPos(heading)>top+options2.headingsOffset+10){var index=i3===0?i3:i3-1;return topHeader=headings[index],!0}else if(i3===headings.length-1)return topHeader=headings[headings.length-1],!0});var oldActiveTocLink=tocElement.querySelector("."+options2.activeLinkClass),activeTocLink=tocElement.querySelector("."+options2.linkClass+".node-name--"+topHeader.nodeName+'[href="'+options2.basePath+"#"+topHeader.id.replace(/([ #;&,.+*~':"!^$[\]()=>|/\\@])/g,"\\$1")+'"]');if(oldActiveTocLink===activeTocLink)return;var tocLinks=tocElement.querySelectorAll("."+options2.linkClass);forEach.call(tocLinks,function(tocLink){updateClassname(tocLink,tocLink.className.replace(SPACE_CHAR+options2.activeLinkClass,""))});var tocLis=tocElement.querySelectorAll("."+options2.listItemClass);forEach.call(tocLis,function(tocLi){updateClassname(tocLi,tocLi.className.replace(SPACE_CHAR+options2.activeListItemClass,""))}),activeTocLink&&activeTocLink.className.indexOf(options2.activeLinkClass)===-1&&(activeTocLink.className+=SPACE_CHAR+options2.activeLinkClass);var li=activeTocLink&&activeTocLink.parentNode;li&&li.className.indexOf(options2.activeListItemClass)===-1&&(li.className+=SPACE_CHAR+options2.activeListItemClass);var tocLists=tocElement.querySelectorAll("."+options2.listClass+"."+options2.collapsibleClass);forEach.call(tocLists,function(list){list.className.indexOf(options2.isCollapsedClass)===-1&&(list.className+=SPACE_CHAR+options2.isCollapsedClass)}),activeTocLink&&activeTocLink.nextSibling&&activeTocLink.nextSibling.className.indexOf(options2.isCollapsedClass)!==-1&&updateClassname(activeTocLink.nextSibling,activeTocLink.nextSibling.className.replace(SPACE_CHAR+options2.isCollapsedClass,"")),removeCollapsedFromParents(activeTocLink&&activeTocLink.parentNode.parentNode)}}function removeCollapsedFromParents(element){return element&&element.className.indexOf(options2.collapsibleClass)!==-1&&element.className.indexOf(options2.isCollapsedClass)!==-1?(updateClassname(element,element.className.replace(SPACE_CHAR+options2.isCollapsedClass,"")),removeCollapsedFromParents(element.parentNode.parentNode)):element}function disableTocAnimation(event){var target=event.target||event.srcElement;typeof target.className!="string"||target.className.indexOf(options2.linkClass)===-1||(currentlyHighlighting=!1)}function enableTocAnimation(){currentlyHighlighting=!0}return{enableTocAnimation,disableTocAnimation,render,updateToc}}}});var require_parse_content=__commonJS({"../../node_modules/tocbot/src/js/parse-content.js"(exports2,module2){module2.exports=function(options2){var reduce=[].reduce;function getLastItem(array2){return array2[array2.length-1]}function getHeadingLevel(heading){return+heading.nodeName.toUpperCase().replace("H","")}function isHTMLElement(maybeElement){try{return maybeElement instanceof window.HTMLElement||maybeElement instanceof window.parent.HTMLElement}catch{return maybeElement instanceof window.HTMLElement}}function getHeadingObject(heading){if(!isHTMLElement(heading))return heading;if(options2.ignoreHiddenElements&&(!heading.offsetHeight||!heading.offsetParent))return null;let headingLabel=heading.getAttribute("data-heading-label")||(options2.headingLabelCallback?String(options2.headingLabelCallback(heading.innerText)):(heading.innerText||heading.textContent).trim());var obj={id:heading.id,children:[],nodeName:heading.nodeName,headingLevel:getHeadingLevel(heading),textContent:headingLabel};return options2.includeHtml&&(obj.childNodes=heading.childNodes),options2.headingObjectCallback?options2.headingObjectCallback(obj,heading):obj}function addNode(node,nest){for(var obj=getHeadingObject(node),level=obj.headingLevel,array2=nest,lastItem=getLastItem(array2),lastItemLevel=lastItem?lastItem.headingLevel:0,counter=level-lastItemLevel;counter>0&&(lastItem=getLastItem(array2),!(lastItem&&level===lastItem.headingLevel));)lastItem&&lastItem.children!==void 0&&(array2=lastItem.children),counter--;return level>=options2.collapseDepth&&(obj.isCollapsed=!0),array2.push(obj),array2}function selectHeadings(contentElement,headingSelector){var selectors=headingSelector;options2.ignoreSelector&&(selectors=headingSelector.split(",").map(function(selector){return selector.trim()+":not("+options2.ignoreSelector+")"}));try{return contentElement.querySelectorAll(selectors)}catch{return console.warn("Headers not found with selector: "+selectors),null}}function nestHeadingsArray(headingsArray){return reduce.call(headingsArray,function(prev,curr){var currentHeading=getHeadingObject(curr);return currentHeading&&addNode(currentHeading,prev.nest),prev},{nest:[]})}return{nestHeadingsArray,selectHeadings}}}});var require_update_toc_scroll=__commonJS({"../../node_modules/tocbot/src/js/update-toc-scroll.js"(exports2,module2){module2.exports=function(options2){var toc=options2.tocElement||document.querySelector(options2.tocSelector);if(toc&&toc.scrollHeight>toc.clientHeight){var activeItem=toc.querySelector("."+options2.activeListItemClass);activeItem&&(toc.scrollTop=activeItem.offsetTop-options2.tocScrollOffset)}}}});var require_scroll_smooth=__commonJS({"../../node_modules/tocbot/src/js/scroll-smooth/index.js"(exports2){exports2.initSmoothScrolling=initSmoothScrolling;function initSmoothScrolling(options2){var duration=options2.duration,offset=options2.offset,pageUrl=location.hash?stripHash(location.href):location.href;delegatedLinkHijacking();function delegatedLinkHijacking(){document.body.addEventListener("click",onClick,!1);function onClick(e3){!isInPageLink(e3.target)||e3.target.className.indexOf("no-smooth-scroll")>-1||e3.target.href.charAt(e3.target.href.length-2)==="#"&&e3.target.href.charAt(e3.target.href.length-1)==="!"||e3.target.className.indexOf(options2.linkClass)===-1||jump(e3.target.hash,{duration,offset,callback:function(){setFocus(e3.target.hash)}})}}function isInPageLink(n3){return n3.tagName.toLowerCase()==="a"&&(n3.hash.length>0||n3.href.charAt(n3.href.length-1)==="#")&&(stripHash(n3.href)===pageUrl||stripHash(n3.href)+"#"===pageUrl)}function stripHash(url){return url.slice(0,url.lastIndexOf("#"))}function setFocus(hash){var element=document.getElementById(hash.substring(1));element&&(/^(?:a|select|input|button|textarea)$/i.test(element.tagName)||(element.tabIndex=-1),element.focus())}}function jump(target,options2){var start=window.pageYOffset,opt={duration:options2.duration,offset:options2.offset||0,callback:options2.callback,easing:options2.easing||easeInOutQuad},tgt=document.querySelector('[id="'+decodeURI(target).split("#").join("")+'"]')||document.querySelector('[id="'+target.split("#").join("")+'"]'),distance=typeof target=="string"?opt.offset+(target?tgt&&tgt.getBoundingClientRect().top||0:-(document.documentElement.scrollTop||document.body.scrollTop)):target,duration=typeof opt.duration=="function"?opt.duration(distance):opt.duration,timeStart,timeElapsed;requestAnimationFrame(function(time){timeStart=time,loop(time)});function loop(time){timeElapsed=time-timeStart,window.scrollTo(0,opt.easing(timeElapsed,start,distance,duration)),timeElapsed<duration?requestAnimationFrame(loop):end()}function end(){window.scrollTo(0,start+distance),typeof opt.callback=="function"&&opt.callback()}function easeInOutQuad(t3,b3,c3,d3){return t3/=d3/2,t3<1?c3/2*t3*t3+b3:(t3--,-c3/2*(t3*(t3-2)-1)+b3)}}}});var require_js=__commonJS({"../../node_modules/tocbot/src/js/index.js"(exports2,module2){(function(root3,factory){typeof define=="function"&&define.amd?define([],factory(root3)):typeof exports2=="object"?module2.exports=factory(root3):root3.tocbot=factory(root3)})(typeof global<"u"?global:window||global,function(root3){"use strict";var defaultOptions2=require_default_options(),options2={},tocbot2={},BuildHtml=require_build_html(),ParseContent=require_parse_content(),updateTocScroll=require_update_toc_scroll(),buildHtml,parseContent,supports=!!root3&&!!root3.document&&!!root3.document.querySelector&&!!root3.addEventListener;if(typeof window>"u"&&!supports)return;var headingsArray,hasOwnProperty5=Object.prototype.hasOwnProperty;function extend(){for(var target={},i3=0;i3<arguments.length;i3++){var source2=arguments[i3];for(var key2 in source2)hasOwnProperty5.call(source2,key2)&&(target[key2]=source2[key2])}return target}function throttle(fn,threshold,scope){threshold||(threshold=250);var last,deferTimer;return function(){var context=scope||this,now=+new Date,args2=arguments;last&&now<last+threshold?(clearTimeout(deferTimer),deferTimer=setTimeout(function(){last=now,fn.apply(context,args2)},threshold)):(last=now,fn.apply(context,args2))}}function getContentElement(options3){try{return options3.contentElement||document.querySelector(options3.contentSelector)}catch{return console.warn("Contents element not found: "+options3.contentSelector),null}}function getTocElement(options3){try{return options3.tocElement||document.querySelector(options3.tocSelector)}catch{return console.warn("TOC element not found: "+options3.tocSelector),null}}return tocbot2.destroy=function(){var tocElement=getTocElement(options2);tocElement!==null&&(options2.skipRendering||tocElement&&(tocElement.innerHTML=""),options2.scrollContainer&&document.querySelector(options2.scrollContainer)?(document.querySelector(options2.scrollContainer).removeEventListener("scroll",this._scrollListener,!1),document.querySelector(options2.scrollContainer).removeEventListener("resize",this._scrollListener,!1),buildHtml&&document.querySelector(options2.scrollContainer).removeEventListener("click",this._clickListener,!1)):(document.removeEventListener("scroll",this._scrollListener,!1),document.removeEventListener("resize",this._scrollListener,!1),buildHtml&&document.removeEventListener("click",this._clickListener,!1)))},tocbot2.init=function(customOptions){if(supports){options2=extend(defaultOptions2,customOptions||{}),this.options=options2,this.state={},options2.scrollSmooth&&(options2.duration=options2.scrollSmoothDuration,options2.offset=options2.scrollSmoothOffset,tocbot2.scrollSmooth=require_scroll_smooth().initSmoothScrolling(options2)),buildHtml=BuildHtml(options2),parseContent=ParseContent(options2),this._buildHtml=buildHtml,this._parseContent=parseContent,this._headingsArray=headingsArray,tocbot2.destroy();var contentElement=getContentElement(options2);if(contentElement!==null){var tocElement=getTocElement(options2);if(tocElement!==null&&(headingsArray=parseContent.selectHeadings(contentElement,options2.headingSelector),headingsArray!==null)){var nestedHeadingsObj=parseContent.nestHeadingsArray(headingsArray),nestedHeadings=nestedHeadingsObj.nest;if(!options2.skipRendering)buildHtml.render(tocElement,nestedHeadings);else return this;this._scrollListener=throttle(function(e3){buildHtml.updateToc(headingsArray),!options2.disableTocScrollSync&&updateTocScroll(options2);var isTop=e3&&e3.target&&e3.target.scrollingElement&&e3.target.scrollingElement.scrollTop===0;(e3&&(e3.eventPhase===0||e3.currentTarget===null)||isTop)&&(buildHtml.updateToc(headingsArray),options2.scrollEndCallback&&options2.scrollEndCallback(e3))},options2.throttleTimeout),this._scrollListener(),options2.scrollContainer&&document.querySelector(options2.scrollContainer)?(document.querySelector(options2.scrollContainer).addEventListener("scroll",this._scrollListener,!1),document.querySelector(options2.scrollContainer).addEventListener("resize",this._scrollListener,!1)):(document.addEventListener("scroll",this._scrollListener,!1),document.addEventListener("resize",this._scrollListener,!1));var timeout=null;return this._clickListener=throttle(function(event){options2.scrollSmooth&&buildHtml.disableTocAnimation(event),buildHtml.updateToc(headingsArray),timeout&&clearTimeout(timeout),timeout=setTimeout(function(){buildHtml.enableTocAnimation()},options2.scrollSmoothDuration)},options2.throttleTimeout),options2.scrollContainer&&document.querySelector(options2.scrollContainer)?document.querySelector(options2.scrollContainer).addEventListener("click",this._clickListener,!1):document.addEventListener("click",this._clickListener,!1),this}}}},tocbot2.refresh=function(customOptions){tocbot2.destroy(),tocbot2.init(customOptions||this.options)},root3.tocbot=tocbot2,tocbot2})}});var index_exports={};__export(index_exports,{AddContext:()=>AddContext,Anchor:()=>Anchor,AnchorMdx:()=>AnchorMdx,ArgTypes:()=>ArgTypes,BooleanControl:()=>BooleanControl,Canvas:()=>Canvas,CodeOrSourceMdx:()=>CodeOrSourceMdx,ColorControl:()=>ColorControl2,ColorItem:()=>ColorItem,ColorPalette:()=>ColorPalette,Controls:()=>Controls3,DateControl:()=>DateControl,Description:()=>DescriptionContainer,DescriptionType:()=>DescriptionType,Docs:()=>Docs,DocsContainer:()=>DocsContainer,DocsContext:()=>DocsContext,DocsPage:()=>DocsPage,DocsStory:()=>DocsStory,ExternalDocs:()=>ExternalDocs,ExternalDocsContainer:()=>ExternalDocsContainer,FilesControl:()=>FilesControl,HeaderMdx:()=>HeaderMdx,HeadersMdx:()=>HeadersMdx,Heading:()=>Heading2,IconGallery:()=>IconGallery,IconItem:()=>IconItem,Markdown:()=>Markdown,Meta:()=>Meta,NumberControl:()=>NumberControl,ObjectControl:()=>ObjectControl,OptionsControl:()=>OptionsControl,PRIMARY_STORY:()=>PRIMARY_STORY,Primary:()=>Primary,PureArgsTable:()=>ArgsTable,RangeControl:()=>RangeControl,Source:()=>Source2,SourceContainer:()=>SourceContainer,SourceContext:()=>SourceContext,Stories:()=>Stories,Story:()=>Story2,Subheading:()=>Subheading,Subtitle:()=>Subtitle2,TextControl:()=>TextControl,Title:()=>Title2,Typeset:()=>Typeset,UNKNOWN_ARGS_HASH:()=>UNKNOWN_ARGS_HASH,Unstyled:()=>Unstyled,Wrapper:()=>Wrapper12,anchorBlockIdFromId:()=>anchorBlockIdFromId,argsHash:()=>argsHash,assertIsFn:()=>assertIsFn,extractTitle:()=>extractTitle,format:()=>format2,formatDate:()=>formatDate,formatTime:()=>formatTime,getStoryId:()=>getStoryId2,getStoryProps:()=>getStoryProps,parse:()=>parse2,parseDate:()=>parseDate,parseTime:()=>parseTime,slugs:()=>slugs,useOf:()=>useOf,useSourceProps:()=>useSourceProps});module.exports=__toCommonJS(index_exports);var import_react2=__toESM(require("react")),import_components2=require("storybook/internal/components"),import_theming2=require("storybook/internal/theming");var import_react=__toESM(require("react")),import_components=require("storybook/internal/components"),import_theming=require("storybook/internal/theming");function _extends(){return _extends=Object.assign?Object.assign.bind():function(n3){for(var e3=1;e3<arguments.length;e3++){var t3=arguments[e3];for(var r3 in t3)({}).hasOwnProperty.call(t3,r3)&&(n3[r3]=t3[r3])}return n3},_extends.apply(null,arguments)}function _assertThisInitialized(e3){if(e3===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e3}function _setPrototypeOf(t3,e3){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t4,e4){return t4.__proto__=e4,t4},_setPrototypeOf(t3,e3)}function _inheritsLoose(t3,o3){t3.prototype=Object.create(o3.prototype),t3.prototype.constructor=t3,_setPrototypeOf(t3,o3)}function _getPrototypeOf(t3){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t4){return t4.__proto__||Object.getPrototypeOf(t4)},_getPrototypeOf(t3)}function _isNativeFunction(t3){try{return Function.toString.call(t3).indexOf("[native code]")!==-1}catch{return typeof t3=="function"}}function _isNativeReflectConstruct(){try{var t3=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(_isNativeReflectConstruct=function(){return!!t3})()}function _construct(t3,e3,r3){if(_isNativeReflectConstruct())return Reflect.construct.apply(null,arguments);var o3=[null];o3.push.apply(o3,e3);var p3=new(t3.bind.apply(t3,o3));return r3&&_setPrototypeOf(p3,r3.prototype),p3}function _wrapNativeSuper(t3){var r3=typeof Map=="function"?new Map:void 0;return _wrapNativeSuper=function(t4){if(t4===null||!_isNativeFunction(t4))return t4;if(typeof t4!="function")throw new TypeError("Super expression must either be null or a function");if(r3!==void 0){if(r3.has(t4))return r3.get(t4);r3.set(t4,Wrapper13)}function Wrapper13(){return _construct(t4,arguments,_getPrototypeOf(this).constructor)}return Wrapper13.prototype=Object.create(t4.prototype,{constructor:{value:Wrapper13,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(Wrapper13,t4)},_wrapNativeSuper(t3)}var ERRORS={1:`Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).

`,2:`Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).

`,3:`Passed an incorrect argument to a color function, please pass a string representation of a color.

`,4:`Couldn't generate valid rgb string from %s, it returned %s.

`,5:`Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.

`,6:`Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).

`,7:`Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).

`,8:`Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.

`,9:`Please provide a number of steps to the modularScale helper.

`,10:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,11:`Invalid value passed as base to modularScale, expected number or em string but got "%s"

`,12:`Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.

`,13:`Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.

`,14:`Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.

`,15:`Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.

`,16:`You must provide a template to this method.

`,17:`You passed an unsupported selector state to this method.

`,18:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,19:`fromSize and toSize must be provided as stringified numbers with the same units.

`,20:`expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,21:"expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",22:"expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",23:`fontFace expects a name of a font-family.

`,24:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,25:`fontFace expects localFonts to be an array.

`,26:`fontFace expects fileFormats to be an array.

`,27:`radialGradient requries at least 2 color-stops to properly render.

`,28:`Please supply a filename to retinaImage() as the first argument.

`,29:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,30:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",31:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation

`,32:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])
To pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')

`,33:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation

`,34:`borderRadius expects a radius value as a string or number as the second argument.

`,35:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,36:`Property must be a string value.

`,37:`Syntax Error at %s.

`,38:`Formula contains a function that needs parentheses at %s.

`,39:`Formula is missing closing parenthesis at %s.

`,40:`Formula has too many closing parentheses at %s.

`,41:`All values in a formula must have the same unit or be unitless.

`,42:`Please provide a number of steps to the modularScale helper.

`,43:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,44:`Invalid value passed as base to modularScale, expected number or em/rem string but got %s.

`,45:`Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.

`,46:`Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.

`,47:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,48:`fromSize and toSize must be provided as stringified numbers with the same units.

`,49:`Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,50:`Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.

`,51:`Expects the first argument object to have the properties prop, fromSize, and toSize.

`,52:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,53:`fontFace expects localFonts to be an array.

`,54:`fontFace expects fileFormats to be an array.

`,55:`fontFace expects a name of a font-family.

`,56:`linearGradient requries at least 2 color-stops to properly render.

`,57:`radialGradient requries at least 2 color-stops to properly render.

`,58:`Please supply a filename to retinaImage() as the first argument.

`,59:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,60:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",61:`Property must be a string value.

`,62:`borderRadius expects a radius value as a string or number as the second argument.

`,63:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,64:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.

`,65:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').

`,66:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.

`,67:`You must provide a template to this method.

`,68:`You passed an unsupported selector state to this method.

`,69:`Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.

`,70:`Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.

`,71:`Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.

`,72:`Passed invalid base value %s to %s(), please pass a value like "12px" or 12.

`,73:`Please provide a valid CSS variable.

`,74:`CSS variable not found and no default was provided.

`,75:`important requires a valid style object, got a %s instead.

`,76:`fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.

`,77:`remToPx expects a value in "rem" but you provided it in "%s".

`,78:`base must be set in "px" or "%" but you set it in "%s".
`};function format(){for(var _len=arguments.length,args2=new Array(_len),_key=0;_key<_len;_key++)args2[_key]=arguments[_key];var a3=args2[0],b3=[],c3;for(c3=1;c3<args2.length;c3+=1)b3.push(args2[c3]);return b3.forEach(function(d3){a3=a3.replace(/%[a-z]/,d3)}),a3}var PolishedError=function(_Error){_inheritsLoose(PolishedError2,_Error);function PolishedError2(code){var _this;if(process.env.NODE_ENV==="production")_this=_Error.call(this,"An error occurred. See https://github.com/styled-components/polished/blob/main/src/internalHelpers/errors.md#"+code+" for more information.")||this;else{for(var _len2=arguments.length,args2=new Array(_len2>1?_len2-1:0),_key2=1;_key2<_len2;_key2++)args2[_key2-1]=arguments[_key2];_this=_Error.call(this,format.apply(void 0,[ERRORS[code]].concat(args2)))||this}return _assertThisInitialized(_this)}return PolishedError2}(_wrapNativeSuper(Error));function colorToInt(color){return Math.round(color*255)}function convertToInt(red,green,blue){return colorToInt(red)+","+colorToInt(green)+","+colorToInt(blue)}function hslToRgb(hue,saturation,lightness,convert3){if(convert3===void 0&&(convert3=convertToInt),saturation===0)return convert3(lightness,lightness,lightness);var huePrime=(hue%360+360)%360/60,chroma=(1-Math.abs(2*lightness-1))*saturation,secondComponent=chroma*(1-Math.abs(huePrime%2-1)),red=0,green=0,blue=0;huePrime>=0&&huePrime<1?(red=chroma,green=secondComponent):huePrime>=1&&huePrime<2?(red=secondComponent,green=chroma):huePrime>=2&&huePrime<3?(green=chroma,blue=secondComponent):huePrime>=3&&huePrime<4?(green=secondComponent,blue=chroma):huePrime>=4&&huePrime<5?(red=secondComponent,blue=chroma):huePrime>=5&&huePrime<6&&(red=chroma,blue=secondComponent);var lightnessModification=lightness-chroma/2,finalRed=red+lightnessModification,finalGreen=green+lightnessModification,finalBlue=blue+lightnessModification;return convert3(finalRed,finalGreen,finalBlue)}var namedColorMap={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};function nameToHex(color){if(typeof color!="string")return color;var normalizedColorName=color.toLowerCase();return namedColorMap[normalizedColorName]?"#"+namedColorMap[normalizedColorName]:color}var hexRegex=/^#[a-fA-F0-9]{6}$/,hexRgbaRegex=/^#[a-fA-F0-9]{8}$/,reducedHexRegex=/^#[a-fA-F0-9]{3}$/,reducedRgbaHexRegex=/^#[a-fA-F0-9]{4}$/,rgbRegex=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,rgbaRegex=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,hslRegex=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,hslaRegex=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function parseToRgb(color){if(typeof color!="string")throw new PolishedError(3);var normalizedColor=nameToHex(color);if(normalizedColor.match(hexRegex))return{red:parseInt(""+normalizedColor[1]+normalizedColor[2],16),green:parseInt(""+normalizedColor[3]+normalizedColor[4],16),blue:parseInt(""+normalizedColor[5]+normalizedColor[6],16)};if(normalizedColor.match(hexRgbaRegex)){var alpha=parseFloat((parseInt(""+normalizedColor[7]+normalizedColor[8],16)/255).toFixed(2));return{red:parseInt(""+normalizedColor[1]+normalizedColor[2],16),green:parseInt(""+normalizedColor[3]+normalizedColor[4],16),blue:parseInt(""+normalizedColor[5]+normalizedColor[6],16),alpha}}if(normalizedColor.match(reducedHexRegex))return{red:parseInt(""+normalizedColor[1]+normalizedColor[1],16),green:parseInt(""+normalizedColor[2]+normalizedColor[2],16),blue:parseInt(""+normalizedColor[3]+normalizedColor[3],16)};if(normalizedColor.match(reducedRgbaHexRegex)){var _alpha=parseFloat((parseInt(""+normalizedColor[4]+normalizedColor[4],16)/255).toFixed(2));return{red:parseInt(""+normalizedColor[1]+normalizedColor[1],16),green:parseInt(""+normalizedColor[2]+normalizedColor[2],16),blue:parseInt(""+normalizedColor[3]+normalizedColor[3],16),alpha:_alpha}}var rgbMatched=rgbRegex.exec(normalizedColor);if(rgbMatched)return{red:parseInt(""+rgbMatched[1],10),green:parseInt(""+rgbMatched[2],10),blue:parseInt(""+rgbMatched[3],10)};var rgbaMatched=rgbaRegex.exec(normalizedColor.substring(0,50));if(rgbaMatched)return{red:parseInt(""+rgbaMatched[1],10),green:parseInt(""+rgbaMatched[2],10),blue:parseInt(""+rgbaMatched[3],10),alpha:parseFloat(""+rgbaMatched[4])>1?parseFloat(""+rgbaMatched[4])/100:parseFloat(""+rgbaMatched[4])};var hslMatched=hslRegex.exec(normalizedColor);if(hslMatched){var hue=parseInt(""+hslMatched[1],10),saturation=parseInt(""+hslMatched[2],10)/100,lightness=parseInt(""+hslMatched[3],10)/100,rgbColorString="rgb("+hslToRgb(hue,saturation,lightness)+")",hslRgbMatched=rgbRegex.exec(rgbColorString);if(!hslRgbMatched)throw new PolishedError(4,normalizedColor,rgbColorString);return{red:parseInt(""+hslRgbMatched[1],10),green:parseInt(""+hslRgbMatched[2],10),blue:parseInt(""+hslRgbMatched[3],10)}}var hslaMatched=hslaRegex.exec(normalizedColor.substring(0,50));if(hslaMatched){var _hue=parseInt(""+hslaMatched[1],10),_saturation=parseInt(""+hslaMatched[2],10)/100,_lightness=parseInt(""+hslaMatched[3],10)/100,_rgbColorString="rgb("+hslToRgb(_hue,_saturation,_lightness)+")",_hslRgbMatched=rgbRegex.exec(_rgbColorString);if(!_hslRgbMatched)throw new PolishedError(4,normalizedColor,_rgbColorString);return{red:parseInt(""+_hslRgbMatched[1],10),green:parseInt(""+_hslRgbMatched[2],10),blue:parseInt(""+_hslRgbMatched[3],10),alpha:parseFloat(""+hslaMatched[4])>1?parseFloat(""+hslaMatched[4])/100:parseFloat(""+hslaMatched[4])}}throw new PolishedError(5)}function rgbToHsl(color){var red=color.red/255,green=color.green/255,blue=color.blue/255,max=Math.max(red,green,blue),min=Math.min(red,green,blue),lightness=(max+min)/2;if(max===min)return color.alpha!==void 0?{hue:0,saturation:0,lightness,alpha:color.alpha}:{hue:0,saturation:0,lightness};var hue,delta=max-min,saturation=lightness>.5?delta/(2-max-min):delta/(max+min);switch(max){case red:hue=(green-blue)/delta+(green<blue?6:0);break;case green:hue=(blue-red)/delta+2;break;default:hue=(red-green)/delta+4;break}return hue*=60,color.alpha!==void 0?{hue,saturation,lightness,alpha:color.alpha}:{hue,saturation,lightness}}function parseToHsl(color){return rgbToHsl(parseToRgb(color))}var reduceHexValue=function(value3){return value3.length===7&&value3[1]===value3[2]&&value3[3]===value3[4]&&value3[5]===value3[6]?"#"+value3[1]+value3[3]+value3[5]:value3},reduceHexValue$1=reduceHexValue;function numberToHex(value3){var hex=value3.toString(16);return hex.length===1?"0"+hex:hex}function colorToHex(color){return numberToHex(Math.round(color*255))}function convertToHex(red,green,blue){return reduceHexValue$1("#"+colorToHex(red)+colorToHex(green)+colorToHex(blue))}function hslToHex(hue,saturation,lightness){return hslToRgb(hue,saturation,lightness,convertToHex)}function hsl(value3,saturation,lightness){if(typeof value3=="number"&&typeof saturation=="number"&&typeof lightness=="number")return hslToHex(value3,saturation,lightness);if(typeof value3=="object"&&saturation===void 0&&lightness===void 0)return hslToHex(value3.hue,value3.saturation,value3.lightness);throw new PolishedError(1)}function hsla(value3,saturation,lightness,alpha){if(typeof value3=="number"&&typeof saturation=="number"&&typeof lightness=="number"&&typeof alpha=="number")return alpha>=1?hslToHex(value3,saturation,lightness):"rgba("+hslToRgb(value3,saturation,lightness)+","+alpha+")";if(typeof value3=="object"&&saturation===void 0&&lightness===void 0&&alpha===void 0)return value3.alpha>=1?hslToHex(value3.hue,value3.saturation,value3.lightness):"rgba("+hslToRgb(value3.hue,value3.saturation,value3.lightness)+","+value3.alpha+")";throw new PolishedError(2)}function rgb(value3,green,blue){if(typeof value3=="number"&&typeof green=="number"&&typeof blue=="number")return reduceHexValue$1("#"+numberToHex(value3)+numberToHex(green)+numberToHex(blue));if(typeof value3=="object"&&green===void 0&&blue===void 0)return reduceHexValue$1("#"+numberToHex(value3.red)+numberToHex(value3.green)+numberToHex(value3.blue));throw new PolishedError(6)}function rgba(firstValue,secondValue,thirdValue,fourthValue){if(typeof firstValue=="string"&&typeof secondValue=="number"){var rgbValue=parseToRgb(firstValue);return"rgba("+rgbValue.red+","+rgbValue.green+","+rgbValue.blue+","+secondValue+")"}else{if(typeof firstValue=="number"&&typeof secondValue=="number"&&typeof thirdValue=="number"&&typeof fourthValue=="number")return fourthValue>=1?rgb(firstValue,secondValue,thirdValue):"rgba("+firstValue+","+secondValue+","+thirdValue+","+fourthValue+")";if(typeof firstValue=="object"&&secondValue===void 0&&thirdValue===void 0&&fourthValue===void 0)return firstValue.alpha>=1?rgb(firstValue.red,firstValue.green,firstValue.blue):"rgba("+firstValue.red+","+firstValue.green+","+firstValue.blue+","+firstValue.alpha+")"}throw new PolishedError(7)}var isRgb=function(color){return typeof color.red=="number"&&typeof color.green=="number"&&typeof color.blue=="number"&&(typeof color.alpha!="number"||typeof color.alpha>"u")},isRgba=function(color){return typeof color.red=="number"&&typeof color.green=="number"&&typeof color.blue=="number"&&typeof color.alpha=="number"},isHsl=function(color){return typeof color.hue=="number"&&typeof color.saturation=="number"&&typeof color.lightness=="number"&&(typeof color.alpha!="number"||typeof color.alpha>"u")},isHsla=function(color){return typeof color.hue=="number"&&typeof color.saturation=="number"&&typeof color.lightness=="number"&&typeof color.alpha=="number"};function toColorString(color){if(typeof color!="object")throw new PolishedError(8);if(isRgba(color))return rgba(color);if(isRgb(color))return rgb(color);if(isHsla(color))return hsla(color);if(isHsl(color))return hsl(color);throw new PolishedError(8)}function curried(f4,length,acc){return function(){var combined=acc.concat(Array.prototype.slice.call(arguments));return combined.length>=length?f4.apply(this,combined):curried(f4,length,combined)}}function curry(f4){return curried(f4,f4.length,[])}function guard(lowerBoundary,upperBoundary,value3){return Math.max(lowerBoundary,Math.min(upperBoundary,value3))}function darken(amount,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{lightness:guard(0,1,hslColor.lightness-parseFloat(amount))}))}var curriedDarken=curry(darken),curriedDarken$1=curriedDarken;function lighten(amount,color){if(color==="transparent")return color;var hslColor=parseToHsl(color);return toColorString(_extends({},hslColor,{lightness:guard(0,1,hslColor.lightness+parseFloat(amount))}))}var curriedLighten=curry(lighten),curriedLighten$1=curriedLighten;function opacify(amount,color){if(color==="transparent")return color;var parsedColor=parseToRgb(color),alpha=typeof parsedColor.alpha=="number"?parsedColor.alpha:1,colorWithAlpha=_extends({},parsedColor,{alpha:guard(0,1,(alpha*100+parseFloat(amount)*100)/100)});return rgba(colorWithAlpha)}var curriedOpacify=curry(opacify),curriedOpacify$1=curriedOpacify;function transparentize(amount,color){if(color==="transparent")return color;var parsedColor=parseToRgb(color),alpha=typeof parsedColor.alpha=="number"?parsedColor.alpha:1,colorWithAlpha=_extends({},parsedColor,{alpha:guard(0,1,+(alpha*100-parseFloat(amount)*100).toFixed(2)/100)});return rgba(colorWithAlpha)}var curriedTransparentize=curry(transparentize),curriedTransparentize$1=curriedTransparentize;var Wrapper=import_theming.styled.div(import_components.withReset,({theme})=>({backgroundColor:theme.base==="light"?"rgba(0,0,0,.01)":"rgba(255,255,255,.01)",borderRadius:theme.appBorderRadius,border:`1px dashed ${theme.appBorderColor}`,display:"flex",alignItems:"center",justifyContent:"center",padding:20,margin:"25px 0 40px",color:curriedTransparentize$1(.3,theme.color.defaultText),fontSize:theme.typography.size.s2})),EmptyBlock=props=>import_react.default.createElement(Wrapper,{...props,className:"docblock-emptyblock sb-unstyled"});var StyledSyntaxHighlighter=(0,import_theming2.styled)(import_components2.SyntaxHighlighter)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,lineHeight:"19px",margin:"25px 0 40px",borderRadius:theme.appBorderRadius,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0","pre.prismjs":{padding:20,background:"inherit"}}));var SourceSkeletonWrapper=import_theming2.styled.div(({theme})=>({background:theme.background.content,borderRadius:theme.appBorderRadius,border:`1px solid ${theme.appBorderColor}`,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",margin:"25px 0 40px",padding:"20px 20px 20px 22px"})),SourceSkeletonPlaceholder=import_theming2.styled.div(({theme})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,height:17,marginTop:1,width:"60%",[`&:first-child${import_theming2.ignoreSsrWarning}`]:{margin:0}})),SourceSkeleton=()=>import_react2.default.createElement(SourceSkeletonWrapper,null,import_react2.default.createElement(SourceSkeletonPlaceholder,null),import_react2.default.createElement(SourceSkeletonPlaceholder,{style:{width:"80%"}}),import_react2.default.createElement(SourceSkeletonPlaceholder,{style:{width:"30%"}}),import_react2.default.createElement(SourceSkeletonPlaceholder,{style:{width:"80%"}})),Source=({isLoading,error,language,code,dark,format:format3=!1,...rest})=>{let{typography}=(0,import_theming2.useTheme)();if(isLoading)return import_react2.default.createElement(SourceSkeleton,null);if(error)return import_react2.default.createElement(EmptyBlock,null,error);let syntaxHighlighter=import_react2.default.createElement(StyledSyntaxHighlighter,{bordered:!0,copyable:!0,format:format3,language,className:"docblock-source sb-unstyled",...rest},code);if(typeof dark>"u")return syntaxHighlighter;let overrideTheme=dark?import_theming2.themes.dark:import_theming2.themes.light;return import_react2.default.createElement(import_theming2.ThemeProvider,{theme:(0,import_theming2.convert)({...overrideTheme,fontCode:typography.fonts.mono,fontBase:typography.fonts.base})},syntaxHighlighter)};var import_react3=__toESM(require("react")),import_components3=require("storybook/internal/components"),import_theming3=require("storybook/internal/theming");var toGlobalSelector=element=>`& :where(${element}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${element}))`,breakpoint=600,Title=import_theming3.styled.h1(import_components3.withReset,({theme})=>({color:theme.color.defaultText,fontSize:theme.typography.size.m3,fontWeight:theme.typography.weight.bold,lineHeight:"32px",[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.l1,lineHeight:"36px",marginBottom:"16px"}})),Subtitle=import_theming3.styled.h2(import_components3.withReset,({theme})=>({fontWeight:theme.typography.weight.regular,fontSize:theme.typography.size.s3,lineHeight:"20px",borderBottom:"none",marginBottom:15,[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.m1,lineHeight:"28px",marginBottom:24},color:curriedTransparentize$1(.25,theme.color.defaultText)})),DocsContent=import_theming3.styled.div(({theme})=>{let reset={fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s3,margin:0,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch"},headers={margin:"20px 0 8px",padding:0,cursor:"text",position:"relative",color:theme.color.defaultText,"&:first-of-type":{marginTop:0,paddingTop:0},"&:hover a.anchor":{textDecoration:"none"},"& code":{fontSize:"inherit"}},code={lineHeight:1,margin:"0 2px",padding:"3px 5px",whiteSpace:"nowrap",borderRadius:3,fontSize:theme.typography.size.s2-1,border:theme.base==="light"?`1px solid ${theme.color.mediumlight}`:`1px solid ${theme.color.darker}`,color:theme.base==="light"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),backgroundColor:theme.base==="light"?theme.color.lighter:theme.color.border};return{maxWidth:1e3,width:"100%",[toGlobalSelector("a")]:{...reset,fontSize:"inherit",lineHeight:"24px",color:theme.color.secondary,textDecoration:"none","&.absent":{color:"#cc0000"},"&.anchor":{display:"block",paddingLeft:30,marginLeft:-30,cursor:"pointer",position:"absolute",top:0,left:0,bottom:0}},[toGlobalSelector("blockquote")]:{...reset,margin:"16px 0",borderLeft:`4px solid ${theme.color.medium}`,padding:"0 15px",color:theme.color.dark,"& > :first-of-type":{marginTop:0},"& > :last-child":{marginBottom:0}},[toGlobalSelector("div")]:reset,[toGlobalSelector("dl")]:{...reset,margin:"16px 0",padding:0,"& dt":{fontSize:"14px",fontWeight:"bold",fontStyle:"italic",padding:0,margin:"16px 0 4px"},"& dt:first-of-type":{padding:0},"& dt > :first-of-type":{marginTop:0},"& dt > :last-child":{marginBottom:0},"& dd":{margin:"0 0 16px",padding:"0 15px"},"& dd > :first-of-type":{marginTop:0},"& dd > :last-child":{marginBottom:0}},[toGlobalSelector("h1")]:{...reset,...headers,fontSize:`${theme.typography.size.l1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector("h2")]:{...reset,...headers,fontSize:`${theme.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${theme.appBorderColor}`},[toGlobalSelector("h3")]:{...reset,...headers,fontSize:`${theme.typography.size.m1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector("h4")]:{...reset,...headers,fontSize:`${theme.typography.size.s3}px`},[toGlobalSelector("h5")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`},[toGlobalSelector("h6")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`,color:theme.color.dark},[toGlobalSelector("hr")]:{border:"0 none",borderTop:`1px solid ${theme.appBorderColor}`,height:4,padding:0},[toGlobalSelector("img")]:{maxWidth:"100%"},[toGlobalSelector("li")]:{...reset,fontSize:theme.typography.size.s2,color:theme.color.defaultText,lineHeight:"24px","& + li":{marginTop:".25em"},"& ul, & ol":{marginTop:".25em",marginBottom:0},"& code":code},[toGlobalSelector("ol")]:{...reset,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0}},[toGlobalSelector("p")]:{...reset,margin:"16px 0",fontSize:theme.typography.size.s2,lineHeight:"24px",color:theme.color.defaultText,"& code":code},[toGlobalSelector("pre")]:{...reset,fontFamily:theme.typography.fonts.mono,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",lineHeight:"18px",padding:"11px 1rem",whiteSpace:"pre-wrap",color:"inherit",borderRadius:3,margin:"1rem 0","&:not(.prismjs)":{background:"transparent",border:"none",borderRadius:0,padding:0,margin:0},"& pre, &.prismjs":{padding:15,margin:0,whiteSpace:"pre-wrap",color:"inherit",fontSize:"13px",lineHeight:"19px",code:{color:"inherit",fontSize:"inherit"}},"& code":{whiteSpace:"pre"},"& code, & tt":{border:"none"}},[toGlobalSelector("span")]:{...reset,"&.frame":{display:"block",overflow:"hidden","& > span":{border:`1px solid ${theme.color.medium}`,display:"block",float:"left",overflow:"hidden",margin:"13px 0 0",padding:7,width:"auto"},"& span img":{display:"block",float:"left"},"& span span":{clear:"both",color:theme.color.darkest,display:"block",padding:"5px 0 0"}},"&.align-center":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"center"},"& span img":{margin:"0 auto",textAlign:"center"}},"&.align-right":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px 0 0",textAlign:"right"},"& span img":{margin:0,textAlign:"right"}},"&.float-left":{display:"block",marginRight:13,overflow:"hidden",float:"left","& span":{margin:"13px 0 0"}},"&.float-right":{display:"block",marginLeft:13,overflow:"hidden",float:"right","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"right"}}},[toGlobalSelector("table")]:{...reset,margin:"16px 0",fontSize:theme.typography.size.s2,lineHeight:"24px",padding:0,borderCollapse:"collapse","& tr":{borderTop:`1px solid ${theme.appBorderColor}`,backgroundColor:theme.appContentBg,margin:0,padding:0},"& tr:nth-of-type(2n)":{backgroundColor:theme.base==="dark"?theme.color.darker:theme.color.lighter},"& tr th":{fontWeight:"bold",color:theme.color.defaultText,border:`1px solid ${theme.appBorderColor}`,margin:0,padding:"6px 13px"},"& tr td":{border:`1px solid ${theme.appBorderColor}`,color:theme.color.defaultText,margin:0,padding:"6px 13px"},"& tr th :first-of-type, & tr td :first-of-type":{marginTop:0},"& tr th :last-child, & tr td :last-child":{marginBottom:0}},[toGlobalSelector("ul")]:{...reset,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0},listStyle:"disc"}}}),DocsWrapper=import_theming3.styled.div(({theme})=>({background:theme.background.content,display:"flex",justifyContent:"center",padding:"4rem 20px",minHeight:"100vh",boxSizing:"border-box",gap:"3rem",[`@media (min-width: ${breakpoint}px)`]:{}})),DocsPageWrapper=({children,toc})=>import_react3.default.createElement(DocsWrapper,{className:"sbdocs sbdocs-wrapper"},import_react3.default.createElement(DocsContent,{className:"sbdocs sbdocs-content"},children),toc);var import_react8=__toESM(require("react")),import_components6=require("storybook/internal/components"),import_theming6=require("storybook/internal/theming");var getBlockBackgroundStyle=theme=>({borderRadius:theme.appBorderRadius,background:theme.background.content,boxShadow:theme.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",border:`1px solid ${theme.appBorderColor}`});var import_react6=__toESM(require("react")),import_components4=require("storybook/internal/components"),import_theming4=require("storybook/internal/theming");var import_react4=__toESM(require("react")),{window:globalWindow}=globalThis,IFrame=class extends import_react4.Component{constructor(){super(...arguments);this.iframe=null}componentDidMount(){let{id:id2}=this.props;this.iframe=globalWindow.document.getElementById(id2)}shouldComponentUpdate(nextProps){let{scale}=nextProps;return scale!==this.props.scale&&this.setIframeBodyStyle({width:`${scale*100}%`,height:`${scale*100}%`,transform:`scale(${1/scale})`,transformOrigin:"top left"}),!1}setIframeBodyStyle(style){return Object.assign(this.iframe.contentDocument.body.style,style)}render(){let{id:id2,title,src,allowFullScreen,scale,...rest}=this.props;return import_react4.default.createElement("iframe",{id:id2,title,src,...allowFullScreen?{allow:"fullscreen"}:{},loading:"lazy",...rest})}};var import_react5=require("react"),ZoomContext=(0,import_react5.createContext)({scale:1});var{PREVIEW_URL}=globalThis,BASE_URL=PREVIEW_URL||"iframe.html",storyBlockIdFromId=({story,primary})=>`story--${story.id}${primary?"--primary":""}`,InlineStory=props=>{let storyRef=(0,import_react6.useRef)(),[showLoader,setShowLoader]=(0,import_react6.useState)(!0),[error,setError]=(0,import_react6.useState)(),{story,height,autoplay,forceInitialArgs,renderStoryToElement}=props;return(0,import_react6.useEffect)(()=>{if(!(story&&storyRef.current))return()=>{};let element=storyRef.current,cleanup=renderStoryToElement(story,element,{showMain:()=>{},showError:({title,description})=>setError(new Error(`${title} - ${description}`)),showException:err=>setError(err)},{autoplay,forceInitialArgs});return setShowLoader(!1),()=>{Promise.resolve().then(()=>cleanup())}},[autoplay,renderStoryToElement,story]),error?import_react6.default.createElement("pre",null,import_react6.default.createElement(import_components4.ErrorFormatter,{error})):import_react6.default.createElement(import_react6.default.Fragment,null,height?import_react6.default.createElement("style",null,`#${storyBlockIdFromId(props)} { min-height: ${height}; transform: translateZ(0); overflow: auto }`):null,showLoader&&import_react6.default.createElement(StorySkeleton,null),import_react6.default.createElement("div",{ref:storyRef,id:`${storyBlockIdFromId(props)}-inner`,"data-name":story.name}))},IFrameStory=({story,height="500px"})=>import_react6.default.createElement("div",{style:{width:"100%",height}},import_react6.default.createElement(ZoomContext.Consumer,null,({scale})=>import_react6.default.createElement(IFrame,{key:"iframe",id:`iframe--${story.id}`,title:story.name,src:(0,import_components4.getStoryHref)(BASE_URL,story.id,{viewMode:"story"}),allowFullScreen:!0,scale,style:{width:"100%",height:"100%",border:"0 none"}}))),ErrorMessage=import_theming4.styled.strong(({theme})=>({color:theme.color.orange})),Story=props=>{let{inline,story}=props;return inline&&!props.autoplay&&story.usesMount?import_react6.default.createElement(ErrorMessage,null,"This story mounts inside of play. Set"," ",import_react6.default.createElement("a",{href:"https://storybook.js.org/docs/api/doc-blocks/doc-block-story#autoplay"},"autoplay")," ","to true to view this story."):import_react6.default.createElement("div",{id:storyBlockIdFromId(props),className:"sb-story sb-unstyled","data-story-block":"true"},inline?import_react6.default.createElement(InlineStory,{...props}):import_react6.default.createElement(IFrameStory,{...props}))},StorySkeleton=()=>import_react6.default.createElement(import_components4.Loader,null);var import_react7=__toESM(require("react")),import_components5=require("storybook/internal/components"),import_theming5=require("storybook/internal/theming"),import_icons=require("@storybook/icons"),Bar=(0,import_theming5.styled)(import_components5.FlexBar)({position:"absolute",left:0,right:0,top:0,transition:"transform .2s linear"}),Wrapper2=import_theming5.styled.div({display:"flex",alignItems:"center",gap:4}),IconPlaceholder=import_theming5.styled.div(({theme})=>({width:14,height:14,borderRadius:2,margin:"0 7px",backgroundColor:theme.appBorderColor,animation:`${theme.animation.glow} 1.5s ease-in-out infinite`})),Toolbar=({isLoading,storyId,baseUrl,zoom,resetZoom,...rest})=>import_react7.default.createElement(Bar,{...rest},import_react7.default.createElement(Wrapper2,{key:"left"},isLoading?[1,2,3].map(key2=>import_react7.default.createElement(IconPlaceholder,{key:key2})):import_react7.default.createElement(import_react7.default.Fragment,null,import_react7.default.createElement(import_components5.IconButton,{key:"zoomin",onClick:e3=>{e3.preventDefault(),zoom(.8)},title:"Zoom in"},import_react7.default.createElement(import_icons.ZoomIcon,null)),import_react7.default.createElement(import_components5.IconButton,{key:"zoomout",onClick:e3=>{e3.preventDefault(),zoom(1.25)},title:"Zoom out"},import_react7.default.createElement(import_icons.ZoomOutIcon,null)),import_react7.default.createElement(import_components5.IconButton,{key:"zoomreset",onClick:e3=>{e3.preventDefault(),resetZoom()},title:"Reset zoom"},import_react7.default.createElement(import_icons.ZoomResetIcon,null)))));var ChildrenContainer=import_theming6.styled.div(({isColumn,columns,layout})=>({display:isColumn||!columns?"block":"flex",position:"relative",flexWrap:"wrap",overflow:"auto",flexDirection:isColumn?"column":"row","& .innerZoomElementWrapper > *":isColumn?{width:layout!=="fullscreen"?"calc(100% - 20px)":"100%",display:"block"}:{maxWidth:layout!=="fullscreen"?"calc(100% - 20px)":"100%",display:"inline-block"}}),({layout="padded"})=>layout==="centered"||layout==="padded"?{padding:"30px 20px","& .innerZoomElementWrapper > *":{width:"auto",border:"10px solid transparent!important"}}:{},({layout="padded"})=>layout==="centered"?{display:"flex",justifyContent:"center",justifyItems:"center",alignContent:"center",alignItems:"center"}:{},({columns})=>columns&&columns>1?{".innerZoomElementWrapper > *":{minWidth:`calc(100% / ${columns} - 20px)`}}:{}),StyledSource=(0,import_theming6.styled)(Source)(({theme})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:theme.appBorderRadius,borderBottomRightRadius:theme.appBorderRadius,border:"none",background:theme.base==="light"?"rgba(0, 0, 0, 0.85)":curriedDarken$1(.05,theme.background.content),color:theme.color.lightest,button:{background:theme.base==="light"?"rgba(0, 0, 0, 0.85)":curriedDarken$1(.05,theme.background.content)}})),PreviewContainer=import_theming6.styled.div(({theme,withSource,isExpanded})=>({position:"relative",overflow:"hidden",margin:"25px 0 40px",...getBlockBackgroundStyle(theme),borderBottomLeftRadius:withSource&&isExpanded&&0,borderBottomRightRadius:withSource&&isExpanded&&0,borderBottomWidth:isExpanded&&0,"h3 + &":{marginTop:"16px"}}),({withToolbar})=>withToolbar&&{paddingTop:40}),getSource=(withSource,expanded,setExpanded)=>{switch(!0){case!!(withSource&&withSource.error):return{source:null,actionItem:{title:"No code available",className:"docblock-code-toggle docblock-code-toggle--disabled",disabled:!0,onClick:()=>setExpanded(!1)}};case expanded:return{source:import_react8.default.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:"Hide code",className:"docblock-code-toggle docblock-code-toggle--expanded",onClick:()=>setExpanded(!1)}};default:return{source:import_react8.default.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:"Show code",className:"docblock-code-toggle",onClick:()=>setExpanded(!0)}}}};function getStoryId(children){if(import_react8.Children.count(children)===1){let elt=children;if(elt.props)return elt.props.id}return null}var PositionedToolbar=(0,import_theming6.styled)(Toolbar)({position:"absolute",top:0,left:0,right:0,height:40}),Relative=import_theming6.styled.div({overflow:"hidden",position:"relative"}),Preview=({isLoading,isColumn,columns,children,withSource,withToolbar=!1,isExpanded=!1,additionalActions,className,layout="padded",...props})=>{let[expanded,setExpanded]=(0,import_react8.useState)(isExpanded),{source:source2,actionItem}=getSource(withSource,expanded,setExpanded),[scale,setScale]=(0,import_react8.useState)(1),previewClasses=[className].concat(["sbdocs","sbdocs-preview","sb-unstyled"]),defaultActionItems=withSource?[actionItem]:[],[additionalActionItems,setAdditionalActionItems]=(0,import_react8.useState)(additionalActions?[...additionalActions]:[]),actionItems=[...defaultActionItems,...additionalActionItems],{window:globalWindow4}=globalThis,copyToClipboard=(0,import_react8.useCallback)(async text=>{let{createCopyToClipboardFunction}=await import("storybook/internal/components");createCopyToClipboardFunction()},[]),onCopyCapture=e3=>{let selection=globalWindow4.getSelection();selection&&selection.type==="Range"||(e3.preventDefault(),additionalActionItems.filter(item=>item.title==="Copied").length===0&&copyToClipboard(source2.props.code).then(()=>{setAdditionalActionItems([...additionalActionItems,{title:"Copied",onClick:()=>{}}]),globalWindow4.setTimeout(()=>setAdditionalActionItems(additionalActionItems.filter(item=>item.title!=="Copied")),1500)}))};return import_react8.default.createElement(PreviewContainer,{withSource,withToolbar,...props,className:previewClasses.join(" ")},withToolbar&&import_react8.default.createElement(PositionedToolbar,{isLoading,border:!0,zoom:z3=>setScale(scale*z3),resetZoom:()=>setScale(1),storyId:getStoryId(children),baseUrl:"./iframe.html"}),import_react8.default.createElement(ZoomContext.Provider,{value:{scale}},import_react8.default.createElement(Relative,{className:"docs-story",onCopyCapture:withSource&&onCopyCapture},import_react8.default.createElement(ChildrenContainer,{isColumn:isColumn||!Array.isArray(children),columns,layout},import_react8.default.createElement(import_components6.Zoom.Element,{scale},Array.isArray(children)?children.map((child,i3)=>import_react8.default.createElement("div",{key:i3},child)):import_react8.default.createElement("div",null,children))),import_react8.default.createElement(import_components6.ActionBar,{actionItems}))),withSource&&expanded&&source2)},StyledPreview=(0,import_theming6.styled)(Preview)(()=>({".docs-story":{paddingTop:32,paddingBottom:40}}));var import_react32=__toESM(require("react")),import_client_logger4=require("storybook/internal/client-logger"),import_components19=require("storybook/internal/components"),import_csf=require("storybook/internal/csf"),import_theming24=require("storybook/internal/theming"),import_icons8=require("@storybook/icons");init_compat();var import_react28=__toESM(require("react")),import_components17=require("storybook/internal/components"),import_theming20=require("storybook/internal/theming");var e=__toESM(require("react"),1);function r(){return r=Object.assign?Object.assign.bind():function(e3){for(var r3=1;r3<arguments.length;r3++){var n3=arguments[r3];for(var t3 in n3)Object.prototype.hasOwnProperty.call(n3,t3)&&(e3[t3]=n3[t3])}return e3},r.apply(this,arguments)}var n,t=["children","options"],i={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"};(function(e3){e3[e3.MAX=0]="MAX",e3[e3.HIGH=1]="HIGH",e3[e3.MED=2]="MED",e3[e3.LOW=3]="LOW",e3[e3.MIN=4]="MIN"})(n||(n={}));var a=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce(function(e3,r3){return e3[r3.toLowerCase()]=r3,e3},{class:"className",for:"htmlFor"}),l={amp:"&",apos:"'",gt:">",lt:"<",nbsp:"\xA0",quot:"\u201C"},o=["style","script"],c=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,u=/mailto:/i,s=/\n{2,}$/,d=/^(\s*>[\s\S]*?)(?=\n\n|$)/,f2=/^ *> ?/gm,p=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,h=/^ {2,}\n/,m=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,g=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,y=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,k=/^(`+)\s*([\s\S]*?[^`])\s*\1(?!`)/,v=/^(?:\n *)*\n/,x=/\r\n?/g,b=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,S=/^\[\^([^\]]+)]/,C=/\f/g,E=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,w=/^\s*?\[(x|\s)\]/,z=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,L=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,A=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,T=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,$=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,O=/^<!--[\s\S]*?(?:-->)/,B=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,M=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,R=/^\{.*\}$/,I=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,U=/^<([^ >]+@[^ >]+)>/,D=/^<([^ >]+:\/[^ >]+)>/,N=/-([a-z])?/gi,j=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,H=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,P=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,F=/^\[([^\]]*)\] ?\[([^\]]*)\]/,_=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,G=/\t/g,W=/(^ *\||\| *$)/g,Z=/^ *:-+: *$/,q=/^ *:-+ *$/,Q=/^ *-+: *$/,V="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|~~.*?~~|==.*?==|.|\\n)*?)",X=new RegExp("^([*_])\\1"+V+"\\1\\1(?!\\1)"),J=new RegExp("^([*_])"+V+"\\1(?!\\1|\\w)"),K=new RegExp("^=="+V+"=="),Y=new RegExp("^~~"+V+"~~"),ee=/^\\([^0-9A-Za-z\s])/,re=/^[\s\S]+?(?=[^0-9A-Z\s\u00c0-\uffff&#;.()'"]|\d+\.|\n\n| {2,}\n|\w+:\S|$)/i,ne=/^\n+/,te=/^([ \t]*)/,ie=/\\([^\\])/g,ae=/ *\n+$/,le=/(?:^|\n)( *)$/,oe="(?:\\d+\\.)",ce="(?:[*+-])";function ue(e3){return"( *)("+(e3===1?oe:ce)+") +"}var se=ue(1),de=ue(2);function fe(e3){return new RegExp("^"+(e3===1?se:de))}var pe=fe(1),he=fe(2);function me(e3){return new RegExp("^"+(e3===1?se:de)+"[^\\n]*(?:\\n(?!\\1"+(e3===1?oe:ce)+" )[^\\n]*)*(\\n|$)","gm")}var ge=me(1),ye=me(2);function ke(e3){var r3=e3===1?oe:ce;return new RegExp("^( *)("+r3+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+r3+" (?!"+r3+" ))\\n*|\\s*\\n*$)")}var ve=ke(1),xe=ke(2);function be(e3,r3){var n3=r3===1,t3=n3?ve:xe,a3=n3?ge:ye,l3=n3?pe:he;return{match:function(e4,r4){var n4=le.exec(r4.prevCapture);return n4&&(r4.list||!r4.inline&&!r4.simple)?t3.exec(e4=n4[1]+e4):null},order:1,parse:function(e4,r4,t4){var i3=n3?+e4[2]:void 0,o3=e4[0].replace(s,`
`).match(a3),c3=!1;return{items:o3.map(function(e5,n4){var i4=l3.exec(e5)[0].length,a4=new RegExp("^ {1,"+i4+"}","gm"),u3=e5.replace(a4,"").replace(l3,""),s3=n4===o3.length-1,d3=u3.indexOf(`

`)!==-1||s3&&c3;c3=d3;var f4,p3=t4.inline,h3=t4.list;t4.list=!0,d3?(t4.inline=!1,f4=u3.replace(ae,`

`)):(t4.inline=!0,f4=u3.replace(ae,""));var m3=r4(f4,t4);return t4.inline=p3,t4.list=h3,m3}),ordered:n3,start:i3}},render:function(r4,n4,t4){return e3(r4.ordered?"ol":"ul",{key:t4.key,start:r4.type===i.orderedList?r4.start:void 0},r4.items.map(function(r5,i3){return e3("li",{key:i3},n4(r5,t4))}))}}}var Se=new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`),Ce=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,Ee=[d,g,y,z,A,L,O,j,ge,ve,ye,xe],we=[].concat(Ee,[/^[^\n]+(?:  \n|\n{2,})/,T,M]);function ze(e3){return e3.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function Le(e3){return Q.test(e3)?"right":Z.test(e3)?"center":q.test(e3)?"left":null}function Ae(e3,r3,n3,t3){var a3=n3.inTable;n3.inTable=!0;var l3=e3.trim().split(/( *(?:`[^`]*`|\\\||\|) *)/).reduce(function(e4,a4){return a4.trim()==="|"?e4.push(t3?{type:i.tableSeparator}:{type:i.text,text:a4}):a4!==""&&e4.push.apply(e4,r3(a4,n3)),e4},[]);n3.inTable=a3;var o3=[[]];return l3.forEach(function(e4,r4){e4.type===i.tableSeparator?r4!==0&&r4!==l3.length-1&&o3.push([]):(e4.type!==i.text||l3[r4+1]!=null&&l3[r4+1].type!==i.tableSeparator||(e4.text=e4.text.trimEnd()),o3[o3.length-1].push(e4))}),o3}function Te(e3,r3,n3){n3.inline=!0;var t3=e3[2]?e3[2].replace(W,"").split("|").map(Le):[],a3=e3[3]?function(e4,r4,n4){return e4.trim().split(`
`).map(function(e5){return Ae(e5,r4,n4,!0)})}(e3[3],r3,n3):[],l3=Ae(e3[1],r3,n3,!!a3.length);return n3.inline=!1,a3.length?{align:t3,cells:a3,header:l3,type:i.table}:{children:l3,type:i.paragraph}}function $e(e3,r3){return e3.align[r3]==null?{}:{textAlign:e3.align[r3]}}function Oe(e3){return function(r3,n3){return n3.inline?e3.exec(r3):null}}function Be(e3){return function(r3,n3){return n3.inline||n3.simple?e3.exec(r3):null}}function Me(e3){return function(r3,n3){return n3.inline||n3.simple?null:e3.exec(r3)}}function Re(e3){return function(r3){return e3.exec(r3)}}function Ie(e3,r3){if(r3.inline||r3.simple)return null;var n3="";e3.split(`
`).every(function(e4){return!Ee.some(function(r4){return r4.test(e4)})&&(n3+=e4+`
`,e4.trim())});var t3=n3.trimEnd();return t3==""?null:[n3,t3]}function Ue(e3){try{if(decodeURIComponent(e3).replace(/[^A-Za-z0-9/:]/g,"").match(/^\s*(javascript|vbscript|data(?!:image)):/i))return null}catch{return null}return e3}function De(e3){return e3.replace(ie,"$1")}function Ne(e3,r3,n3){var t3=n3.inline||!1,i3=n3.simple||!1;n3.inline=!0,n3.simple=!0;var a3=e3(r3,n3);return n3.inline=t3,n3.simple=i3,a3}function je(e3,r3,n3){var t3=n3.inline||!1,i3=n3.simple||!1;n3.inline=!1,n3.simple=!0;var a3=e3(r3,n3);return n3.inline=t3,n3.simple=i3,a3}function He(e3,r3,n3){var t3=n3.inline||!1;n3.inline=!1;var i3=e3(r3,n3);return n3.inline=t3,i3}var Pe=function(e3,r3,n3){return{children:Ne(r3,e3[1],n3)}};function Fe(){return{}}function _e(){return null}function Ge(){return[].slice.call(arguments).filter(Boolean).join(" ")}function We(e3,r3,n3){for(var t3=e3,i3=r3.split(".");i3.length&&(t3=t3[i3[0]])!==void 0;)i3.shift();return t3||n3}function Ze(e3,r3){var n3=We(r3,e3);return n3?typeof n3=="function"||typeof n3=="object"&&"render"in n3?n3:We(r3,e3+".component",e3):e3}function qe(n3,t3){var s3;function W3(e3,n4){var i3,a3=We(t3.overrides,e3+".props",{});return(i3=t3).createElement.apply(i3,[Ze(e3,t3.overrides),r({},n4,a3,{className:Ge(n4?.className,a3.className)||void 0})].concat([].slice.call(arguments,2)))}function Z3(e3){e3=e3.replace(E,"");var r3=!1;t3.forceInline?r3=!0:t3.forceBlock||(r3=_.test(e3)===!1);for(var n4=oe2(le3(r3?e3:e3.trimEnd().replace(ne,"")+`

`,{inline:r3}));typeof n4[n4.length-1]=="string"&&!n4[n4.length-1].trim();)n4.pop();if(t3.wrapper===null)return n4;var i3,a3=t3.wrapper||(r3?"span":"div");if(n4.length>1||t3.forceWrapper)i3=n4;else{if(n4.length===1)return typeof(i3=n4[0])=="string"?W3("span",{key:"outer"},i3):i3;i3=null}return t3.createElement(a3,{key:"outer"},i3)}function q3(e3,r3){var n4=r3.match(c);return n4?n4.reduce(function(r4,n5){var i3=n5.indexOf("=");if(i3!==-1){var l3=function(e4){return e4.indexOf("-")!==-1&&e4.match(B)===null&&(e4=e4.replace(N,function(e5,r5){return r5.toUpperCase()})),e4}(n5.slice(0,i3)).trim(),o3=function(e4){var r5=e4[0];return(r5==='"'||r5==="'")&&e4.length>=2&&e4[e4.length-1]===r5?e4.slice(1,-1):e4}(n5.slice(i3+1).trim()),c3=a[l3]||l3;if(c3==="ref")return r4;var u3=r4[c3]=function(e4,r5,n6,t4){return r5==="style"?n6.split(/;\s?/).reduce(function(e5,r6){var n7=r6.slice(0,r6.indexOf(":"));return e5[n7.trim().replace(/(-[a-z])/g,function(e6){return e6[1].toUpperCase()})]=r6.slice(n7.length+1).trim(),e5},{}):r5==="href"||r5==="src"?t4(n6,e4,r5):(n6.match(R)&&(n6=n6.slice(1,n6.length-1)),n6==="true"||n6!=="false"&&n6)}(e3,l3,o3,t3.sanitizer);typeof u3=="string"&&(T.test(u3)||M.test(u3))&&(r4[c3]=Z3(u3.trim()))}else n5!=="style"&&(r4[a[n5]||n5]=!0);return r4},{}):null}n3===void 0&&(n3=""),t3===void 0&&(t3={}),t3.overrides=t3.overrides||{},t3.sanitizer=t3.sanitizer||Ue,t3.slugify=t3.slugify||ze,t3.namedCodesToUnicode=t3.namedCodesToUnicode?r({},l,t3.namedCodesToUnicode):l,t3.createElement=t3.createElement||e.createElement;var Q3=[],V3={},ie2=((s3={})[i.blockQuote]={match:Me(d),order:1,parse:function(e3,r3,n4){var t4=e3[0].replace(f2,"").match(p);return{alert:t4[1],children:r3(t4[2],n4)}},render:function(e3,r3,n4){var a3={key:n4.key};return e3.alert&&(a3.className="markdown-alert-"+t3.slugify(e3.alert.toLowerCase(),ze),e3.children.unshift({attrs:{},children:[{type:i.text,text:e3.alert}],noInnerParse:!0,type:i.htmlBlock,tag:"header"})),W3("blockquote",a3,r3(e3.children,n4))}},s3[i.breakLine]={match:Re(h),order:1,parse:Fe,render:function(e3,r3,n4){return W3("br",{key:n4.key})}},s3[i.breakThematic]={match:Me(m),order:1,parse:Fe,render:function(e3,r3,n4){return W3("hr",{key:n4.key})}},s3[i.codeBlock]={match:Me(y),order:0,parse:function(e3){return{lang:void 0,text:e3[0].replace(/^ {4}/gm,"").replace(/\n+$/,"")}},render:function(e3,n4,t4){return W3("pre",{key:t4.key},W3("code",r({},e3.attrs,{className:e3.lang?"lang-"+e3.lang:""}),e3.text))}},s3[i.codeFenced]={match:Me(g),order:0,parse:function(e3){return{attrs:q3("code",e3[3]||""),lang:e3[2]||void 0,text:e3[4],type:i.codeBlock}}},s3[i.codeInline]={match:Be(k),order:3,parse:function(e3){return{text:e3[2]}},render:function(e3,r3,n4){return W3("code",{key:n4.key},e3.text)}},s3[i.footnote]={match:Me(b),order:0,parse:function(e3){return Q3.push({footnote:e3[2],identifier:e3[1]}),{}},render:_e},s3[i.footnoteReference]={match:Oe(S),order:1,parse:function(e3){return{target:"#"+t3.slugify(e3[1],ze),text:e3[1]}},render:function(e3,r3,n4){return W3("a",{key:n4.key,href:t3.sanitizer(e3.target,"a","href")},W3("sup",{key:n4.key},e3.text))}},s3[i.gfmTask]={match:Oe(w),order:1,parse:function(e3){return{completed:e3[1].toLowerCase()==="x"}},render:function(e3,r3,n4){return W3("input",{checked:e3.completed,key:n4.key,readOnly:!0,type:"checkbox"})}},s3[i.heading]={match:Me(t3.enforceAtxHeadings?L:z),order:1,parse:function(e3,r3,n4){return{children:Ne(r3,e3[2],n4),id:t3.slugify(e3[2],ze),level:e3[1].length}},render:function(e3,r3,n4){return W3("h"+e3.level,{id:e3.id,key:n4.key},r3(e3.children,n4))}},s3[i.headingSetext]={match:Me(A),order:0,parse:function(e3,r3,n4){return{children:Ne(r3,e3[1],n4),level:e3[2]==="="?1:2,type:i.heading}}},s3[i.htmlBlock]={match:Re(T),order:1,parse:function(e3,r3,n4){var t4,i3=e3[3].match(te),a3=new RegExp("^"+i3[1],"gm"),l3=e3[3].replace(a3,""),c3=(t4=l3,we.some(function(e4){return e4.test(t4)})?He:Ne),u3=e3[1].toLowerCase(),s4=o.indexOf(u3)!==-1,d3=(s4?u3:e3[1]).trim(),f4={attrs:q3(d3,e3[2]),noInnerParse:s4,tag:d3};return n4.inAnchor=n4.inAnchor||u3==="a",s4?f4.text=e3[3]:f4.children=c3(r3,l3,n4),n4.inAnchor=!1,f4},render:function(e3,n4,t4){return W3(e3.tag,r({key:t4.key},e3.attrs),e3.text||(e3.children?n4(e3.children,t4):""))}},s3[i.htmlSelfClosing]={match:Re(M),order:1,parse:function(e3){var r3=e3[1].trim();return{attrs:q3(r3,e3[2]||""),tag:r3}},render:function(e3,n4,t4){return W3(e3.tag,r({},e3.attrs,{key:t4.key}))}},s3[i.htmlComment]={match:Re(O),order:1,parse:function(){return{}},render:_e},s3[i.image]={match:Be(Ce),order:1,parse:function(e3){return{alt:e3[1],target:De(e3[2]),title:e3[3]}},render:function(e3,r3,n4){return W3("img",{key:n4.key,alt:e3.alt||void 0,title:e3.title||void 0,src:t3.sanitizer(e3.target,"img","src")})}},s3[i.link]={match:Oe(Se),order:3,parse:function(e3,r3,n4){return{children:je(r3,e3[1],n4),target:De(e3[2]),title:e3[3]}},render:function(e3,r3,n4){return W3("a",{key:n4.key,href:t3.sanitizer(e3.target,"a","href"),title:e3.title},r3(e3.children,n4))}},s3[i.linkAngleBraceStyleDetector]={match:Oe(D),order:0,parse:function(e3){return{children:[{text:e3[1],type:i.text}],target:e3[1],type:i.link}}},s3[i.linkBareUrlDetector]={match:function(e3,r3){return r3.inAnchor||t3.disableAutoLink?null:Oe(I)(e3,r3)},order:0,parse:function(e3){return{children:[{text:e3[1],type:i.text}],target:e3[1],title:void 0,type:i.link}}},s3[i.linkMailtoDetector]={match:Oe(U),order:0,parse:function(e3){var r3=e3[1],n4=e3[1];return u.test(n4)||(n4="mailto:"+n4),{children:[{text:r3.replace("mailto:",""),type:i.text}],target:n4,type:i.link}}},s3[i.orderedList]=be(W3,1),s3[i.unorderedList]=be(W3,2),s3[i.newlineCoalescer]={match:Me(v),order:3,parse:Fe,render:function(){return`
`}},s3[i.paragraph]={match:Ie,order:3,parse:Pe,render:function(e3,r3,n4){return W3("p",{key:n4.key},r3(e3.children,n4))}},s3[i.ref]={match:Oe(H),order:0,parse:function(e3){return V3[e3[1]]={target:e3[2],title:e3[4]},{}},render:_e},s3[i.refImage]={match:Be(P),order:0,parse:function(e3){return{alt:e3[1]||void 0,ref:e3[2]}},render:function(e3,r3,n4){return V3[e3.ref]?W3("img",{key:n4.key,alt:e3.alt,src:t3.sanitizer(V3[e3.ref].target,"img","src"),title:V3[e3.ref].title}):null}},s3[i.refLink]={match:Oe(F),order:0,parse:function(e3,r3,n4){return{children:r3(e3[1],n4),fallbackChildren:e3[0],ref:e3[2]}},render:function(e3,r3,n4){return V3[e3.ref]?W3("a",{key:n4.key,href:t3.sanitizer(V3[e3.ref].target,"a","href"),title:V3[e3.ref].title},r3(e3.children,n4)):W3("span",{key:n4.key},e3.fallbackChildren)}},s3[i.table]={match:Me(j),order:1,parse:Te,render:function(e3,r3,n4){var t4=e3;return W3("table",{key:n4.key},W3("thead",null,W3("tr",null,t4.header.map(function(e4,i3){return W3("th",{key:i3,style:$e(t4,i3)},r3(e4,n4))}))),W3("tbody",null,t4.cells.map(function(e4,i3){return W3("tr",{key:i3},e4.map(function(e5,i4){return W3("td",{key:i4,style:$e(t4,i4)},r3(e5,n4))}))})))}},s3[i.text]={match:Re(re),order:4,parse:function(e3){return{text:e3[0].replace($,function(e4,r3){return t3.namedCodesToUnicode[r3]?t3.namedCodesToUnicode[r3]:e4})}},render:function(e3){return e3.text}},s3[i.textBolded]={match:Be(X),order:2,parse:function(e3,r3,n4){return{children:r3(e3[2],n4)}},render:function(e3,r3,n4){return W3("strong",{key:n4.key},r3(e3.children,n4))}},s3[i.textEmphasized]={match:Be(J),order:3,parse:function(e3,r3,n4){return{children:r3(e3[2],n4)}},render:function(e3,r3,n4){return W3("em",{key:n4.key},r3(e3.children,n4))}},s3[i.textEscaped]={match:Be(ee),order:1,parse:function(e3){return{text:e3[1],type:i.text}}},s3[i.textMarked]={match:Be(K),order:3,parse:Pe,render:function(e3,r3,n4){return W3("mark",{key:n4.key},r3(e3.children,n4))}},s3[i.textStrikethroughed]={match:Be(Y),order:3,parse:Pe,render:function(e3,r3,n4){return W3("del",{key:n4.key},r3(e3.children,n4))}},s3);t3.disableParsingRawHTML===!0&&(delete ie2[i.htmlBlock],delete ie2[i.htmlSelfClosing]);var ae2,le3=function(e3){var r3=Object.keys(e3);function n4(t4,i3){var a3=[];for(i3.prevCapture=i3.prevCapture||"";t4;)for(var l3=0;l3<r3.length;){var o3=r3[l3],c3=e3[o3],u3=c3.match(t4,i3);if(u3){var s4=u3[0];i3.prevCapture+=s4,t4=t4.substring(s4.length);var d3=c3.parse(u3,n4,i3);d3.type==null&&(d3.type=o3),a3.push(d3);break}l3++}return i3.prevCapture="",a3}return r3.sort(function(r4,n5){var t4=e3[r4].order,i3=e3[n5].order;return t4!==i3?t4-i3:r4<n5?-1:1}),function(e4,r4){return n4(function(e5){return e5.replace(x,`
`).replace(C,"").replace(G,"    ")}(e4),r4)}}(ie2),oe2=(ae2=function(e3,r3){return function(n4,t4,i3){var a3=e3[n4.type].render;return r3?r3(function(){return a3(n4,t4,i3)},n4,t4,i3):a3(n4,t4,i3)}}(ie2,t3.renderRule),function e3(r3,n4){if(n4===void 0&&(n4={}),Array.isArray(r3)){for(var t4=n4.key,i3=[],a3=!1,l3=0;l3<r3.length;l3++){n4.key=l3;var o3=e3(r3[l3],n4),c3=typeof o3=="string";c3&&a3?i3[i3.length-1]+=o3:o3!==null&&i3.push(o3),a3=c3}return n4.key=t4,i3}return ae2(r3,e3,n4)}),ce2=Z3(n3);return Q3.length?W3("div",null,ce2,W3("footer",{key:"footer"},Q3.map(function(e3){return W3("div",{id:t3.slugify(e3.identifier,ze),key:e3.identifier},e3.identifier,oe2(le3(e3.footnote,{inline:!0})))}))):ce2}function index_module_default(r3){var n3=r3.children,i3=n3===void 0?"":n3,a3=r3.options,l3=function(e3,r4){if(e3==null)return{};var n4,t3,i4={},a4=Object.keys(e3);for(t3=0;t3<a4.length;t3++)r4.indexOf(n4=a4[t3])>=0||(i4[n4]=e3[n4]);return i4}(r3,t);return e.cloneElement(qe(i3,a3),l3)}var import_react25=__toESM(require("react")),import_components14=require("storybook/internal/components");var import_react24=__toESM(require("react"));var import_react9=__toESM(require("react")),import_components7=require("storybook/internal/components"),import_theming7=require("storybook/internal/theming");init_helpers();var Label=import_theming7.styled.label(({theme})=>({lineHeight:"18px",alignItems:"center",marginBottom:8,display:"inline-block",position:"relative",whiteSpace:"nowrap",background:theme.boolean.background,borderRadius:"3em",padding:1,'&[aria-disabled="true"]':{opacity:.5,input:{cursor:"not-allowed"}},input:{appearance:"none",width:"100%",height:"100%",position:"absolute",left:0,top:0,margin:0,padding:0,border:"none",background:"transparent",cursor:"pointer",borderRadius:"3em","&:focus":{outline:"none",boxShadow:`${theme.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:"center",fontSize:theme.typography.size.s1,fontWeight:theme.typography.weight.bold,lineHeight:"1",cursor:"pointer",display:"inline-block",padding:"7px 15px",transition:"all 100ms ease-out",userSelect:"none",borderRadius:"3em",color:curriedTransparentize$1(.5,theme.color.defaultText),background:"transparent","&:hover":{boxShadow:`${curriedOpacify$1(.3,theme.appBorderColor)} 0 0 0 1px inset`},"&:active":{boxShadow:`${curriedOpacify$1(.05,theme.appBorderColor)} 0 0 0 2px inset`,color:curriedOpacify$1(1,theme.appBorderColor)},"&:first-of-type":{paddingRight:8},"&:last-of-type":{paddingLeft:8}},"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type":{background:theme.boolean.selectedBackground,boxShadow:theme.base==="light"?`${curriedOpacify$1(.1,theme.appBorderColor)} 0 0 2px`:`${theme.appBorderColor} 0 0 0 1px`,color:theme.color.defaultText,padding:"7px 15px"}})),parse=value3=>value3==="true",BooleanControl=({name:name2,value:value3,onChange,onBlur,onFocus,argType})=>{let onSetFalse=(0,import_react9.useCallback)(()=>onChange(!1),[onChange]),readonly=!!argType?.table?.readonly;if(value3===void 0)return import_react9.default.createElement(import_components7.Button,{variant:"outline",size:"medium",id:getControlSetterButtonId(name2),onClick:onSetFalse,disabled:readonly},"Set boolean");let controlId=getControlId(name2),parsedValue=typeof value3=="string"?parse(value3):value3;return import_react9.default.createElement(Label,{"aria-disabled":readonly,htmlFor:controlId,"aria-label":name2},import_react9.default.createElement("input",{id:controlId,type:"checkbox",onChange:e3=>onChange(e3.target.checked),checked:parsedValue,role:"switch",disabled:readonly,name:name2,onBlur,onFocus}),import_react9.default.createElement("span",{"aria-hidden":"true"},"False"),import_react9.default.createElement("span",{"aria-hidden":"true"},"True"))};var import_react10=__toESM(require("react")),import_components8=require("storybook/internal/components"),import_theming8=require("storybook/internal/theming");init_helpers();var parseDate=value3=>{let[year,month,day]=value3.split("-"),result2=new Date;return result2.setFullYear(parseInt(year,10),parseInt(month,10)-1,parseInt(day,10)),result2},parseTime=value3=>{let[hours,minutes]=value3.split(":"),result2=new Date;return result2.setHours(parseInt(hours,10)),result2.setMinutes(parseInt(minutes,10)),result2},formatDate=value3=>{let date=new Date(value3),year=`000${date.getFullYear()}`.slice(-4),month=`0${date.getMonth()+1}`.slice(-2),day=`0${date.getDate()}`.slice(-2);return`${year}-${month}-${day}`},formatTime=value3=>{let date=new Date(value3),hours=`0${date.getHours()}`.slice(-2),minutes=`0${date.getMinutes()}`.slice(-2);return`${hours}:${minutes}`},FormInput=(0,import_theming8.styled)(import_components8.Form.Input)(({readOnly})=>({opacity:readOnly?.5:1})),FlexSpaced=import_theming8.styled.div(({theme})=>({flex:1,display:"flex",input:{marginLeft:10,flex:1,height:32,"&::-webkit-calendar-picker-indicator":{opacity:.5,height:12,filter:theme.base==="light"?void 0:"invert(1)"}},"input:first-of-type":{marginLeft:0,flexGrow:4},"input:last-of-type":{flexGrow:3}})),DateControl=({name:name2,value:value3,onChange,onFocus,onBlur,argType})=>{let[valid,setValid]=(0,import_react10.useState)(!0),dateRef=(0,import_react10.useRef)(),timeRef=(0,import_react10.useRef)(),readonly=!!argType?.table?.readonly;(0,import_react10.useEffect)(()=>{valid!==!1&&(dateRef&&dateRef.current&&(dateRef.current.value=value3?formatDate(value3):""),timeRef&&timeRef.current&&(timeRef.current.value=value3?formatTime(value3):""))},[value3]);let onDateChange=e3=>{if(!e3.target.value)return onChange();let parsed=parseDate(e3.target.value),result2=new Date(value3);result2.setFullYear(parsed.getFullYear(),parsed.getMonth(),parsed.getDate());let time=result2.getTime();time&&onChange(time),setValid(!!time)},onTimeChange=e3=>{if(!e3.target.value)return onChange();let parsed=parseTime(e3.target.value),result2=new Date(value3);result2.setHours(parsed.getHours()),result2.setMinutes(parsed.getMinutes());let time=result2.getTime();time&&onChange(time),setValid(!!time)},controlId=getControlId(name2);return import_react10.default.createElement(FlexSpaced,null,import_react10.default.createElement(FormInput,{type:"date",max:"9999-12-31",ref:dateRef,id:`${controlId}-date`,name:`${controlId}-date`,readOnly:readonly,onChange:onDateChange,onFocus,onBlur}),import_react10.default.createElement(FormInput,{type:"time",id:`${controlId}-time`,name:`${controlId}-time`,ref:timeRef,onChange:onTimeChange,readOnly:readonly,onFocus,onBlur}),valid?null:import_react10.default.createElement("div",null,"invalid"))};var import_react11=__toESM(require("react")),import_components9=require("storybook/internal/components"),import_theming9=require("storybook/internal/theming");init_helpers();var Wrapper3=import_theming9.styled.label({display:"flex"}),parse2=value3=>{let result2=parseFloat(value3);return Number.isNaN(result2)?void 0:result2},format2=value3=>value3!=null?String(value3):"",FormInput2=(0,import_theming9.styled)(import_components9.Form.Input)(({readOnly})=>({opacity:readOnly?.5:1})),NumberControl=({name:name2,value:value3,onChange,min,max,step,onBlur,onFocus,argType})=>{let[inputValue,setInputValue]=(0,import_react11.useState)(typeof value3=="number"?value3:""),[forceVisible,setForceVisible]=(0,import_react11.useState)(!1),[parseError,setParseError]=(0,import_react11.useState)(null),readonly=!!argType?.table?.readonly,handleChange=(0,import_react11.useCallback)(event=>{setInputValue(event.target.value);let result2=parseFloat(event.target.value);Number.isNaN(result2)?setParseError(new Error(`'${event.target.value}' is not a number`)):(onChange(result2),setParseError(null))},[onChange,setParseError]),onForceVisible=(0,import_react11.useCallback)(()=>{setInputValue("0"),onChange(0),setForceVisible(!0)},[setForceVisible]),htmlElRef=(0,import_react11.useRef)(null);return(0,import_react11.useEffect)(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select()},[forceVisible]),(0,import_react11.useEffect)(()=>{inputValue!==(typeof value3=="number"?value3:"")&&setInputValue(value3)},[value3]),value3===void 0?import_react11.default.createElement(import_components9.Button,{variant:"outline",size:"medium",id:getControlSetterButtonId(name2),onClick:onForceVisible,disabled:readonly},"Set number"):import_react11.default.createElement(Wrapper3,null,import_react11.default.createElement(FormInput2,{ref:htmlElRef,id:getControlId(name2),type:"number",onChange:handleChange,size:"flex",placeholder:"Edit number...",value:inputValue,valid:parseError?"error":null,autoFocus:forceVisible,readOnly:readonly,name:name2,min,max,step,onFocus,onBlur}))};var import_react15=__toESM(require("react"));var import_react12=__toESM(require("react")),import_client_logger=require("storybook/internal/client-logger"),import_theming10=require("storybook/internal/theming");init_helpers();var selectedKey=(value3,options2)=>{let entry=options2&&Object.entries(options2).find(([_key,val])=>val===value3);return entry?entry[0]:void 0},selectedKeys=(value3,options2)=>value3&&options2?Object.entries(options2).filter(entry=>value3.includes(entry[1])).map(entry=>entry[0]):[],selectedValues=(keys,options2)=>keys&&options2&&keys.map(key2=>options2[key2]);var Wrapper4=import_theming10.styled.div(({isInline})=>isInline?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},props=>{if(props["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),Text=import_theming10.styled.span({"[aria-readonly=true] &":{opacity:.5}}),Label2=import_theming10.styled.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),CheckboxControl=({name:name2,options:options2,value:value3,onChange,isInline,argType})=>{if(!options2)return import_client_logger.logger.warn(`Checkbox with no options: ${name2}`),import_react12.default.createElement(import_react12.default.Fragment,null,"-");let initial=selectedKeys(value3,options2),[selected,setSelected]=(0,import_react12.useState)(initial),readonly=!!argType?.table?.readonly,handleChange=e3=>{let option=e3.target.value,updated=[...selected];updated.includes(option)?updated.splice(updated.indexOf(option),1):updated.push(option),onChange(selectedValues(updated,options2)),setSelected(updated)};(0,import_react12.useEffect)(()=>{setSelected(selectedKeys(value3,options2))},[value3]);let controlId=getControlId(name2);return import_react12.default.createElement(Wrapper4,{"aria-readonly":readonly,isInline},Object.keys(options2).map((key2,index)=>{let id2=`${controlId}-${index}`;return import_react12.default.createElement(Label2,{key:id2,htmlFor:id2},import_react12.default.createElement("input",{type:"checkbox",disabled:readonly,id:id2,name:id2,value:key2,onChange:handleChange,checked:selected?.includes(key2)}),import_react12.default.createElement(Text,null,key2))}))};var import_react13=__toESM(require("react")),import_client_logger2=require("storybook/internal/client-logger"),import_theming11=require("storybook/internal/theming");init_helpers();var Wrapper5=import_theming11.styled.div(({isInline})=>isInline?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},props=>{if(props["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),Text2=import_theming11.styled.span({"[aria-readonly=true] &":{opacity:.5}}),Label3=import_theming11.styled.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),RadioControl=({name:name2,options:options2,value:value3,onChange,isInline,argType})=>{if(!options2)return import_client_logger2.logger.warn(`Radio with no options: ${name2}`),import_react13.default.createElement(import_react13.default.Fragment,null,"-");let selection=selectedKey(value3,options2),controlId=getControlId(name2),readonly=!!argType?.table?.readonly;return import_react13.default.createElement(Wrapper5,{"aria-readonly":readonly,isInline},Object.keys(options2).map((key2,index)=>{let id2=`${controlId}-${index}`;return import_react13.default.createElement(Label3,{key:id2,htmlFor:id2},import_react13.default.createElement("input",{type:"radio",id:id2,name:controlId,disabled:readonly,value:key2,onChange:e3=>onChange(options2[e3.currentTarget.value]),checked:key2===selection}),import_react13.default.createElement(Text2,null,key2))}))};var import_react14=__toESM(require("react")),import_client_logger3=require("storybook/internal/client-logger"),import_theming12=require("storybook/internal/theming"),import_icons2=require("@storybook/icons");init_helpers();var styleResets={appearance:"none",border:"0 none",boxSizing:"inherit",display:" block",margin:" 0",background:"transparent",padding:0,fontSize:"inherit",position:"relative"},OptionsSelect=import_theming12.styled.select(styleResets,({theme})=>({boxSizing:"border-box",position:"relative",padding:"6px 10px",width:"100%",color:theme.input.color||"inherit",background:theme.input.background,borderRadius:theme.input.borderRadius,boxShadow:`${theme.input.border} 0 0 0 1px inset`,fontSize:theme.typography.size.s2-1,lineHeight:"20px","&:focus":{boxShadow:`${theme.color.secondary} 0 0 0 1px inset`,outline:"none"},"&[disabled]":{cursor:"not-allowed",opacity:.5},"::placeholder":{color:theme.textMutedColor},"&[multiple]":{overflow:"auto",padding:0,option:{display:"block",padding:"6px 10px",marginLeft:1,marginRight:1}}})),SelectWrapper=import_theming12.styled.span(({theme})=>({display:"inline-block",lineHeight:"normal",overflow:"hidden",position:"relative",verticalAlign:"top",width:"100%",svg:{position:"absolute",zIndex:1,pointerEvents:"none",height:"12px",marginTop:"-6px",right:"12px",top:"50%",fill:theme.textMutedColor,path:{fill:theme.textMutedColor}}})),NO_SELECTION="Choose option...",SingleSelect=({name:name2,value:value3,options:options2,onChange,argType})=>{let handleChange=e3=>{onChange(options2[e3.currentTarget.value])},selection=selectedKey(value3,options2)||NO_SELECTION,controlId=getControlId(name2),readonly=!!argType?.table?.readonly;return import_react14.default.createElement(SelectWrapper,null,import_react14.default.createElement(import_icons2.ChevronSmallDownIcon,null),import_react14.default.createElement(OptionsSelect,{disabled:readonly,id:controlId,value:selection,onChange:handleChange},import_react14.default.createElement("option",{key:"no-selection",disabled:!0},NO_SELECTION),Object.keys(options2).map(key2=>import_react14.default.createElement("option",{key:key2,value:key2},key2))))},MultiSelect=({name:name2,value:value3,options:options2,onChange,argType})=>{let handleChange=e3=>{let selection2=Array.from(e3.currentTarget.options).filter(option=>option.selected).map(option=>option.value);onChange(selectedValues(selection2,options2))},selection=selectedKeys(value3,options2),controlId=getControlId(name2),readonly=!!argType?.table?.readonly;return import_react14.default.createElement(SelectWrapper,null,import_react14.default.createElement(OptionsSelect,{disabled:readonly,id:controlId,multiple:!0,value:selection,onChange:handleChange},Object.keys(options2).map(key2=>import_react14.default.createElement("option",{key:key2,value:key2},key2))))},SelectControl=props=>{let{name:name2,options:options2}=props;return options2?props.isMulti?import_react14.default.createElement(MultiSelect,{...props}):import_react14.default.createElement(SingleSelect,{...props}):(import_client_logger3.logger.warn(`Select with no options: ${name2}`),import_react14.default.createElement(import_react14.default.Fragment,null,"-"))};var normalizeOptions=(options2,labels)=>Array.isArray(options2)?options2.reduce((acc,item)=>(acc[labels?.[item]||String(item)]=item,acc),{}):options2,Controls={check:CheckboxControl,"inline-check":CheckboxControl,radio:RadioControl,"inline-radio":RadioControl,select:SelectControl,"multi-select":SelectControl},OptionsControl=props=>{let{type="select",labels,argType}=props,normalized={...props,argType,options:argType?normalizeOptions(argType.options,labels):{},isInline:type.includes("inline"),isMulti:type.includes("multi")},Control=Controls[type];if(Control)return import_react15.default.createElement(Control,{...normalized});throw new Error(`Unknown options type: ${type}`)};var import_react18=__toESM(require("react")),import_components10=require("storybook/internal/components"),import_theming13=require("storybook/internal/theming"),import_icons3=require("@storybook/icons");init_compat();init_helpers();var import_react17=__toESM(require("react"));var import_react16=__toESM(require("react"));var ERROR="Error",OBJECT="Object",ARRAY="Array",STRING="String",NUMBER="Number",BOOLEAN="Boolean",DATE="Date",NULL="Null",UNDEFINED="Undefined",FUNCTION="Function",SYMBOL="Symbol";var ADD_DELTA_TYPE="ADD_DELTA_TYPE",REMOVE_DELTA_TYPE="REMOVE_DELTA_TYPE",UPDATE_DELTA_TYPE="UPDATE_DELTA_TYPE";var VALUE="value",KEY="key";function getObjectType(obj){return obj!==null&&typeof obj=="object"&&!Array.isArray(obj)&&typeof obj[Symbol.iterator]=="function"?"Iterable":Object.prototype.toString.call(obj).slice(8,-1)}function isComponentWillChange(oldValue,newValue){let oldType=getObjectType(oldValue),newType=getObjectType(newValue);return(oldType==="Function"||newType==="Function")&&newType!==oldType}var JsonAddValue=class extends import_react16.Component{constructor(props){super(props),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this)}componentDidMount(){let{inputRefKey,inputRefValue}=this.state,{onlyValue}=this.props;inputRefKey&&typeof inputRefKey.focus=="function"&&inputRefKey.focus(),onlyValue&&inputRefValue&&typeof inputRefValue.focus=="function"&&inputRefValue.focus(),document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.onSubmit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.props.handleCancel()))}onSubmit(){let{handleAdd,onlyValue,onSubmitValueParser,keyPath,deep}=this.props,{inputRefKey,inputRefValue}=this.state,result2={};if(!onlyValue){if(!inputRefKey.value)return;result2.key=inputRefKey.value}result2.newValue=onSubmitValueParser(!1,keyPath,deep,result2.key,inputRefValue.value),handleAdd(result2)}refInputKey(node){this.state.inputRefKey=node}refInputValue(node){this.state.inputRefValue=node}render(){let{handleCancel,onlyValue,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep}=this.props,addButtonElementLayout=(0,import_react16.cloneElement)(addButtonElement,{onClick:this.onSubmit}),cancelButtonElementLayout=(0,import_react16.cloneElement)(cancelButtonElement,{onClick:handleCancel}),inputElementValue=inputElementGenerator(VALUE,keyPath,deep),inputElementValueLayout=(0,import_react16.cloneElement)(inputElementValue,{placeholder:"Value",ref:this.refInputValue}),inputElementKeyLayout=null;if(!onlyValue){let inputElementKey=inputElementGenerator(KEY,keyPath,deep);inputElementKeyLayout=(0,import_react16.cloneElement)(inputElementKey,{placeholder:"Key",ref:this.refInputKey})}return import_react16.default.createElement("span",{className:"rejt-add-value-node"},inputElementKeyLayout,inputElementValueLayout,cancelButtonElementLayout,addButtonElementLayout)}};JsonAddValue.defaultProps={onlyValue:!1,addButtonElement:import_react16.default.createElement("button",null,"+"),cancelButtonElement:import_react16.default.createElement("button",null,"c")};var JsonArray=class extends import_react16.Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={data:props.data,name:props.name,keyPath,deep:props.deep,nextDeep:props.deep+1,collapsed:props.isCollapsed(keyPath,props.deep,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data)}handleAddMode(){this.setState({addFormVisible:!0})}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}))}handleRemoveItem(index){return()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[index];beforeRemoveAction(index,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key:index,oldValue,type:REMOVE_DELTA_TYPE};data.splice(index,1),this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult)}).catch(logger4.error)}}handleAddValueAdd({newValue}){let{data,keyPath,nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;beforeAddAction(data.length,keyPath,deep,newValue).then(()=>{let newData=[...data,newValue];this.setState({data:newData}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],newData),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key:newData.length-1,newValue})}).catch(logger4.error)}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleEditValue({key:key2,value:value3}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key2];beforeUpdateAction(key2,keyPath,deep,oldValue,value3).then(()=>{data[key2]=value3,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key:key2,newValue:value3,oldValue}),resolve(void 0)}).catch(reject)})}renderCollapsed(){let{name:name2,data,keyPath,deep}=this.state,{handleRemove,readOnly,getStyle,dataType,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name2,data,keyPath,deep,dataType),isReadOnly=readOnly(name2,data,keyPath,deep,dataType),removeItemButton=(0,import_react16.cloneElement)(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return import_react16.default.createElement("span",{className:"rejt-collapsed"},import_react16.default.createElement("span",{className:"rejt-collapsed-text",style:collapsed,onClick:this.handleCollapseMode},"[...] ",data.length," ",data.length===1?"item":"items"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name:name2,data,keyPath,deep,addFormVisible,nextDeep}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,delimiter,ul,addForm}=getStyle(name2,data,keyPath,deep,dataType),isReadOnly=readOnly(name2,data,keyPath,deep,dataType),addItemButton=(0,import_react16.cloneElement)(plusMenuElement,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:plus}),removeItemButton=(0,import_react16.cloneElement)(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return import_react16.default.createElement("span",{className:"rejt-not-collapsed"},import_react16.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"["),!addFormVisible&&addItemButton,import_react16.default.createElement("ul",{className:"rejt-not-collapsed-list",style:ul},data.map((item,index)=>import_react16.default.createElement(JsonNode,{key:index,name:index.toString(),data:item,keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveItem(index),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}))),!isReadOnly&&addFormVisible&&import_react16.default.createElement("div",{className:"rejt-add-form",style:addForm},import_react16.default.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue:!0,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),import_react16.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"]"),!isReadOnly&&removeItemButton)}render(){let{name:name2,collapsed,data,keyPath,deep}=this.state,{dataType,getStyle}=this.props,value3=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name2,data,keyPath,deep,dataType);return import_react16.default.createElement("div",{className:"rejt-array-node"},import_react16.default.createElement("span",{onClick:this.handleCollapseMode},import_react16.default.createElement("span",{className:"rejt-name",style:style.name},name2," :"," ")),value3)}};JsonArray.defaultProps={keyPath:[],deep:0,minusMenuElement:import_react16.default.createElement("span",null," - "),plusMenuElement:import_react16.default.createElement("span",null," + ")};var JsonFunctionValue=class extends import_react16.Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name:name2,value:value3,keyPath,deep}=this.state,{readOnly,dataType}=this.props,readOnlyResult=readOnly(name2,value3,keyPath,deep,dataType);editEnabled&&!readOnlyResult&&typeof inputRef.focus=="function"&&inputRef.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.handleEdit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name:name2,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name2,inputRef.value);handleUpdateValue({value:newValue,key:name2}).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit()}).catch(logger4.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(node){this.state.inputRef=node}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:name2,value:value3,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,textareaElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name2,originalValue,keyPath,deep,dataType),result2=null,minusElement=null,resultOnlyResult=readOnly(name2,originalValue,keyPath,deep,dataType);if(editEnabled&&!resultOnlyResult){let textareaElement=textareaElementGenerator(VALUE,comeFromKeyPath,deep,name2,originalValue,dataType),editButtonElementLayout=(0,import_react16.cloneElement)(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=(0,import_react16.cloneElement)(cancelButtonElement,{onClick:this.handleCancelEdit}),textareaElementLayout=(0,import_react16.cloneElement)(textareaElement,{ref:this.refInput,defaultValue:originalValue});result2=import_react16.default.createElement("span",{className:"rejt-edit-form",style:style.editForm},textareaElementLayout," ",cancelButtonElementLayout,editButtonElementLayout),minusElement=null}else{result2=import_react16.default.createElement("span",{className:"rejt-value",style:style.value,onClick:resultOnlyResult?null:this.handleEditMode},value3);let minusMenuLayout=(0,import_react16.cloneElement)(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:style.minus});minusElement=resultOnlyResult?null:minusMenuLayout}return import_react16.default.createElement("li",{className:"rejt-function-value-node",style:style.li},import_react16.default.createElement("span",{className:"rejt-name",style:style.name},name2," :"," "),result2,minusElement)}};JsonFunctionValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>{},editButtonElement:import_react16.default.createElement("button",null,"e"),cancelButtonElement:import_react16.default.createElement("button",null,"c"),minusMenuElement:import_react16.default.createElement("span",null," - ")};var JsonNode=class extends import_react16.Component{constructor(props){super(props),this.state={data:props.data,name:props.name,keyPath:props.keyPath,deep:props.deep}}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}render(){let{data,name:name2,keyPath,deep}=this.state,{isCollapsed,handleRemove,handleUpdateValue,onUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,readOnlyTrue=()=>!0,dataType=getObjectType(data);switch(dataType){case ERROR:return import_react16.default.createElement(JsonObject,{data,name:name2,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly:readOnlyTrue,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case OBJECT:return import_react16.default.createElement(JsonObject,{data,name:name2,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case ARRAY:return import_react16.default.createElement(JsonArray,{data,name:name2,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case STRING:return import_react16.default.createElement(JsonValue,{name:name2,value:`"${data}"`,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NUMBER:return import_react16.default.createElement(JsonValue,{name:name2,value:data,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case BOOLEAN:return import_react16.default.createElement(JsonValue,{name:name2,value:data?"true":"false",originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case DATE:return import_react16.default.createElement(JsonValue,{name:name2,value:data.toISOString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NULL:return import_react16.default.createElement(JsonValue,{name:name2,value:"null",originalValue:"null",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case UNDEFINED:return import_react16.default.createElement(JsonValue,{name:name2,value:"undefined",originalValue:"undefined",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case FUNCTION:return import_react16.default.createElement(JsonFunctionValue,{name:name2,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,textareaElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case SYMBOL:return import_react16.default.createElement(JsonValue,{name:name2,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});default:return null}}};JsonNode.defaultProps={keyPath:[],deep:0};var JsonObject=class extends import_react16.Component{constructor(props){super(props);let keyPath=props.deep===-1?[]:[...props.keyPath,props.name];this.state={name:props.name,data:props.data,keyPath,deep:props.deep,nextDeep:props.deep+1,collapsed:props.isCollapsed(keyPath,props.deep,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data)}handleAddMode(){this.setState({addFormVisible:!0})}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleAddValueAdd({key:key2,newValue}){let{data,keyPath,nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;beforeAddAction(key2,keyPath,deep,newValue).then(()=>{data[key2]=newValue,this.setState({data}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key:key2,newValue})}).catch(logger4.error)}handleRemoveValue(key2){return()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key2];beforeRemoveAction(key2,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key:key2,oldValue,type:REMOVE_DELTA_TYPE};delete data[key2],this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult)}).catch(logger4.error)}}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}))}handleEditValue({key:key2,value:value3}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key2];beforeUpdateAction(key2,keyPath,deep,oldValue,value3).then(()=>{data[key2]=value3,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key:key2,newValue:value3,oldValue}),resolve()}).catch(reject)})}renderCollapsed(){let{name:name2,keyPath,deep,data}=this.state,{handleRemove,readOnly,dataType,getStyle,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name2,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name2,data,keyPath,deep,dataType),removeItemButton=(0,import_react16.cloneElement)(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus});return import_react16.default.createElement("span",{className:"rejt-collapsed"},import_react16.default.createElement("span",{className:"rejt-collapsed-text",style:collapsed,onClick:this.handleCollapseMode},"{...}"," ",keyList.length," ",keyList.length===1?"key":"keys"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name:name2,data,keyPath,deep,nextDeep,addFormVisible}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,addForm,ul,delimiter}=getStyle(name2,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name2,data,keyPath,deep,dataType),addItemButton=(0,import_react16.cloneElement)(plusMenuElement,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:plus}),removeItemButton=(0,import_react16.cloneElement)(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:minus}),list=keyList.map(key2=>import_react16.default.createElement(JsonNode,{key:key2,name:key2,data:data[key2],keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveValue(key2),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}));return import_react16.default.createElement("span",{className:"rejt-not-collapsed"},import_react16.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"{"),!isReadOnly&&addItemButton,import_react16.default.createElement("ul",{className:"rejt-not-collapsed-list",style:ul},list),!isReadOnly&&addFormVisible&&import_react16.default.createElement("div",{className:"rejt-add-form",style:addForm},import_react16.default.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),import_react16.default.createElement("span",{className:"rejt-not-collapsed-delimiter",style:delimiter},"}"),!isReadOnly&&removeItemButton)}render(){let{name:name2,collapsed,data,keyPath,deep}=this.state,{getStyle,dataType}=this.props,value3=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name2,data,keyPath,deep,dataType);return import_react16.default.createElement("div",{className:"rejt-object-node"},import_react16.default.createElement("span",{onClick:this.handleCollapseMode},import_react16.default.createElement("span",{className:"rejt-name",style:style.name},name2," :"," ")),value3)}};JsonObject.defaultProps={keyPath:[],deep:0,minusMenuElement:import_react16.default.createElement("span",null," - "),plusMenuElement:import_react16.default.createElement("span",null," + ")};var JsonValue=class extends import_react16.Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name:name2,value:value3,keyPath,deep}=this.state,{readOnly,dataType}=this.props,isReadOnly=readOnly(name2,value3,keyPath,deep,dataType);editEnabled&&!isReadOnly&&typeof inputRef.focus=="function"&&inputRef.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code==="Enter"||event.key==="Enter")&&(event.preventDefault(),this.handleEdit()),(event.code==="Escape"||event.key==="Escape")&&(event.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name:name2,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name2,inputRef.value);handleUpdateValue({value:newValue,key:name2}).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit()}).catch(logger4.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(node){this.state.inputRef=node}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:name2,value:value3,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,inputElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name2,originalValue,keyPath,deep,dataType),isReadOnly=readOnly(name2,originalValue,keyPath,deep,dataType),isEditing=editEnabled&&!isReadOnly,inputElement=inputElementGenerator(VALUE,comeFromKeyPath,deep,name2,originalValue,dataType),editButtonElementLayout=(0,import_react16.cloneElement)(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=(0,import_react16.cloneElement)(cancelButtonElement,{onClick:this.handleCancelEdit}),inputElementLayout=(0,import_react16.cloneElement)(inputElement,{ref:this.refInput,defaultValue:JSON.stringify(originalValue)}),minusMenuLayout=(0,import_react16.cloneElement)(minusMenuElement,{onClick:handleRemove,className:"rejt-minus-menu",style:style.minus});return import_react16.default.createElement("li",{className:"rejt-value-node",style:style.li},import_react16.default.createElement("span",{className:"rejt-name",style:style.name},name2," : "),isEditing?import_react16.default.createElement("span",{className:"rejt-edit-form",style:style.editForm},inputElementLayout," ",cancelButtonElementLayout,editButtonElementLayout):import_react16.default.createElement("span",{className:"rejt-value",style:style.value,onClick:isReadOnly?null:this.handleEditMode},String(value3)),!isReadOnly&&!isEditing&&minusMenuLayout)}};JsonValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>Promise.resolve(),editButtonElement:import_react16.default.createElement("button",null,"e"),cancelButtonElement:import_react16.default.createElement("button",null,"c"),minusMenuElement:import_react16.default.createElement("span",null," - ")};function parse3(string){let result2=string;if(result2.indexOf("function")===0)return(0,eval)(`(${result2})`);try{result2=JSON.parse(string)}catch{}return result2}var object={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},array={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},value2={minus:{color:"red"},editForm:{},value:{color:"#7bba3d"},li:{minHeight:"22px",lineHeight:"22px",outline:"0px"},name:{color:"#2287CD"}};var JsonTree=class extends import_react17.Component{constructor(props){super(props),this.state={data:props.data,rootName:props.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this)}static getDerivedStateFromProps(props,state){return props.data!==state.data||props.rootName!==state.rootName?{data:props.data,rootName:props.rootName}:null}onUpdate(key2,data){this.setState({data}),this.props.onFullyUpdate(data)}removeRoot(){this.onUpdate(null,null)}render(){let{data,rootName}=this.state,{isCollapsed,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElement,textareaElement,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser,fallback=null}=this.props,dataType=getObjectType(data),readOnlyFunction=readOnly;getObjectType(readOnly)==="Boolean"&&(readOnlyFunction=()=>readOnly);let inputElementFunction=inputElement;inputElement&&getObjectType(inputElement)!=="Function"&&(inputElementFunction=()=>inputElement);let textareaElementFunction=textareaElement;return textareaElement&&getObjectType(textareaElement)!=="Function"&&(textareaElementFunction=()=>textareaElement),dataType==="Object"||dataType==="Array"?import_react17.default.createElement("div",{className:"rejt-tree"},import_react17.default.createElement(JsonNode,{data,name:rootName,deep:-1,isCollapsed,onUpdate:this.onUpdate,onDeltaUpdate,readOnly:readOnlyFunction,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator:inputElementFunction,textareaElementGenerator:textareaElementFunction,minusMenuElement,plusMenuElement,handleRemove:this.removeRoot,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser})):fallback}};JsonTree.defaultProps={rootName:"root",isCollapsed:(keyPath,deep)=>deep!==-1,getStyle:(keyName,data,keyPath,deep,dataType)=>{switch(dataType){case"Object":case"Error":return object;case"Array":return array;default:return value2}},readOnly:()=>!1,onFullyUpdate:()=>{},onDeltaUpdate:()=>{},beforeRemoveAction:()=>Promise.resolve(),beforeAddAction:()=>Promise.resolve(),beforeUpdateAction:()=>Promise.resolve(),logger:{error:()=>{}},onSubmitValueParser:(isEditMode,keyPath,deep,name2,rawValue)=>parse3(rawValue),inputElement:()=>import_react17.default.createElement("input",null),textareaElement:()=>import_react17.default.createElement("textarea",null),fallback:null};var{window:globalWindow2}=globalThis,Wrapper6=import_theming13.styled.div(({theme})=>({position:"relative",display:"flex",'&[aria-readonly="true"]':{opacity:.5},".rejt-tree":{marginLeft:"1rem",fontSize:"13px"},".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed":{"& > svg":{opacity:0,transition:"opacity 0.2s"}},".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed":{"& > svg":{opacity:1}},".rejt-edit-form button":{display:"none"},".rejt-add-form":{marginLeft:10},".rejt-add-value-node":{display:"inline-flex",alignItems:"center"},".rejt-name":{lineHeight:"22px"},".rejt-not-collapsed-delimiter":{lineHeight:"22px"},".rejt-plus-menu":{marginLeft:5},".rejt-object-node > span > *, .rejt-array-node > span > *":{position:"relative",zIndex:2},".rejt-object-node, .rejt-array-node":{position:"relative"},".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before":{content:'""',position:"absolute",top:0,display:"block",width:"100%",marginLeft:"-1rem",padding:"0 4px 0 1rem",height:22},".rejt-collapsed::before, .rejt-not-collapsed::before":{zIndex:1,background:"transparent",borderRadius:4,transition:"background 0.2s",pointerEvents:"none",opacity:.1},".rejt-object-node:hover, .rejt-array-node:hover":{"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before":{background:theme.color.secondary}},".rejt-collapsed::after, .rejt-not-collapsed::after":{content:'""',position:"absolute",display:"inline-block",pointerEvents:"none",width:0,height:0},".rejt-collapsed::after":{left:-8,top:8,borderTop:"3px solid transparent",borderBottom:"3px solid transparent",borderLeft:"3px solid rgba(153,153,153,0.6)"},".rejt-not-collapsed::after":{left:-10,top:10,borderTop:"3px solid rgba(153,153,153,0.6)",borderLeft:"3px solid transparent",borderRight:"3px solid transparent"},".rejt-value":{display:"inline-block",border:"1px solid transparent",borderRadius:4,margin:"1px 0",padding:"0 4px",cursor:"text",color:theme.color.defaultText},".rejt-value-node:hover > .rejt-value":{background:theme.color.lighter,borderColor:theme.appBorderColor}})),ButtonInline=import_theming13.styled.button(({theme,primary})=>({border:0,height:20,margin:1,borderRadius:4,background:primary?theme.color.secondary:"transparent",color:primary?theme.color.lightest:theme.color.dark,fontWeight:primary?"bold":"normal",cursor:"pointer",order:primary?"initial":9})),ActionAddIcon=(0,import_theming13.styled)(import_icons3.AddIcon)(({theme,disabled})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?"not-allowed":"pointer",color:theme.textMutedColor,"&:hover":disabled?{}:{color:theme.color.ancillary},"svg + &":{marginLeft:0}})),ActionSubstractIcon=(0,import_theming13.styled)(import_icons3.SubtractIcon)(({theme,disabled})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?"not-allowed":"pointer",color:theme.textMutedColor,"&:hover":disabled?{}:{color:theme.color.negative},"svg + &":{marginLeft:0}})),Input=import_theming13.styled.input(({theme,placeholder})=>({outline:0,margin:placeholder?1:"1px 0",padding:"3px 4px",color:theme.color.defaultText,background:theme.background.app,border:`1px solid ${theme.appBorderColor}`,borderRadius:4,lineHeight:"14px",width:placeholder==="Key"?80:120,"&:focus":{border:`1px solid ${theme.color.secondary}`}})),RawButton=(0,import_theming13.styled)(import_components10.IconButton)(({theme})=>({position:"absolute",zIndex:2,top:2,right:2,height:21,padding:"0 3px",background:theme.background.bar,border:`1px solid ${theme.appBorderColor}`,borderRadius:3,color:theme.textMutedColor,fontSize:"9px",fontWeight:"bold",textDecoration:"none",span:{marginLeft:3,marginTop:1}})),RawInput=(0,import_theming13.styled)(import_components10.Form.Textarea)(({theme})=>({flex:1,padding:"7px 6px",fontFamily:theme.typography.fonts.mono,fontSize:"12px",lineHeight:"18px","&::placeholder":{fontFamily:theme.typography.fonts.base,fontSize:"13px"},"&:placeholder-shown":{padding:"7px 10px"}})),ENTER_EVENT={bubbles:!0,cancelable:!0,key:"Enter",code:"Enter",keyCode:13},dispatchEnterKey=event=>{event.currentTarget.dispatchEvent(new globalWindow2.KeyboardEvent("keydown",ENTER_EVENT))},selectValue=event=>{event.currentTarget.select()},getCustomStyleFunction=theme=>()=>({name:{color:theme.color.secondary},collapsed:{color:theme.color.dark},ul:{listStyle:"none",margin:"0 0 0 1rem",padding:0},li:{outline:0}}),ObjectControl=({name:name2,value:value3,onChange,argType})=>{let theme=(0,import_theming13.useTheme)(),data=(0,import_react18.useMemo)(()=>value3&&cloneDeep2(value3),[value3]),hasData=data!=null,[showRaw,setShowRaw]=(0,import_react18.useState)(!hasData),[parseError,setParseError]=(0,import_react18.useState)(null),readonly=!!argType?.table?.readonly,updateRaw=(0,import_react18.useCallback)(raw=>{try{raw&&onChange(JSON.parse(raw)),setParseError(void 0)}catch(e3){setParseError(e3)}},[onChange]),[forceVisible,setForceVisible]=(0,import_react18.useState)(!1),onForceVisible=(0,import_react18.useCallback)(()=>{onChange({}),setForceVisible(!0)},[setForceVisible]),htmlElRef=(0,import_react18.useRef)(null);if((0,import_react18.useEffect)(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select()},[forceVisible]),!hasData)return import_react18.default.createElement(import_components10.Button,{disabled:readonly,id:getControlSetterButtonId(name2),onClick:onForceVisible},"Set object");let rawJSONForm=import_react18.default.createElement(RawInput,{ref:htmlElRef,id:getControlId(name2),name:name2,defaultValue:value3===null?"":JSON.stringify(value3,null,2),onBlur:event=>updateRaw(event.target.value),placeholder:"Edit JSON string...",autoFocus:forceVisible,valid:parseError?"error":null,readOnly:readonly}),isObjectOrArray=Array.isArray(value3)||typeof value3=="object"&&value3?.constructor===Object;return import_react18.default.createElement(Wrapper6,{"aria-readonly":readonly},isObjectOrArray&&import_react18.default.createElement(RawButton,{onClick:e3=>{e3.preventDefault(),setShowRaw(v3=>!v3)}},showRaw?import_react18.default.createElement(import_icons3.EyeCloseIcon,null):import_react18.default.createElement(import_icons3.EyeIcon,null),import_react18.default.createElement("span",null,"RAW")),showRaw?rawJSONForm:import_react18.default.createElement(JsonTree,{readOnly:readonly||!isObjectOrArray,isCollapsed:isObjectOrArray?void 0:()=>!0,data,rootName:name2,onFullyUpdate:onChange,getStyle:getCustomStyleFunction(theme),cancelButtonElement:import_react18.default.createElement(ButtonInline,{type:"button"},"Cancel"),editButtonElement:import_react18.default.createElement(ButtonInline,{type:"submit"},"Save"),addButtonElement:import_react18.default.createElement(ButtonInline,{type:"submit",primary:!0},"Save"),plusMenuElement:import_react18.default.createElement(ActionAddIcon,null),minusMenuElement:import_react18.default.createElement(ActionSubstractIcon,null),inputElement:(_3,__,___,key2)=>key2?import_react18.default.createElement(Input,{onFocus:selectValue,onBlur:dispatchEnterKey}):import_react18.default.createElement(Input,null),fallback:rawJSONForm}))};var import_react19=__toESM(require("react")),import_theming14=require("storybook/internal/theming");init_helpers();var RangeInput=import_theming14.styled.input(({theme,min,max,value:value3,disabled})=>({"&":{width:"100%",backgroundColor:"transparent",appearance:"none"},"&::-webkit-slider-runnable-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value3-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} ${(value3-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value3-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} ${(value3-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:disabled?"not-allowed":"pointer"},"&::-webkit-slider-thumb":{marginTop:"-6px",width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:disabled?"not-allowed":"grab",appearance:"none",background:`${theme.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${curriedDarken$1(.05,theme.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${theme.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:disabled?"not-allowed":"grab"}},"&:focus":{outline:"none","&::-webkit-slider-runnable-track":{borderColor:rgba(theme.color.secondary,.4)},"&::-webkit-slider-thumb":{borderColor:theme.color.secondary,boxShadow:`0 0px 5px 0px ${theme.color.secondary}`}},"&::-moz-range-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value3-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} ${(value3-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value3-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} ${(value3-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:disabled?"not-allowed":"pointer",outline:"none"},"&::-moz-range-thumb":{width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:disabled?"not-allowed":"grap",background:`${theme.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${curriedDarken$1(.05,theme.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${theme.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&::-ms-track":{background:theme.base==="light"?`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value3-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} ${(value3-min)/(max-min)*100}%, 
            ${curriedDarken$1(.02,theme.input.background)} 100%)`:`linear-gradient(to right, 
            ${theme.color.green} 0%, ${theme.color.green} ${(value3-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} ${(value3-min)/(max-min)*100}%, 
            ${curriedLighten$1(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,color:"transparent",width:"100%",height:"6px",cursor:"pointer"},"&::-ms-fill-lower":{borderRadius:6},"&::-ms-fill-upper":{borderRadius:6},"&::-ms-thumb":{width:16,height:16,background:`${theme.input.background}`,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:50,cursor:"grab",marginTop:0},"@supports (-ms-ime-align:auto)":{"input[type=range]":{margin:"0"}}})),RangeLabel=import_theming14.styled.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:"nowrap",fontFeatureSettings:"tnum",fontVariantNumeric:"tabular-nums","[aria-readonly=true] &":{opacity:.5}}),RangeCurrentAndMaxLabel=(0,import_theming14.styled)(RangeLabel)(({numberOFDecimalsPlaces,max})=>({width:`${numberOFDecimalsPlaces+max.toString().length*2+3}ch`,textAlign:"right",flexShrink:0})),RangeWrapper=import_theming14.styled.div({display:"flex",alignItems:"center",width:"100%"});function getNumberOfDecimalPlaces(number){let match=number.toString().match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return match?Math.max(0,(match[1]?match[1].length:0)-(match[2]?+match[2]:0)):0}var RangeControl=({name:name2,value:value3,onChange,min=0,max=100,step=1,onBlur,onFocus,argType})=>{let handleChange=event=>{onChange(parse2(event.target.value))},hasValue=value3!==void 0,numberOFDecimalsPlaces=(0,import_react19.useMemo)(()=>getNumberOfDecimalPlaces(step),[step]),readonly=!!argType?.table?.readonly;return import_react19.default.createElement(RangeWrapper,{"aria-readonly":readonly},import_react19.default.createElement(RangeLabel,null,min),import_react19.default.createElement(RangeInput,{id:getControlId(name2),type:"range",disabled:readonly,onChange:handleChange,name:name2,value:value3,min,max,step,onFocus,onBlur}),import_react19.default.createElement(RangeCurrentAndMaxLabel,{numberOFDecimalsPlaces,max},hasValue?value3.toFixed(numberOFDecimalsPlaces):"--"," / ",max))};var import_react20=__toESM(require("react")),import_components11=require("storybook/internal/components"),import_theming15=require("storybook/internal/theming");init_helpers();var Wrapper7=import_theming15.styled.label({display:"flex"}),MaxLength=import_theming15.styled.div(({isMaxed})=>({marginLeft:"0.75rem",paddingTop:"0.35rem",color:isMaxed?"red":void 0})),TextControl=({name:name2,value:value3,onChange,onFocus,onBlur,maxLength,argType})=>{let handleChange=event=>{onChange(event.target.value)},readonly=!!argType?.table?.readonly,[forceVisible,setForceVisible]=(0,import_react20.useState)(!1),onForceVisible=(0,import_react20.useCallback)(()=>{onChange(""),setForceVisible(!0)},[setForceVisible]);if(value3===void 0)return import_react20.default.createElement(import_components11.Button,{variant:"outline",size:"medium",disabled:readonly,id:getControlSetterButtonId(name2),onClick:onForceVisible},"Set string");let isValid=typeof value3=="string";return import_react20.default.createElement(Wrapper7,null,import_react20.default.createElement(import_components11.Form.Textarea,{id:getControlId(name2),maxLength,onChange:handleChange,disabled:readonly,size:"flex",placeholder:"Edit string...",autoFocus:forceVisible,valid:isValid?null:"error",name:name2,value:isValid?value3:"",onFocus,onBlur}),maxLength&&import_react20.default.createElement(MaxLength,{isMaxed:value3?.length===maxLength},value3?.length??0," / ",maxLength))};var import_react21=__toESM(require("react")),import_components12=require("storybook/internal/components"),import_theming16=require("storybook/internal/theming");init_helpers();var FileInput=(0,import_theming16.styled)(import_components12.Form.Input)({padding:10});function revokeOldUrls(urls){urls.forEach(url=>{url.startsWith("blob:")&&URL.revokeObjectURL(url)})}var FilesControl=({onChange,name:name2,accept="image/*",value:value3,argType})=>{let inputElement=(0,import_react21.useRef)(null),readonly=argType?.control?.readOnly;function handleFileChange(e3){if(!e3.target.files)return;let fileUrls=Array.from(e3.target.files).map(file=>URL.createObjectURL(file));onChange(fileUrls),revokeOldUrls(value3)}return(0,import_react21.useEffect)(()=>{value3==null&&inputElement.current&&(inputElement.current.value=null)},[value3,name2]),import_react21.default.createElement(FileInput,{ref:inputElement,id:getControlId(name2),type:"file",name:name2,multiple:!0,disabled:readonly,onChange:handleFileChange,accept,size:"flex"})};var LazyColorControl=(0,import_react24.lazy)(()=>Promise.resolve().then(()=>(init_Color(),Color_exports))),ColorControl2=props=>import_react24.default.createElement(import_react24.Suspense,{fallback:import_react24.default.createElement("div",null)},import_react24.default.createElement(LazyColorControl,{...props}));var Controls2={array:ObjectControl,object:ObjectControl,boolean:BooleanControl,color:ColorControl2,date:DateControl,number:NumberControl,check:OptionsControl,"inline-check":OptionsControl,radio:OptionsControl,"inline-radio":OptionsControl,select:OptionsControl,"multi-select":OptionsControl,range:RangeControl,text:TextControl,file:FilesControl},NoControl=()=>import_react25.default.createElement(import_react25.default.Fragment,null,"-"),ArgControl=({row,arg,updateArgs,isHovered})=>{let{key:key2,control}=row,[isFocused,setFocused]=(0,import_react25.useState)(!1),[boxedValue,setBoxedValue]=(0,import_react25.useState)({value:arg});(0,import_react25.useEffect)(()=>{isFocused||setBoxedValue({value:arg})},[isFocused,arg]);let onChange=(0,import_react25.useCallback)(argVal=>(setBoxedValue({value:argVal}),updateArgs({[key2]:argVal}),argVal),[updateArgs,key2]),onBlur=(0,import_react25.useCallback)(()=>setFocused(!1),[]),onFocus=(0,import_react25.useCallback)(()=>setFocused(!0),[]);if(!control||control.disable){let canBeSetup=control?.disable!==!0&&row?.type?.name!=="function";return isHovered&&canBeSetup?import_react25.default.createElement(import_components14.Link,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},"Setup controls"):import_react25.default.createElement(NoControl,null)}let props={name:key2,argType:row,value:boxedValue.value,onChange,onBlur,onFocus},Control=Controls2[control.type]||NoControl;return import_react25.default.createElement(Control,{...props,...control,controlType:control.type})};var import_react26=__toESM(require("react")),import_components15=require("storybook/internal/components"),import_theming18=require("storybook/internal/theming"),Table=import_theming18.styled.table(({theme})=>({"&&":{borderCollapse:"collapse",borderSpacing:0,border:"none",tr:{border:"none !important",background:"none"},"td, th":{padding:0,border:"none",width:"auto!important"},marginTop:0,marginBottom:0,"th:first-of-type, td:first-of-type":{paddingLeft:0},"th:last-of-type, td:last-of-type":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,"&:not(:first-of-type)":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:"none",border:"none"},code:(0,import_components15.codeCommon)({theme}),div:{span:{fontWeight:"bold"}},"& code":{margin:0,display:"inline-block",fontSize:theme.typography.size.s1}}})),ArgJsDoc=({tags})=>{let params=(tags.params||[]).filter(x3=>x3.description),hasDisplayableParams=params.length!==0,hasDisplayableDeprecated=tags.deprecated!=null,hasDisplayableReturns=tags.returns!=null&&tags.returns.description!=null;return!hasDisplayableParams&&!hasDisplayableReturns&&!hasDisplayableDeprecated?null:import_react26.default.createElement(import_react26.default.Fragment,null,import_react26.default.createElement(Table,null,import_react26.default.createElement("tbody",null,hasDisplayableDeprecated&&import_react26.default.createElement("tr",{key:"deprecated"},import_react26.default.createElement("td",{colSpan:2},import_react26.default.createElement("strong",null,"Deprecated"),": ",tags.deprecated.toString())),hasDisplayableParams&&params.map(x3=>import_react26.default.createElement("tr",{key:x3.name},import_react26.default.createElement("td",null,import_react26.default.createElement("code",null,x3.name)),import_react26.default.createElement("td",null,x3.description))),hasDisplayableReturns&&import_react26.default.createElement("tr",{key:"returns"},import_react26.default.createElement("td",null,import_react26.default.createElement("code",null,"Returns")),import_react26.default.createElement("td",null,tags.returns.description)))))};var import_react27=__toESM(require("react")),import_components16=require("storybook/internal/components"),import_theming19=require("storybook/internal/theming"),import_icons5=require("@storybook/icons");init_compat();var import_memoizerific=__toESM(require_memoizerific()),ITEMS_BEFORE_EXPANSION=8,Summary=import_theming19.styled.div(({isExpanded})=>({display:"flex",flexDirection:isExpanded?"column":"row",flexWrap:"wrap",alignItems:"flex-start",marginBottom:"-4px",minWidth:100})),Text3=import_theming19.styled.span(import_components16.codeCommon,({theme,simple=!1})=>({flex:"0 0 auto",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,wordBreak:"break-word",whiteSpace:"normal",maxWidth:"100%",margin:0,marginRight:"4px",marginBottom:"4px",paddingTop:"2px",paddingBottom:"2px",lineHeight:"13px",...simple&&{background:"transparent",border:"0 none",paddingLeft:0}})),ExpandButton=import_theming19.styled.button(({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,marginBottom:"4px",background:"none",border:"none"})),Expandable=import_theming19.styled.div(import_components16.codeCommon,({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,fontSize:theme.typography.size.s1,margin:0,whiteSpace:"nowrap",display:"flex",alignItems:"center"})),Detail=import_theming19.styled.div(({theme,width})=>({width,minWidth:200,maxWidth:800,padding:15,fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,boxSizing:"content-box","& code":{padding:"0 !important"}})),ChevronUpIcon=(0,import_theming19.styled)(import_icons5.ChevronSmallUpIcon)({marginLeft:4}),ChevronDownIcon=(0,import_theming19.styled)(import_icons5.ChevronSmallDownIcon)({marginLeft:4}),EmptyArg=()=>import_react27.default.createElement("span",null,"-"),ArgText=({text,simple})=>import_react27.default.createElement(Text3,{simple},text),calculateDetailWidth=(0,import_memoizerific.default)(1e3)(detail=>{let lines=detail.split(/\r?\n/);return`${Math.max(...lines.map(x3=>x3.length))}ch`}),getSummaryItems=summary=>{if(!summary)return[summary];let summaryItems=summary.split("|").map(value3=>value3.trim());return uniq(summaryItems)},renderSummaryItems=(summaryItems,isExpanded=!0)=>{let items=summaryItems;return isExpanded||(items=summaryItems.slice(0,ITEMS_BEFORE_EXPANSION)),items.map(item=>import_react27.default.createElement(ArgText,{key:item,text:item===""?'""':item}))},ArgSummary=({value:value3,initialExpandedArgs})=>{let{summary,detail}=value3,[isOpen,setIsOpen]=(0,import_react27.useState)(!1),[isExpanded,setIsExpanded]=(0,import_react27.useState)(initialExpandedArgs||!1);if(summary==null)return null;let summaryAsString=typeof summary.toString=="function"?summary.toString():summary;if(detail==null){if(/[(){}[\]<>]/.test(summaryAsString))return import_react27.default.createElement(ArgText,{text:summaryAsString});let summaryItems=getSummaryItems(summaryAsString),itemsCount=summaryItems.length;return itemsCount>ITEMS_BEFORE_EXPANSION?import_react27.default.createElement(Summary,{isExpanded},renderSummaryItems(summaryItems,isExpanded),import_react27.default.createElement(ExpandButton,{onClick:()=>setIsExpanded(!isExpanded)},isExpanded?"Show less...":`Show ${itemsCount-ITEMS_BEFORE_EXPANSION} more...`)):import_react27.default.createElement(Summary,null,renderSummaryItems(summaryItems))}return import_react27.default.createElement(import_components16.WithTooltipPure,{closeOnOutsideClick:!0,placement:"bottom",visible:isOpen,onVisibleChange:isVisible=>{setIsOpen(isVisible)},tooltip:import_react27.default.createElement(Detail,{width:calculateDetailWidth(detail)},import_react27.default.createElement(import_components16.SyntaxHighlighter,{language:"jsx",format:!1},detail))},import_react27.default.createElement(Expandable,{className:"sbdocs-expandable"},import_react27.default.createElement("span",null,summaryAsString),isOpen?import_react27.default.createElement(ChevronUpIcon,null):import_react27.default.createElement(ChevronDownIcon,null)))},ArgValue=({value:value3,initialExpandedArgs})=>value3==null?import_react27.default.createElement(EmptyArg,null):import_react27.default.createElement(ArgSummary,{value:value3,initialExpandedArgs});var Name=import_theming20.styled.span({fontWeight:"bold"}),Required=import_theming20.styled.span(({theme})=>({color:theme.color.negative,fontFamily:theme.typography.fonts.mono,cursor:"help"})),Description=import_theming20.styled.div(({theme})=>({"&&":{p:{margin:"0 0 10px 0"},a:{color:theme.color.secondary}},code:{...(0,import_components17.codeCommon)({theme}),fontSize:12,fontFamily:theme.typography.fonts.mono},"& code":{margin:0,display:"inline-block"},"& pre > code":{whiteSpace:"pre-wrap"}})),Type=import_theming20.styled.div(({theme,hasDescription})=>({color:theme.base==="light"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.2,theme.color.defaultText),marginTop:hasDescription?4:0})),TypeWithJsDoc=import_theming20.styled.div(({theme,hasDescription})=>({color:theme.base==="light"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.2,theme.color.defaultText),marginTop:hasDescription?12:0,marginBottom:12})),StyledTd=import_theming20.styled.td(({theme,expandable})=>({paddingLeft:expandable?"40px !important":"20px !important"})),toSummary=value3=>value3&&{summary:typeof value3=="string"?value3:value3.name},ArgRow=props=>{let[isHovered,setIsHovered]=(0,import_react28.useState)(!1),{row,updateArgs,compact,expandable,initialExpandedArgs}=props,{name:name2,description}=row,table=row.table||{},type=table.type||toSummary(row.type),defaultValue=table.defaultValue||row.defaultValue,required=row.type?.required,hasDescription=description!=null&&description!=="";return import_react28.default.createElement("tr",{onMouseEnter:()=>setIsHovered(!0),onMouseLeave:()=>setIsHovered(!1)},import_react28.default.createElement(StyledTd,{expandable},import_react28.default.createElement(Name,null,name2),required?import_react28.default.createElement(Required,{title:"Required"},"*"):null),compact?null:import_react28.default.createElement("td",null,hasDescription&&import_react28.default.createElement(Description,null,import_react28.default.createElement(index_module_default,null,description)),table.jsDocTags!=null?import_react28.default.createElement(import_react28.default.Fragment,null,import_react28.default.createElement(TypeWithJsDoc,{hasDescription},import_react28.default.createElement(ArgValue,{value:type,initialExpandedArgs})),import_react28.default.createElement(ArgJsDoc,{tags:table.jsDocTags})):import_react28.default.createElement(Type,{hasDescription},import_react28.default.createElement(ArgValue,{value:type,initialExpandedArgs}))),compact?null:import_react28.default.createElement("td",null,import_react28.default.createElement(ArgValue,{value:defaultValue,initialExpandedArgs})),updateArgs?import_react28.default.createElement("td",null,import_react28.default.createElement(ArgControl,{...props,isHovered})):null)};var import_react29=__toESM(require("react")),import_components18=require("storybook/internal/components"),import_theming21=require("storybook/internal/theming"),import_icons6=require("@storybook/icons"),Wrapper9=import_theming21.styled.div(({inAddonPanel,theme})=>({height:inAddonPanel?"100%":"auto",display:"flex",border:inAddonPanel?"none":`1px solid ${theme.appBorderColor}`,borderRadius:inAddonPanel?0:theme.appBorderRadius,padding:inAddonPanel?0:40,alignItems:"center",justifyContent:"center",flexDirection:"column",gap:15,background:theme.background.content})),Links=import_theming21.styled.div(({theme})=>({display:"flex",fontSize:theme.typography.size.s2-1,gap:25})),Divider=import_theming21.styled.div(({theme})=>({width:1,height:16,backgroundColor:theme.appBorderColor})),Empty=({inAddonPanel})=>{let[isLoading,setIsLoading]=(0,import_react29.useState)(!0);return(0,import_react29.useEffect)(()=>{let load=setTimeout(()=>{setIsLoading(!1)},100);return()=>clearTimeout(load)},[]),isLoading?null:import_react29.default.createElement(Wrapper9,{inAddonPanel},import_react29.default.createElement(import_components18.EmptyTabContent,{title:inAddonPanel?"Interactive story playground":"Args table with interactive controls couldn't be auto-generated",description:import_react29.default.createElement(import_react29.default.Fragment,null,"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically."),footer:import_react29.default.createElement(Links,null,inAddonPanel&&import_react29.default.createElement(import_react29.default.Fragment,null,import_react29.default.createElement(import_components18.Link,{href:"https://youtu.be/0gOfS6K0x0E",target:"_blank",withArrow:!0},import_react29.default.createElement(import_icons6.VideoIcon,null)," Watch 5m video"),import_react29.default.createElement(Divider,null),import_react29.default.createElement(import_components18.Link,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},import_react29.default.createElement(import_icons6.DocumentIcon,null)," Read docs")),!inAddonPanel&&import_react29.default.createElement(import_components18.Link,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},import_react29.default.createElement(import_icons6.DocumentIcon,null)," Learn how to set that up"))}))};var import_react30=__toESM(require("react")),import_theming22=require("storybook/internal/theming"),import_icons7=require("@storybook/icons");var ExpanderIconDown=(0,import_theming22.styled)(import_icons7.ChevronDownIcon)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base==="light"?curriedTransparentize$1(.25,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),border:"none",display:"inline-block"})),ExpanderIconRight=(0,import_theming22.styled)(import_icons7.ChevronRightIcon)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base==="light"?curriedTransparentize$1(.25,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),border:"none",display:"inline-block"})),FlexWrapper=import_theming22.styled.span(({theme})=>({display:"flex",lineHeight:"20px",alignItems:"center"})),Section=import_theming22.styled.td(({theme})=>({position:"relative",letterSpacing:"0.35em",textTransform:"uppercase",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s1-1,color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText),background:`${theme.background.app} !important`,"& ~ td":{background:`${theme.background.app} !important`}})),Subsection=import_theming22.styled.td(({theme})=>({position:"relative",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,background:theme.background.app})),StyledTd2=import_theming22.styled.td({position:"relative"}),StyledTr=import_theming22.styled.tr(({theme})=>({"&:hover > td":{backgroundColor:`${curriedLighten$1(.005,theme.background.app)} !important`,boxShadow:`${theme.color.mediumlight} 0 - 1px 0 0 inset`,cursor:"row-resize"}})),ClickIntercept=import_theming22.styled.button({background:"none",border:"none",padding:"0",font:"inherit",position:"absolute",top:0,bottom:0,left:0,right:0,height:"100%",width:"100%",color:"transparent",cursor:"row-resize !important"}),SectionRow=({level="section",label,children,initialExpanded=!0,colSpan=3})=>{let[expanded,setExpanded]=(0,import_react30.useState)(initialExpanded),Level=level==="subsection"?Subsection:Section,itemCount=children?.length||0,caption=level==="subsection"?`${itemCount} item${itemCount!==1?"s":""}`:"",helperText=`${expanded?"Hide":"Show"} ${level==="subsection"?itemCount:label} item${itemCount!==1?"s":""}`;return import_react30.default.createElement(import_react30.default.Fragment,null,import_react30.default.createElement(StyledTr,{title:helperText},import_react30.default.createElement(Level,{colSpan:1},import_react30.default.createElement(ClickIntercept,{onClick:e3=>setExpanded(!expanded),tabIndex:0},helperText),import_react30.default.createElement(FlexWrapper,null,expanded?import_react30.default.createElement(ExpanderIconDown,null):import_react30.default.createElement(ExpanderIconRight,null),label)),import_react30.default.createElement(StyledTd2,{colSpan:colSpan-1},import_react30.default.createElement(ClickIntercept,{onClick:e3=>setExpanded(!expanded),tabIndex:-1,style:{outline:"none"}},helperText),expanded?null:caption)),expanded?children:null)};var import_react31=__toESM(require("react")),import_theming23=require("storybook/internal/theming"),Row=import_theming23.styled.div(({theme})=>({display:"flex",gap:16,borderBottom:`1px solid ${theme.appBorderColor}`,"&:last-child":{borderBottom:0}})),Column=import_theming23.styled.div(({numColumn})=>({display:"flex",flexDirection:"column",flex:numColumn||1,gap:5,padding:"12px 20px"})),SkeletonText=import_theming23.styled.div(({theme,width,height})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,width:width||"100%",height:height||16,borderRadius:3})),columnWidth=[2,4,2,2],Skeleton=()=>import_react31.default.createElement(import_react31.default.Fragment,null,import_react31.default.createElement(Row,null,import_react31.default.createElement(Column,{numColumn:columnWidth[0]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[1]},import_react31.default.createElement(SkeletonText,{width:"30%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[2]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[3]},import_react31.default.createElement(SkeletonText,{width:"60%"}))),import_react31.default.createElement(Row,null,import_react31.default.createElement(Column,{numColumn:columnWidth[0]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[1]},import_react31.default.createElement(SkeletonText,{width:"80%"}),import_react31.default.createElement(SkeletonText,{width:"30%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[2]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[3]},import_react31.default.createElement(SkeletonText,{width:"60%"}))),import_react31.default.createElement(Row,null,import_react31.default.createElement(Column,{numColumn:columnWidth[0]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[1]},import_react31.default.createElement(SkeletonText,{width:"80%"}),import_react31.default.createElement(SkeletonText,{width:"30%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[2]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[3]},import_react31.default.createElement(SkeletonText,{width:"60%"}))),import_react31.default.createElement(Row,null,import_react31.default.createElement(Column,{numColumn:columnWidth[0]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[1]},import_react31.default.createElement(SkeletonText,{width:"80%"}),import_react31.default.createElement(SkeletonText,{width:"30%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[2]},import_react31.default.createElement(SkeletonText,{width:"60%"})),import_react31.default.createElement(Column,{numColumn:columnWidth[3]},import_react31.default.createElement(SkeletonText,{width:"60%"}))));var TableWrapper=import_theming24.styled.table(({theme,compact,inAddonPanel})=>({"&&":{borderSpacing:0,color:theme.color.defaultText,"td, th":{padding:0,border:"none",verticalAlign:"top",textOverflow:"ellipsis"},fontSize:theme.typography.size.s2-1,lineHeight:"20px",textAlign:"left",width:"100%",marginTop:inAddonPanel?0:25,marginBottom:inAddonPanel?0:40,"thead th:first-of-type, td:first-of-type":{width:"25%"},"th:first-of-type, td:first-of-type":{paddingLeft:20},"th:nth-of-type(2), td:nth-of-type(2)":{...compact?null:{width:"35%"}},"td:nth-of-type(3)":{...compact?null:{width:"15%"}},"th:last-of-type, td:last-of-type":{paddingRight:20,...compact?null:{width:"25%"}},th:{color:theme.base==="light"?curriedTransparentize$1(.25,theme.color.defaultText):curriedTransparentize$1(.45,theme.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:"10px",paddingBottom:"10px","&:not(:first-of-type)":{paddingLeft:15,paddingRight:15},"&:last-of-type":{paddingRight:20}},marginLeft:inAddonPanel?0:1,marginRight:inAddonPanel?0:1,tbody:{...inAddonPanel?null:{filter:theme.base==="light"?"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))":"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))"},"> tr > *":{background:theme.background.content,borderTop:`1px solid ${theme.appBorderColor}`},...inAddonPanel?null:{"> tr:first-of-type > *":{borderBlockStart:`1px solid ${theme.appBorderColor}`},"> tr:last-of-type > *":{borderBlockEnd:`1px solid ${theme.appBorderColor}`},"> tr > *:first-of-type":{borderInlineStart:`1px solid ${theme.appBorderColor}`},"> tr > *:last-of-type":{borderInlineEnd:`1px solid ${theme.appBorderColor}`},"> tr:first-of-type > td:first-of-type":{borderTopLeftRadius:theme.appBorderRadius},"> tr:first-of-type > td:last-of-type":{borderTopRightRadius:theme.appBorderRadius},"> tr:last-of-type > td:first-of-type":{borderBottomLeftRadius:theme.appBorderRadius},"> tr:last-of-type > td:last-of-type":{borderBottomRightRadius:theme.appBorderRadius}}}}})),StyledIconButton=(0,import_theming24.styled)(import_components19.IconButton)(({theme})=>({margin:"-4px -12px -4px 0"})),ControlHeadingWrapper=import_theming24.styled.span({display:"flex",justifyContent:"space-between"});var sortFns={alpha:(a3,b3)=>a3.name.localeCompare(b3.name),requiredFirst:(a3,b3)=>+!!b3.type?.required-+!!a3.type?.required||a3.name.localeCompare(b3.name),none:void 0},groupRows=(rows,sort)=>{let sections={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!rows)return sections;Object.entries(rows).forEach(([key2,row])=>{let{category,subcategory}=row?.table||{};if(category){let section=sections.sections[category]||{ungrouped:[],subsections:{}};if(!subcategory)section.ungrouped.push({key:key2,...row});else{let subsection=section.subsections[subcategory]||[];subsection.push({key:key2,...row}),section.subsections[subcategory]=subsection}sections.sections[category]=section}else if(subcategory){let subsection=sections.ungroupedSubsections[subcategory]||[];subsection.push({key:key2,...row}),sections.ungroupedSubsections[subcategory]=subsection}else sections.ungrouped.push({key:key2,...row})});let sortFn=sortFns[sort],sortSubsection=record=>sortFn?Object.keys(record).reduce((acc,cur)=>({...acc,[cur]:record[cur].sort(sortFn)}),{}):record;return{ungrouped:sections.ungrouped.sort(sortFn),ungroupedSubsections:sortSubsection(sections.ungroupedSubsections),sections:Object.keys(sections.sections).reduce((acc,cur)=>({...acc,[cur]:{ungrouped:sections.sections[cur].ungrouped.sort(sortFn),subsections:sortSubsection(sections.sections[cur].subsections)}}),{})}},safeIncludeConditionalArg=(row,args2,globals)=>{try{return(0,import_csf.includeConditionalArg)(row,args2,globals)}catch(err){return import_client_logger4.once.warn(err.message),!1}},ArgsTable=props=>{let{updateArgs,resetArgs,compact,inAddonPanel,initialExpandedArgs,sort="none",isLoading}=props;if("error"in props){let{error}=props;return import_react32.default.createElement(EmptyBlock,null,error,"\xA0",import_react32.default.createElement(import_components19.Link,{href:"http://storybook.js.org/docs/",target:"_blank",withArrow:!0},import_react32.default.createElement(import_icons8.DocumentIcon,null)," Read the docs"))}if(isLoading)return import_react32.default.createElement(Skeleton,null);let{rows,args:args2,globals}="rows"in props&&props,groups=groupRows(pickBy(rows||{},row=>!row?.table?.disable&&safeIncludeConditionalArg(row,args2||{},globals||{})),sort),hasNoUngrouped=groups.ungrouped.length===0,hasNoSections=Object.entries(groups.sections).length===0,hasNoUngroupedSubsections=Object.entries(groups.ungroupedSubsections).length===0;if(hasNoUngrouped&&hasNoSections&&hasNoUngroupedSubsections)return import_react32.default.createElement(Empty,{inAddonPanel});let colSpan=1;updateArgs&&(colSpan+=1),compact||(colSpan+=2);let expandable=Object.keys(groups.sections).length>0,common={updateArgs,compact,inAddonPanel,initialExpandedArgs};return import_react32.default.createElement(import_components19.ResetWrapper,null,import_react32.default.createElement(TableWrapper,{compact,inAddonPanel,className:"docblock-argstable sb-unstyled"},import_react32.default.createElement("thead",{className:"docblock-argstable-head"},import_react32.default.createElement("tr",null,import_react32.default.createElement("th",null,import_react32.default.createElement("span",null,"Name")),compact?null:import_react32.default.createElement("th",null,import_react32.default.createElement("span",null,"Description")),compact?null:import_react32.default.createElement("th",null,import_react32.default.createElement("span",null,"Default")),updateArgs?import_react32.default.createElement("th",null,import_react32.default.createElement(ControlHeadingWrapper,null,"Control"," ",!isLoading&&resetArgs&&import_react32.default.createElement(StyledIconButton,{onClick:()=>resetArgs(),title:"Reset controls"},import_react32.default.createElement(import_icons8.UndoIcon,{"aria-hidden":!0})))):null)),import_react32.default.createElement("tbody",{className:"docblock-argstable-body"},groups.ungrouped.map(row=>import_react32.default.createElement(ArgRow,{key:row.key,row,arg:args2&&args2[row.key],...common})),Object.entries(groups.ungroupedSubsections).map(([subcategory,subsection])=>import_react32.default.createElement(SectionRow,{key:subcategory,label:subcategory,level:"subsection",colSpan},subsection.map(row=>import_react32.default.createElement(ArgRow,{key:row.key,row,arg:args2&&args2[row.key],expandable,...common})))),Object.entries(groups.sections).map(([category,section])=>import_react32.default.createElement(SectionRow,{key:category,label:category,level:"section",colSpan},section.ungrouped.map(row=>import_react32.default.createElement(ArgRow,{key:row.key,row,arg:args2&&args2[row.key],...common})),Object.entries(section.subsections).map(([subcategory,subsection])=>import_react32.default.createElement(SectionRow,{key:subcategory,label:subcategory,level:"subsection",colSpan},subsection.map(row=>import_react32.default.createElement(ArgRow,{key:row.key,row,arg:args2&&args2[row.key],expandable,...common})))))))))};var import_react33=__toESM(require("react")),import_components20=require("storybook/internal/components");var TabbedArgsTable=({tabs,...props})=>{let entries=Object.entries(tabs);return entries.length===1?import_react33.default.createElement(ArgsTable,{...entries[0][1],...props}):import_react33.default.createElement(import_components20.TabsState,null,entries.map((entry,index)=>{let[label,table]=entry,id2=`prop_table_div_${label}`,Component4="div",argsTableProps=index===0?props:{sort:props.sort};return import_react33.default.createElement(Component4,{key:id2,id:id2,title:label},({active})=>active?import_react33.default.createElement(ArgsTable,{key:`prop_table_${label}`,...table,...argsTableProps}):null)}))};var import_react34=__toESM(require("react")),import_components21=require("storybook/internal/components"),import_theming25=require("storybook/internal/theming");var Label4=import_theming25.styled.div(({theme})=>({marginRight:30,fontSize:`${theme.typography.size.s1}px`,color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText)})),Sample=import_theming25.styled.div({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),TypeSpecimen=import_theming25.styled.div({display:"flex",flexDirection:"row",alignItems:"baseline","&:not(:last-child)":{marginBottom:"1rem"}}),Wrapper10=import_theming25.styled.div(import_components21.withReset,({theme})=>({...getBlockBackgroundStyle(theme),margin:"25px 0 40px",padding:"30px 20px"})),Typeset=({fontFamily,fontSizes,fontWeight,sampleText,...props})=>import_react34.default.createElement(Wrapper10,{...props,className:"docblock-typeset sb-unstyled"},fontSizes.map(size=>import_react34.default.createElement(TypeSpecimen,{key:size},import_react34.default.createElement(Label4,null,size),import_react34.default.createElement(Sample,{style:{fontFamily,fontSize:size,fontWeight,lineHeight:1.2}},sampleText||"Was he a beast if music could move him so?"))));var import_react35=__toESM(require("react")),import_components22=require("storybook/internal/components"),import_theming26=require("storybook/internal/theming");var ItemTitle=import_theming26.styled.div(({theme})=>({fontWeight:theme.typography.weight.bold,color:theme.color.defaultText})),ItemSubtitle=import_theming26.styled.div(({theme})=>({color:theme.base==="light"?curriedTransparentize$1(.2,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText)})),ItemDescription=import_theming26.styled.div({flex:"0 0 30%",lineHeight:"20px",marginTop:5}),SwatchLabel=import_theming26.styled.div(({theme})=>({flex:1,textAlign:"center",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,lineHeight:1,overflow:"hidden",color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText),"> div":{display:"inline-block",overflow:"hidden",maxWidth:"100%",textOverflow:"ellipsis"},span:{display:"block",marginTop:2}})),SwatchLabels=import_theming26.styled.div({display:"flex",flexDirection:"row"}),Swatch2=import_theming26.styled.div(({background})=>({position:"relative",flex:1,"&::before":{position:"absolute",top:0,left:0,width:"100%",height:"100%",background,content:'""'}})),SwatchColors=import_theming26.styled.div(({theme})=>({...getBlockBackgroundStyle(theme),display:"flex",flexDirection:"row",height:50,marginBottom:5,overflow:"hidden",backgroundColor:"white",backgroundImage:"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)",backgroundClip:"padding-box"})),SwatchSpecimen=import_theming26.styled.div({display:"flex",flexDirection:"column",flex:1,position:"relative",marginBottom:30}),Swatches2=import_theming26.styled.div({flex:1,display:"flex",flexDirection:"row"}),Item=import_theming26.styled.div({display:"flex",alignItems:"flex-start"}),ListName=import_theming26.styled.div({flex:"0 0 30%"}),ListSwatches=import_theming26.styled.div({flex:1}),ListHeading=import_theming26.styled.div(({theme})=>({display:"flex",flexDirection:"row",alignItems:"center",paddingBottom:20,fontWeight:theme.typography.weight.bold,color:theme.base==="light"?curriedTransparentize$1(.4,theme.color.defaultText):curriedTransparentize$1(.6,theme.color.defaultText)})),List=import_theming26.styled.div(({theme})=>({fontSize:theme.typography.size.s2,lineHeight:"20px",display:"flex",flexDirection:"column"}));function renderSwatch(color,index){return import_react35.default.createElement(Swatch2,{key:`${color}-${index}`,title:color,background:color})}function renderSwatchLabel(color,index,colorDescription){return import_react35.default.createElement(SwatchLabel,{key:`${color}-${index}`,title:color},import_react35.default.createElement("div",null,color,colorDescription&&import_react35.default.createElement("span",null,colorDescription)))}function renderSwatchSpecimen(colors){if(Array.isArray(colors))return import_react35.default.createElement(SwatchSpecimen,null,import_react35.default.createElement(SwatchColors,null,colors.map((color,index)=>renderSwatch(color,index))),import_react35.default.createElement(SwatchLabels,null,colors.map((color,index)=>renderSwatchLabel(color,index))));let swatchElements=[],labelElements=[];for(let colorKey in colors){let colorValue=colors[colorKey];swatchElements.push(renderSwatch(colorValue,swatchElements.length)),labelElements.push(renderSwatchLabel(colorKey,labelElements.length,colorValue))}return import_react35.default.createElement(SwatchSpecimen,null,import_react35.default.createElement(SwatchColors,null,swatchElements),import_react35.default.createElement(SwatchLabels,null,labelElements))}var ColorItem=({title,subtitle,colors})=>import_react35.default.createElement(Item,null,import_react35.default.createElement(ItemDescription,null,import_react35.default.createElement(ItemTitle,null,title),import_react35.default.createElement(ItemSubtitle,null,subtitle)),import_react35.default.createElement(Swatches2,null,renderSwatchSpecimen(colors))),ColorPalette=({children,...props})=>import_react35.default.createElement(import_components22.ResetWrapper,null,import_react35.default.createElement(List,{...props,className:"docblock-colorpalette sb-unstyled"},import_react35.default.createElement(ListHeading,null,import_react35.default.createElement(ListName,null,"Name"),import_react35.default.createElement(ListSwatches,null,"Swatches")),children));var import_react36=__toESM(require("react")),import_components23=require("storybook/internal/components"),import_theming27=require("storybook/internal/theming");var ItemLabel=import_theming27.styled.div(({theme})=>({fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,color:theme.color.defaultText,marginLeft:10,lineHeight:1.2})),ItemSpecimen=import_theming27.styled.div(({theme})=>({...getBlockBackgroundStyle(theme),overflow:"hidden",height:40,width:40,display:"flex",alignItems:"center",justifyContent:"center",flex:"none","> img, > svg":{width:20,height:20}})),Item2=import_theming27.styled.div({display:"inline-flex",flexDirection:"row",alignItems:"center",flex:"0 1 calc(20% - 10px)",minWidth:120,margin:"0px 10px 30px 0"}),List2=import_theming27.styled.div({display:"flex",flexFlow:"row wrap"}),IconItem=({name:name2,children})=>import_react36.default.createElement(Item2,null,import_react36.default.createElement(ItemSpecimen,null,children),import_react36.default.createElement(ItemLabel,null,name2)),IconGallery=({children,...props})=>import_react36.default.createElement(import_components23.ResetWrapper,null,import_react36.default.createElement(List2,{...props,className:"docblock-icongallery sb-unstyled"},children));var import_react37=__toESM(require("react")),anchorBlockIdFromId=storyId=>`anchor--${storyId}`,Anchor=({storyId,children})=>import_react37.default.createElement("div",{id:anchorBlockIdFromId(storyId),className:"sb-anchor"},children);var import_react40=__toESM(require("react")),import_preview_api=require("storybook/internal/preview-api");var import_react39=require("react");var import_react38=require("react");globalThis&&globalThis.__DOCS_CONTEXT__===void 0&&(globalThis.__DOCS_CONTEXT__=(0,import_react38.createContext)(null),globalThis.__DOCS_CONTEXT__.displayName="DocsContext");var DocsContext=globalThis?globalThis.__DOCS_CONTEXT__:(0,import_react38.createContext)(null);var useOf=(moduleExportOrType,validTypes)=>(0,import_react39.useContext)(DocsContext).resolveOf(moduleExportOrType,validTypes);var titleCase=str=>str.split("-").map(part=>part.charAt(0).toUpperCase()+part.slice(1)).join(""),getComponentName=component=>{if(component)return typeof component=="string"?component.includes("-")?titleCase(component):component:component.__docgenInfo&&component.__docgenInfo.displayName?component.__docgenInfo.displayName:component.name};function scrollToElement(element,block="start"){element.scrollIntoView({behavior:"smooth",block,inline:"nearest"})}function extractComponentArgTypes(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error("Args unsupported. See Args documentation for your framework.");return extractArgTypes(component)}function getArgTypesFromResolved(resolved){if(resolved.type==="component"){let{component:component2,projectAnnotations:{parameters:parameters2}}=resolved;return{argTypes:extractComponentArgTypes(component2,parameters2),parameters:parameters2,component:component2}}if(resolved.type==="meta"){let{preparedMeta:{argTypes:argTypes2,parameters:parameters2,component:component2,subcomponents:subcomponents2}}=resolved;return{argTypes:argTypes2,parameters:parameters2,component:component2,subcomponents:subcomponents2}}let{story:{argTypes,parameters,component,subcomponents}}=resolved;return{argTypes,parameters,component,subcomponents}}var ArgTypes=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let resolved=useOf(of||"meta"),{argTypes,parameters,component,subcomponents}=getArgTypesFromResolved(resolved),argTypesParameters=parameters.docs?.argTypes||{},include=props.include??argTypesParameters.include,exclude=props.exclude??argTypesParameters.exclude,sort=props.sort??argTypesParameters.sort,filteredArgTypes=(0,import_preview_api.filterArgTypes)(argTypes,include,exclude);if(!(!!subcomponents&&Object.keys(subcomponents).length>0))return import_react40.default.createElement(ArgsTable,{rows:filteredArgTypes,sort});let mainComponentName=getComponentName(component),subcomponentTabs=Object.fromEntries(Object.entries(subcomponents).map(([key2,comp])=>[key2,{rows:(0,import_preview_api.filterArgTypes)(extractComponentArgTypes(comp,parameters),include,exclude),sort}])),tabs={[mainComponentName]:{rows:filteredArgTypes,sort},...subcomponentTabs};return import_react40.default.createElement(TabbedArgsTable,{tabs,sort})};var import_react45=__toESM(require("react"));var import_react42=__toESM(require("react")),import_docs_tools2=require("storybook/internal/docs-tools");var import_react41=__toESM(require("react")),import_docs_tools=require("storybook/internal/docs-tools");var __create2=Object.create,__defProp2=Object.defineProperty,__getOwnPropDesc2=Object.getOwnPropertyDescriptor,__getOwnPropNames2=Object.getOwnPropertyNames,__getProtoOf2=Object.getPrototypeOf,__hasOwnProp2=Object.prototype.hasOwnProperty,__commonJS2=(cb,mod)=>function(){return mod||(0,cb[__getOwnPropNames2(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports},__copyProps2=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key2 of __getOwnPropNames2(from))!__hasOwnProp2.call(to,key2)&&key2!==except&&__defProp2(to,key2,{get:()=>from[key2],enumerable:!(desc=__getOwnPropDesc2(from,key2))||desc.enumerable});return to},__toESM2=(mod,isNodeMode,target)=>(target=mod!=null?__create2(__getProtoOf2(mod)):{},__copyProps2(isNodeMode||!mod||!mod.__esModule?__defProp2(target,"default",{value:mod,enumerable:!0}):target,mod)),eventProperties=["bubbles","cancelBubble","cancelable","composed","currentTarget","defaultPrevented","eventPhase","isTrusted","returnValue","srcElement","target","timeStamp","type"],customEventSpecificProperties=["detail"];function extractEventHiddenProperties(event){let rebuildEvent=eventProperties.filter(value3=>event[value3]!==void 0).reduce((acc,value3)=>({...acc,[value3]:event[value3]}),{});return event instanceof CustomEvent&&customEventSpecificProperties.filter(value3=>event[value3]!==void 0).forEach(value3=>{rebuildEvent[value3]=event[value3]}),rebuildEvent}var import_memoizerific2=__toESM(require_memoizerific(),1),require_shams=__commonJS2({"node_modules/has-symbols/shams.js"(exports2,module2){"use strict";module2.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var obj={},sym=Symbol("test"),symObj=Object(sym);if(typeof sym=="string"||Object.prototype.toString.call(sym)!=="[object Symbol]"||Object.prototype.toString.call(symObj)!=="[object Symbol]")return!1;var symVal=42;obj[sym]=symVal;for(sym in obj)return!1;if(typeof Object.keys=="function"&&Object.keys(obj).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(obj).length!==0)return!1;var syms=Object.getOwnPropertySymbols(obj);if(syms.length!==1||syms[0]!==sym||!Object.prototype.propertyIsEnumerable.call(obj,sym))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var descriptor=Object.getOwnPropertyDescriptor(obj,sym);if(descriptor.value!==symVal||descriptor.enumerable!==!0)return!1}return!0}}}),require_has_symbols=__commonJS2({"node_modules/has-symbols/index.js"(exports2,module2){"use strict";var origSymbol=typeof Symbol<"u"&&Symbol,hasSymbolSham=require_shams();module2.exports=function(){return typeof origSymbol!="function"||typeof Symbol!="function"||typeof origSymbol("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:hasSymbolSham()}}}),require_implementation=__commonJS2({"node_modules/function-bind/implementation.js"(exports2,module2){"use strict";var ERROR_MESSAGE="Function.prototype.bind called on incompatible ",slice=Array.prototype.slice,toStr=Object.prototype.toString,funcType="[object Function]";module2.exports=function(that){var target=this;if(typeof target!="function"||toStr.call(target)!==funcType)throw new TypeError(ERROR_MESSAGE+target);for(var args2=slice.call(arguments,1),bound,binder=function(){if(this instanceof bound){var result2=target.apply(this,args2.concat(slice.call(arguments)));return Object(result2)===result2?result2:this}else return target.apply(that,args2.concat(slice.call(arguments)))},boundLength=Math.max(0,target.length-args2.length),boundArgs=[],i3=0;i3<boundLength;i3++)boundArgs.push("$"+i3);if(bound=Function("binder","return function ("+boundArgs.join(",")+"){ return binder.apply(this,arguments); }")(binder),target.prototype){var Empty2=function(){};Empty2.prototype=target.prototype,bound.prototype=new Empty2,Empty2.prototype=null}return bound}}}),require_function_bind=__commonJS2({"node_modules/function-bind/index.js"(exports2,module2){"use strict";var implementation=require_implementation();module2.exports=Function.prototype.bind||implementation}}),require_src=__commonJS2({"node_modules/has/src/index.js"(exports2,module2){"use strict";var bind=require_function_bind();module2.exports=bind.call(Function.call,Object.prototype.hasOwnProperty)}}),require_get_intrinsic=__commonJS2({"node_modules/get-intrinsic/index.js"(exports2,module2){"use strict";var undefined2,$SyntaxError=SyntaxError,$Function=Function,$TypeError=TypeError,getEvalledConstructor=function(expressionSyntax){try{return $Function('"use strict"; return ('+expressionSyntax+").constructor;")()}catch{}},$gOPD=Object.getOwnPropertyDescriptor;if($gOPD)try{$gOPD({},"")}catch{$gOPD=null}var throwTypeError=function(){throw new $TypeError},ThrowTypeError=$gOPD?function(){try{return arguments.callee,throwTypeError}catch{try{return $gOPD(arguments,"callee").get}catch{return throwTypeError}}}():throwTypeError,hasSymbols=require_has_symbols()(),getProto=Object.getPrototypeOf||function(x3){return x3.__proto__},needsEval={},TypedArray=typeof Uint8Array>"u"?undefined2:getProto(Uint8Array),INTRINSICS={"%AggregateError%":typeof AggregateError>"u"?undefined2:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?undefined2:ArrayBuffer,"%ArrayIteratorPrototype%":hasSymbols?getProto([][Symbol.iterator]()):undefined2,"%AsyncFromSyncIteratorPrototype%":undefined2,"%AsyncFunction%":needsEval,"%AsyncGenerator%":needsEval,"%AsyncGeneratorFunction%":needsEval,"%AsyncIteratorPrototype%":needsEval,"%Atomics%":typeof Atomics>"u"?undefined2:Atomics,"%BigInt%":typeof BigInt>"u"?undefined2:BigInt,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?undefined2:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":typeof Float32Array>"u"?undefined2:Float32Array,"%Float64Array%":typeof Float64Array>"u"?undefined2:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?undefined2:FinalizationRegistry,"%Function%":$Function,"%GeneratorFunction%":needsEval,"%Int8Array%":typeof Int8Array>"u"?undefined2:Int8Array,"%Int16Array%":typeof Int16Array>"u"?undefined2:Int16Array,"%Int32Array%":typeof Int32Array>"u"?undefined2:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":hasSymbols?getProto(getProto([][Symbol.iterator]())):undefined2,"%JSON%":typeof JSON=="object"?JSON:undefined2,"%Map%":typeof Map>"u"?undefined2:Map,"%MapIteratorPrototype%":typeof Map>"u"||!hasSymbols?undefined2:getProto(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?undefined2:Promise,"%Proxy%":typeof Proxy>"u"?undefined2:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":typeof Reflect>"u"?undefined2:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?undefined2:Set,"%SetIteratorPrototype%":typeof Set>"u"||!hasSymbols?undefined2:getProto(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?undefined2:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":hasSymbols?getProto(""[Symbol.iterator]()):undefined2,"%Symbol%":hasSymbols?Symbol:undefined2,"%SyntaxError%":$SyntaxError,"%ThrowTypeError%":ThrowTypeError,"%TypedArray%":TypedArray,"%TypeError%":$TypeError,"%Uint8Array%":typeof Uint8Array>"u"?undefined2:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?undefined2:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?undefined2:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?undefined2:Uint32Array,"%URIError%":URIError,"%WeakMap%":typeof WeakMap>"u"?undefined2:WeakMap,"%WeakRef%":typeof WeakRef>"u"?undefined2:WeakRef,"%WeakSet%":typeof WeakSet>"u"?undefined2:WeakSet},doEval=function doEval2(name2){var value22;if(name2==="%AsyncFunction%")value22=getEvalledConstructor("async function () {}");else if(name2==="%GeneratorFunction%")value22=getEvalledConstructor("function* () {}");else if(name2==="%AsyncGeneratorFunction%")value22=getEvalledConstructor("async function* () {}");else if(name2==="%AsyncGenerator%"){var fn=doEval2("%AsyncGeneratorFunction%");fn&&(value22=fn.prototype)}else if(name2==="%AsyncIteratorPrototype%"){var gen=doEval2("%AsyncGenerator%");gen&&(value22=getProto(gen.prototype))}return INTRINSICS[name2]=value22,value22},LEGACY_ALIASES={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},bind=require_function_bind(),hasOwn=require_src(),$concat=bind.call(Function.call,Array.prototype.concat),$spliceApply=bind.call(Function.apply,Array.prototype.splice),$replace=bind.call(Function.call,String.prototype.replace),$strSlice=bind.call(Function.call,String.prototype.slice),$exec=bind.call(Function.call,RegExp.prototype.exec),rePropName2=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,reEscapeChar2=/\\(\\)?/g,stringToPath2=function(string){var first=$strSlice(string,0,1),last=$strSlice(string,-1);if(first==="%"&&last!=="%")throw new $SyntaxError("invalid intrinsic syntax, expected closing `%`");if(last==="%"&&first!=="%")throw new $SyntaxError("invalid intrinsic syntax, expected opening `%`");var result2=[];return $replace(string,rePropName2,function(match,number,quote,subString){result2[result2.length]=quote?$replace(subString,reEscapeChar2,"$1"):number||match}),result2},getBaseIntrinsic=function(name2,allowMissing){var intrinsicName=name2,alias;if(hasOwn(LEGACY_ALIASES,intrinsicName)&&(alias=LEGACY_ALIASES[intrinsicName],intrinsicName="%"+alias[0]+"%"),hasOwn(INTRINSICS,intrinsicName)){var value22=INTRINSICS[intrinsicName];if(value22===needsEval&&(value22=doEval(intrinsicName)),typeof value22>"u"&&!allowMissing)throw new $TypeError("intrinsic "+name2+" exists, but is not available. Please file an issue!");return{alias,name:intrinsicName,value:value22}}throw new $SyntaxError("intrinsic "+name2+" does not exist!")};module2.exports=function(name2,allowMissing){if(typeof name2!="string"||name2.length===0)throw new $TypeError("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof allowMissing!="boolean")throw new $TypeError('"allowMissing" argument must be a boolean');if($exec(/^%?[^%]*%?$/,name2)===null)throw new $SyntaxError("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var parts=stringToPath2(name2),intrinsicBaseName=parts.length>0?parts[0]:"",intrinsic=getBaseIntrinsic("%"+intrinsicBaseName+"%",allowMissing),intrinsicRealName=intrinsic.name,value22=intrinsic.value,skipFurtherCaching=!1,alias=intrinsic.alias;alias&&(intrinsicBaseName=alias[0],$spliceApply(parts,$concat([0,1],alias)));for(var i3=1,isOwn=!0;i3<parts.length;i3+=1){var part=parts[i3],first=$strSlice(part,0,1),last=$strSlice(part,-1);if((first==='"'||first==="'"||first==="`"||last==='"'||last==="'"||last==="`")&&first!==last)throw new $SyntaxError("property names with quotes must have matching quotes");if((part==="constructor"||!isOwn)&&(skipFurtherCaching=!0),intrinsicBaseName+="."+part,intrinsicRealName="%"+intrinsicBaseName+"%",hasOwn(INTRINSICS,intrinsicRealName))value22=INTRINSICS[intrinsicRealName];else if(value22!=null){if(!(part in value22)){if(!allowMissing)throw new $TypeError("base intrinsic for "+name2+" exists, but the property is not available.");return}if($gOPD&&i3+1>=parts.length){var desc=$gOPD(value22,part);isOwn=!!desc,isOwn&&"get"in desc&&!("originalValue"in desc.get)?value22=desc.get:value22=value22[part]}else isOwn=hasOwn(value22,part),value22=value22[part];isOwn&&!skipFurtherCaching&&(INTRINSICS[intrinsicRealName]=value22)}}return value22}}}),require_call_bind=__commonJS2({"node_modules/call-bind/index.js"(exports2,module2){"use strict";var bind=require_function_bind(),GetIntrinsic=require_get_intrinsic(),$apply=GetIntrinsic("%Function.prototype.apply%"),$call=GetIntrinsic("%Function.prototype.call%"),$reflectApply=GetIntrinsic("%Reflect.apply%",!0)||bind.call($call,$apply),$gOPD=GetIntrinsic("%Object.getOwnPropertyDescriptor%",!0),$defineProperty=GetIntrinsic("%Object.defineProperty%",!0),$max=GetIntrinsic("%Math.max%");if($defineProperty)try{$defineProperty({},"a",{value:1})}catch{$defineProperty=null}module2.exports=function(originalFunction){var func=$reflectApply(bind,$call,arguments);if($gOPD&&$defineProperty){var desc=$gOPD(func,"length");desc.configurable&&$defineProperty(func,"length",{value:1+$max(0,originalFunction.length-(arguments.length-1))})}return func};var applyBind=function(){return $reflectApply(bind,$apply,arguments)};$defineProperty?$defineProperty(module2.exports,"apply",{value:applyBind}):module2.exports.apply=applyBind}}),require_callBound=__commonJS2({"node_modules/call-bind/callBound.js"(exports2,module2){"use strict";var GetIntrinsic=require_get_intrinsic(),callBind=require_call_bind(),$indexOf=callBind(GetIntrinsic("String.prototype.indexOf"));module2.exports=function(name2,allowMissing){var intrinsic=GetIntrinsic(name2,!!allowMissing);return typeof intrinsic=="function"&&$indexOf(name2,".prototype.")>-1?callBind(intrinsic):intrinsic}}}),require_shams2=__commonJS2({"node_modules/has-tostringtag/shams.js"(exports2,module2){"use strict";var hasSymbols=require_shams();module2.exports=function(){return hasSymbols()&&!!Symbol.toStringTag}}}),require_is_regex=__commonJS2({"node_modules/is-regex/index.js"(exports2,module2){"use strict";var callBound=require_callBound(),hasToStringTag=require_shams2()(),has,$exec,isRegexMarker,badStringifier;hasToStringTag&&(has=callBound("Object.prototype.hasOwnProperty"),$exec=callBound("RegExp.prototype.exec"),isRegexMarker={},throwRegexMarker=function(){throw isRegexMarker},badStringifier={toString:throwRegexMarker,valueOf:throwRegexMarker},typeof Symbol.toPrimitive=="symbol"&&(badStringifier[Symbol.toPrimitive]=throwRegexMarker));var throwRegexMarker,$toString=callBound("Object.prototype.toString"),gOPD=Object.getOwnPropertyDescriptor,regexClass="[object RegExp]";module2.exports=hasToStringTag?function(value22){if(!value22||typeof value22!="object")return!1;var descriptor=gOPD(value22,"lastIndex"),hasLastIndexDataProperty=descriptor&&has(descriptor,"value");if(!hasLastIndexDataProperty)return!1;try{$exec(value22,badStringifier)}catch(e3){return e3===isRegexMarker}}:function(value22){return!value22||typeof value22!="object"&&typeof value22!="function"?!1:$toString(value22)===regexClass}}}),require_is_function=__commonJS2({"node_modules/is-function/index.js"(exports2,module2){module2.exports=isFunction3;var toString2=Object.prototype.toString;function isFunction3(fn){if(!fn)return!1;var string=toString2.call(fn);return string==="[object Function]"||typeof fn=="function"&&string!=="[object RegExp]"||typeof window<"u"&&(fn===window.setTimeout||fn===window.alert||fn===window.confirm||fn===window.prompt)}}}),require_is_symbol=__commonJS2({"node_modules/is-symbol/index.js"(exports2,module2){"use strict";var toStr=Object.prototype.toString,hasSymbols=require_has_symbols()();hasSymbols?(symToStr=Symbol.prototype.toString,symStringRegex=/^Symbol\(.*\)$/,isSymbolObject=function(value22){return typeof value22.valueOf()!="symbol"?!1:symStringRegex.test(symToStr.call(value22))},module2.exports=function(value22){if(typeof value22=="symbol")return!0;if(toStr.call(value22)!=="[object Symbol]")return!1;try{return isSymbolObject(value22)}catch{return!1}}):module2.exports=function(value22){return!1};var symToStr,symStringRegex,isSymbolObject}}),import_is_regex=__toESM2(require_is_regex()),import_is_function=__toESM2(require_is_function()),import_is_symbol=__toESM2(require_is_symbol());function isObject(val){return val!=null&&typeof val=="object"&&Array.isArray(val)===!1}var freeGlobal=typeof global=="object"&&global&&global.Object===Object&&global,freeGlobal_default=freeGlobal,freeSelf=typeof self=="object"&&self&&self.Object===Object&&self,root2=freeGlobal_default||freeSelf||Function("return this")(),root_default=root2,Symbol2=root_default.Symbol,Symbol_default=Symbol2,objectProto=Object.prototype,hasOwnProperty=objectProto.hasOwnProperty,nativeObjectToString=objectProto.toString,symToStringTag=Symbol_default?Symbol_default.toStringTag:void 0;function getRawTag(value22){var isOwn=hasOwnProperty.call(value22,symToStringTag),tag=value22[symToStringTag];try{value22[symToStringTag]=void 0;var unmasked=!0}catch{}var result2=nativeObjectToString.call(value22);return unmasked&&(isOwn?value22[symToStringTag]=tag:delete value22[symToStringTag]),result2}var getRawTag_default=getRawTag,objectProto2=Object.prototype,nativeObjectToString2=objectProto2.toString;function objectToString(value22){return nativeObjectToString2.call(value22)}var objectToString_default=objectToString,nullTag="[object Null]",undefinedTag="[object Undefined]",symToStringTag2=Symbol_default?Symbol_default.toStringTag:void 0;function baseGetTag(value22){return value22==null?value22===void 0?undefinedTag:nullTag:symToStringTag2&&symToStringTag2 in Object(value22)?getRawTag_default(value22):objectToString_default(value22)}var baseGetTag_default=baseGetTag;function isObjectLike(value22){return value22!=null&&typeof value22=="object"}var isObjectLike_default=isObjectLike,symbolTag="[object Symbol]";function isSymbol(value22){return typeof value22=="symbol"||isObjectLike_default(value22)&&baseGetTag_default(value22)==symbolTag}var isSymbol_default=isSymbol;function arrayMap(array2,iteratee){for(var index=-1,length=array2==null?0:array2.length,result2=Array(length);++index<length;)result2[index]=iteratee(array2[index],index,array2);return result2}var arrayMap_default=arrayMap,isArray=Array.isArray,isArray_default=isArray,INFINITY=1/0,symbolProto=Symbol_default?Symbol_default.prototype:void 0,symbolToString=symbolProto?symbolProto.toString:void 0;function baseToString(value22){if(typeof value22=="string")return value22;if(isArray_default(value22))return arrayMap_default(value22,baseToString)+"";if(isSymbol_default(value22))return symbolToString?symbolToString.call(value22):"";var result2=value22+"";return result2=="0"&&1/value22==-INFINITY?"-0":result2}var baseToString_default=baseToString;function isObject2(value22){var type=typeof value22;return value22!=null&&(type=="object"||type=="function")}var isObject_default=isObject2,asyncTag="[object AsyncFunction]",funcTag="[object Function]",genTag="[object GeneratorFunction]",proxyTag="[object Proxy]";function isFunction(value22){if(!isObject_default(value22))return!1;var tag=baseGetTag_default(value22);return tag==funcTag||tag==genTag||tag==asyncTag||tag==proxyTag}var isFunction_default=isFunction,coreJsData=root_default["__core-js_shared__"],coreJsData_default=coreJsData,maskSrcKey=function(){var uid=/[^.]+$/.exec(coreJsData_default&&coreJsData_default.keys&&coreJsData_default.keys.IE_PROTO||"");return uid?"Symbol(src)_1."+uid:""}();function isMasked(func){return!!maskSrcKey&&maskSrcKey in func}var isMasked_default=isMasked,funcProto=Function.prototype,funcToString=funcProto.toString;function toSource(func){if(func!=null){try{return funcToString.call(func)}catch{}try{return func+""}catch{}}return""}var toSource_default=toSource,reRegExpChar=/[\\^$.*+?()[\]{}|]/g,reIsHostCtor=/^\[object .+?Constructor\]$/,funcProto2=Function.prototype,objectProto3=Object.prototype,funcToString2=funcProto2.toString,hasOwnProperty2=objectProto3.hasOwnProperty,reIsNative=RegExp("^"+funcToString2.call(hasOwnProperty2).replace(reRegExpChar,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function baseIsNative(value22){if(!isObject_default(value22)||isMasked_default(value22))return!1;var pattern=isFunction_default(value22)?reIsNative:reIsHostCtor;return pattern.test(toSource_default(value22))}var baseIsNative_default=baseIsNative;function getValue(object2,key2){return object2?.[key2]}var getValue_default=getValue;function getNative(object2,key2){var value22=getValue_default(object2,key2);return baseIsNative_default(value22)?value22:void 0}var getNative_default=getNative;function eq(value22,other){return value22===other||value22!==value22&&other!==other}var eq_default=eq,reIsDeepProp=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,reIsPlainProp=/^\w*$/;function isKey(value22,object2){if(isArray_default(value22))return!1;var type=typeof value22;return type=="number"||type=="symbol"||type=="boolean"||value22==null||isSymbol_default(value22)?!0:reIsPlainProp.test(value22)||!reIsDeepProp.test(value22)||object2!=null&&value22 in Object(object2)}var isKey_default=isKey,nativeCreate=getNative_default(Object,"create"),nativeCreate_default=nativeCreate;function hashClear(){this.__data__=nativeCreate_default?nativeCreate_default(null):{},this.size=0}var hashClear_default=hashClear;function hashDelete(key2){var result2=this.has(key2)&&delete this.__data__[key2];return this.size-=result2?1:0,result2}var hashDelete_default=hashDelete,HASH_UNDEFINED="__lodash_hash_undefined__",objectProto4=Object.prototype,hasOwnProperty3=objectProto4.hasOwnProperty;function hashGet(key2){var data=this.__data__;if(nativeCreate_default){var result2=data[key2];return result2===HASH_UNDEFINED?void 0:result2}return hasOwnProperty3.call(data,key2)?data[key2]:void 0}var hashGet_default=hashGet,objectProto5=Object.prototype,hasOwnProperty4=objectProto5.hasOwnProperty;function hashHas(key2){var data=this.__data__;return nativeCreate_default?data[key2]!==void 0:hasOwnProperty4.call(data,key2)}var hashHas_default=hashHas,HASH_UNDEFINED2="__lodash_hash_undefined__";function hashSet(key2,value22){var data=this.__data__;return this.size+=this.has(key2)?0:1,data[key2]=nativeCreate_default&&value22===void 0?HASH_UNDEFINED2:value22,this}var hashSet_default=hashSet;function Hash(entries){var index=-1,length=entries==null?0:entries.length;for(this.clear();++index<length;){var entry=entries[index];this.set(entry[0],entry[1])}}Hash.prototype.clear=hashClear_default;Hash.prototype.delete=hashDelete_default;Hash.prototype.get=hashGet_default;Hash.prototype.has=hashHas_default;Hash.prototype.set=hashSet_default;var Hash_default=Hash;function listCacheClear(){this.__data__=[],this.size=0}var listCacheClear_default=listCacheClear;function assocIndexOf(array2,key2){for(var length=array2.length;length--;)if(eq_default(array2[length][0],key2))return length;return-1}var assocIndexOf_default=assocIndexOf,arrayProto=Array.prototype,splice=arrayProto.splice;function listCacheDelete(key2){var data=this.__data__,index=assocIndexOf_default(data,key2);if(index<0)return!1;var lastIndex=data.length-1;return index==lastIndex?data.pop():splice.call(data,index,1),--this.size,!0}var listCacheDelete_default=listCacheDelete;function listCacheGet(key2){var data=this.__data__,index=assocIndexOf_default(data,key2);return index<0?void 0:data[index][1]}var listCacheGet_default=listCacheGet;function listCacheHas(key2){return assocIndexOf_default(this.__data__,key2)>-1}var listCacheHas_default=listCacheHas;function listCacheSet(key2,value22){var data=this.__data__,index=assocIndexOf_default(data,key2);return index<0?(++this.size,data.push([key2,value22])):data[index][1]=value22,this}var listCacheSet_default=listCacheSet;function ListCache(entries){var index=-1,length=entries==null?0:entries.length;for(this.clear();++index<length;){var entry=entries[index];this.set(entry[0],entry[1])}}ListCache.prototype.clear=listCacheClear_default;ListCache.prototype.delete=listCacheDelete_default;ListCache.prototype.get=listCacheGet_default;ListCache.prototype.has=listCacheHas_default;ListCache.prototype.set=listCacheSet_default;var ListCache_default=ListCache,Map2=getNative_default(root_default,"Map"),Map_default=Map2;function mapCacheClear(){this.size=0,this.__data__={hash:new Hash_default,map:new(Map_default||ListCache_default),string:new Hash_default}}var mapCacheClear_default=mapCacheClear;function isKeyable(value22){var type=typeof value22;return type=="string"||type=="number"||type=="symbol"||type=="boolean"?value22!=="__proto__":value22===null}var isKeyable_default=isKeyable;function getMapData(map,key2){var data=map.__data__;return isKeyable_default(key2)?data[typeof key2=="string"?"string":"hash"]:data.map}var getMapData_default=getMapData;function mapCacheDelete(key2){var result2=getMapData_default(this,key2).delete(key2);return this.size-=result2?1:0,result2}var mapCacheDelete_default=mapCacheDelete;function mapCacheGet(key2){return getMapData_default(this,key2).get(key2)}var mapCacheGet_default=mapCacheGet;function mapCacheHas(key2){return getMapData_default(this,key2).has(key2)}var mapCacheHas_default=mapCacheHas;function mapCacheSet(key2,value22){var data=getMapData_default(this,key2),size=data.size;return data.set(key2,value22),this.size+=data.size==size?0:1,this}var mapCacheSet_default=mapCacheSet;function MapCache(entries){var index=-1,length=entries==null?0:entries.length;for(this.clear();++index<length;){var entry=entries[index];this.set(entry[0],entry[1])}}MapCache.prototype.clear=mapCacheClear_default;MapCache.prototype.delete=mapCacheDelete_default;MapCache.prototype.get=mapCacheGet_default;MapCache.prototype.has=mapCacheHas_default;MapCache.prototype.set=mapCacheSet_default;var MapCache_default=MapCache,FUNC_ERROR_TEXT="Expected a function";function memoize3(func,resolver){if(typeof func!="function"||resolver!=null&&typeof resolver!="function")throw new TypeError(FUNC_ERROR_TEXT);var memoized=function(){var args2=arguments,key2=resolver?resolver.apply(this,args2):args2[0],cache=memoized.cache;if(cache.has(key2))return cache.get(key2);var result2=func.apply(this,args2);return memoized.cache=cache.set(key2,result2)||cache,result2};return memoized.cache=new(memoize3.Cache||MapCache_default),memoized}memoize3.Cache=MapCache_default;var memoize_default=memoize3,MAX_MEMOIZE_SIZE=500;function memoizeCapped(func){var result2=memoize_default(func,function(key2){return cache.size===MAX_MEMOIZE_SIZE&&cache.clear(),key2}),cache=result2.cache;return result2}var memoizeCapped_default=memoizeCapped,rePropName=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,reEscapeChar=/\\(\\)?/g,stringToPath=memoizeCapped_default(function(string){var result2=[];return string.charCodeAt(0)===46&&result2.push(""),string.replace(rePropName,function(match,number,quote,subString){result2.push(quote?subString.replace(reEscapeChar,"$1"):number||match)}),result2}),stringToPath_default=stringToPath;function toString(value22){return value22==null?"":baseToString_default(value22)}var toString_default=toString;function castPath(value22,object2){return isArray_default(value22)?value22:isKey_default(value22,object2)?[value22]:stringToPath_default(toString_default(value22))}var castPath_default=castPath,INFINITY2=1/0;function toKey(value22){if(typeof value22=="string"||isSymbol_default(value22))return value22;var result2=value22+"";return result2=="0"&&1/value22==-INFINITY2?"-0":result2}var toKey_default=toKey;function baseGet(object2,path){path=castPath_default(path,object2);for(var index=0,length=path.length;object2!=null&&index<length;)object2=object2[toKey_default(path[index++])];return index&&index==length?object2:void 0}var baseGet_default=baseGet;function get(object2,path,defaultValue){var result2=object2==null?void 0:baseGet_default(object2,path);return result2===void 0?defaultValue:result2}var get_default=get,isObject3=isObject,removeCodeComments=code=>{let inQuoteChar=null,inBlockComment=!1,inLineComment=!1,inRegexLiteral=!1,newCode="";if(code.indexOf("//")>=0||code.indexOf("/*")>=0)for(let i3=0;i3<code.length;i3+=1)!inQuoteChar&&!inBlockComment&&!inLineComment&&!inRegexLiteral?code[i3]==='"'||code[i3]==="'"||code[i3]==="`"?inQuoteChar=code[i3]:code[i3]==="/"&&code[i3+1]==="*"?inBlockComment=!0:code[i3]==="/"&&code[i3+1]==="/"?inLineComment=!0:code[i3]==="/"&&code[i3+1]!=="/"&&(inRegexLiteral=!0):(inQuoteChar&&(code[i3]===inQuoteChar&&code[i3-1]!=="\\"||code[i3]===`
`&&inQuoteChar!=="`")&&(inQuoteChar=null),inRegexLiteral&&(code[i3]==="/"&&code[i3-1]!=="\\"||code[i3]===`
`)&&(inRegexLiteral=!1),inBlockComment&&code[i3-1]==="/"&&code[i3-2]==="*"&&(inBlockComment=!1),inLineComment&&code[i3]===`
`&&(inLineComment=!1)),!inBlockComment&&!inLineComment&&(newCode+=code[i3]);else newCode=code;return newCode},cleanCode=(0,import_memoizerific2.default)(1e4)(code=>removeCodeComments(code).replace(/\n\s*/g,"").trim()),convertShorthandMethods=function(key2,stringified){let fnHead=stringified.slice(0,stringified.indexOf("{")),fnBody=stringified.slice(stringified.indexOf("{"));if(fnHead.includes("=>")||fnHead.includes("function"))return stringified;let modifiedHead=fnHead;return modifiedHead=modifiedHead.replace(key2,"function"),modifiedHead+fnBody},dateFormat=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/,isJSON=input=>input.match(/^[\[\{\"\}].*[\]\}\"]$/);function convertUnconventionalData(data){if(!isObject3(data))return data;let result2=data,wasMutated=!1;return typeof Event<"u"&&data instanceof Event&&(result2=extractEventHiddenProperties(result2),wasMutated=!0),result2=Object.keys(result2).reduce((acc,key2)=>{try{result2[key2]&&result2[key2].toJSON,acc[key2]=result2[key2]}catch{wasMutated=!0}return acc},{}),wasMutated?result2:data}var replacer=function(options2){let objects,map,stack,keys;return function(key2,value22){try{if(key2==="")return keys=[],objects=new Map([[value22,"[]"]]),map=new Map,stack=[],value22;let origin=map.get(this)||this;for(;stack.length&&origin!==stack[0];)stack.shift(),keys.pop();if(typeof value22=="boolean")return value22;if(value22===void 0)return options2.allowUndefined?"_undefined_":void 0;if(value22===null)return null;if(typeof value22=="number")return value22===-1/0?"_-Infinity_":value22===1/0?"_Infinity_":Number.isNaN(value22)?"_NaN_":value22;if(typeof value22=="bigint")return`_bigint_${value22.toString()}`;if(typeof value22=="string")return dateFormat.test(value22)?options2.allowDate?`_date_${value22}`:void 0:value22;if((0,import_is_regex.default)(value22))return options2.allowRegExp?`_regexp_${value22.flags}|${value22.source}`:void 0;if((0,import_is_function.default)(value22)){if(!options2.allowFunction)return;let{name:name2}=value22,stringified=value22.toString();return stringified.match(/(\[native code\]|WEBPACK_IMPORTED_MODULE|__webpack_exports__|__webpack_require__)/)?`_function_${name2}|${(()=>{}).toString()}`:`_function_${name2}|${cleanCode(convertShorthandMethods(key2,stringified))}`}if((0,import_is_symbol.default)(value22)){if(!options2.allowSymbol)return;let globalRegistryKey=Symbol.keyFor(value22);return globalRegistryKey!==void 0?`_gsymbol_${globalRegistryKey}`:`_symbol_${value22.toString().slice(7,-1)}`}if(stack.length>=options2.maxDepth)return Array.isArray(value22)?`[Array(${value22.length})]`:"[Object]";if(value22===this)return`_duplicate_${JSON.stringify(keys)}`;if(value22 instanceof Error&&options2.allowError)return{__isConvertedError__:!0,errorProperties:{...value22.cause?{cause:value22.cause}:{},...value22,name:value22.name,message:value22.message,stack:value22.stack,"_constructor-name_":value22.constructor.name}};if(value22.constructor&&value22.constructor.name&&value22.constructor.name!=="Object"&&!Array.isArray(value22)&&!options2.allowClass)return;let found=objects.get(value22);if(!found){let converted=Array.isArray(value22)?value22:convertUnconventionalData(value22);if(value22.constructor&&value22.constructor.name&&value22.constructor.name!=="Object"&&!Array.isArray(value22)&&options2.allowClass)try{Object.assign(converted,{"_constructor-name_":value22.constructor.name})}catch{}return keys.push(key2),stack.unshift(converted),objects.set(value22,JSON.stringify(keys)),value22!==converted&&map.set(value22,converted),converted}return`_duplicate_${found}`}catch{return}}},reviver2=function reviver(options){let refs=[],root;return function revive(key,value){if(key===""&&(root=value,refs.forEach(({target,container,replacement})=>{let replacementArr=isJSON(replacement)?JSON.parse(replacement):replacement.split(".");replacementArr.length===0?container[target]=root:container[target]=get_default(root,replacementArr)})),key==="_constructor-name_")return value;if(isObject3(value)&&value.__isConvertedError__){let{message,...properties}=value.errorProperties,error=new Error(message);return Object.assign(error,properties),error}if(isObject3(value)&&value["_constructor-name_"]&&options.allowFunction){let name2=value["_constructor-name_"];if(name2!=="Object"){let Fn=new Function(`return function ${name2.replace(/[^a-zA-Z0-9$_]+/g,"")}(){}`)();Object.setPrototypeOf(value,new Fn)}return delete value["_constructor-name_"],value}if(typeof value=="string"&&value.startsWith("_function_")&&options.allowFunction){let[,name,source]=value.match(/_function_([^|]*)\|(.*)/)||[],sourceSanitized=source.replace(/[(\(\))|\\| |\]|`]*$/,"");if(!options.lazyEval)return eval(`(${sourceSanitized})`);let result=(...args)=>{let f=eval(`(${sourceSanitized})`);return f(...args)};return Object.defineProperty(result,"toString",{value:()=>sourceSanitized}),Object.defineProperty(result,"name",{value:name}),result}if(typeof value=="string"&&value.startsWith("_regexp_")&&options.allowRegExp){let[,flags,source2]=value.match(/_regexp_([^|]*)\|(.*)/)||[];return new RegExp(source2,flags)}return typeof value=="string"&&value.startsWith("_date_")&&options.allowDate?new Date(value.replace("_date_","")):typeof value=="string"&&value.startsWith("_duplicate_")?(refs.push({target:key,container:this,replacement:value.replace(/^_duplicate_/,"")}),null):typeof value=="string"&&value.startsWith("_symbol_")&&options.allowSymbol?Symbol(value.replace("_symbol_","")):typeof value=="string"&&value.startsWith("_gsymbol_")&&options.allowSymbol?Symbol.for(value.replace("_gsymbol_","")):typeof value=="string"&&value==="_-Infinity_"?-1/0:typeof value=="string"&&value==="_Infinity_"?1/0:typeof value=="string"&&value==="_NaN_"?NaN:typeof value=="string"&&value.startsWith("_bigint_")&&typeof BigInt=="function"?BigInt(value.replace("_bigint_","")):value}},defaultOptions={maxDepth:10,space:void 0,allowFunction:!0,allowRegExp:!0,allowDate:!0,allowClass:!0,allowError:!0,allowUndefined:!0,allowSymbol:!0,lazyEval:!0},stringify=(data,options2={})=>{let mergedOptions={...defaultOptions,...options2};return JSON.stringify(convertUnconventionalData(data),replacer(mergedOptions),options2.space)},mutator=()=>{let mutated=new Map;return function mutateUndefined(value22){isObject3(value22)&&Object.entries(value22).forEach(([k3,v3])=>{v3==="_undefined_"?value22[k3]=void 0:mutated.get(v3)||(mutated.set(v3,!0),mutateUndefined(v3))}),Array.isArray(value22)&&value22.forEach((v3,index)=>{v3==="_undefined_"?(mutated.set(v3,!0),value22[index]=void 0):mutated.get(v3)||(mutated.set(v3,!0),mutateUndefined(v3))})}},parse4=(data,options2={})=>{let mergedOptions={...defaultOptions,...options2},result2=JSON.parse(data,reviver2(mergedOptions));return mutator()(result2),result2};function argsHash(args2){return stringify(args2,{allowFunction:!1})}var SourceContext=(0,import_react41.createContext)({sources:{}}),UNKNOWN_ARGS_HASH="--unknown--",SourceContainer=({children,channel})=>{let[sources,setSources]=(0,import_react41.useState)({});return(0,import_react41.useEffect)(()=>{let handleSnippetRendered=(idOrEvent,inputSource=null,inputFormat=!1)=>{let{id:id2,args:args2=void 0,source:source2,format:format3}=typeof idOrEvent=="string"?{id:idOrEvent,source:inputSource,format:inputFormat}:idOrEvent,hash=args2?argsHash(args2):UNKNOWN_ARGS_HASH;setSources(current=>({...current,[id2]:{...current[id2],[hash]:{code:source2,format:format3}}}))};return channel.on(import_docs_tools.SNIPPET_RENDERED,handleSnippetRendered),()=>channel.off(import_docs_tools.SNIPPET_RENDERED,handleSnippetRendered)},[]),import_react41.default.createElement(SourceContext.Provider,{value:{sources}},children)};var getStorySource=(storyId,args2,sourceContext)=>{let{sources}=sourceContext,sourceMap=sources?.[storyId];return sourceMap?.[argsHash(args2)]||sourceMap?.[UNKNOWN_ARGS_HASH]||{code:""}},getSnippet=({snippet,storyContext,typeFromProps,transformFromProps})=>{let{__isArgsStory:isArgsStory}=storyContext.parameters,sourceParameters=storyContext.parameters.docs?.source||{},type=typeFromProps||sourceParameters.type||import_docs_tools2.SourceType.AUTO;if(sourceParameters.code!==void 0)return sourceParameters.code;let code=type===import_docs_tools2.SourceType.DYNAMIC||type===import_docs_tools2.SourceType.AUTO&&snippet&&isArgsStory?snippet:sourceParameters.originalSource||"";return(transformFromProps??sourceParameters.transform)?.(code,storyContext)||code},useSourceProps=(props,docsContext,sourceContext)=>{let story,{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");if(of)story=docsContext.resolveOf(of,["story"]).story;else try{story=docsContext.storyById()}catch{}let sourceParameters=story?.parameters?.docs?.source||{},{code}=props,format3=props.format??sourceParameters.format,language=props.language??sourceParameters.language??"jsx",dark=props.dark??sourceParameters.dark??!1;if(!code&&!story)return{error:"Oh no! The source is not available."};if(code)return{code,format:format3,language,dark};let storyContext=docsContext.getStoryContext(story),argsForSource=props.__forceInitialArgs?storyContext.initialArgs:storyContext.unmappedArgs,source2=getStorySource(story.id,argsForSource,sourceContext);return format3=source2.format??story.parameters.docs?.source?.format??!1,{code:getSnippet({snippet:source2.code,storyContext:{...storyContext,args:argsForSource},typeFromProps:props.type,transformFromProps:props.transform}),format:format3,language,dark}},Source2=props=>{let sourceContext=(0,import_react42.useContext)(SourceContext),docsContext=(0,import_react42.useContext)(DocsContext),sourceProps=useSourceProps(props,docsContext,sourceContext);return import_react42.default.createElement(Source,{...sourceProps})};var import_react44=__toESM(require("react"));var import_react43=require("react");function useStory(storyId,context){let stories=useStories([storyId],context);return stories&&stories[0]}function useStories(storyIds,context){let[storiesById,setStories]=(0,import_react43.useState)({});return(0,import_react43.useEffect)(()=>{Promise.all(storyIds.map(async storyId=>{let story=await context.loadStory(storyId);setStories(current=>current[storyId]===story?current:{...current,[storyId]:story})}))}),storyIds.map(storyId=>{if(storiesById[storyId])return storiesById[storyId];try{return context.storyById(storyId)}catch{return null}})}var getStoryId2=(props,context)=>{let{of,meta}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");return meta&&context.referenceMeta(meta,!1),context.resolveOf(of||"story",["story"]).story.id},getStoryProps=(props,story,context)=>{let{parameters={}}=story||{},{docs={}}=parameters,storyParameters=docs.story||{};if(docs.disable)return null;if(props.inline??storyParameters.inline??!1){let height2=props.height??storyParameters.height,autoplay=props.autoplay??storyParameters.autoplay??!1;return{story,inline:!0,height:height2,autoplay,forceInitialArgs:!!props.__forceInitialArgs,primary:!!props.__primary,renderStoryToElement:context.renderStoryToElement}}let height=props.height??storyParameters.height??storyParameters.iframeHeight??"100px";return{story,inline:!1,height,primary:!!props.__primary}},Story2=(props={__forceInitialArgs:!1,__primary:!1})=>{let context=(0,import_react44.useContext)(DocsContext),storyId=getStoryId2(props,context),story=useStory(storyId,context);if(!story)return import_react44.default.createElement(StorySkeleton,null);let storyProps=getStoryProps(props,story,context);return storyProps?import_react44.default.createElement(Story,{...storyProps}):null};var Canvas=props=>{let docsContext=(0,import_react45.useContext)(DocsContext),sourceContext=(0,import_react45.useContext)(SourceContext),{of,source:source2}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let{story}=useOf(of||"story",["story"]),sourceProps=useSourceProps({...source2,...of&&{of}},docsContext,sourceContext),layout=props.layout??story.parameters.layout??story.parameters.docs?.canvas?.layout??"padded",withToolbar=props.withToolbar??story.parameters.docs?.canvas?.withToolbar??!1,additionalActions=props.additionalActions??story.parameters.docs?.canvas?.additionalActions,sourceState=props.sourceState??story.parameters.docs?.canvas?.sourceState??"hidden",className=props.className??story.parameters.docs?.canvas?.className;return import_react45.default.createElement(Preview,{withSource:sourceState==="none"?void 0:sourceProps,isExpanded:sourceState==="shown",withToolbar,additionalActions,className,layout},import_react45.default.createElement(Story2,{of:of||story.moduleExport,meta:props.meta,...props.story}))};var import_react48=__toESM(require("react")),import_preview_api2=require("storybook/internal/preview-api");var import_react46=require("react"),import_core_events=require("storybook/internal/core-events"),useArgs=(story,context)=>{let result2=useArgsIfDefined(story,context);if(!result2)throw new Error("No result when story was defined");return result2},useArgsIfDefined=(story,context)=>{let storyContext=story?context.getStoryContext(story):{args:{}},{id:storyId}=story||{id:"none"},[args2,setArgs]=(0,import_react46.useState)(storyContext.args);(0,import_react46.useEffect)(()=>{let onArgsUpdated=changed=>{changed.storyId===storyId&&setArgs(changed.args)};return context.channel.on(import_core_events.STORY_ARGS_UPDATED,onArgsUpdated),()=>context.channel.off(import_core_events.STORY_ARGS_UPDATED,onArgsUpdated)},[storyId,context.channel]);let updateArgs=(0,import_react46.useCallback)(updatedArgs=>context.channel.emit(import_core_events.UPDATE_STORY_ARGS,{storyId,updatedArgs}),[storyId,context.channel]),resetArgs=(0,import_react46.useCallback)(argNames=>context.channel.emit(import_core_events.RESET_STORY_ARGS,{storyId,argNames}),[storyId,context.channel]);return story&&[args2,updateArgs,resetArgs]};var import_react47=require("react"),import_core_events2=require("storybook/internal/core-events"),useGlobals=(story,context)=>{let storyContext=context.getStoryContext(story),[globals,setGlobals]=(0,import_react47.useState)(storyContext.globals);return(0,import_react47.useEffect)(()=>{let onGlobalsUpdated=changed=>{setGlobals(changed.globals)};return context.channel.on(import_core_events2.GLOBALS_UPDATED,onGlobalsUpdated),()=>context.channel.off(import_core_events2.GLOBALS_UPDATED,onGlobalsUpdated)},[context.channel]),[globals]};function extractComponentArgTypes2(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error("Args unsupported. See Args documentation for your framework.");return extractArgTypes(component)}var Controls3=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let context=(0,import_react48.useContext)(DocsContext),{story}=context.resolveOf(of||"story",["story"]),{parameters,argTypes,component,subcomponents}=story,controlsParameters=parameters.docs?.controls||{},include=props.include??controlsParameters.include,exclude=props.exclude??controlsParameters.exclude,sort=props.sort??controlsParameters.sort,[args2,updateArgs,resetArgs]=useArgs(story,context),[globals]=useGlobals(story,context),filteredArgTypes=(0,import_preview_api2.filterArgTypes)(argTypes,include,exclude);if(!(!!subcomponents&&Object.keys(subcomponents).length>0))return Object.keys(filteredArgTypes).length>0||Object.keys(args2).length>0?import_react48.default.createElement(ArgsTable,{rows:filteredArgTypes,sort,args:args2,globals,updateArgs,resetArgs}):null;let mainComponentName=getComponentName(component),subcomponentTabs=Object.fromEntries(Object.entries(subcomponents).map(([key2,comp])=>[key2,{rows:(0,import_preview_api2.filterArgTypes)(extractComponentArgTypes2(comp,parameters),include,exclude),sort}])),tabs={[mainComponentName]:{rows:filteredArgTypes,sort},...subcomponentTabs};return import_react48.default.createElement(TabbedArgsTable,{tabs,sort,args:args2,globals,updateArgs,resetArgs})};var import_react51=__toESM(require("react"));var import_react50=__toESM(require("react"));var import_ts_dedent=require("ts-dedent");var import_react49=__toESM(require("react")),import_components28=require("storybook/internal/components"),import_core_events3=require("storybook/internal/core-events"),import_theming28=require("storybook/internal/theming"),import_icons9=require("@storybook/icons");var{document:document2}=globalThis,assertIsFn=val=>{if(typeof val!="function")throw new Error(`Expected story function, got: ${val}`);return val},AddContext=props=>{let{children,...rest}=props,parentContext=import_react49.default.useContext(DocsContext);return import_react49.default.createElement(DocsContext.Provider,{value:{...parentContext,...rest}},children)},CodeOrSourceMdx=({className,children,...rest})=>{if(typeof className!="string"&&(typeof children!="string"||!children.match(/[\n\r]/g)))return import_react49.default.createElement(import_components28.Code,null,children);let language=className&&className.split("-");return import_react49.default.createElement(Source,{language:language&&language[1]||"text",format:!1,code:children,...rest})};function navigate(context,url){context.channel.emit(import_core_events3.NAVIGATE_URL,url)}var A2=import_components28.components.a,AnchorInPage=({hash,children})=>{let context=(0,import_react49.useContext)(DocsContext);return import_react49.default.createElement(A2,{href:hash,target:"_self",onClick:event=>{let id2=hash.substring(1);document2.getElementById(id2)&&navigate(context,hash)}},children)},AnchorMdx=props=>{let{href,target,children,...rest}=props,context=(0,import_react49.useContext)(DocsContext);return!href||target==="_blank"||/^https?:\/\//.test(href)?import_react49.default.createElement(A2,{...props}):href.startsWith("#")?import_react49.default.createElement(AnchorInPage,{hash:href},children):import_react49.default.createElement(A2,{href,onClick:event=>{event.button===0&&!event.altKey&&!event.ctrlKey&&!event.metaKey&&!event.shiftKey&&(event.preventDefault(),navigate(context,event.currentTarget.getAttribute("href")))},target,...rest},children)},SUPPORTED_MDX_HEADERS=["h1","h2","h3","h4","h5","h6"],OcticonHeaders=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:(0,import_theming28.styled)(headerType)({"& svg":{position:"relative",top:"-0.1em",visibility:"hidden"},"&:hover svg":{visibility:"visible"}})}),{}),OcticonAnchor=import_theming28.styled.a(()=>({float:"left",lineHeight:"inherit",paddingRight:"10px",marginLeft:"-24px",color:"inherit"})),HeaderWithOcticonAnchor=({as,id:id2,children,...rest})=>{let context=(0,import_react49.useContext)(DocsContext),OcticonHeader=OcticonHeaders[as],hash=`#${id2}`;return import_react49.default.createElement(OcticonHeader,{id:id2,...rest},import_react49.default.createElement(OcticonAnchor,{"aria-hidden":"true",href:hash,tabIndex:-1,target:"_self",onClick:event=>{document2.getElementById(id2)&&navigate(context,hash)}},import_react49.default.createElement(import_icons9.LinkIcon,null)),children)},HeaderMdx=props=>{let{as,id:id2,children,...rest}=props;if(id2)return import_react49.default.createElement(HeaderWithOcticonAnchor,{as,id:id2,...rest},children);let Component4=as,{as:omittedAs,...withoutAs}=props;return import_react49.default.createElement(Component4,{...(0,import_components28.nameSpaceClassNames)(withoutAs,as)})},HeadersMdx=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:props=>import_react49.default.createElement(HeaderMdx,{as:headerType,...props})}),{});var Markdown=props=>{if(!props.children)return null;if(typeof props.children!="string")throw new Error(import_ts_dedent.dedent`The Markdown block only accepts children as a single string, but children were of type: '${typeof props.children}'
        This is often caused by not wrapping the child in a template string.
        
        This is invalid:
        <Markdown>
          # Some heading
          A paragraph
        </Markdown>

        Instead do:
        <Markdown>
        {\`
          # Some heading
          A paragraph
        \`}
        </Markdown>
      `);return import_react50.default.createElement(index_module_default,{...props,options:{forceBlock:!0,overrides:{code:CodeOrSourceMdx,a:AnchorMdx,...HeadersMdx,...props?.options?.overrides},...props?.options}})};var DescriptionType=(DescriptionType2=>(DescriptionType2.INFO="info",DescriptionType2.NOTES="notes",DescriptionType2.DOCGEN="docgen",DescriptionType2.AUTO="auto",DescriptionType2))(DescriptionType||{}),getDescriptionFromResolvedOf=resolvedOf=>{switch(resolvedOf.type){case"story":return resolvedOf.story.parameters.docs?.description?.story||null;case"meta":{let{parameters,component}=resolvedOf.preparedMeta,metaDescription=parameters.docs?.description?.component;return metaDescription||parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}case"component":{let{component,projectAnnotations:{parameters}}=resolvedOf;return parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}default:throw new Error(`Unrecognized module type resolved from 'useOf', got: ${resolvedOf.type}`)}},DescriptionContainer=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let resolvedOf=useOf(of||"meta"),markdown=getDescriptionFromResolvedOf(resolvedOf);return markdown?import_react51.default.createElement(Markdown,null,markdown):null};var import_react62=__toESM(require("react"));var import_react53=__toESM(require("react")),import_theming30=require("storybook/internal/theming");var import_react52=__toESM(require("react")),import_core_events4=require("storybook/internal/core-events"),import_theming29=require("storybook/internal/theming"),tocbot=__toESM(require_js()),Wrapper11=import_theming29.styled.div(({theme})=>({width:"10rem","@media (max-width: 768px)":{display:"none"}})),Content=import_theming29.styled.div(({theme})=>({position:"fixed",bottom:0,top:0,width:"10rem",paddingTop:"4rem",paddingBottom:"2rem",overflowY:"auto",fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch","& *":{boxSizing:"border-box"},"& > .toc-wrapper > .toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`}}},"& .toc-list-item":{position:"relative",listStyleType:"none",marginLeft:20,paddingTop:3,paddingBottom:3},"& .toc-list-item::before":{content:'""',position:"absolute",height:"100%",top:0,left:0,transform:"translateX(calc(-2px - 20px))",borderLeft:`solid 2px ${theme.color.mediumdark}`,opacity:0,transition:"opacity 0.2s"},"& .toc-list-item.is-active-li::before":{opacity:1},"& .toc-list-item > a":{color:theme.color.defaultText,textDecoration:"none"},"& .toc-list-item.is-active-li > a":{fontWeight:600,color:theme.color.secondary,textDecoration:"none"}})),Heading=import_theming29.styled.p(({theme})=>({fontWeight:600,fontSize:"0.875em",color:theme.textColor,textTransform:"uppercase",marginBottom:10})),OptionalTitle=({title})=>title===null?null:typeof title=="string"?import_react52.default.createElement(Heading,null,title):title,TableOfContents=({title,disable,headingSelector,contentsSelector,ignoreSelector,unsafeTocbotOptions,channel})=>((0,import_react52.useEffect)(()=>{if(disable)return()=>{};let configuration={tocSelector:".toc-wrapper",contentSelector:contentsSelector??".sbdocs-content",headingSelector:headingSelector??"h3",ignoreSelector:ignoreSelector??".docs-story *, .skip-toc",headingsOffset:40,scrollSmoothOffset:-40,orderedList:!1,onClick:e3=>{if(e3.preventDefault(),e3.currentTarget instanceof HTMLAnchorElement){let[,headerId]=e3.currentTarget.href.split("#");headerId&&channel.emit(import_core_events4.NAVIGATE_URL,`#${headerId}`)}},...unsafeTocbotOptions},timeout=setTimeout(()=>tocbot.init(configuration),100);return()=>{clearTimeout(timeout),tocbot.destroy()}},[channel,disable,ignoreSelector,contentsSelector,headingSelector,unsafeTocbotOptions]),import_react52.default.createElement(import_react52.default.Fragment,null,import_react52.default.createElement(Wrapper11,null,disable?null:import_react52.default.createElement(Content,null,import_react52.default.createElement(OptionalTitle,{title:title||null}),import_react52.default.createElement("div",{className:"toc-wrapper"})))));var{document:document3,window:globalWindow3}=globalThis,DocsContainer=({context,theme,children})=>{let toc;try{toc=context.resolveOf("meta",["meta"]).preparedMeta.parameters?.docs?.toc}catch{toc=context?.projectAnnotations?.parameters?.docs?.toc}return(0,import_react53.useEffect)(()=>{let url;try{if(url=new URL(globalWindow3.parent.location.toString()),url.hash){let element=document3.getElementById(decodeURIComponent(url.hash.substring(1)));element&&setTimeout(()=>{scrollToElement(element)},200)}}catch{}}),import_react53.default.createElement(DocsContext.Provider,{value:context},import_react53.default.createElement(SourceContainer,{channel:context.channel},import_react53.default.createElement(import_theming30.ThemeProvider,{theme:(0,import_theming30.ensure)(theme)},import_react53.default.createElement(DocsPageWrapper,{toc:toc?import_react53.default.createElement(TableOfContents,{className:"sbdocs sbdocs-toc--custom",channel:context.channel,...toc}):null},children))))};var import_react61=__toESM(require("react"));var import_react57=__toESM(require("react"));var import_react56=__toESM(require("react"));var import_react55=__toESM(require("react")),import_components32=require("storybook/internal/components");var import_react54=__toESM(require("react")),import_components31=require("storybook/internal/components");var regex=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g;var own=Object.hasOwnProperty,BananaSlug=class{constructor(){this.occurrences,this.reset()}slug(value3,maintainCase){let self2=this,result2=slug(value3,maintainCase===!0),originalSlug=result2;for(;own.call(self2.occurrences,result2);)self2.occurrences[originalSlug]++,result2=originalSlug+"-"+self2.occurrences[originalSlug];return self2.occurrences[result2]=0,result2}reset(){this.occurrences=Object.create(null)}};function slug(value3,maintainCase){return typeof value3!="string"?"":(maintainCase||(value3=value3.toLowerCase()),value3.replace(regex,"").replace(/ /g,"-"))}var slugs=new BananaSlug,Heading2=({children,disableAnchor,...props})=>{if(disableAnchor||typeof children!="string")return import_react54.default.createElement(import_components31.H2,null,children);let tagID=slugs.slug(children.toLowerCase());return import_react54.default.createElement(HeaderMdx,{as:"h2",id:tagID,...props},children)};var Subheading=({children,disableAnchor})=>{if(disableAnchor||typeof children!="string")return import_react55.default.createElement(import_components32.H3,null,children);let tagID=slugs.slug(children.toLowerCase());return import_react55.default.createElement(HeaderMdx,{as:"h3",id:tagID},children)};var DocsStory=({of,expanded=!0,withToolbar:withToolbarProp=!1,__forceInitialArgs=!1,__primary=!1})=>{let{story}=useOf(of||"story",["story"]),withToolbar=story.parameters.docs?.canvas?.withToolbar??withToolbarProp;return import_react56.default.createElement(Anchor,{storyId:story.id},expanded&&import_react56.default.createElement(import_react56.default.Fragment,null,import_react56.default.createElement(Subheading,null,story.name),import_react56.default.createElement(DescriptionContainer,{of})),import_react56.default.createElement(Canvas,{of,withToolbar,story:{__forceInitialArgs,__primary},source:{__forceInitialArgs}}))};var Primary=props=>{let{of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let{csfFile}=useOf(of||"meta",["meta"]),primaryStory=(0,import_react57.useContext)(DocsContext).componentStoriesFromCSFFile(csfFile)[0];return primaryStory?import_react57.default.createElement(DocsStory,{of:primaryStory.moduleExport,expanded:!1,__primary:!0,withToolbar:!0}):null};var import_react58=__toESM(require("react")),import_theming31=require("storybook/internal/theming");var StyledHeading=(0,import_theming31.styled)(Heading2)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,fontWeight:theme.typography.weight.bold,lineHeight:"16px",letterSpacing:"0.35em",textTransform:"uppercase",color:theme.textMutedColor,border:0,marginBottom:"12px","&:first-of-type":{marginTop:"56px"}})),Stories=({title="Stories",includePrimary=!0})=>{let{componentStories,projectAnnotations,getStoryContext}=(0,import_react58.useContext)(DocsContext),stories=componentStories(),{stories:{filter}={filter:void 0}}=projectAnnotations.parameters?.docs||{};return filter&&(stories=stories.filter(story=>filter(story,getStoryContext(story)))),stories.some(story=>story.tags?.includes("autodocs"))&&(stories=stories.filter(story=>story.tags?.includes("autodocs")&&!story.usesMount)),includePrimary||(stories=stories.slice(1)),!stories||stories.length===0?null:import_react58.default.createElement(import_react58.default.Fragment,null,typeof title=="string"?import_react58.default.createElement(StyledHeading,null,title):title,stories.map(story=>story&&import_react58.default.createElement(DocsStory,{key:story.id,of:story.moduleExport,expanded:!0,__forceInitialArgs:!0})))};var import_react59=__toESM(require("react")),import_client_logger5=require("storybook/internal/client-logger");var DEPRECATION_MIGRATION_LINK="https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#subtitle-block-and-parameterscomponentsubtitle",Subtitle2=props=>{let{of,children}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let preparedMeta;try{preparedMeta=useOf(of||"meta",["meta"]).preparedMeta}catch(error){if(children&&!error.message.includes("did you forget to use <Meta of={} />?"))throw error}let{componentSubtitle,docs}=preparedMeta?.parameters||{};componentSubtitle&&(0,import_client_logger5.deprecate)(`Using 'parameters.componentSubtitle' property to subtitle stories is deprecated. See ${DEPRECATION_MIGRATION_LINK}`);let content=children||docs?.subtitle||componentSubtitle;return content?import_react59.default.createElement(Subtitle,{className:"sbdocs-subtitle sb-unstyled"},content):null};var import_react60=__toESM(require("react"));var STORY_KIND_PATH_SEPARATOR=/\s*\/\s*/,extractTitle=title=>{let groups=title.trim().split(STORY_KIND_PATH_SEPARATOR);return groups?.[groups?.length-1]||title},Title2=props=>{let{children,of}=props;if("of"in props&&of===void 0)throw new Error("Unexpected `of={undefined}`, did you mistype a CSF file reference?");let preparedMeta;try{preparedMeta=useOf(of||"meta",["meta"]).preparedMeta}catch(error){if(children&&!error.message.includes("did you forget to use <Meta of={} />?"))throw error}let content=children||extractTitle(preparedMeta?.title);return content?import_react60.default.createElement(Title,{className:"sbdocs-title sb-unstyled"},content):null};var DocsPage=()=>{let resolvedOf=useOf("meta",["meta"]),{stories}=resolvedOf.csfFile,isSingleStory=Object.keys(stories).length===1;return import_react61.default.createElement(import_react61.default.Fragment,null,import_react61.default.createElement(Title2,null),import_react61.default.createElement(Subtitle2,null),import_react61.default.createElement(DescriptionContainer,{of:"meta"}),isSingleStory?import_react61.default.createElement(DescriptionContainer,{of:"story"}):null,import_react61.default.createElement(Primary,null),import_react61.default.createElement(Controls3,null),isSingleStory?null:import_react61.default.createElement(Stories,null))};function Docs({context,docsParameter}){let Container=docsParameter.container||DocsContainer,Page=docsParameter.page||DocsPage;return import_react62.default.createElement(Container,{context,theme:docsParameter.theme},import_react62.default.createElement(Page,null))}var import_react63=__toESM(require("react")),import_preview_api5=require("storybook/internal/preview-api");var import_channels=require("storybook/internal/channels"),import_preview_api4=require("storybook/internal/preview-api");var import_preview_api3=require("storybook/internal/preview-api"),ExternalDocsContext=class extends import_preview_api3.DocsContext{constructor(channel,store,renderStoryToElement,processMetaExports){super(channel,store,renderStoryToElement,[]);this.channel=channel;this.store=store;this.renderStoryToElement=renderStoryToElement;this.processMetaExports=processMetaExports;this.referenceMeta=(metaExports,attach)=>{let csfFile=this.processMetaExports(metaExports);this.referenceCSFFile(csfFile),super.referenceMeta(metaExports,attach)}}};var ConstantMap=class{constructor(prefix){this.prefix=prefix;this.entries=new Map}get(key2){return this.entries.has(key2)||this.entries.set(key2,`${this.prefix}${this.entries.size}`),this.entries.get(key2)}},ExternalPreview=class extends import_preview_api4.Preview{constructor(projectAnnotations){super(path=>Promise.resolve(this.moduleExportsByImportPath[path]),()=>(0,import_preview_api4.composeConfigs)([{parameters:{docs:{story:{inline:!0}}}},this.projectAnnotations]),new import_channels.Channel({}));this.projectAnnotations=projectAnnotations;this.importPaths=new ConstantMap("./importPath/");this.titles=new ConstantMap("title-");this.storyIndex={v:5,entries:{}};this.moduleExportsByImportPath={};this.processMetaExports=metaExports=>{let importPath=this.importPaths.get(metaExports);this.moduleExportsByImportPath[importPath]=metaExports;let title=metaExports.default.title||this.titles.get(metaExports),csfFile=this.storyStoreValue.processCSFFileWithCache(metaExports,importPath,title);return Object.values(csfFile.stories).forEach(({id:id2,name:name2})=>{this.storyIndex.entries[id2]={id:id2,importPath,title,name:name2,type:"story"}}),this.onStoriesChanged({storyIndex:this.storyIndex}),csfFile};this.docsContext=()=>new ExternalDocsContext(this.channel,this.storyStoreValue,this.renderStoryToElement.bind(this),this.processMetaExports.bind(this))}async getStoryIndexFromServer(){return this.storyIndex}};function usePreview(projectAnnotations){let previewRef=(0,import_react63.useRef)();return previewRef.current||(previewRef.current=new ExternalPreview(projectAnnotations)),previewRef.current}function ExternalDocs({projectAnnotationsList,children}){let projectAnnotations=(0,import_preview_api5.composeConfigs)(projectAnnotationsList),preview2=usePreview(projectAnnotations),docsParameter={...projectAnnotations.parameters?.docs,page:()=>children};return import_react63.default.createElement(Docs,{docsParameter,context:preview2.docsContext()})}var import_react64=__toESM(require("react")),import_theming32=require("storybook/internal/theming");var preview,ExternalDocsContainer=({projectAnnotations,children})=>(preview||(preview=new ExternalPreview(projectAnnotations)),import_react64.default.createElement(DocsContext.Provider,{value:preview.docsContext()},import_react64.default.createElement(import_theming32.ThemeProvider,{theme:(0,import_theming32.ensure)(import_theming32.themes.light)},children)));var import_react65=__toESM(require("react"));var Meta=({of})=>{let context=(0,import_react65.useContext)(DocsContext);of&&context.referenceMeta(of,!0);try{let primary=context.storyById();return import_react65.default.createElement(Anchor,{storyId:primary.id})}catch{return null}};var import_react66=__toESM(require("react")),Unstyled=props=>import_react66.default.createElement("div",{...props,className:"sb-unstyled"});var import_react67=__toESM(require("react")),Wrapper12=({children})=>import_react67.default.createElement("div",{style:{fontFamily:"sans-serif"}},children);var PRIMARY_STORY="^";0&&(module.exports={AddContext,Anchor,AnchorMdx,ArgTypes,BooleanControl,Canvas,CodeOrSourceMdx,ColorControl,ColorItem,ColorPalette,Controls,DateControl,Description,DescriptionType,Docs,DocsContainer,DocsContext,DocsPage,DocsStory,ExternalDocs,ExternalDocsContainer,FilesControl,HeaderMdx,HeadersMdx,Heading,IconGallery,IconItem,Markdown,Meta,NumberControl,ObjectControl,OptionsControl,PRIMARY_STORY,Primary,PureArgsTable,RangeControl,Source,SourceContainer,SourceContext,Stories,Story,Subheading,Subtitle,TextControl,Title,Typeset,UNKNOWN_ARGS_HASH,Unstyled,Wrapper,anchorBlockIdFromId,argsHash,assertIsFn,extractTitle,format,formatDate,formatTime,getStoryId,getStoryProps,parse,parseDate,parseTime,slugs,useOf,useSourceProps});
