"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __esm=(fn,res)=>function(){return fn&&(res=(0,fn[__getOwnPropNames(fn)[0]])(fn=0)),res};var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var import_react_docgen,getNameOrValue,isReactForwardRefCall,actualNameHandler,actualNameHandler_default,init_actualNameHandler=__esm({"src/plugins/docgen-handlers/actualNameHandler.ts"(){"use strict";import_react_docgen=require("react-docgen"),{getNameOrValue,isReactForwardRefCall}=import_react_docgen.utils,actualNameHandler=function(documentation,componentDefinition){if(documentation.set("definedInFile",componentDefinition.hub.file.opts.filename),(componentDefinition.isClassDeclaration()||componentDefinition.isFunctionDeclaration())&&componentDefinition.has("id"))documentation.set("actualName",getNameOrValue(componentDefinition.get("id")));else if(componentDefinition.isArrowFunctionExpression()||componentDefinition.isFunctionExpression()||isReactForwardRefCall(componentDefinition)){let currentPath=componentDefinition;for(;currentPath.parentPath;){if(currentPath.parentPath.isVariableDeclarator()){documentation.set("actualName",getNameOrValue(currentPath.parentPath.get("id")));return}if(currentPath.parentPath.isAssignmentExpression()){let leftPath=currentPath.parentPath.get("left");if(leftPath.isIdentifier()||leftPath.isLiteral()){documentation.set("actualName",getNameOrValue(leftPath));return}}currentPath=currentPath.parentPath}documentation.set("actualName","")}},actualNameHandler_default=actualNameHandler}});function defaultLookupModule(filename,basedir){let resolveOptions={basedir,extensions:RESOLVE_EXTENSIONS,includeCoreModules:!1};try{return import_resolve.default.sync(filename,resolveOptions)}catch(error){let ext=(0,import_node_path.extname)(filename),newFilename;switch(ext){case".js":case".mjs":case".cjs":newFilename=`${filename.slice(0,-2)}ts`;break;case".jsx":newFilename=`${filename.slice(0,-3)}tsx`;break;default:throw error}return import_resolve.default.sync(newFilename,{...resolveOptions,extensions:[]})}}var import_node_path,import_resolve,ReactDocgenResolveError,RESOLVE_EXTENSIONS,init_docgen_resolver=__esm({"src/plugins/docgen-resolver.ts"(){"use strict";import_node_path=require("path"),import_resolve=__toESM(require("resolve")),ReactDocgenResolveError=class extends Error{constructor(filename){super(`'${filename}' was ignored by react-docgen.`);this.code="MODULE_NOT_FOUND"}},RESOLVE_EXTENSIONS=[".js",".cts",".mts",".ctsx",".mtsx",".ts",".tsx",".mjs",".cjs",".mts",".cts",".jsx"]}});var react_docgen_exports={};__export(react_docgen_exports,{getReactDocgenImporter:()=>getReactDocgenImporter,reactDocgen:()=>reactDocgen});async function reactDocgen({include=/\.(mjs|tsx?|jsx?)$/,exclude=[/node_modules\/.*/]}={}){let cwd=process.cwd(),filter=(0,import_pluginutils.createFilter)(include,exclude),tsconfigPath=await(0,import_find_up.default)("tsconfig.json",{cwd}),tsconfig=TsconfigPaths.loadConfig(tsconfigPath),matchPath;return tsconfig.resultType==="success"&&(import_node_logger.logger.info("Using tsconfig paths for react-docgen"),matchPath=TsconfigPaths.createMatchPath(tsconfig.absoluteBaseUrl,tsconfig.paths,["browser","module","main"])),{name:"storybook:react-docgen-plugin",enforce:"pre",async transform(src,id){if(filter((0,import_node_path2.relative)(cwd,id)))try{let docgenResults=(0,import_react_docgen2.parse)(src,{resolver:defaultResolver,handlers,importer:getReactDocgenImporter(matchPath),filename:id}),s=new import_magic_string.default(src);return docgenResults.forEach(info=>{let{actualName,definedInFile,...docgenInfo}=info;if(actualName&&definedInFile==id){let docNode=JSON.stringify(docgenInfo);s.append(`;${actualName}.__docgenInfo=${docNode}`)}}),{code:s.toString(),map:s.generateMap({hires:!0,source:id})}}catch(e){if(e.code===import_react_docgen2.ERROR_CODES.MISSING_DEFINITION)return;throw e}}}}function getReactDocgenImporter(matchPath){return(0,import_react_docgen2.makeFsImporter)((filename,basedir)=>{let mappedFilenameByPaths=matchPath&&matchPath(filename)||filename,result=defaultLookupModule(mappedFilenameByPaths,basedir);if(result.includes(`${import_node_path2.sep}react-native${import_node_path2.sep}index.js`)){let replaced=result.replace(`${import_node_path2.sep}react-native${import_node_path2.sep}index.js`,`${import_node_path2.sep}react-native-web${import_node_path2.sep}dist${import_node_path2.sep}index.js`);if((0,import_node_fs.existsSync)(replaced)&&RESOLVE_EXTENSIONS.find(ext=>result.endsWith(ext)))return replaced}if(RESOLVE_EXTENSIONS.find(ext=>result.endsWith(ext)))return result;throw new ReactDocgenResolveError(filename)})}var import_node_fs,import_node_path2,import_node_logger,import_pluginutils,import_find_up,import_magic_string,import_react_docgen2,TsconfigPaths,defaultHandlers,defaultResolver,handlers,init_react_docgen=__esm({"src/plugins/react-docgen.ts"(){"use strict";import_node_fs=require("fs"),import_node_path2=require("path"),import_node_logger=require("storybook/internal/node-logger"),import_pluginutils=require("@rollup/pluginutils"),import_find_up=__toESM(require("find-up")),import_magic_string=__toESM(require("magic-string")),import_react_docgen2=require("react-docgen"),TsconfigPaths=__toESM(require("tsconfig-paths"));init_actualNameHandler();init_docgen_resolver();defaultHandlers=Object.values(import_react_docgen2.builtinHandlers).map(handler=>handler),defaultResolver=new import_react_docgen2.builtinResolvers.FindExportedDefinitionsResolver,handlers=[...defaultHandlers,actualNameHandler_default]}});var preset_exports={};__export(preset_exports,{core:()=>core,viteFinal:()=>viteFinal});module.exports=__toCommonJS(preset_exports);var import_node_path3=require("path"),getAbsolutePath=input=>(0,import_node_path3.dirname)(require.resolve((0,import_node_path3.join)(input,"package.json"))),core={builder:getAbsolutePath("@storybook/builder-vite"),renderer:getAbsolutePath("@storybook/react")},viteFinal=async(config,{presets})=>{let plugins=[...config?.plugins??[]],{reactDocgen:reactDocgenOption,reactDocgenTypescriptOptions}=await presets.apply("typescript",{}),typescriptPresent;try{require.resolve("typescript"),typescriptPresent=!0}catch{typescriptPresent=!1}if(reactDocgenOption==="react-docgen-typescript"&&typescriptPresent&&plugins.push(require("@joshwooding/vite-plugin-react-docgen-typescript")({...reactDocgenTypescriptOptions,savePropValueAsString:!0})),typeof reactDocgenOption=="string"){let{reactDocgen:reactDocgen2}=await Promise.resolve().then(()=>(init_react_docgen(),react_docgen_exports));plugins.unshift(await reactDocgen2({include:reactDocgenOption==="react-docgen"?/\.(mjs|tsx?|jsx?)$/:/\.(mjs|jsx?)$/}))}return{...config,plugins}};0&&(module.exports={core,viteFinal});
