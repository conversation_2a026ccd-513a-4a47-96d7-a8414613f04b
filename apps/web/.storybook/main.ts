import type { StorybookConfig } from '@storybook/react-vite';
import { resolve } from 'path';

const config: StorybookConfig = {
  stories: [
    '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    '../../../shared/**/*.stories.@(js|jsx|mjs|ts|tsx)',
  ],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-links',
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },
  viteFinal: async (config) => {
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@': resolve(__dirname, '../src'),
        '@shared': resolve(__dirname, '../../../shared'),
        '@taptap/shared': resolve(__dirname, '../../../shared/index'),
      };
    }

    // Define globals for browser compatibility
    if (config.define) {
      config.define = {
        ...config.define,
        'process.env': '{}',
        'process.env.NODE_ENV': JSON.stringify('development'),
        'process.env.VITE_PLATFORM': JSON.stringify('web'),
        'global': 'globalThis',
      };
    }

    // Add polyfill for process
    if (config.optimizeDeps) {
      config.optimizeDeps.include = [...(config.optimizeDeps.include || []), 'process'];
    } else {
      config.optimizeDeps = {
        include: ['process'],
      };
    }

    return config;
  },
};

export default config;
