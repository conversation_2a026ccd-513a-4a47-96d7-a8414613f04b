import type { Preview } from '@storybook/react';
import '../src/styles/tailwind.css';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        {
          name: 'light',
          value: '#ffffff',
        },
        {
          name: 'dark',
          value: '#333333',
        },
        {
          name: 'gray',
          value: '#f5f5f5',
        },
      ],
    },
    docs: {
      toc: true,
    },
  },
  globalTypes: {
    platform: {
      name: 'Platform',
      description: 'Platform context for components',
      defaultValue: 'web',
      toolbar: {
        icon: 'browser',
        items: [
          { value: 'web', title: 'Web Platform' },
          { value: 'zalo', title: 'Zalo Mini App' },
        ],
      },
    },
  },
};

export default preview;
