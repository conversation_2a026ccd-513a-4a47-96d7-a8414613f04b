{"version": 3, "file": "LoginPage-DxfIsWge.js", "sources": ["../../src/pages/LoginPage.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const LoginPage: React.FC = () => {\n  return (\n    <div style={{ padding: '20px', maxWidth: '400px', margin: '0 auto' }}>\n      <h1>Login</h1>\n      <p>Please log in to continue</p>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": ["LoginPage", "jsxs", "jsx"], "mappings": "qEAEO,MAAMA,EAAsB,IAE/BC,OAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,SAAU,QAAS,OAAQ,QAAA,EACxD,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAA,OAAA,CAAK,EACTA,EAAAA,IAAC,KAAE,SAAA,2BAAA,CAAyB,CAAA,EAC9B"}