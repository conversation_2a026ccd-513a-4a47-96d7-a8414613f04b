{"version": 3, "file": "GamesPage-iJlWKziE.js", "sources": ["../../src/pages/GamesPage.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const GamesPage: React.FC = () => {\n  return (\n    <div style={{ padding: '20px' }}>\n      <h1>Games</h1>\n      <p>Browse and discover games</p>\n    </div>\n  );\n};\n\nexport default GamesPage;\n"], "names": ["GamesPage", "jsx"], "mappings": "qEAEO,MAAMA,EAAsB,WAE9B,MAAA,CAAI,MAAO,CAAE,QAAS,QACrB,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAA,OAAA,CAAK,EACTA,EAAAA,IAAC,KAAE,SAAA,2BAAA,CAAyB,CAAA,EAC9B"}