{"version": 3, "file": "NotFoundPage-Dt6hKlbt.js", "sources": ["../../src/pages/NotFoundPage.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const NotFoundPage: React.FC = () => {\n  return (\n    <div style={{ padding: '20px', textAlign: 'center' }}>\n      <h1>404 - Page Not Found</h1>\n      <p>The page you're looking for doesn't exist.</p>\n    </div>\n  );\n};\n\nexport default NotFoundPage;\n"], "names": ["NotFoundPage", "jsxs", "jsx"], "mappings": "qEAEO,MAAMA,EAAyB,IAElCC,EAAAA,KAAC,OAAI,MAAO,CAAE,QAAS,OAAQ,UAAW,UACxC,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAA,sBAAA,CAAoB,EACxBA,EAAAA,IAAC,KAAE,SAAA,4CAAA,CAA0C,CAAA,EAC/C"}