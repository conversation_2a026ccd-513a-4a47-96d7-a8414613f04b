const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/HomePage-B9Fgl1c1.js","assets/vendor-C1xA9yCq.js","assets/LoginPage-DxfIsWge.js","assets/GameDetailPage-DLB2DhXM.js","assets/ProfilePage-DsDpUSZG.js","assets/GamesPage-iJlWKziE.js","assets/NotFoundPage-Dt6hKlbt.js"])))=>i.map(i=>d[i]);
var N=Object.defineProperty;var C=(i,t,e)=>t in i?N(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var m=(i,t,e)=>C(i,typeof t!="symbol"?t+"":t,e);import{r as f,a as F,R as g,b as V,c as p,B as $}from"./vendor-C1xA9yCq.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function e(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=e(o);fetch(o.href,s)}})();var I={exports:{}},_={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var B=f,z=Symbol.for("react.element"),J=Symbol.for("react.fragment"),W=Object.prototype.hasOwnProperty,G=B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,M={key:!0,ref:!0,__self:!0,__source:!0};function O(i,t,e){var r,o={},s=null,a=null;e!==void 0&&(s=""+e),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)W.call(t,r)&&!M.hasOwnProperty(r)&&(o[r]=t[r]);if(i&&i.defaultProps)for(r in t=i.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:z,type:i,key:s,ref:a,props:o,_owner:G.current}}_.Fragment=J;_.jsx=O;_.jsxs=O;I.exports=_;var n=I.exports,E={},T=F;E.createRoot=T.createRoot,E.hydrateRoot=T.hydrateRoot;const q="modulepreload",K=function(i){return"/"+i},P={},c=function(t,e,r){let o=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),l=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));o=Promise.allSettled(e.map(u=>{if(u=K(u),u in P)return;P[u]=!0;const d=u.endsWith(".css"),Z=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${Z}`))return;const w=document.createElement("link");if(w.rel=d?"stylesheet":q,d||(w.as="script"),w.crossOrigin="",w.href=u,l&&w.setAttribute("nonce",l),document.head.appendChild(w),d)return new Promise((D,U)=>{w.addEventListener("load",D),w.addEventListener("error",()=>U(new Error(`Unable to preload CSS for ${u}`)))})}))}function s(a){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=a,window.dispatchEvent(l),!l.defaultPrevented)throw a}return o.then(a=>{for(const l of a||[])l.status==="rejected"&&s(l.reason);return t().catch(s)})},H=({children:i})=>n.jsxs("div",{className:"min-h-screen bg-gray-50",children:[n.jsx("nav",{className:"bg-white shadow-sm border-b",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:n.jsxs("div",{className:"flex justify-between items-center h-16",children:[n.jsx("h1",{className:"text-2xl font-bold text-primary-600",children:"TapTap"}),n.jsx("div",{className:"flex items-center space-x-4",children:n.jsx("button",{className:"btn btn-primary",children:"Login"})})]})})}),n.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:i})]}),Y=({size:i="medium",className:t=""})=>{const e={small:"w-5 h-5",medium:"w-10 h-10",large:"w-16 h-16"};return n.jsx("div",{className:`mx-auto ${e[i]} ${t}`,children:n.jsx("div",{className:"animate-spin rounded-full border-4 border-primary-200 border-t-primary-600 h-full w-full"})})};class Q extends f.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,e){console.error("Error caught by boundary:",t,e)}render(){return this.state.hasError?n.jsxs("div",{style:{padding:"20px",textAlign:"center"},children:[n.jsx("h1",{children:"Something went wrong."}),n.jsx("p",{children:"Please refresh the page or try again later."})]}):this.props.children}}var S=(i=>(i.WEB="web",i.ZALO="zalo",i))(S||{});const x=()=>typeof window>"u"?"web":window.ZaloJavaScriptInterface||navigator.userAgent.includes("Zalo")||window.zmp||window.ZaloJavaScriptInterface?"zalo":"web";class A{constructor(){m(this,"currentUser",null);m(this,"token",null)}async getCurrentUser(){if(this.currentUser)return this.currentUser;try{const{storageService:t}=await c(async()=>{const{storageService:o}=await Promise.resolve().then(()=>y);return{storageService:o}},void 0),e=await t.getItem("user_profile"),r=await t.getItem("user_token");if(e&&r)return this.currentUser=e,this.token=r,this.currentUser}catch(t){console.error("Failed to load user from storage:",t)}return null}async saveUserData(t,e){try{const{storageService:r}=await c(async()=>{const{storageService:o}=await Promise.resolve().then(()=>y);return{storageService:o}},void 0);await r.setItem("user_profile",t),await r.setItem("user_token",e),this.currentUser=t,this.token=e}catch(r){throw console.error("Failed to save user data:",r),r}}async clearUserData(){try{const{storageService:t}=await c(async()=>{const{storageService:e}=await Promise.resolve().then(()=>y);return{storageService:e}},void 0);await t.removeItem("user_profile"),await t.removeItem("user_token"),this.currentUser=null,this.token=null}catch(t){throw console.error("Failed to clear user data:",t),t}}getToken(){return this.token}isAuthenticated(){return this.currentUser!==null&&this.token!==null}}const b={baseURL:"https://api.taptap.com"};class X extends A{constructor(){super(...arguments);m(this,"apiBaseUrl",b.baseURL)}async login(e){try{const r=await fetch(`${this.apiBaseUrl}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,platform:"web"})});if(!r.ok)throw new Error(`Login failed: ${r.statusText}`);const o=await r.json();if(o.success){const s=o.data.user,a=o.data.token;return await this.saveUserData(s,a),s}else throw new Error(o.message||"Login failed")}catch(r){throw console.error("Web login failed:",r),r}}async register(e){try{const r=await fetch(`${this.apiBaseUrl}/auth/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,platform:"web"})});if(!r.ok)throw new Error(`Registration failed: ${r.statusText}`);const o=await r.json();if(o.success){const s=o.data.user,a=o.data.token;return await this.saveUserData(s,a),s}else throw new Error(o.message||"Registration failed")}catch(r){throw console.error("Web registration failed:",r),r}}async logout(){try{this.token&&await fetch(`${this.apiBaseUrl}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${this.token}`,"Content-Type":"application/json"}})}catch(e){console.error("Web logout API call failed:",e)}finally{await this.clearUserData()}}async refreshToken(){try{const{storageService:e}=await c(async()=>{const{storageService:a}=await Promise.resolve().then(()=>y);return{storageService:a}},void 0),r=await e.getItem("refresh_token");if(!r)throw new Error("No refresh token available");const o=await fetch(`${this.apiBaseUrl}/auth/refresh`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:r})});if(!o.ok)throw new Error(`Token refresh failed: ${o.statusText}`);const s=await o.json();if(s.success){const a=s.data.token;return await e.setItem("user_token",a),this.token=a,a}else throw new Error(s.message||"Token refresh failed")}catch(e){throw console.error("Web token refresh failed:",e),await this.clearUserData(),e}}async loginWithGoogle(){throw new Error("Google login not implemented yet")}async loginWithFacebook(){throw new Error("Facebook login not implemented yet")}async forgotPassword(e){try{const r=await fetch(`${this.apiBaseUrl}/auth/forgot-password`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})});if(!r.ok)throw new Error(`Forgot password failed: ${r.statusText}`);const o=await r.json();if(!o.success)throw new Error(o.message||"Forgot password failed")}catch(r){throw console.error("Web forgot password failed:",r),r}}async resetPassword(e,r){try{const o=await fetch(`${this.apiBaseUrl}/auth/reset-password`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e,newPassword:r})});if(!o.ok)throw new Error(`Reset password failed: ${o.statusText}`);const s=await o.json();if(!s.success)throw new Error(s.message||"Reset password failed")}catch(o){throw console.error("Web reset password failed:",o),o}}}class h{static isZaloEnvironment(){return typeof window<"u"&&(window.ZaloJavaScriptInterface||navigator.userAgent.includes("Zalo")||window.zmp)}static async getAccessToken(){if(!this.isZaloEnvironment())return console.warn("getAccessToken called outside Zalo environment"),"DEFAULT_ACCESS_TOKEN";try{const{getAccessToken:t}=await c(async()=>{const{getAccessToken:e}=await import("zmp-sdk");return{getAccessToken:e}},[]);return await t()}catch(t){throw console.error("Failed to get access token:",t),t}}static async getUserInfo(){if(!this.isZaloEnvironment())return console.warn("getUserInfo called outside Zalo environment"),{id:"test-user",name:"Test User",avatar:"https://via.placeholder.com/150",phone:"+84123456789"};try{const{getUserInfo:t}=await c(async()=>{const{getUserInfo:e}=await import("zmp-sdk");return{getUserInfo:e}},[]);return await t()}catch(t){throw console.error("Failed to get user info:",t),t}}static async getPhoneNumber(){if(!this.isZaloEnvironment())return console.warn("getPhoneNumber called outside Zalo environment"),"+84123456789";try{const{getPhoneNumber:t}=await c(async()=>{const{getPhoneNumber:e}=await import("zmp-sdk");return{getPhoneNumber:e}},[]);return await t()}catch(t){throw console.error("Failed to get phone number:",t),t}}static async getLocation(){if(!this.isZaloEnvironment())return console.warn("getLocation called outside Zalo environment"),{latitude:21.0285,longitude:105.8542,accuracy:10};try{const{getLocation:t}=await c(async()=>{const{getLocation:e}=await import("zmp-sdk");return{getLocation:e}},[]);return await t()}catch(t){throw console.error("Failed to get location:",t),t}}static async openChat(t){if(!this.isZaloEnvironment()){console.warn("openChat called outside Zalo environment");return}try{const{openChat:e}=await c(async()=>{const{openChat:r}=await import("zmp-sdk");return{openChat:r}},[]);return await e({userId:t})}catch(e){throw console.error("Failed to open chat:",e),e}}static async followOA(t){if(!this.isZaloEnvironment()){console.warn("followOA called outside Zalo environment");return}try{const{followOA:e}=await c(async()=>{const{followOA:r}=await import("zmp-sdk");return{followOA:r}},[]);return await e({oaId:t})}catch(e){throw console.error("Failed to follow OA:",e),e}}static async shareToChat(t){if(!this.isZaloEnvironment()){console.warn("shareToChat called outside Zalo environment");return}try{const{shareToChat:e}=await c(async()=>{const{shareToChat:r}=await import("zmp-sdk");return{shareToChat:r}},[]);return await e(t)}catch(e){throw console.error("Failed to share to chat:",e),e}}static async createPayment(t){if(!this.isZaloEnvironment())return console.warn("createPayment called outside Zalo environment"),{success:!1,error:"Payment not supported outside Zalo environment"};try{const{payment:e}=await c(async()=>{const{payment:r}=await import("zmp-sdk");return{payment:r}},[]);return await e(t)}catch(e){throw console.error("Failed to create payment:",e),e}}static async setStorage(t,e){if(!this.isZaloEnvironment()){console.warn("setStorage called outside Zalo environment, using localStorage"),localStorage.setItem(t,JSON.stringify(e));return}try{const{setStorage:r}=await c(async()=>{const{setStorage:o}=await import("zmp-sdk");return{setStorage:o}},[]);return await r({key:t,data:e})}catch(r){throw console.error("Failed to set storage:",r),r}}static async getStorage(t){if(!this.isZaloEnvironment()){console.warn("getStorage called outside Zalo environment, using localStorage");const e=localStorage.getItem(t);return{data:e?JSON.parse(e):null}}try{const{getStorage:e}=await c(async()=>{const{getStorage:r}=await import("zmp-sdk");return{getStorage:r}},[]);return await e({key:t})}catch(e){throw console.error("Failed to get storage:",e),e}}static async clearStorage(){if(!this.isZaloEnvironment()){console.warn("clearStorage called outside Zalo environment, using localStorage"),localStorage.clear();return}try{const{clearStorage:t}=await c(async()=>{const{clearStorage:e}=await import("zmp-sdk");return{clearStorage:e}},[]);return await t()}catch(t){throw console.error("Failed to clear storage:",t),t}}static async setTitle(t){if(!this.isZaloEnvironment()){console.warn("setTitle called outside Zalo environment"),document.title=t;return}try{const{setTitle:e}=await c(async()=>{const{setTitle:r}=await import("zmp-sdk");return{setTitle:r}},[]);return await e(t)}catch(e){throw console.error("Failed to set title:",e),e}}static async showToast(t){if(!this.isZaloEnvironment()){console.warn("showToast called outside Zalo environment"),console.log("Toast:",t);return}try{const{showToast:e}=await c(async()=>{const{showToast:r}=await import("zmp-sdk");return{showToast:r}},[]);return await e(t)}catch(e){throw console.error("Failed to show toast:",e),e}}static async showModal(t){if(!this.isZaloEnvironment())return console.warn("showModal called outside Zalo environment"),{confirm:window.confirm(`${t.title}
${t.content}`)};try{const{showModal:e}=await c(async()=>{const{showModal:r}=await import("zmp-sdk");return{showModal:r}},[]);return await e(t)}catch(e){throw console.error("Failed to show modal:",e),e}}}class ee extends A{constructor(){super(...arguments);m(this,"apiBaseUrl",b.baseURL)}async login(e){try{const r=await h.getAccessToken(),o=await h.getUserInfo(),s=await fetch(`${this.apiBaseUrl}/auth/zalo-login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({accessToken:r,userInfo:o,platform:"zalo"})});if(!s.ok)throw new Error(`Zalo login failed: ${s.statusText}`);const a=await s.json();if(a.success){const l={id:a.data.user.id,name:a.data.user.name,avatar:a.data.user.avatar,phone:a.data.user.phone,email:a.data.user.email,createdAt:new Date(a.data.user.createdAt),updatedAt:new Date(a.data.user.updatedAt)},u=a.data.token;return await this.saveUserData(l,u),l}else throw new Error(a.message||"Zalo login failed")}catch(r){throw console.error("Zalo login failed:",r),r}}async register(e){try{const r=await h.getAccessToken(),o=await h.getUserInfo(),s=await fetch(`${this.apiBaseUrl}/auth/zalo-register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({accessToken:r,userInfo:o,additionalData:e,platform:"zalo"})});if(!s.ok)throw new Error(`Zalo registration failed: ${s.statusText}`);const a=await s.json();if(a.success){const l={id:a.data.user.id,name:a.data.user.name,avatar:a.data.user.avatar,phone:a.data.user.phone,email:a.data.user.email,createdAt:new Date(a.data.user.createdAt),updatedAt:new Date(a.data.user.updatedAt)},u=a.data.token;return await this.saveUserData(l,u),l}else throw new Error(a.message||"Zalo registration failed")}catch(r){throw console.error("Zalo registration failed:",r),r}}async logout(){try{this.token&&await fetch(`${this.apiBaseUrl}/auth/logout`,{method:"POST",headers:{Authorization:`Bearer ${this.token}`,"Content-Type":"application/json"}})}catch(e){console.error("Zalo logout API call failed:",e)}finally{await this.clearUserData()}}async refreshToken(){try{const e=await h.getAccessToken(),r=await fetch(`${this.apiBaseUrl}/auth/zalo-refresh`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({accessToken:e})});if(!r.ok)throw new Error(`Zalo token refresh failed: ${r.statusText}`);const o=await r.json();if(o.success){const s=o.data.token,{storageService:a}=await c(async()=>{const{storageService:l}=await Promise.resolve().then(()=>y);return{storageService:l}},void 0);return await a.setItem("user_token",s),this.token=s,s}else throw new Error(o.message||"Zalo token refresh failed")}catch(e){throw console.error("Zalo token refresh failed:",e),await this.clearUserData(),e}}async getPhoneNumber(){try{return await h.getPhoneNumber()}catch(e){throw console.error("Failed to get phone number:",e),e}}async followOfficialAccount(e){try{await h.followOA(e)}catch(r){throw console.error("Failed to follow OA:",r),r}}async shareToChat(e){try{await h.shareToChat(e)}catch(r){throw console.error("Failed to share to chat:",r),r}}async openChat(e){try{await h.openChat(e)}catch(r){throw console.error("Failed to open chat:",r),r}}}class k{static getInstance(){if(!this.instance){const t=x();this.instance=t===S.ZALO?new ee:new X}return this.instance}static resetInstance(){this.instance=void 0}}m(k,"instance");const v=k.getInstance();class j{async setItem(t,e){try{const r=JSON.stringify(e);localStorage.setItem(t,r)}catch(r){throw console.error("Web storage setItem failed:",r),r}}async getItem(t){try{const e=localStorage.getItem(t);return e?JSON.parse(e):null}catch(e){return console.error("Web storage getItem failed:",e),null}}async removeItem(t){try{localStorage.removeItem(t)}catch(e){throw console.error("Web storage removeItem failed:",e),e}}async clear(){try{localStorage.clear()}catch(t){throw console.error("Web storage clear failed:",t),t}}}class L{async setItem(t,e){try{const{setStorage:r}=await c(async()=>{const{setStorage:o}=await import("zmp-sdk");return{setStorage:o}},[]);await r({key:t,data:e})}catch(r){console.error("Zalo storage setItem failed:",r);try{localStorage.setItem(t,JSON.stringify(e))}catch{throw r}}}async getItem(t){try{const{getStorage:e}=await c(async()=>{const{getStorage:o}=await import("zmp-sdk");return{getStorage:o}},[]);return(await e({key:t})).data}catch(e){console.error("Zalo storage getItem failed:",e);try{const r=localStorage.getItem(t);return r?JSON.parse(r):null}catch{return null}}}async removeItem(t){try{const{setStorage:e}=await c(async()=>{const{setStorage:r}=await import("zmp-sdk");return{setStorage:r}},[]);await e({key:t,data:null})}catch(e){console.error("Zalo storage removeItem failed:",e);try{localStorage.removeItem(t)}catch{throw e}}}async clear(){try{const{clearStorage:t}=await c(async()=>{const{clearStorage:e}=await import("zmp-sdk");return{clearStorage:e}},[]);await t()}catch(t){console.error("Zalo storage clear failed:",t);try{localStorage.clear()}catch{throw t}}}}class R{static getInstance(){if(!this.instance){const t=x();this.instance=t===S.ZALO?new L:new j}return this.instance}}m(R,"instance");const te=R.getInstance(),y=Object.freeze(Object.defineProperty({__proto__:null,WebStorageService:j,ZaloStorageService:L,storageService:te},Symbol.toStringTag,{value:"Module"})),re=f.createContext(void 0),oe=({children:i})=>{const[t,e]=f.useState(null),[r,o]=f.useState(!0);f.useEffect(()=>{(async()=>{try{const d=await v.getCurrentUser();e(d)}catch(d){console.error("Failed to get current user:",d)}finally{o(!1)}})()},[]);const l={user:t,login:async u=>{o(!0);try{const d=await v.login(u);return e(d),d}catch(d){throw console.error("Login failed:",d),d}finally{o(!1)}},logout:async()=>{try{await v.logout(),e(null)}catch(u){throw console.error("Logout failed:",u),u}},loading:r};return n.jsx(re.Provider,{value:l,children:i})},ae=g.lazy(()=>c(()=>import("./HomePage-B9Fgl1c1.js"),__vite__mapDeps([0,1]))),se=g.lazy(()=>c(()=>import("./LoginPage-DxfIsWge.js"),__vite__mapDeps([2,1]))),ne=g.lazy(()=>c(()=>import("./GameDetailPage-DLB2DhXM.js"),__vite__mapDeps([3,1]))),ie=g.lazy(()=>c(()=>import("./ProfilePage-DsDpUSZG.js"),__vite__mapDeps([4,1]))),ce=g.lazy(()=>c(()=>import("./GamesPage-iJlWKziE.js"),__vite__mapDeps([5,1]))),le=g.lazy(()=>c(()=>import("./NotFoundPage-Dt6hKlbt.js"),__vite__mapDeps([6,1])));function ue(){return n.jsx(Q,{children:n.jsx(oe,{children:n.jsx(H,{children:n.jsx(f.Suspense,{fallback:n.jsx(Y,{}),children:n.jsxs(V,{children:[n.jsx(p,{path:"/",element:n.jsx(ae,{})}),n.jsx(p,{path:"/login",element:n.jsx(se,{})}),n.jsx(p,{path:"/games",element:n.jsx(ce,{})}),n.jsx(p,{path:"/games/:id",element:n.jsx(ne,{})}),n.jsx(p,{path:"/profile",element:n.jsx(ie,{})}),n.jsx(p,{path:"*",element:n.jsx(le,{})})]})})})})})}const de=E.createRoot(document.getElementById("root"));de.render(n.jsx(g.StrictMode,{children:n.jsx($,{children:n.jsx(ue,{})})}));export{n as j};
//# sourceMappingURL=index-Eixu5sWq.js.map
