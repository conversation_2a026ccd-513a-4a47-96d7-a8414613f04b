{"version": 3, "mappings": ";;;;;;;;;GASa,IAAIA,EAAEC,EAAiBC,EAAE,OAAO,IAAI,eAAe,EAAEC,EAAE,OAAO,IAAI,gBAAgB,EAAEC,EAAE,OAAO,UAAU,eAAeC,EAAEL,EAAE,mDAAmD,kBAAkBM,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,EAAE,EAClP,SAASC,EAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAKC,EAAE,KAAcJ,IAAT,SAAaG,EAAE,GAAGH,GAAYD,EAAE,MAAX,SAAiBI,EAAE,GAAGJ,EAAE,KAAcA,EAAE,MAAX,SAAiBK,EAAEL,EAAE,KAAK,IAAIE,KAAKF,EAAEL,EAAE,KAAKK,EAAEE,CAAC,GAAG,CAACL,EAAE,eAAeK,CAAC,IAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,GAAGH,GAAGA,EAAE,aAAa,IAAIG,KAAKF,EAAED,EAAE,aAAaC,EAAWG,EAAED,CAAC,aAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,MAAM,CAAC,SAAST,EAAE,KAAKM,EAAE,IAAIK,EAAE,IAAIC,EAAE,MAAMF,EAAE,OAAOP,EAAE,OAAO,CAAC,YAAkBF,EAAEY,EAAA,IAAYR,EAAEQ,EAAA,KAAaR,ECPxWS,EAAA,QAAiBf,uBCDfG,EAAIH,EAENgB,EAAA,WAAqBb,EAAE,WACvBa,EAAA,YAAsBb,EAAE,q/BCCbc,EAAgC,CAAC,CAAE,SAAAC,KAE5CC,OAAC,OAAI,UAAU,0BACb,UAAAC,MAAC,OAAI,UAAU,8BACb,SAAAA,MAAC,OAAI,UAAU,yCACb,SAAAD,OAAC,OAAI,UAAU,yCACb,UAAAC,MAAC,MAAG,UAAU,sCAAsC,kBAAM,EAC1DA,MAAC,OAAI,UAAU,8BACb,eAAC,UAAO,UAAU,kBAAkB,iBAAK,EAC3C,GACF,EACF,EACF,EACAA,MAAC,QAAK,UAAU,8CACb,SAAAF,CAAA,CACH,GACF,ECfSG,EAAgD,CAAC,CAC5D,KAAAC,EAAO,SACP,UAAAC,EAAY,EACd,IAAM,CACJ,MAAMC,EAAc,CAClB,MAAO,UACP,OAAQ,YACR,MAAO,aAGT,OACEJ,MAAC,OAAI,UAAW,WAAWI,EAAYF,CAAI,CAAC,IAAIC,CAAS,GACvD,SAAAH,MAAC,OAAI,UAAU,2FAA2F,EAC5G,CAEJ,ECXO,MAAMK,UAAsBC,WAAwB,CACzD,YAAYC,EAAc,CACxB,MAAMA,CAAK,EACX,KAAK,MAAQ,CAAE,SAAU,GAC3B,CAEA,OAAO,yBAAyBC,EAAqB,CACnD,MAAO,CAAE,SAAU,GAAM,MAAAA,CAAA,CAC3B,CAEA,kBAAkBA,EAAcC,EAAgB,CAC9C,QAAQ,MAAM,4BAA6BD,EAAOC,CAAS,CAC7D,CAEA,QAAS,CACP,OAAI,KAAK,MAAM,SAEXV,OAAC,OAAI,MAAO,CAAE,QAAS,OAAQ,UAAW,UACxC,UAAAC,MAAC,MAAG,iCAAqB,EACzBA,MAAC,KAAE,uDAA2C,GAChD,EAIG,KAAK,MAAM,QACpB,CACF,CCpCO,IAAKU,OACVA,EAAA,IAAM,MACNA,EAAA,KAAO,OAFGA,OAAA,IAaL,MAAMC,EAAiB,IACxB,OAAO,OAAW,IAAoB,MAGtC,OAAO,yBACP,UAAU,UAAU,SAAS,MAAM,GAEnC,OAAO,KACP,OAAO,wBACF,OAGF,MCdF,MAAeC,CAAuC,CAAtD,cACKC,EAAA,mBAA2B,MAC3BA,EAAA,aAAuB,MAOjC,MAAM,gBAAuC,CAC3C,GAAI,KAAK,YACP,OAAO,KAAK,YAId,GAAI,CACF,KAAM,CAAE,eAAAC,CAAA,EAAmB,MAAAC,EAAA,+BAAAD,CAAA,QAAM,2BAAAE,CAAA,EAAmB,sBAAAF,CAAA,WAC9CG,EAAW,MAAMH,EAAe,QAAQ,cAAc,EACtDI,EAAQ,MAAMJ,EAAe,QAAQ,YAAY,EAEvD,GAAIG,GAAYC,EACd,YAAK,YAAcD,EACnB,KAAK,MAAQC,EACN,KAAK,WAEhB,OAASV,EAAO,CACd,QAAQ,MAAM,oCAAqCA,CAAK,CAC1D,CAEA,OAAO,IACT,CAEA,MAAgB,aAAaW,EAAYD,EAA8B,CACrE,GAAI,CACF,KAAM,CAAE,eAAAJ,CAAA,EAAmB,MAAAC,EAAA,+BAAAD,CAAA,QAAM,2BAAAE,CAAA,EAAmB,sBAAAF,CAAA,WACpD,MAAMA,EAAe,QAAQ,eAAgBK,CAAI,EACjD,MAAML,EAAe,QAAQ,aAAcI,CAAK,EAChD,KAAK,YAAcC,EACnB,KAAK,MAAQD,CACf,OAASV,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACR,CACF,CAEA,MAAgB,eAA+B,CAC7C,GAAI,CACF,KAAM,CAAE,eAAAM,CAAA,EAAmB,MAAAC,EAAA,+BAAAD,CAAA,QAAM,2BAAAE,CAAA,EAAmB,sBAAAF,CAAA,WACpD,MAAMA,EAAe,WAAW,cAAc,EAC9C,MAAMA,EAAe,WAAW,YAAY,EAC5C,KAAK,YAAc,KACnB,KAAK,MAAQ,IACf,OAASN,EAAO,CACd,cAAQ,MAAM,6BAA8BA,CAAK,EAC3CA,CACR,CACF,CAEA,UAA0B,CACxB,OAAO,KAAK,KACd,CAEA,iBAA2B,CACzB,OAAO,KAAK,cAAgB,MAAQ,KAAK,QAAU,IACrD,CACF,CCrEO,MAAMY,EAAa,CACxB,QAA0C,wBAG5C,ECRO,MAAMC,UAAuBT,CAAgB,CAA7C,kCACGC,EAAA,kBAAaO,EAAW,SAEhC,MAAM,MAAME,EAA0C,CACpD,GAAI,CACF,MAAMC,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,cAAe,CAC5D,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CACnB,GAAGD,EACH,SAAU,MACX,EACF,EAED,GAAI,CAACC,EAAS,GACZ,MAAM,IAAI,MAAM,iBAAiBA,EAAS,UAAU,EAAE,EAGxD,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAIC,EAAK,QAAS,CAChB,MAAML,EAAOK,EAAK,KAAK,KACjBN,EAAQM,EAAK,KAAK,MAExB,aAAM,KAAK,aAAaL,EAAMD,CAAK,EAC5BC,CACT,KACE,OAAM,IAAI,MAAMK,EAAK,SAAW,cAAc,CAElD,OAAShB,EAAO,CACd,cAAQ,MAAM,oBAAqBA,CAAK,EAClCA,CACR,CACF,CAEA,MAAM,SAASS,EAA0C,CACvD,GAAI,CACF,MAAMM,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,iBAAkB,CAC/D,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CACnB,GAAGN,EACH,SAAU,MACX,EACF,EAED,GAAI,CAACM,EAAS,GACZ,MAAM,IAAI,MAAM,wBAAwBA,EAAS,UAAU,EAAE,EAG/D,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAIC,EAAK,QAAS,CAChB,MAAML,EAAOK,EAAK,KAAK,KACjBN,EAAQM,EAAK,KAAK,MAExB,aAAM,KAAK,aAAaL,EAAMD,CAAK,EAC5BC,CACT,KACE,OAAM,IAAI,MAAMK,EAAK,SAAW,qBAAqB,CAEzD,OAAShB,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CACR,CACF,CAEA,MAAM,QAAwB,CAC5B,GAAI,CACE,KAAK,OACP,MAAM,MAAM,GAAG,KAAK,UAAU,eAAgB,CAC5C,OAAQ,OACR,QAAS,CACP,cAAiB,UAAU,KAAK,KAAK,GACrC,eAAgB,mBAClB,CACD,CAEL,OAASA,EAAO,CACd,QAAQ,MAAM,8BAA+BA,CAAK,CACpD,SAEE,MAAM,KAAK,eACb,CACF,CAEA,MAAM,cAAgC,CACpC,GAAI,CACF,KAAM,CAAE,eAAAM,CAAA,EAAmB,MAAAC,EAAA,+BAAAD,CAAA,QAAM,2BAAAE,CAAA,EAAmB,sBAAAF,CAAA,WAC9CW,EAAe,MAAMX,EAAe,QAAQ,eAAe,EAEjE,GAAI,CAACW,EACH,MAAM,IAAI,MAAM,4BAA4B,EAG9C,MAAMF,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,gBAAiB,CAC9D,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,aAAAE,EAAc,EACtC,EAED,GAAI,CAACF,EAAS,GACZ,MAAM,IAAI,MAAM,yBAAyBA,EAAS,UAAU,EAAE,EAGhE,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAIC,EAAK,QAAS,CAChB,MAAME,EAAWF,EAAK,KAAK,MAC3B,aAAMV,EAAe,QAAQ,aAAcY,CAAQ,EACnD,KAAK,MAAQA,EACNA,CACT,KACE,OAAM,IAAI,MAAMF,EAAK,SAAW,sBAAsB,CAE1D,OAAShB,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAEhD,MAAM,KAAK,gBACLA,CACR,CACF,CAGA,MAAM,iBAAiC,CAErC,MAAM,IAAI,MAAM,kCAAkC,CACpD,CAEA,MAAM,mBAAmC,CAEvC,MAAM,IAAI,MAAM,oCAAoC,CACtD,CAEA,MAAM,eAAemB,EAA8B,CACjD,GAAI,CACF,MAAMJ,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,wBAAyB,CACtE,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,MAAAI,EAAO,EAC/B,EAED,GAAI,CAACJ,EAAS,GACZ,MAAM,IAAI,MAAM,2BAA2BA,EAAS,UAAU,EAAE,EAGlE,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAI,CAACC,EAAK,QACR,MAAM,IAAI,MAAMA,EAAK,SAAW,wBAAwB,CAE5D,OAAShB,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5CA,CACR,CACF,CAEA,MAAM,cAAcU,EAAeU,EAAoC,CACrE,GAAI,CACF,MAAML,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,uBAAwB,CACrE,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,MAAAL,EAAO,YAAAU,EAAa,EAC5C,EAED,GAAI,CAACL,EAAS,GACZ,MAAM,IAAI,MAAM,0BAA0BA,EAAS,UAAU,EAAE,EAGjE,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAI,CAACC,EAAK,QACR,MAAM,IAAI,MAAMA,EAAK,SAAW,uBAAuB,CAE3D,OAAShB,EAAO,CACd,cAAQ,MAAM,6BAA8BA,CAAK,EAC3CA,CACR,CACF,CACF,CChMO,MAAMqB,CAAW,CACtB,OAAe,mBAA6B,CAC1C,OAAO,OAAO,OAAW,MACjB,OAAO,yBACP,UAAU,UAAU,SAAS,MAAM,GACnC,OAAO,IACjB,CAGA,aAAa,gBAAkC,CAC7C,GAAI,CAAC,KAAK,oBACR,eAAQ,KAAK,gDAAgD,EACtD,uBAGT,GAAI,CACF,KAAM,CAAE,eAAAC,CAAA,EAAmB,MAAAf,EAAA,+BAAAe,GAAA,KAAM,QAAO,SAAS,wBAAAA,CAAA,OACjD,OAAO,MAAMA,EAAA,CACf,OAAStB,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5CA,CACR,CACF,CAEA,aAAa,aAAc,CACzB,GAAI,CAAC,KAAK,oBACR,eAAQ,KAAK,6CAA6C,EACnD,CACL,GAAI,YACJ,KAAM,YACN,OAAQ,kCACR,MAAO,gBAIX,GAAI,CACF,KAAM,CAAE,YAAAuB,CAAA,EAAgB,MAAAhB,EAAA,4BAAAgB,GAAA,KAAM,QAAO,SAAS,qBAAAA,CAAA,OAC9C,OAAO,MAAMA,EAAA,CACf,OAASvB,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CACR,CACF,CAEA,aAAa,gBAAkC,CAC7C,GAAI,CAAC,KAAK,oBACR,eAAQ,KAAK,gDAAgD,EACtD,eAGT,GAAI,CACF,KAAM,CAAE,eAAAwB,CAAA,EAAmB,MAAAjB,EAAA,+BAAAiB,GAAA,KAAM,QAAO,SAAS,wBAAAA,CAAA,OACjD,OAAO,MAAMA,EAAA,CACf,OAASxB,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5CA,CACR,CACF,CAGA,aAAa,aAAc,CACzB,GAAI,CAAC,KAAK,oBACR,eAAQ,KAAK,6CAA6C,EAEnD,CACL,SAAU,QACV,UAAW,SACX,SAAU,IAId,GAAI,CACF,KAAM,CAAE,YAAAyB,CAAA,EAAgB,MAAAlB,EAAA,4BAAAkB,GAAA,KAAM,QAAO,SAAS,qBAAAA,CAAA,OAC9C,OAAO,MAAMA,EAAA,CACf,OAASzB,EAAO,CACd,cAAQ,MAAM,0BAA2BA,CAAK,EACxCA,CACR,CACF,CAGA,aAAa,SAAS0B,EAAgB,CACpC,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,0CAA0C,EACvD,MACF,CAEA,GAAI,CACF,KAAM,CAAE,SAAAC,CAAA,EAAa,MAAApB,EAAA,yBAAAoB,GAAA,KAAM,QAAO,SAAS,kBAAAA,CAAA,OAC3C,OAAO,MAAMA,EAAS,CAAE,OAAAD,EAAQ,CAClC,OAAS1B,EAAO,CACd,cAAQ,MAAM,uBAAwBA,CAAK,EACrCA,CACR,CACF,CAEA,aAAa,SAAS4B,EAAc,CAClC,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,0CAA0C,EACvD,MACF,CAEA,GAAI,CACF,KAAM,CAAE,SAAAC,CAAA,EAAa,MAAAtB,EAAA,yBAAAsB,GAAA,KAAM,QAAO,SAAS,kBAAAA,CAAA,OAC3C,OAAO,MAAMA,EAAS,CAAE,KAAAD,EAAM,CAChC,OAAS5B,EAAO,CACd,cAAQ,MAAM,uBAAwBA,CAAK,EACrCA,CACR,CACF,CAEA,aAAa,YAAY8B,EAKtB,CACD,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,6CAA6C,EAC1D,MACF,CAEA,GAAI,CACF,KAAM,CAAE,YAAAC,CAAA,EAAgB,MAAAxB,EAAA,4BAAAwB,GAAA,KAAM,QAAO,SAAS,qBAAAA,CAAA,OAC9C,OAAO,MAAMA,EAAYD,CAAM,CACjC,OAAS9B,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CACR,CACF,CAGA,aAAa,cAAcgC,EAKxB,CACD,GAAI,CAAC,KAAK,oBACR,eAAQ,KAAK,+CAA+C,EACrD,CACL,QAAS,GACT,MAAO,kDAIX,GAAI,CACF,KAAM,CAAE,QAAAC,CAAA,EAAY,MAAA1B,EAAA,wBAAA0B,GAAA,KAAM,QAAO,SAAS,iBAAAA,CAAA,OAC1C,OAAO,MAAMA,EAAQD,CAAK,CAC5B,OAAShC,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACR,CACF,CAGA,aAAa,WAAWkC,EAAalB,EAAW,CAC9C,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,gEAAgE,EAC7E,aAAa,QAAQkB,EAAK,KAAK,UAAUlB,CAAI,CAAC,EAC9C,MACF,CAEA,GAAI,CACF,KAAM,CAAE,WAAAmB,CAAA,EAAe,MAAA5B,EAAA,2BAAA4B,GAAA,KAAM,QAAO,SAAS,oBAAAA,CAAA,OAC7C,OAAO,MAAMA,EAAW,CAAE,IAAAD,EAAK,KAAAlB,EAAM,CACvC,OAAShB,EAAO,CACd,cAAQ,MAAM,yBAA0BA,CAAK,EACvCA,CACR,CACF,CAEA,aAAa,WAAWkC,EAAa,CACnC,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,gEAAgE,EAC7E,MAAME,EAAO,aAAa,QAAQF,CAAG,EACrC,MAAO,CAAE,KAAME,EAAO,KAAK,MAAMA,CAAI,EAAI,KAC3C,CAEA,GAAI,CACF,KAAM,CAAE,WAAAC,CAAA,EAAe,MAAA9B,EAAA,2BAAA8B,GAAA,KAAM,QAAO,SAAS,oBAAAA,CAAA,OAC7C,OAAO,MAAMA,EAAW,CAAE,IAAAH,EAAK,CACjC,OAASlC,EAAO,CACd,cAAQ,MAAM,yBAA0BA,CAAK,EACvCA,CACR,CACF,CAEA,aAAa,cAAe,CAC1B,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,kEAAkE,EAC/E,aAAa,QACb,MACF,CAEA,GAAI,CACF,KAAM,CAAE,aAAAsC,CAAA,EAAiB,MAAA/B,EAAA,6BAAA+B,GAAA,KAAM,QAAO,SAAS,sBAAAA,CAAA,OAC/C,OAAO,MAAMA,EAAA,CACf,OAAStC,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CACR,CACF,CAGA,aAAa,SAASuC,EAAe,CACnC,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,0CAA0C,EACvD,SAAS,MAAQA,EACjB,MACF,CAEA,GAAI,CACF,KAAM,CAAE,SAAAC,CAAA,EAAa,MAAAjC,EAAA,yBAAAiC,GAAA,KAAM,QAAO,SAAS,kBAAAA,CAAA,OAC3C,OAAO,MAAMA,EAASD,CAAK,CAC7B,OAASvC,EAAO,CACd,cAAQ,MAAM,uBAAwBA,CAAK,EACrCA,CACR,CACF,CAEA,aAAa,UAAUyC,EAAiB,CACtC,GAAI,CAAC,KAAK,oBAAqB,CAC7B,QAAQ,KAAK,2CAA2C,EACxD,QAAQ,IAAI,SAAUA,CAAO,EAC7B,MACF,CAEA,GAAI,CACF,KAAM,CAAE,UAAAC,CAAA,EAAc,MAAAnC,EAAA,0BAAAmC,GAAA,KAAM,QAAO,SAAS,mBAAAA,CAAA,OAC5C,OAAO,MAAMA,EAAUD,CAAO,CAChC,OAASzC,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtCA,CACR,CACF,CAEA,aAAa,UAAU8B,EAMpB,CACD,GAAI,CAAC,KAAK,oBACR,eAAQ,KAAK,2CAA2C,EACjD,CAAE,QAAS,OAAO,QAAQ,GAAGA,EAAO,KAAK;AAAA,EAAKA,EAAO,OAAO,EAAE,GAGvE,GAAI,CACF,KAAM,CAAE,UAAAa,CAAA,EAAc,MAAApC,EAAA,0BAAAoC,GAAA,KAAM,QAAO,SAAS,mBAAAA,CAAA,OAC5C,OAAO,MAAMA,EAAUb,CAAM,CAC/B,OAAS9B,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtCA,CACR,CACF,CACF,CC7PO,MAAM4C,WAAwBxC,CAAgB,CAA9C,kCACGC,EAAA,kBAAaO,EAAW,SAEhC,MAAM,MAAMiC,EAA4C,CACtD,GAAI,CAEF,MAAMC,EAAc,MAAMzB,EAAW,iBAC/B0B,EAAW,MAAM1B,EAAW,cAG5BN,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,mBAAoB,CACjE,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CACnB,YAAA+B,EACA,SAAAC,EACA,SAAU,OACX,EACF,EAED,GAAI,CAAChC,EAAS,GACZ,MAAM,IAAI,MAAM,sBAAsBA,EAAS,UAAU,EAAE,EAG7D,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAIC,EAAK,QAAS,CAChB,MAAML,EAAa,CACjB,GAAIK,EAAK,KAAK,KAAK,GACnB,KAAMA,EAAK,KAAK,KAAK,KACrB,OAAQA,EAAK,KAAK,KAAK,OACvB,MAAOA,EAAK,KAAK,KAAK,MACtB,MAAOA,EAAK,KAAK,KAAK,MACtB,UAAW,IAAI,KAAKA,EAAK,KAAK,KAAK,SAAS,EAC5C,UAAW,IAAI,KAAKA,EAAK,KAAK,KAAK,SAAS,GAGxCN,EAAQM,EAAK,KAAK,MACxB,aAAM,KAAK,aAAaL,EAAMD,CAAK,EAC5BC,CACT,KACE,OAAM,IAAI,MAAMK,EAAK,SAAW,mBAAmB,CAEvD,OAAShB,EAAO,CACd,cAAQ,MAAM,qBAAsBA,CAAK,EACnCA,CACR,CACF,CAEA,MAAM,SAASS,EAA0C,CACvD,GAAI,CAGF,MAAMqC,EAAc,MAAMzB,EAAW,iBAC/B0B,EAAW,MAAM1B,EAAW,cAE5BN,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,sBAAuB,CACpE,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CACnB,YAAA+B,EACA,SAAAC,EACA,eAAgBtC,EAChB,SAAU,OACX,EACF,EAED,GAAI,CAACM,EAAS,GACZ,MAAM,IAAI,MAAM,6BAA6BA,EAAS,UAAU,EAAE,EAGpE,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAIC,EAAK,QAAS,CAChB,MAAML,EAAa,CACjB,GAAIK,EAAK,KAAK,KAAK,GACnB,KAAMA,EAAK,KAAK,KAAK,KACrB,OAAQA,EAAK,KAAK,KAAK,OACvB,MAAOA,EAAK,KAAK,KAAK,MACtB,MAAOA,EAAK,KAAK,KAAK,MACtB,UAAW,IAAI,KAAKA,EAAK,KAAK,KAAK,SAAS,EAC5C,UAAW,IAAI,KAAKA,EAAK,KAAK,KAAK,SAAS,GAGxCN,EAAQM,EAAK,KAAK,MACxB,aAAM,KAAK,aAAaL,EAAMD,CAAK,EAC5BC,CACT,KACE,OAAM,IAAI,MAAMK,EAAK,SAAW,0BAA0B,CAE9D,OAAShB,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACR,CACF,CAEA,MAAM,QAAwB,CAC5B,GAAI,CACE,KAAK,OACP,MAAM,MAAM,GAAG,KAAK,UAAU,eAAgB,CAC5C,OAAQ,OACR,QAAS,CACP,cAAiB,UAAU,KAAK,KAAK,GACrC,eAAgB,mBAClB,CACD,CAEL,OAASA,EAAO,CACd,QAAQ,MAAM,+BAAgCA,CAAK,CACrD,SAEE,MAAM,KAAK,eACb,CACF,CAEA,MAAM,cAAgC,CACpC,GAAI,CAEF,MAAM8C,EAAc,MAAMzB,EAAW,iBAE/BN,EAAW,MAAM,MAAM,GAAG,KAAK,UAAU,qBAAsB,CACnE,OAAQ,OACR,QAAS,CACP,eAAgB,oBAElB,KAAM,KAAK,UAAU,CAAE,YAAA+B,EAAa,EACrC,EAED,GAAI,CAAC/B,EAAS,GACZ,MAAM,IAAI,MAAM,8BAA8BA,EAAS,UAAU,EAAE,EAGrE,MAAMC,EAAO,MAAMD,EAAS,OAE5B,GAAIC,EAAK,QAAS,CAChB,MAAME,EAAWF,EAAK,KAAK,MACrB,CAAE,eAAAV,CAAA,EAAmB,MAAAC,EAAA,+BAAAD,CAAA,QAAM,2BAAAE,CAAA,EAAmB,sBAAAF,CAAA,WACpD,aAAMA,EAAe,QAAQ,aAAcY,CAAQ,EACnD,KAAK,MAAQA,EACNA,CACT,KACE,OAAM,IAAI,MAAMF,EAAK,SAAW,2BAA2B,CAE/D,OAAShB,EAAO,CACd,cAAQ,MAAM,6BAA8BA,CAAK,EAEjD,MAAM,KAAK,gBACLA,CACR,CACF,CAGA,MAAM,gBAAkC,CACtC,GAAI,CACF,OAAO,MAAMqB,EAAW,gBAC1B,OAASrB,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5CA,CACR,CACF,CAEA,MAAM,sBAAsB4B,EAA6B,CACvD,GAAI,CACF,MAAMP,EAAW,SAASO,CAAI,CAChC,OAAS5B,EAAO,CACd,cAAQ,MAAM,uBAAwBA,CAAK,EACrCA,CACR,CACF,CAEA,MAAM,YAAYgD,EAKA,CAChB,GAAI,CACF,MAAM3B,EAAW,YAAY2B,CAAO,CACtC,OAAShD,EAAO,CACd,cAAQ,MAAM,2BAA4BA,CAAK,EACzCA,CACR,CACF,CAEA,MAAM,SAAS0B,EAA+B,CAC5C,GAAI,CACF,MAAML,EAAW,SAASK,CAAM,CAClC,OAAS1B,EAAO,CACd,cAAQ,MAAM,uBAAwBA,CAAK,EACrCA,CACR,CACF,CACF,CCnMA,MAAMiD,CAAmB,CAGvB,OAAO,aAA2B,CAChC,GAAI,CAAC,KAAK,SAAU,CAClB,MAAMC,EAAW/C,EAAA,EACjB,KAAK,SAAW+C,IAAahD,EAAS,KAClC,IAAI0C,GACJ,IAAI/B,CACV,CACA,OAAO,KAAK,QACd,CAEA,OAAO,eAAsB,CAC3B,KAAK,SAAW,MAClB,CACF,CAfER,EADI4C,EACW,YAiBV,MAAME,EAAcF,EAAmB,cCfvC,MAAMG,CAA4C,CACvD,MAAM,QAAQlB,EAAamB,EAA2B,CACpD,GAAI,CACF,MAAMC,EAAkB,KAAK,UAAUD,CAAK,EAC5C,aAAa,QAAQnB,EAAKoB,CAAe,CAC3C,OAAStD,EAAO,CACd,cAAQ,MAAM,8BAA+BA,CAAK,EAC5CA,CACR,CACF,CAEA,MAAM,QAAQkC,EAA2B,CACvC,GAAI,CACF,MAAME,EAAO,aAAa,QAAQF,CAAG,EACrC,OAAOE,EAAO,KAAK,MAAMA,CAAI,EAAI,IACnC,OAASpC,EAAO,CACd,eAAQ,MAAM,8BAA+BA,CAAK,EAC3C,IACT,CACF,CAEA,MAAM,WAAWkC,EAA4B,CAC3C,GAAI,CACF,aAAa,WAAWA,CAAG,CAC7B,OAASlC,EAAO,CACd,cAAQ,MAAM,iCAAkCA,CAAK,EAC/CA,CACR,CACF,CAEA,MAAM,OAAuB,CAC3B,GAAI,CACF,aAAa,OACf,OAASA,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACR,CACF,CACF,CC5CO,MAAMuD,CAA6C,CACxD,MAAM,QAAQrB,EAAamB,EAA2B,CACpD,GAAI,CAEF,KAAM,CAAE,WAAAlB,CAAA,EAAe,MAAA5B,EAAA,2BAAA4B,GAAA,KAAM,QAAO,SAAS,oBAAAA,CAAA,OAC7C,MAAMA,EAAW,CAAE,IAAAD,EAAK,KAAMmB,EAAO,CACvC,OAASrD,EAAO,CACd,QAAQ,MAAM,+BAAgCA,CAAK,EAEnD,GAAI,CACF,aAAa,QAAQkC,EAAK,KAAK,UAAUmB,CAAK,CAAC,CACjD,MAAwB,CACtB,MAAMrD,CACR,CACF,CACF,CAEA,MAAM,QAAQkC,EAA2B,CACvC,GAAI,CACF,KAAM,CAAE,WAAAG,CAAA,EAAe,MAAA9B,EAAA,2BAAA8B,GAAA,KAAM,QAAO,SAAS,oBAAAA,CAAA,OAE7C,OADe,MAAMA,EAAW,CAAE,IAAAH,EAAK,GACzB,IAChB,OAASlC,EAAO,CACd,QAAQ,MAAM,+BAAgCA,CAAK,EAEnD,GAAI,CACF,MAAMoC,EAAO,aAAa,QAAQF,CAAG,EACrC,OAAOE,EAAO,KAAK,MAAMA,CAAI,EAAI,IACnC,MAAwB,CACtB,OAAO,IACT,CACF,CACF,CAEA,MAAM,WAAWF,EAA4B,CAC3C,GAAI,CACF,KAAM,CAAE,WAAAC,CAAA,EAAe,MAAA5B,EAAA,2BAAA4B,GAAA,KAAM,QAAO,SAAS,oBAAAA,CAAA,OAC7C,MAAMA,EAAW,CAAE,IAAAD,EAAK,KAAM,KAAM,CACtC,OAASlC,EAAO,CACd,QAAQ,MAAM,kCAAmCA,CAAK,EAEtD,GAAI,CACF,aAAa,WAAWkC,CAAG,CAC7B,MAAwB,CACtB,MAAMlC,CACR,CACF,CACF,CAEA,MAAM,OAAuB,CAC3B,GAAI,CACF,KAAM,CAAE,aAAAsC,CAAA,EAAiB,MAAA/B,EAAA,6BAAA+B,GAAA,KAAM,QAAO,SAAS,sBAAAA,CAAA,OAC/C,MAAMA,EAAA,CACR,OAAStC,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,EAEjD,GAAI,CACF,aAAa,OACf,MAAwB,CACtB,MAAMA,CACR,CACF,CACF,CACF,CC5DA,MAAMwD,CAAsB,CAG1B,OAAO,aAA8B,CACnC,GAAI,CAAC,KAAK,SAAU,CAClB,MAAMN,EAAW/C,EAAA,EACjB,KAAK,SAAW+C,IAAahD,EAAS,KAClC,IAAIqD,EACJ,IAAIH,CACV,CACA,OAAO,KAAK,QACd,CACF,CAXE/C,EADImD,EACW,YAaV,MAAMlD,GAAiBkD,EAAsB,sKCT9CC,GAAcC,gBAA4C,MAAS,EAc5DC,GAA4C,CAAC,CAAE,SAAArE,KAAe,CACzE,KAAM,CAACqB,EAAMiD,CAAO,EAAIC,WAAsB,IAAI,EAC5C,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAI,EAE3CG,YAAU,IAAM,EACG,SAAY,CAC3B,GAAI,CACF,MAAMC,EAAc,MAAMd,EAAY,iBACtCS,EAAQK,CAAW,CACrB,OAASjE,EAAO,CACd,QAAQ,MAAM,8BAA+BA,CAAK,CACpD,SACE+D,EAAW,EAAK,CAClB,CACF,GAEA,CACF,EAAG,EAAE,EA0BL,MAAMV,EAA0B,CAC9B,KAAA1C,EACA,MA1BY,MAAOG,GAAsB,CACzCiD,EAAW,EAAI,EACf,GAAI,CACF,MAAMtD,EAAW,MAAM0C,EAAY,MAAMrC,CAAW,EACpD,OAAA8C,EAAQnD,CAAQ,EACTA,CACT,OAAST,EAAO,CACd,cAAQ,MAAM,gBAAiBA,CAAK,EAC9BA,CACR,SACE+D,EAAW,EAAK,CAClB,CACF,EAeE,OAba,SAAY,CACzB,GAAI,CACF,MAAMZ,EAAY,SAClBS,EAAQ,IAAI,CACd,OAAS5D,EAAO,CACd,cAAQ,MAAM,iBAAkBA,CAAK,EAC/BA,CACR,CACF,EAME,QAAA8D,CAAA,EAGF,OACEtE,MAACiE,GAAY,SAAZ,CAAqB,MAAAJ,EACnB,SAAA/D,CAAA,CACH,CAEJ,ECxEM4E,GAAWC,EAAM,KAAK,IAAA5D,EAAA,IAAM,OAAO,wBAAkB,yBAAC,EACtD6D,GAAYD,EAAM,KAAK,IAAA5D,EAAA,IAAM,OAAO,yBAAmB,yBAAC,EACxD8D,GAAiBF,EAAM,KAAK,IAAA5D,EAAA,IAAM,OAAO,8BAAwB,yBAAC,EAClE+D,GAAcH,EAAM,KAAK,IAAA5D,EAAA,IAAM,OAAO,2BAAqB,yBAAC,EAC5DgE,GAAYJ,EAAM,KAAK,IAAA5D,EAAA,IAAM,OAAO,yBAAmB,yBAAC,EACxDiE,GAAeL,EAAM,KAAK,IAAA5D,EAAA,IAAM,OAAO,4BAAsB,yBAAC,EAEpE,SAASkE,IAAM,CACb,OACEjF,MAACK,EAAA,CACC,SAAAL,MAACmE,GAAA,CACC,SAAAnE,MAACH,EAAA,CACC,SAAAG,MAACkF,WAAA,CAAS,SAAUlF,MAACC,EAAA,EAAe,EAClC,gBAACkF,EAAA,CACC,UAAAnF,MAACoF,GAAM,KAAK,IAAI,QAASpF,MAAC0E,KAAS,EAAI,QACtCU,EAAA,CAAM,KAAK,SAAS,QAASpF,MAAC4E,KAAU,EAAI,QAC5CQ,EAAA,CAAM,KAAK,SAAS,QAASpF,MAAC+E,KAAU,EAAI,QAC5CK,EAAA,CAAM,KAAK,aAAa,QAASpF,MAAC6E,KAAe,EAAI,QACrDO,EAAA,CAAM,KAAK,WAAW,QAASpF,MAAC8E,KAAY,EAAI,QAChDM,EAAA,CAAM,KAAK,IAAI,QAASpF,MAACgF,KAAa,EAAI,GAC7C,EACF,EACF,EACF,EACF,CAEJ,CClBA,MAAMK,GAAOC,EAAS,WACpB,SAAS,eAAe,MAAM,CAChC,EAEAD,GAAK,aACFV,EAAM,WAAN,CACC,eAACY,EAAA,CACC,SAAAvF,MAACiF,GAAA,EAAI,EACP,EACF,CACF", "names": ["f", "require$$0", "k", "l", "m", "n", "p", "q", "c", "a", "g", "b", "d", "e", "h", "reactJsxRuntime_production_min", "jsxRuntimeModule", "client", "Layout", "children", "jsxs", "jsx", "LoadingSpinner", "size", "className", "sizeClasses", "Error<PERSON>ou<PERSON><PERSON>", "Component", "props", "error", "errorInfo", "Platform", "detectPlatform", "BaseAuthService", "__publicField", "storageService", "__vitePreload", "index", "userData", "token", "user", "API_CONFIG", "WebAuthService", "credentials", "response", "data", "refreshToken", "newToken", "email", "newPassword", "ZMPService", "getAccessToken", "getUserInfo", "getPhoneNumber", "getLocation", "userId", "openChat", "oaId", "followOA", "params", "shareToChat", "order", "payment", "key", "setStorage", "item", "getStorage", "clearStorage", "title", "setTitle", "message", "showToast", "showModal", "ZaloAuthService", "_credentials", "accessToken", "userInfo", "content", "AuthServiceFactory", "platform", "authService", "WebStorageService", "value", "serializedValue", "ZaloStorageService", "StorageServiceFactory", "AuthContext", "createContext", "<PERSON>th<PERSON><PERSON><PERSON>", "setUser", "useState", "loading", "setLoading", "useEffect", "currentUser", "HomePage", "React", "LoginPage", "GameDetailPage", "ProfilePage", "GamesPage", "NotFoundPage", "App", "Suspense", "Routes", "Route", "root", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "ignoreList": [0, 1, 2], "sources": ["../../../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../../../node_modules/react/jsx-runtime.js", "../../../../node_modules/react-dom/client.js", "../../src/components/Layout.tsx", "../../src/components/LoadingSpinner.tsx", "../../src/components/ErrorBoundary.tsx", "../../../../shared/utils/platform.ts", "../../../../shared/services/auth/auth.service.ts", "../../../../shared/constants/config.ts", "../../../../shared/services/auth/auth.web.ts", "../../../../shared/services/zalo/zmp-sdk.ts", "../../../../shared/services/auth/auth.zalo.ts", "../../../../shared/services/auth/index.ts", "../../../../shared/services/storage/storage.web.ts", "../../../../shared/services/storage/storage.zalo.ts", "../../../../shared/services/storage/index.ts", "../../src/providers/AuthProvider.tsx", "../../src/App.tsx", "../../src/main.tsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import React from 'react';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport const Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <h1 className=\"text-2xl font-bold text-primary-600\">TapTap</h1>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"btn btn-primary\">Login</button>\n            </div>\n          </div>\n        </div>\n      </nav>\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {children}\n      </main>\n    </div>\n  );\n};\n", "import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'small' | 'medium' | 'large';\n  className?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'medium', \n  className = '' \n}) => {\n  const sizeClasses = {\n    small: 'w-5 h-5',\n    medium: 'w-10 h-10',\n    large: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`mx-auto ${sizeClasses[size]} ${className}`}>\n      <div className=\"animate-spin rounded-full border-4 border-primary-200 border-t-primary-600 h-full w-full\"></div>\n    </div>\n  );\n};\n", "import React, { Component, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    console.error('Error caught by boundary:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div style={{ padding: '20px', textAlign: 'center' }}>\n          <h1>Something went wrong.</h1>\n          <p>Please refresh the page or try again later.</p>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n", "// Platform Detection\nexport enum Platform {\n  WEB = 'web',\n  ZALO = 'zalo'\n}\n\n// Extend Window interface for Zalo Mini App\ndeclare global {\n  interface Window {\n    ZaloJavaScriptInterface?: any;\n    zmp?: any;\n  }\n}\n\nexport const detectPlatform = (): Platform => {\n  if (typeof window === 'undefined') return Platform.WEB;\n  \n  // Check if running in Zalo environment\n  if (window.ZaloJavaScriptInterface || \n      navigator.userAgent.includes('Zalo') ||\n      // Additional checks for Zalo Mini App environment\n      window.zmp ||\n      window.ZaloJavaScriptInterface) {\n    return Platform.ZALO;\n  }\n  \n  return Platform.WEB;\n};\n\nexport const isZaloMiniApp = (): boolean => detectPlatform() === Platform.ZALO;\nexport const isWebApp = (): boolean => detectPlatform() === Platform.WEB;\n\n// Platform-specific utilities\nexport const getPlatformConfig = () => {\n  const platform = detectPlatform();\n  \n  return {\n    platform,\n    isZalo: platform === Platform.ZALO,\n    isWeb: platform === Platform.WEB,\n    maxFileSize: platform === Platform.ZALO ? 3 * 1024 * 1024 : 10 * 1024 * 1024, // 3MB for Zalo, 10MB for Web\n    supportedFeatures: {\n      geolocation: true,\n      camera: platform === Platform.ZALO,\n      payment: platform === Platform.ZALO,\n      nativeStorage: platform === Platform.ZALO,\n      webShare: platform === Platform.WEB,\n      pushNotifications: platform === Platform.WEB\n    }\n  };\n};\n\n// Environment detection for development\nexport const isDevelopment = (): boolean => {\n  return process.env.NODE_ENV === 'development';\n};\n\nexport const isProduction = (): boolean => {\n  return process.env.NODE_ENV === 'production';\n};\n", "import { User, LoginRequest, RegisterRequest } from '../../types';\n\n// Common Auth Service Interface\nexport interface AuthService {\n  login(credentials?: LoginRequest): Promise<User>;\n  logout(): Promise<void>;\n  getCurrentUser(): Promise<User | null>;\n  register(userData: RegisterRequest): Promise<User>;\n  refreshToken(): Promise<string>;\n}\n\n// Base Auth Service with common functionality\nexport abstract class BaseAuthService implements AuthService {\n  protected currentUser: User | null = null;\n  protected token: string | null = null;\n\n  abstract login(credentials?: LoginRequest): Promise<User>;\n  abstract logout(): Promise<void>;\n  abstract register(userData: RegisterRequest): Promise<User>;\n  abstract refreshToken(): Promise<string>;\n\n  async getCurrentUser(): Promise<User | null> {\n    if (this.currentUser) {\n      return this.currentUser;\n    }\n\n    // Try to load from storage\n    try {\n      const { storageService } = await import('../storage');\n      const userData = await storageService.getItem('user_profile');\n      const token = await storageService.getItem('user_token');\n      \n      if (userData && token) {\n        this.currentUser = userData;\n        this.token = token;\n        return this.currentUser;\n      }\n    } catch (error) {\n      console.error('Failed to load user from storage:', error);\n    }\n\n    return null;\n  }\n\n  protected async saveUserData(user: User, token: string): Promise<void> {\n    try {\n      const { storageService } = await import('../storage');\n      await storageService.setItem('user_profile', user);\n      await storageService.setItem('user_token', token);\n      this.currentUser = user;\n      this.token = token;\n    } catch (error) {\n      console.error('Failed to save user data:', error);\n      throw error;\n    }\n  }\n\n  protected async clearUserData(): Promise<void> {\n    try {\n      const { storageService } = await import('../storage');\n      await storageService.removeItem('user_profile');\n      await storageService.removeItem('user_token');\n      this.currentUser = null;\n      this.token = null;\n    } catch (error) {\n      console.error('Failed to clear user data:', error);\n      throw error;\n    }\n  }\n\n  getToken(): string | null {\n    return this.token;\n  }\n\n  isAuthenticated(): boolean {\n    return this.currentUser !== null && this.token !== null;\n  }\n}\n", "// Common constants for the application\nexport const APP_CONFIG = {\n  name: 'TapTap',\n  version: '1.0.0',\n  description: 'Multi-platform TapTap application',\n  author: 'TapTap Team'\n};\n\nexport const API_CONFIG = {\n  baseURL: process.env.VITE_API_BASE_URL || 'https://api.taptap.com',\n  timeout: 10000,\n  retryAttempts: 3\n};\n\nexport const STORAGE_KEYS = {\n  USER_TOKEN: 'user_token',\n  USER_PROFILE: 'user_profile',\n  GAME_SETTINGS: 'game_settings',\n  THEME_PREFERENCE: 'theme_preference',\n  LANGUAGE: 'language'\n};\n\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/login',\n  PROFILE: '/profile',\n  GAMES: '/games',\n  GAME_DETAIL: '/games/:id',\n  SETTINGS: '/settings',\n  ABOUT: '/about'\n};\n\nexport const THEMES = {\n  LIGHT: 'light',\n  DARK: 'dark',\n  AUTO: 'auto'\n} as const;\n\nexport const LANGUAGES = {\n  EN: 'en',\n  VI: 'vi'\n} as const;\n\nexport const GAME_CATEGORIES = {\n  ACTION: 'action',\n  ADVENTURE: 'adventure',\n  PUZZLE: 'puzzle',\n  STRATEGY: 'strategy',\n  CASUAL: 'casual',\n  SPORTS: 'sports'\n} as const;\n\nexport const ZALO_CONFIG = {\n  appId: process.env.VITE_ZALO_APP_ID || '',\n  oaId: process.env.VITE_OA_ID || '',\n  maxFileSize: 3 * 1024 * 1024, // 3MB\n  supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif'],\n  supportedVideoTypes: ['video/mp4', 'video/quicktime']\n};\n\nexport const WEB_CONFIG = {\n  maxFileSize: 10 * 1024 * 1024, // 10MB\n  supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  supportedVideoTypes: ['video/mp4', 'video/webm', 'video/quicktime']\n};\n", "import { BaseAuthService } from './auth.service';\nimport { User, LoginRequest, RegisterRequest } from '../../types';\nimport { API_CONFIG } from '../../constants/config';\n\nexport class WebAuthService extends BaseAuthService {\n  private apiBaseUrl = API_CONFIG.baseURL;\n\n  async login(credentials: LoginRequest): Promise<User> {\n    try {\n      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...credentials,\n          platform: 'web'\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Login failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        const user = data.data.user;\n        const token = data.data.token;\n        \n        await this.saveUserData(user, token);\n        return user;\n      } else {\n        throw new Error(data.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Web login failed:', error);\n      throw error;\n    }\n  }\n\n  async register(userData: RegisterRequest): Promise<User> {\n    try {\n      const response = await fetch(`${this.apiBaseUrl}/auth/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...userData,\n          platform: 'web'\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Registration failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        const user = data.data.user;\n        const token = data.data.token;\n        \n        await this.saveUserData(user, token);\n        return user;\n      } else {\n        throw new Error(data.message || 'Registration failed');\n      }\n    } catch (error) {\n      console.error('Web registration failed:', error);\n      throw error;\n    }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      if (this.token) {\n        await fetch(`${this.apiBaseUrl}/auth/logout`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.token}`,\n            'Content-Type': 'application/json',\n          },\n        });\n      }\n    } catch (error) {\n      console.error('Web logout API call failed:', error);\n    } finally {\n      // Always clear local data\n      await this.clearUserData();\n    }\n  }\n\n  async refreshToken(): Promise<string> {\n    try {\n      const { storageService } = await import('../storage');\n      const refreshToken = await storageService.getItem('refresh_token');\n      \n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n\n      const response = await fetch(`${this.apiBaseUrl}/auth/refresh`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ refreshToken }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Token refresh failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        const newToken = data.data.token;\n        await storageService.setItem('user_token', newToken);\n        this.token = newToken;\n        return newToken;\n      } else {\n        throw new Error(data.message || 'Token refresh failed');\n      }\n    } catch (error) {\n      console.error('Web token refresh failed:', error);\n      // If refresh fails, clear user data\n      await this.clearUserData();\n      throw error;\n    }\n  }\n\n  // Web-specific methods\n  async loginWithGoogle(): Promise<User> {\n    // Implementation would depend on Google OAuth setup\n    throw new Error('Google login not implemented yet');\n  }\n\n  async loginWithFacebook(): Promise<User> {\n    // Implementation would depend on Facebook OAuth setup\n    throw new Error('Facebook login not implemented yet');\n  }\n\n  async forgotPassword(email: string): Promise<void> {\n    try {\n      const response = await fetch(`${this.apiBaseUrl}/auth/forgot-password`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Forgot password failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (!data.success) {\n        throw new Error(data.message || 'Forgot password failed');\n      }\n    } catch (error) {\n      console.error('Web forgot password failed:', error);\n      throw error;\n    }\n  }\n\n  async resetPassword(token: string, newPassword: string): Promise<void> {\n    try {\n      const response = await fetch(`${this.apiBaseUrl}/auth/reset-password`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ token, newPassword }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Reset password failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (!data.success) {\n        throw new Error(data.message || 'Reset password failed');\n      }\n    } catch (error) {\n      console.error('Web reset password failed:', error);\n      throw error;\n    }\n  }\n}\n", "// ZMP Service - Wrapper for Zalo Mini App SDK\nexport class ZMPService {\n  private static isZaloEnvironment(): boolean {\n    return typeof window !== 'undefined' && \n           (window.ZaloJavaScriptInterface || \n            navigator.userAgent.includes('Zalo') ||\n            window.zmp);\n  }\n\n  // Authentication\n  static async getAccessToken(): Promise<string> {\n    if (!this.isZaloEnvironment()) {\n      console.warn('getAccessToken called outside Zalo environment');\n      return 'DEFAULT_ACCESS_TOKEN';\n    }\n    \n    try {\n      const { getAccessToken } = await import('zmp-sdk');\n      return await getAccessToken();\n    } catch (error) {\n      console.error('Failed to get access token:', error);\n      throw error;\n    }\n  }\n\n  static async getUserInfo() {\n    if (!this.isZaloEnvironment()) {\n      console.warn('getUserInfo called outside Zalo environment');\n      return {\n        id: 'test-user',\n        name: 'Test User',\n        avatar: 'https://via.placeholder.com/150',\n        phone: '+84123456789'\n      };\n    }\n    \n    try {\n      const { getUserInfo } = await import('zmp-sdk');\n      return await getUserInfo();\n    } catch (error) {\n      console.error('Failed to get user info:', error);\n      throw error;\n    }\n  }\n\n  static async getPhoneNumber(): Promise<string> {\n    if (!this.isZaloEnvironment()) {\n      console.warn('getPhoneNumber called outside Zalo environment');\n      return '+84123456789';\n    }\n    \n    try {\n      const { getPhoneNumber } = await import('zmp-sdk');\n      return await getPhoneNumber();\n    } catch (error) {\n      console.error('Failed to get phone number:', error);\n      throw error;\n    }\n  }\n\n  // Location Services\n  static async getLocation() {\n    if (!this.isZaloEnvironment()) {\n      console.warn('getLocation called outside Zalo environment');\n      // Return mock location for development\n      return {\n        latitude: 21.0285,\n        longitude: 105.8542,\n        accuracy: 10\n      };\n    }\n    \n    try {\n      const { getLocation } = await import('zmp-sdk');\n      return await getLocation();\n    } catch (error) {\n      console.error('Failed to get location:', error);\n      throw error;\n    }\n  }\n\n  // Social Features\n  static async openChat(userId: string) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('openChat called outside Zalo environment');\n      return;\n    }\n    \n    try {\n      const { openChat } = await import('zmp-sdk');\n      return await openChat({ userId });\n    } catch (error) {\n      console.error('Failed to open chat:', error);\n      throw error;\n    }\n  }\n\n  static async followOA(oaId: string) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('followOA called outside Zalo environment');\n      return;\n    }\n    \n    try {\n      const { followOA } = await import('zmp-sdk');\n      return await followOA({ oaId });\n    } catch (error) {\n      console.error('Failed to follow OA:', error);\n      throw error;\n    }\n  }\n\n  static async shareToChat(params: {\n    title: string;\n    description: string;\n    thumbnail: string;\n    url: string;\n  }) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('shareToChat called outside Zalo environment');\n      return;\n    }\n    \n    try {\n      const { shareToChat } = await import('zmp-sdk');\n      return await shareToChat(params);\n    } catch (error) {\n      console.error('Failed to share to chat:', error);\n      throw error;\n    }\n  }\n\n  // Payment\n  static async createPayment(order: {\n    amount: number;\n    description: string;\n    orderId: string;\n    extraData?: Record<string, any>;\n  }) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('createPayment called outside Zalo environment');\n      return {\n        success: false,\n        error: 'Payment not supported outside Zalo environment'\n      };\n    }\n    \n    try {\n      const { payment } = await import('zmp-sdk');\n      return await payment(order);\n    } catch (error) {\n      console.error('Failed to create payment:', error);\n      throw error;\n    }\n  }\n\n  // Storage\n  static async setStorage(key: string, data: any) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('setStorage called outside Zalo environment, using localStorage');\n      localStorage.setItem(key, JSON.stringify(data));\n      return;\n    }\n    \n    try {\n      const { setStorage } = await import('zmp-sdk');\n      return await setStorage({ key, data });\n    } catch (error) {\n      console.error('Failed to set storage:', error);\n      throw error;\n    }\n  }\n\n  static async getStorage(key: string) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('getStorage called outside Zalo environment, using localStorage');\n      const item = localStorage.getItem(key);\n      return { data: item ? JSON.parse(item) : null };\n    }\n    \n    try {\n      const { getStorage } = await import('zmp-sdk');\n      return await getStorage({ key });\n    } catch (error) {\n      console.error('Failed to get storage:', error);\n      throw error;\n    }\n  }\n\n  static async clearStorage() {\n    if (!this.isZaloEnvironment()) {\n      console.warn('clearStorage called outside Zalo environment, using localStorage');\n      localStorage.clear();\n      return;\n    }\n    \n    try {\n      const { clearStorage } = await import('zmp-sdk');\n      return await clearStorage();\n    } catch (error) {\n      console.error('Failed to clear storage:', error);\n      throw error;\n    }\n  }\n\n  // UI\n  static async setTitle(title: string) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('setTitle called outside Zalo environment');\n      document.title = title;\n      return;\n    }\n    \n    try {\n      const { setTitle } = await import('zmp-sdk');\n      return await setTitle(title);\n    } catch (error) {\n      console.error('Failed to set title:', error);\n      throw error;\n    }\n  }\n\n  static async showToast(message: string) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('showToast called outside Zalo environment');\n      console.log('Toast:', message);\n      return;\n    }\n    \n    try {\n      const { showToast } = await import('zmp-sdk');\n      return await showToast(message);\n    } catch (error) {\n      console.error('Failed to show toast:', error);\n      throw error;\n    }\n  }\n\n  static async showModal(params: {\n    title: string;\n    content: string;\n    showCancel?: boolean;\n    cancelText?: string;\n    confirmText?: string;\n  }) {\n    if (!this.isZaloEnvironment()) {\n      console.warn('showModal called outside Zalo environment');\n      return { confirm: window.confirm(`${params.title}\\n${params.content}`) };\n    }\n    \n    try {\n      const { showModal } = await import('zmp-sdk');\n      return await showModal(params);\n    } catch (error) {\n      console.error('Failed to show modal:', error);\n      throw error;\n    }\n  }\n}\n", "import { BaseAuthService } from './auth.service';\nimport { User, LoginRequest, RegisterRequest } from '../../types';\nimport { ZMPService } from '../zalo/zmp-sdk';\nimport { API_CONFIG } from '../../constants/config';\n\nexport class ZaloAuthService extends BaseAuthService {\n  private apiBaseUrl = API_CONFIG.baseURL;\n\n  async login(_credentials?: LoginRequest): Promise<User> {\n    try {\n      // Zalo Mini App login flow\n      const accessToken = await ZMPService.getAccessToken();\n      const userInfo = await ZMPService.getUserInfo();\n      \n      // Send to backend for verification and user creation/login\n      const response = await fetch(`${this.apiBaseUrl}/auth/zalo-login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          accessToken,\n          userInfo,\n          platform: 'zalo'\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Zalo login failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        const user: User = {\n          id: data.data.user.id,\n          name: data.data.user.name,\n          avatar: data.data.user.avatar,\n          phone: data.data.user.phone,\n          email: data.data.user.email,\n          createdAt: new Date(data.data.user.createdAt),\n          updatedAt: new Date(data.data.user.updatedAt),\n        };\n        \n        const token = data.data.token;\n        await this.saveUserData(user, token);\n        return user;\n      } else {\n        throw new Error(data.message || 'Zalo login failed');\n      }\n    } catch (error) {\n      console.error('Zalo login failed:', error);\n      throw error;\n    }\n  }\n\n  async register(userData: RegisterRequest): Promise<User> {\n    try {\n      // For Zalo Mini App, registration is usually handled during login\n      // But we can still support additional profile completion\n      const accessToken = await ZMPService.getAccessToken();\n      const userInfo = await ZMPService.getUserInfo();\n      \n      const response = await fetch(`${this.apiBaseUrl}/auth/zalo-register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          accessToken,\n          userInfo,\n          additionalData: userData,\n          platform: 'zalo'\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Zalo registration failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        const user: User = {\n          id: data.data.user.id,\n          name: data.data.user.name,\n          avatar: data.data.user.avatar,\n          phone: data.data.user.phone,\n          email: data.data.user.email,\n          createdAt: new Date(data.data.user.createdAt),\n          updatedAt: new Date(data.data.user.updatedAt),\n        };\n        \n        const token = data.data.token;\n        await this.saveUserData(user, token);\n        return user;\n      } else {\n        throw new Error(data.message || 'Zalo registration failed');\n      }\n    } catch (error) {\n      console.error('Zalo registration failed:', error);\n      throw error;\n    }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      if (this.token) {\n        await fetch(`${this.apiBaseUrl}/auth/logout`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${this.token}`,\n            'Content-Type': 'application/json',\n          },\n        });\n      }\n    } catch (error) {\n      console.error('Zalo logout API call failed:', error);\n    } finally {\n      // Always clear local data\n      await this.clearUserData();\n    }\n  }\n\n  async refreshToken(): Promise<string> {\n    try {\n      // For Zalo Mini App, we can use the access token to refresh\n      const accessToken = await ZMPService.getAccessToken();\n      \n      const response = await fetch(`${this.apiBaseUrl}/auth/zalo-refresh`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ accessToken }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Zalo token refresh failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        const newToken = data.data.token;\n        const { storageService } = await import('../storage');\n        await storageService.setItem('user_token', newToken);\n        this.token = newToken;\n        return newToken;\n      } else {\n        throw new Error(data.message || 'Zalo token refresh failed');\n      }\n    } catch (error) {\n      console.error('Zalo token refresh failed:', error);\n      // If refresh fails, clear user data\n      await this.clearUserData();\n      throw error;\n    }\n  }\n\n  // Zalo-specific methods\n  async getPhoneNumber(): Promise<string> {\n    try {\n      return await ZMPService.getPhoneNumber();\n    } catch (error) {\n      console.error('Failed to get phone number:', error);\n      throw error;\n    }\n  }\n\n  async followOfficialAccount(oaId: string): Promise<void> {\n    try {\n      await ZMPService.followOA(oaId);\n    } catch (error) {\n      console.error('Failed to follow OA:', error);\n      throw error;\n    }\n  }\n\n  async shareToChat(content: {\n    title: string;\n    description: string;\n    thumbnail: string;\n    url: string;\n  }): Promise<void> {\n    try {\n      await ZMPService.shareToChat(content);\n    } catch (error) {\n      console.error('Failed to share to chat:', error);\n      throw error;\n    }\n  }\n\n  async openChat(userId: string): Promise<void> {\n    try {\n      await ZMPService.openChat(userId);\n    } catch (error) {\n      console.error('Failed to open chat:', error);\n      throw error;\n    }\n  }\n}\n", "import { detectPlatform, Platform } from '../../utils/platform';\nimport { WebAuthService } from './auth.web';\nimport { ZaloAuthService } from './auth.zalo';\nimport { AuthService } from './auth.service';\n\n// Auth Service Factory\nclass AuthServiceFactory {\n  private static instance: AuthService;\n\n  static getInstance(): AuthService {\n    if (!this.instance) {\n      const platform = detectPlatform();\n      this.instance = platform === Platform.ZALO \n        ? new ZaloAuthService() \n        : new WebAuthService();\n    }\n    return this.instance;\n  }\n\n  static resetInstance(): void {\n    this.instance = undefined as any;\n  }\n}\n\nexport const authService = AuthServiceFactory.getInstance();\n\n// Export all auth-related types and classes\nexport * from './auth.service';\nexport { WebAuthService } from './auth.web';\nexport { ZaloAuthService } from './auth.zalo';\nexport { AuthServiceFactory };\n", "// Storage Service Interface\nexport interface StorageService {\n  setItem(key: string, value: any): Promise<void>;\n  getItem(key: string): Promise<any>;\n  removeItem(key: string): Promise<void>;\n  clear(): Promise<void>;\n}\n\n// Web Storage Implementation\nexport class WebStorageService implements StorageService {\n  async setItem(key: string, value: any): Promise<void> {\n    try {\n      const serializedValue = JSON.stringify(value);\n      localStorage.setItem(key, serializedValue);\n    } catch (error) {\n      console.error('Web storage setItem failed:', error);\n      throw error;\n    }\n  }\n\n  async getItem(key: string): Promise<any> {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Web storage getItem failed:', error);\n      return null;\n    }\n  }\n\n  async removeItem(key: string): Promise<void> {\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Web storage removeItem failed:', error);\n      throw error;\n    }\n  }\n\n  async clear(): Promise<void> {\n    try {\n      localStorage.clear();\n    } catch (error) {\n      console.error('Web storage clear failed:', error);\n      throw error;\n    }\n  }\n}\n", "import { StorageService } from './storage.web';\n\n// Zalo Storage Implementation\nexport class ZaloStorageService implements StorageService {\n  async setItem(key: string, value: any): Promise<void> {\n    try {\n      // Use dynamic import to avoid issues when zmp-sdk is not available\n      const { setStorage } = await import('zmp-sdk');\n      await setStorage({ key, data: value });\n    } catch (error) {\n      console.error('Zalo storage setItem failed:', error);\n      // Fallback to localStorage if zmp-sdk is not available\n      try {\n        localStorage.setItem(key, JSON.stringify(value));\n      } catch (fallbackError) {\n        throw error;\n      }\n    }\n  }\n\n  async getItem(key: string): Promise<any> {\n    try {\n      const { getStorage } = await import('zmp-sdk');\n      const result = await getStorage({ key });\n      return result.data;\n    } catch (error) {\n      console.error('Zalo storage getItem failed:', error);\n      // Fallback to localStorage if zmp-sdk is not available\n      try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : null;\n      } catch (fallbackError) {\n        return null;\n      }\n    }\n  }\n\n  async removeItem(key: string): Promise<void> {\n    try {\n      const { setStorage } = await import('zmp-sdk');\n      await setStorage({ key, data: null });\n    } catch (error) {\n      console.error('Zalo storage removeItem failed:', error);\n      // Fallback to localStorage\n      try {\n        localStorage.removeItem(key);\n      } catch (fallbackError) {\n        throw error;\n      }\n    }\n  }\n\n  async clear(): Promise<void> {\n    try {\n      const { clearStorage } = await import('zmp-sdk');\n      await clearStorage();\n    } catch (error) {\n      console.error('Zalo storage clear failed:', error);\n      // Fallback to localStorage\n      try {\n        localStorage.clear();\n      } catch (fallbackError) {\n        throw error;\n      }\n    }\n  }\n}\n", "import { detectPlatform, Platform } from '../../utils/platform';\nimport { WebStorageService } from './storage.web';\nimport { ZaloStorageService } from './storage.zalo';\nimport { StorageService } from './storage.web';\n\n// Storage Service Factory\nclass StorageServiceFactory {\n  private static instance: StorageService;\n\n  static getInstance(): StorageService {\n    if (!this.instance) {\n      const platform = detectPlatform();\n      this.instance = platform === Platform.ZALO \n        ? new ZaloStorageService() \n        : new WebStorageService();\n    }\n    return this.instance;\n  }\n}\n\nexport const storageService = StorageServiceFactory.getInstance();\nexport * from './storage.web';\nexport { ZaloStorageService } from './storage.zalo';\n", "import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { authService } from '@taptap/shared';\nimport type { User } from '@taptap/shared';\n\ninterface AuthContextValue {\n  user: User | null;\n  login: (credentials?: any) => Promise<User>;\n  logout: () => Promise<void>;\n  loading: boolean;\n}\n\nconst AuthContext = createContext<AuthContextValue | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const currentUser = await authService.getCurrentUser();\n        setUser(currentUser);\n      } catch (error) {\n        console.error('Failed to get current user:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (credentials?: any) => {\n    setLoading(true);\n    try {\n      const userData = await authService.login(credentials);\n      setUser(userData);\n      return userData;\n    } catch (error) {\n      console.error('Login failed:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n    } catch (error) {\n      console.error('Logout failed:', error);\n      throw error;\n    }\n  };\n\n  const value: AuthContextValue = {\n    user,\n    login,\n    logout,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n", "import React, { Suspense } from 'react'\nimport { Routes, Route } from 'react-router-dom'\nimport { Layout } from './components/Layout'\nimport { LoadingSpinner } from './components/LoadingSpinner'\nimport { ErrorBoundary } from './components/ErrorBoundary'\nimport { AuthProvider } from './providers/AuthProvider'\n\n// Lazy load pages for better performance\nconst HomePage = React.lazy(() => import('./pages/HomePage'))\nconst LoginPage = React.lazy(() => import('./pages/LoginPage'))\nconst GameDetailPage = React.lazy(() => import('./pages/GameDetailPage'))\nconst ProfilePage = React.lazy(() => import('./pages/ProfilePage'))\nconst GamesPage = React.lazy(() => import('./pages/GamesPage'))\nconst NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'))\n\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <AuthProvider>\n        <Layout>\n          <Suspense fallback={<LoadingSpinner />}>\n            <Routes>\n              <Route path=\"/\" element={<HomePage />} />\n              <Route path=\"/login\" element={<LoginPage />} />\n              <Route path=\"/games\" element={<GamesPage />} />\n              <Route path=\"/games/:id\" element={<GameDetailPage />} />\n              <Route path=\"/profile\" element={<ProfilePage />} />\n              <Route path=\"*\" element={<NotFoundPage />} />\n            </Routes>\n          </Suspense>\n        </Layout>\n      </AuthProvider>\n    </ErrorBoundary>\n  )\n}\n\nexport default App\n", "import React from 'react'\nimport <PERSON>actDOM from 'react-dom/client'\nimport { <PERSON><PERSON>erRouter } from 'react-router-dom'\nimport App from './App'\nimport './styles/tailwind.css'\n\n// Error boundary for development\nif (process.env.NODE_ENV === 'development') {\n  // Add error boundary in development\n  const originalError = console.error;\n  console.error = (...args) => {\n    originalError(...args);\n    // You can add error reporting here\n  };\n}\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n)\n\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <App />\n    </BrowserRouter>\n  </React.StrictMode>\n)\n"], "file": "assets/index-Eixu5sWq.js"}