{"version": 3, "file": "GameDetailPage-DLB2DhXM.js", "sources": ["../../src/pages/GameDetailPage.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const GameDetailPage: React.FC = () => {\n  return (\n    <div style={{ padding: '20px' }}>\n      <h1>Game Details</h1>\n      <p>Game information and details</p>\n    </div>\n  );\n};\n\nexport default GameDetailPage;\n"], "names": ["GameDetailPage", "jsx"], "mappings": "qEAEO,MAAMA,EAA2B,WAEnC,MAAA,CAAI,MAAO,CAAE,QAAS,QACrB,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAA,cAAA,CAAY,EAChBA,EAAAA,IAAC,KAAE,SAAA,8BAAA,CAA4B,CAAA,EACjC"}