{"version": 3, "file": "ProfilePage-DsDpUSZG.js", "sources": ["../../src/pages/ProfilePage.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const ProfilePage: React.FC = () => {\n  return (\n    <div style={{ padding: '20px' }}>\n      <h1>Profile</h1>\n      <p>User profile and settings</p>\n    </div>\n  );\n};\n\nexport default ProfilePage;\n"], "names": ["ProfilePage", "jsx"], "mappings": "qEAEO,MAAMA,EAAwB,WAEhC,MAAA,CAAI,MAAO,CAAE,QAAS,QACrB,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAA,SAAA,CAAO,EACXA,EAAAA,IAAC,KAAE,SAAA,2BAAA,CAAyB,CAAA,EAC9B"}