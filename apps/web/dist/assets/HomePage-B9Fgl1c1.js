import{j as e}from"./index-Eixu5sWq.js";import"./vendor-C1xA9yCq.js";const g=({children:t,onClick:a,variant:r="primary",size:l="medium",disabled:n=!1,loading:s=!1,className:i="",type:o="button"})=>{const c="inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",m={primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",outline:"border border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500",ghost:"text-blue-700 hover:bg-blue-50 focus:ring-blue-500"},x={small:"px-3 py-1.5 text-sm",medium:"px-4 py-2 text-sm",large:"px-6 py-3 text-base"},d=`${c} ${m[r]} ${x[l]} ${i}`;return e.jsx("button",{type:o,className:d,onClick:a,disabled:n||s,children:s?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):t})},b=()=>e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to TapTap"}),e.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:"Your gaming hub for discovering new games and connecting with friends."}),e.jsx("div",{className:"space-y-4",children:e.jsx(g,{onClick:()=>alert("Explore games!"),className:"btn btn-primary px-8 py-3 text-lg",children:"Explore Games"})})]}),e.jsxs("div",{className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"card text-center",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Discover Games"}),e.jsx("p",{className:"text-gray-600",children:"Find your next favorite game from thousands of options."})]}),e.jsxs("div",{className:"card text-center",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Connect with Friends"}),e.jsx("p",{className:"text-gray-600",children:"Share your gaming experiences and compete with friends."})]}),e.jsxs("div",{className:"card text-center",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Track Progress"}),e.jsx("p",{className:"text-gray-600",children:"Monitor your gaming achievements and statistics."})]})]})]});export{b as HomePage,b as default};
//# sourceMappingURL=HomePage-B9Fgl1c1.js.map
