{"version": 3, "file": "HomePage-B9Fgl1c1.js", "sources": ["../../../../shared/components/ui/Button/Button.tsx", "../../src/pages/HomePage.tsx"], "sourcesContent": ["import React from 'react';\nimport { isZaloMiniApp } from '../../../utils/platform';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'small' | 'medium' | 'large';\n  disabled?: boolean;\n  loading?: boolean;\n  className?: string;\n  type?: 'button' | 'submit' | 'reset';\n}\n\nexport const Button: React.FC<ButtonProps> = ({\n  children,\n  onClick,\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  className = '',\n  type = 'button',\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variantClasses = {\n    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',\n    outline: 'border border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500',\n    ghost: 'text-blue-700 hover:bg-blue-50 focus:ring-blue-500'\n  };\n  \n  const sizeClasses = {\n    small: 'px-3 py-1.5 text-sm',\n    medium: 'px-4 py-2 text-sm',\n    large: 'px-6 py-3 text-base'\n  };\n\n  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      onClick={onClick}\n      disabled={disabled || loading}\n    >\n      {loading ? (\n        <>\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          Loading...\n        </>\n      ) : (\n        children\n      )}\n    </button>\n  );\n};\n\nexport default Button;\n", "import React from 'react';\nimport { Button } from '@taptap/shared';\n\nexport const HomePage: React.FC = () => {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">Welcome to TapTap</h1>\n        <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n          Your gaming hub for discovering new games and connecting with friends.\n        </p>\n        <div className=\"space-y-4\">\n          <Button \n            onClick={() => alert('Explore games!')}\n            className=\"btn btn-primary px-8 py-3 text-lg\"\n          >\n            Explore Games\n          </Button>\n        </div>\n      </div>\n      \n      <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\">\n        <div className=\"card text-center\">\n          <h3 className=\"text-xl font-semibold mb-2\">Discover Games</h3>\n          <p className=\"text-gray-600\">Find your next favorite game from thousands of options.</p>\n        </div>\n        <div className=\"card text-center\">\n          <h3 className=\"text-xl font-semibold mb-2\">Connect with Friends</h3>\n          <p className=\"text-gray-600\">Share your gaming experiences and compete with friends.</p>\n        </div>\n        <div className=\"card text-center\">\n          <h3 className=\"text-xl font-semibold mb-2\">Track Progress</h3>\n          <p className=\"text-gray-600\">Monitor your gaming achievements and statistics.</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "names": ["<PERSON><PERSON>", "children", "onClick", "variant", "size", "disabled", "loading", "className", "type", "baseClasses", "variantClasses", "sizeClasses", "buttonClasses", "jsx", "jsxs", "Fragment", "HomePage"], "mappings": "qEAcO,MAAMA,EAAgC,CAAC,CAC5C,SAAAC,EACA,QAAAC,EACA,QAAAC,EAAU,UACV,KAAAC,EAAO,SACP,SAAAC,EAAW,GACX,QAAAC,EAAU,GACV,UAAAC,EAAY,GACZ,KAAAC,EAAO,QACT,IAAM,CACJ,MAAMC,EAAc,uLAEdC,EAAiB,CACrB,QAAS,+DACT,UAAW,kEACX,QAAS,4EACT,MAAO,oDAAA,EAGHC,EAAc,CAClB,MAAO,sBACP,OAAQ,oBACR,MAAO,qBAAA,EAGHC,EAAgB,GAAGH,CAAW,IAAIC,EAAeP,CAAO,CAAC,IAAIQ,EAAYP,CAAI,CAAC,IAAIG,CAAS,GAEjG,OACEM,EAAAA,IAAC,SAAA,CACC,KAAAL,EACA,UAAWI,EACX,QAAAV,EACA,SAAUG,GAAYC,EAErB,WACCQ,EAAAA,KAAAC,EAAAA,SAAA,CACE,SAAA,CAAAD,OAAC,OAAI,UAAU,kCAAkC,KAAK,OAAO,QAAQ,YACnE,SAAA,CAAAD,EAAAA,IAAC,SAAA,CAAO,UAAU,aAAa,GAAG,KAAK,GAAG,KAAK,EAAE,KAAK,OAAO,eAAe,YAAY,IAAI,QAC3F,OAAA,CAAK,UAAU,aAAa,KAAK,eAAe,EAAE,iHAAA,CAAkH,CAAA,EACvK,EAAM,YAAA,CAAA,CAER,EAEAZ,CAAA,CAAA,CAIR,EC1Dae,EAAqB,IAE9BF,EAAAA,KAAC,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,cACb,SAAA,CAAAD,EAAAA,IAAC,KAAA,CAAG,UAAU,wCAAwC,SAAA,oBAAiB,EACvEA,EAAAA,IAAC,IAAA,CAAE,UAAU,+CAA+C,SAAA,yEAE5D,EACAA,EAAAA,IAAC,MAAA,CAAI,UAAU,YACb,SAAAA,EAAAA,IAACb,EAAA,CACC,QAAS,IAAM,MAAM,gBAAgB,EACrC,UAAU,oCACX,SAAA,eAAA,CAAA,CAED,CACF,CAAA,EACF,EAEAc,EAAAA,KAAC,MAAA,CAAI,UAAU,8CACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,mBACb,SAAA,CAAAD,EAAAA,IAAC,KAAA,CAAG,UAAU,6BAA6B,SAAA,iBAAc,EACzDA,EAAAA,IAAC,IAAA,CAAE,UAAU,gBAAgB,SAAA,yDAAA,CAAuD,CAAA,EACtF,EACAC,EAAAA,KAAC,MAAA,CAAI,UAAU,mBACb,SAAA,CAAAD,EAAAA,IAAC,KAAA,CAAG,UAAU,6BAA6B,SAAA,uBAAoB,EAC/DA,EAAAA,IAAC,IAAA,CAAE,UAAU,gBAAgB,SAAA,yDAAA,CAAuD,CAAA,EACtF,EACAC,EAAAA,KAAC,MAAA,CAAI,UAAU,mBACb,SAAA,CAAAD,EAAAA,IAAC,KAAA,CAAG,UAAU,6BAA6B,SAAA,iBAAc,EACzDA,EAAAA,IAAC,IAAA,CAAE,UAAU,gBAAgB,SAAA,kDAAA,CAAgD,CAAA,CAAA,CAC/E,CAAA,CAAA,CACF,CAAA,EACF"}