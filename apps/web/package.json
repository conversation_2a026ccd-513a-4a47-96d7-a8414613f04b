{"name": "@taptap/web", "version": "1.0.0", "description": "TapTap Web Application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:tsc": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@taptap/shared": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.0", "zustand": "^4.5.0"}, "devDependencies": {"@storybook/addon-essentials": "^8.4.6", "@storybook/addon-interactions": "^8.4.6", "@storybook/addon-links": "^8.4.6", "@storybook/addon-onboarding": "^8.4.6", "@storybook/blocks": "^8.4.6", "@storybook/react": "^8.4.6", "@storybook/react-vite": "^8.4.6", "@storybook/test": "^8.4.6", "@tailwindcss/forms": "^0.5.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react": "^4.3.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "postcss": "^8.5.6", "storybook": "^8.4.6", "tailwindcss": "^3.4.0", "typescript": "^5.5.0", "vite": "^5.4.0"}}