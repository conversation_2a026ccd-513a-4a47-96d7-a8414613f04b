/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "../../shared/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // TapTap Design System Colors
        'primary-pink': '#F65D79',
        'primary-white': '#FFFFFF',
        'primary-black': '#1A1818',
        'secondary-yellow-70': '#F9DB5B',
        'secondary-yellow-90': '#88700c',
        'secondary-yellow-40': '#fadd62',
        'grey-1': '#5A5A5A',
        'grey-2': '#9A9A9A',
        'grey-3': '#CACACA',
        'grey-5': '#ECECEC',
        'grey-6': '#F8F8F8',
        
        // Fallback colors để tương thích
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          500: '#F65D79',
          600: '#F65D79',
          700: '#F65D79',
          800: '#F65D79',
          900: '#F65D79',
        },
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          500: '#F9DB5B',
          600: '#F9DB5B',
          700: '#F9DB5B',
          800: '#F9DB5B',
          900: '#F9DB5B',
        }
      },
      fontFamily: {
        'archia': ['Archia', 'Inter', 'system-ui', 'sans-serif'],
        'sans': ['Archia', 'Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        '10': '10px',
        '12': '12px',
        '14': '14px',
        '18': '18px',
        '24': '24px',
      },
      lineHeight: {
        '16': '16px',
        '18': '18px',
        '22': '22px',
        '24': '24px',
        '32': '32px',
      },
      fontWeight: {
        'regular': 400,
        'medium': 500,
        'demibold': 600,
        'bold': 700,
      },
      spacing: {
        '3': '6px',
        '6': '16px',
      },
      borderRadius: {
        'sm': '4px',
        'md': '6px',
        'lg': '8px',
        'xl': '12px',
      },
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
