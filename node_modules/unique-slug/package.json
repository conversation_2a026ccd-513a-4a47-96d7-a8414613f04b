{"name": "unique-slug", "version": "5.0.0", "description": "Generate a unique character string suitible for use in files and URLs.", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "keywords": [], "author": "GitHub Inc.", "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.3.0"}, "repository": {"type": "git", "url": "git+https://github.com/npm/unique-slug.git"}, "dependencies": {"imurmurhash": "^0.1.4"}, "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}