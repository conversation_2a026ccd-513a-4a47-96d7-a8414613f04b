"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var preset_exports={};__export(preset_exports,{viteFinal:()=>viteFinal,webpackFinal:()=>webpackFinal});module.exports=__toCommonJS(preset_exports);var import_promises=require("fs/promises"),import_node_path=require("path"),getIsReactVersion18or19=async options=>{let{legacyRootApi}=await options.presets.apply("frameworkOptions")||{};if(legacyRootApi)return!1;let reactDom=(await options.presets.apply("resolvedReact",{})).reactDom||(0,import_node_path.dirname)(require.resolve("react-dom/package.json"));if(!(0,import_node_path.isAbsolute)(reactDom))return!1;let{version}=JSON.parse(await(0,import_promises.readFile)((0,import_node_path.join)(reactDom,"package.json"),"utf-8"));return version.startsWith("18")||version.startsWith("19")||version.startsWith("0.0.0")},webpackFinal=async(config,options)=>await getIsReactVersion18or19(options)?config:{...config,resolve:{...config.resolve,alias:{...config.resolve?.alias,"@storybook/react-dom-shim":"@storybook/react-dom-shim/dist/react-16"}}},viteFinal=async(config,options)=>{if(await getIsReactVersion18or19(options))return config;let alias=Array.isArray(config.resolve?.alias)?config.resolve.alias.concat({find:/^@storybook\/react-dom-shim$/,replacement:"@storybook/react-dom-shim/dist/react-16"}):{...config.resolve?.alias,"@storybook/react-dom-shim":"@storybook/react-dom-shim/dist/react-16"};return{...config,resolve:{...config.resolve,alias}}};0&&(module.exports={viteFinal,webpackFinal});
