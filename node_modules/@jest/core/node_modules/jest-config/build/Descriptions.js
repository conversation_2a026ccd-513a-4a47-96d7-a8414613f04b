'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.default = void 0;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

const descriptions = {
  automock: 'All imported modules in your tests should be mocked automatically',
  bail: 'Stop running tests after `n` failures',
  cacheDirectory:
    'The directory where Je<PERSON> should store its cached dependency information',
  clearMocks:
    'Automatically clear mock calls, instances, contexts and results before every test',
  collectCoverage:
    'Indicates whether the coverage information should be collected while executing the test',
  collectCoverageFrom:
    'An array of glob patterns indicating a set of files for which coverage information should be collected',
  coverageDirectory:
    'The directory where Jest should output its coverage files',
  coveragePathIgnorePatterns:
    'An array of regexp pattern strings used to skip coverage collection',
  coverageProvider:
    'Indicates which provider should be used to instrument code for coverage',
  coverageReporters:
    'A list of reporter names that <PERSON><PERSON> uses when writing coverage reports',
  coverageThreshold:
    'An object that configures minimum threshold enforcement for coverage results',
  dependencyExtractor: 'A path to a custom dependency extractor',
  errorOnDeprecated:
    'Make calling deprecated APIs throw helpful error messages',
  fakeTimers: 'The default configuration for fake timers',
  forceCoverageMatch:
    'Force coverage collection from ignored files using an array of glob patterns',
  globalSetup:
    'A path to a module which exports an async function that is triggered once before all test suites',
  globalTeardown:
    'A path to a module which exports an async function that is triggered once after all test suites',
  globals:
    'A set of global variables that need to be available in all test environments',
  maxWorkers:
    'The maximum amount of workers used to run your tests. Can be specified as % or a number. E.g. maxWorkers: 10% will use 10% of your CPU amount + 1 as the maximum worker number. maxWorkers: 2 will use a maximum of 2 workers.',
  moduleDirectories:
    "An array of directory names to be searched recursively up from the requiring module's location",
  moduleFileExtensions: 'An array of file extensions your modules use',
  moduleNameMapper:
    'A map from regular expressions to module names or to arrays of module names that allow to stub out resources with a single module',
  modulePathIgnorePatterns:
    "An array of regexp pattern strings, matched against all module paths before considered 'visible' to the module loader",
  notify: 'Activates notifications for test results',
  notifyMode:
    'An enum that specifies notification mode. Requires { notify: true }',
  preset: "A preset that is used as a base for Jest's configuration",
  projects: 'Run tests from one or more projects',
  reporters: 'Use this configuration option to add custom reporters to Jest',
  resetMocks: 'Automatically reset mock state before every test',
  resetModules: 'Reset the module registry before running each individual test',
  resolver: 'A path to a custom resolver',
  restoreMocks:
    'Automatically restore mock state and implementation before every test',
  rootDir:
    'The root directory that Jest should scan for tests and modules within',
  roots:
    'A list of paths to directories that Jest should use to search for files in',
  runner:
    "Allows you to use a custom runner instead of Jest's default test runner",
  setupFiles:
    'The paths to modules that run some code to configure or set up the testing environment before each test',
  setupFilesAfterEnv:
    'A list of paths to modules that run some code to configure or set up the testing framework before each test',
  slowTestThreshold:
    'The number of seconds after which a test is considered as slow and reported as such in the results.',
  snapshotSerializers:
    'A list of paths to snapshot serializer modules Jest should use for snapshot testing',
  testEnvironment: 'The test environment that will be used for testing',
  testEnvironmentOptions: 'Options that will be passed to the testEnvironment',
  testLocationInResults: 'Adds a location field to test results',
  testMatch: 'The glob patterns Jest uses to detect test files',
  testPathIgnorePatterns:
    'An array of regexp pattern strings that are matched against all test paths, matched tests are skipped',
  testRegex:
    'The regexp pattern or array of patterns that Jest uses to detect test files',
  testResultsProcessor:
    'This option allows the use of a custom results processor',
  testRunner: 'This option allows use of a custom test runner',
  transform: 'A map from regular expressions to paths to transformers',
  transformIgnorePatterns:
    'An array of regexp pattern strings that are matched against all source file paths, matched files will skip transformation',
  unmockedModulePathPatterns:
    'An array of regexp pattern strings that are matched against all modules before the module loader will automatically return a mock for them',
  verbose:
    'Indicates whether each individual test should be reported during the run',
  watchPathIgnorePatterns:
    'An array of regexp patterns that are matched against all source file paths before re-running tests in watch mode',
  watchman: 'Whether to use watchman for file crawling'
};
var _default = descriptions;
exports.default = _default;
