{"version": 3, "names": ["commander", "data", "_interopRequireWildcard", "require", "_core", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "program", "collect", "value", "previousValue", "values", "split", "push", "option", "usage", "parse", "process", "argv", "opts", "console", "log", "buildExternalHelpers", "whitelist", "outputType"], "sources": ["../src/babel-external-helpers.ts"], "sourcesContent": ["import * as commander from \"commander\";\nimport { buildExternalHelpers } from \"@babel/core\";\n\nconst program = process.env.BABEL_8_BREAKING\n  ? commander.program\n  : commander.default.program;\n\nfunction collect(value: unknown, previousValue: Array<string>): Array<string> {\n  // If the user passed the option with no value, like \"babel-external-helpers --whitelist\", do nothing.\n  if (typeof value !== \"string\") return previousValue;\n\n  const values = value.split(\",\");\n\n  if (previousValue) {\n    previousValue.push(...values);\n    return previousValue;\n  }\n  return values;\n}\n\nprogram.option(\n  \"-l, --whitelist [whitelist]\",\n  \"Whitelist of helpers to ONLY include\",\n  collect,\n);\nprogram.option(\n  \"-t, --output-type [type]\",\n  \"Type of output (global|umd|var)\",\n  \"global\",\n);\n\nprogram.usage(\"[options]\");\nprogram.parse(process.argv);\nconst opts = program.opts();\n\nconsole.log(buildExternalHelpers(opts.whitelist, opts.outputType));\n"], "mappings": ";;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmD,SAAAC,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEnD,MAAMkB,OAAO,GAETxB,SAAS,CAAD,CAAC,CAACe,OAAO,CAACS,OAAO;AAE7B,SAASC,OAAOA,CAACC,KAAc,EAAEC,aAA4B,EAAiB;EAE5E,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE,OAAOC,aAAa;EAEnD,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;EAE/B,IAAIF,aAAa,EAAE;IACjBA,aAAa,CAACG,IAAI,CAAC,GAAGF,MAAM,CAAC;IAC7B,OAAOD,aAAa;EACtB;EACA,OAAOC,MAAM;AACf;AAEAJ,OAAO,CAACO,MAAM,CACZ,6BAA6B,EAC7B,sCAAsC,EACtCN,OACF,CAAC;AACDD,OAAO,CAACO,MAAM,CACZ,0BAA0B,EAC1B,iCAAiC,EACjC,QACF,CAAC;AAEDP,OAAO,CAACQ,KAAK,CAAC,WAAW,CAAC;AAC1BR,OAAO,CAACS,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC;AAC3B,MAAMC,IAAI,GAAGZ,OAAO,CAACY,IAAI,CAAC,CAAC;AAE3BC,OAAO,CAACC,GAAG,CAAC,IAAAC,4BAAoB,EAACH,IAAI,CAACI,SAAS,EAAEJ,IAAI,CAACK,UAAU,CAAC,CAAC", "ignoreList": []}