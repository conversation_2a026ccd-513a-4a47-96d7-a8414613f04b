{"version": 3, "names": ["_slash", "data", "require", "_path", "_fs", "util", "watcher", "asyncGeneratorStep", "n", "t", "e", "r", "o", "a", "c", "i", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "FILE_TYPE", "Object", "freeze", "NON_COMPILABLE", "COMPILED", "IGNORED", "ERR_COMPILATION", "outputFileSync", "filePath", "v", "w", "split", "process", "versions", "node", "mkdirSync", "sync", "path", "dirname", "recursive", "fs", "writeFileSync", "_default", "_x", "_ref", "cliOptions", "babelOptions", "write", "_x2", "_x3", "_write", "src", "base", "relative", "isCompilableExtension", "extensions", "withExtension", "keepFileExtension", "extname", "outFileExtension", "dest", "getDest", "res", "compile", "assign", "sourceFileName", "slash", "map", "outputMap", "sourceMaps", "hasDataSourcemap", "code", "mapLoc", "addSourceMappingUrl", "file", "basename", "JSON", "stringify", "chmod", "verbose", "console", "log", "cwd", "err", "watch", "error", "filename", "join", "outDir", "handleFile", "_x4", "_x5", "_handleFile", "written", "copyFiles", "copyIgnored", "readFileSync", "handle", "_x6", "_handle", "filenameOrDir", "existsSync", "stat", "statSync", "isDirectory", "count", "files", "readdir", "includeDotfiles", "compiledFiles", "startTime", "logSuccess", "debounce", "diff", "hrtime", "Math", "round", "enable", "enableGlobbing", "skipInitialBuild", "deleteDirOnStart", "deleteDir", "filenames", "quiet", "flush", "processing", "getBase", "length", "absoluteBase", "filenameToBaseMap", "Map", "absoluteFilename", "absoluteFilenames", "sep", "get", "undefined", "absoluteFilenameOrDir", "startsWith", "set", "for<PERSON>ach", "startWatcher", "onFilesChange", "all", "filter", "Boolean"], "sources": ["../../src/babel/dir.ts"], "sourcesContent": ["import slash from \"slash\";\nimport path from \"node:path\";\nimport fs from \"node:fs\";\n\nimport * as util from \"./util.ts\";\nimport * as watcher from \"./watcher.ts\";\nimport type { CmdOptions } from \"./options.ts\";\n\nconst FILE_TYPE = Object.freeze({\n  NON_COMPILABLE: \"NON_COMPILABLE\",\n  COMPILED: \"COMPILED\",\n  IGNORED: \"IGNORED\",\n  ERR_COMPILATION: \"ERR_COMPILATION\",\n} as const);\n\nfunction outputFileSync(filePath: string, data: string | Buffer): void {\n  fs.mkdirSync(path.dirname(filePath), { recursive: true });\n  fs.writeFileSync(filePath, data);\n}\n\nexport default async function ({\n  cliOptions,\n  babelOptions,\n}: CmdOptions): Promise<void> {\n  async function write(\n    src: string,\n    base: string,\n  ): Promise<keyof typeof FILE_TYPE> {\n    let relative = path.relative(base, src);\n\n    if (!util.isCompilableExtension(relative, cliOptions.extensions)) {\n      return FILE_TYPE.NON_COMPILABLE;\n    }\n\n    relative = util.withExtension(\n      relative,\n      cliOptions.keepFileExtension\n        ? path.extname(relative)\n        : cliOptions.outFileExtension,\n    );\n\n    const dest = getDest(relative, base);\n\n    try {\n      const res = await util.compile(src, {\n        ...babelOptions,\n        sourceFileName: slash(path.relative(dest + \"/..\", src)),\n      });\n\n      if (!res) return FILE_TYPE.IGNORED;\n\n      if (res.map) {\n        let outputMap: \"both\" | \"external\" | false = false;\n        if (babelOptions.sourceMaps && babelOptions.sourceMaps !== \"inline\") {\n          outputMap = \"external\";\n        } else if (babelOptions.sourceMaps == null) {\n          outputMap = util.hasDataSourcemap(res.code) ? \"external\" : \"both\";\n        }\n\n        if (outputMap) {\n          const mapLoc = dest + \".map\";\n          if (outputMap === \"external\") {\n            res.code = util.addSourceMappingUrl(res.code, mapLoc);\n          }\n          res.map.file = path.basename(relative);\n          outputFileSync(mapLoc, JSON.stringify(res.map));\n        }\n      }\n\n      outputFileSync(dest, res.code);\n      util.chmod(src, dest);\n\n      if (cliOptions.verbose) {\n        console.log(path.relative(process.cwd(), src) + \" -> \" + dest);\n      }\n\n      return FILE_TYPE.COMPILED;\n    } catch (err) {\n      if (cliOptions.watch) {\n        console.error(err);\n        return FILE_TYPE.ERR_COMPILATION;\n      }\n\n      throw err;\n    }\n  }\n\n  function getDest(filename: string, base: string): string {\n    if (cliOptions.relative) {\n      return path.join(base, cliOptions.outDir, filename);\n    }\n    return path.join(cliOptions.outDir, filename);\n  }\n\n  async function handleFile(src: string, base: string): Promise<boolean> {\n    const written = await write(src, base);\n\n    if (\n      (cliOptions.copyFiles && written === FILE_TYPE.NON_COMPILABLE) ||\n      (cliOptions.copyIgnored && written === FILE_TYPE.IGNORED)\n    ) {\n      const filename = path.relative(base, src);\n      const dest = getDest(filename, base);\n      outputFileSync(dest, fs.readFileSync(src));\n      util.chmod(src, dest);\n    }\n    return written === FILE_TYPE.COMPILED;\n  }\n\n  async function handle(filenameOrDir: string): Promise<number> {\n    if (!fs.existsSync(filenameOrDir)) return 0;\n\n    const stat = fs.statSync(filenameOrDir);\n\n    if (stat.isDirectory()) {\n      const dirname = filenameOrDir;\n\n      let count = 0;\n\n      const files = util.readdir(dirname, cliOptions.includeDotfiles);\n      for (const filename of files) {\n        const written = await handleFile(filename, dirname);\n        if (written) count += 1;\n      }\n\n      return count;\n    } else {\n      const filename = filenameOrDir;\n      const written = await handleFile(filename, path.dirname(filename));\n\n      return written ? 1 : 0;\n    }\n  }\n\n  let compiledFiles = 0;\n  let startTime: [number, number] | null = null;\n\n  const logSuccess = util.debounce(function () {\n    if (startTime === null) {\n      // This should never happen, but just in case it's better\n      // to ignore the log message rather than making @babel/cli crash.\n      return;\n    }\n\n    const diff = process.hrtime(startTime);\n\n    console.log(\n      `Successfully compiled ${compiledFiles} ${\n        compiledFiles !== 1 ? \"files\" : \"file\"\n      } with Babel (${diff[0] * 1e3 + Math.round(diff[1] / 1e6)}ms).`,\n    );\n    compiledFiles = 0;\n    startTime = null;\n  }, 100);\n\n  if (cliOptions.watch) watcher.enable({ enableGlobbing: true });\n\n  if (!cliOptions.skipInitialBuild) {\n    if (cliOptions.deleteDirOnStart) {\n      util.deleteDir(cliOptions.outDir);\n    }\n\n    fs.mkdirSync(cliOptions.outDir, { recursive: true });\n\n    startTime = process.hrtime();\n\n    for (const filename of cliOptions.filenames) {\n      // compiledFiles is just incremented without reading its value, so we\n      // don't risk race conditions.\n      compiledFiles += await handle(filename);\n    }\n\n    if (!cliOptions.quiet) {\n      logSuccess();\n      logSuccess.flush();\n    }\n  }\n\n  if (cliOptions.watch) {\n    // This, alongside with debounce, allows us to only log\n    // when we are sure that all the files have been compiled.\n    let processing = 0;\n    const { filenames } = cliOptions;\n    let getBase: (filename: string) => string | null;\n    if (filenames.length === 1) {\n      // fast path: If there is only one filenames, we know it must be the base\n      const base = filenames[0];\n      const absoluteBase = path.resolve(base);\n      getBase = filename => {\n        return filename === absoluteBase ? path.dirname(base) : base;\n      };\n    } else {\n      // A map from absolute compiled file path to its base, from which\n      // the output destination will be determined\n      const filenameToBaseMap: Map<string, string> = new Map(\n        filenames.map(filename => {\n          const absoluteFilename = path.resolve(filename);\n          return [absoluteFilename, path.dirname(filename)];\n        }),\n      );\n\n      const absoluteFilenames: Map<string, string> = new Map(\n        filenames.map(filename => {\n          const absoluteFilename = path.resolve(filename);\n          return [absoluteFilename, filename];\n        }),\n      );\n\n      const { sep } = path;\n      // determine base from the absolute file path\n      getBase = filename => {\n        const base = filenameToBaseMap.get(filename);\n        if (base !== undefined) {\n          return base;\n        }\n        for (const [absoluteFilenameOrDir, relative] of absoluteFilenames) {\n          if (filename.startsWith(absoluteFilenameOrDir + sep)) {\n            filenameToBaseMap.set(filename, relative);\n            return relative;\n          }\n        }\n        // Can't determine the base, probably external deps\n        return \"\";\n      };\n    }\n\n    filenames.forEach(filenameOrDir => {\n      watcher.watch(filenameOrDir);\n    });\n\n    watcher.startWatcher();\n\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\n    watcher.onFilesChange(async filenames => {\n      processing++;\n      if (startTime === null) startTime = process.hrtime();\n\n      try {\n        const written = await Promise.all(\n          filenames.map(filename => handleFile(filename, getBase(filename))),\n        );\n\n        compiledFiles += written.filter(Boolean).length;\n      } catch (err) {\n        console.error(err);\n      }\n\n      processing--;\n      if (processing === 0 && !cliOptions.quiet) logSuccess();\n    });\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAI,IAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAAwC,SAAAK,mBAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,cAAAC,CAAA,GAAAP,CAAA,CAAAK,CAAA,EAAAC,CAAA,GAAAE,CAAA,GAAAD,CAAA,CAAAE,KAAA,WAAAT,CAAA,gBAAAE,CAAA,CAAAF,CAAA,KAAAO,CAAA,CAAAG,IAAA,GAAAT,CAAA,CAAAO,CAAA,IAAAG,OAAA,CAAAC,OAAA,CAAAJ,CAAA,EAAAK,IAAA,CAAAV,CAAA,EAAAC,CAAA;AAAA,SAAAU,kBAAAd,CAAA,6BAAAC,CAAA,SAAAC,CAAA,GAAAa,SAAA,aAAAJ,OAAA,WAAAR,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAL,CAAA,CAAAgB,KAAA,CAAAf,CAAA,EAAAC,CAAA,YAAAe,MAAAjB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,UAAAlB,CAAA,cAAAkB,OAAAlB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,WAAAlB,CAAA,KAAAiB,KAAA;AAGxC,MAAME,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,eAAe,EAAE;AACnB,CAAU,CAAC;AAEX,SAASC,cAAcA,CAACC,QAAgB,EAAElC,IAAqB,EAAQ;EACrE,GAAAmC,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,aAAArC,GAAA,GAAAsC,SAAA,GAAAxC,OAAA,aAAAyC,IAAA,EAAaC,MAAGA,CAAC,CAACC,OAAO,CAACV,QAAQ,CAAC,EAAE;IAAEW,SAAS,EAAE;EAAK,CAAC,CAAC;EACzDC,IAACA,CAAC,CAACC,aAAa,CAACb,QAAQ,EAAElC,IAAI,CAAC;AAClC;AAAC,SAAAgD,SAAAC,EAAA;EAAA,OAAAC,IAAA,CAAA3B,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAA4B,KAAA;EAAAA,IAAA,GAAA7B,iBAAA,CAEc,WAAgB;IAC7B8B,UAAU;IACVC;EACU,CAAC,EAAiB;IAAA,SACbC,KAAKA,CAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAC,MAAA,CAAAjC,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAAkC,OAAA;MAAAA,MAAA,GAAAnC,iBAAA,CAApB,WACEoC,GAAW,EACXC,IAAY,EACqB;QACjC,IAAIC,QAAQ,GAAGhB,MAAGA,CAAC,CAACgB,QAAQ,CAACD,IAAI,EAAED,GAAG,CAAC;QAEvC,IAAI,CAACrD,IAAI,CAACwD,qBAAqB,CAACD,QAAQ,EAAER,UAAU,CAACU,UAAU,CAAC,EAAE;UAChE,OAAOnC,SAAS,CAACG,cAAc;QACjC;QAEA8B,QAAQ,GAAGvD,IAAI,CAAC0D,aAAa,CAC3BH,QAAQ,EACRR,UAAU,CAACY,iBAAiB,GACxBpB,MAAGA,CAAC,CAACqB,OAAO,CAACL,QAAQ,CAAC,GACtBR,UAAU,CAACc,gBACjB,CAAC;QAED,MAAMC,IAAI,GAAGC,OAAO,CAACR,QAAQ,EAAED,IAAI,CAAC;QAEpC,IAAI;UACF,MAAMU,GAAG,SAAShE,IAAI,CAACiE,OAAO,CAACZ,GAAG,EAAA9B,MAAA,CAAA2C,MAAA,KAC7BlB,YAAY;YACfmB,cAAc,EAAEC,OAAIA,CAAC,CAAC7B,MAAGA,CAAC,CAACgB,QAAQ,CAACO,IAAI,GAAG,KAAK,EAAET,GAAG,CAAC;UAAC,EACxD,CAAC;UAEF,IAAI,CAACW,GAAG,EAAE,OAAO1C,SAAS,CAACK,OAAO;UAElC,IAAIqC,GAAG,CAACK,GAAG,EAAE;YACX,IAAIC,SAAsC,GAAG,KAAK;YAClD,IAAItB,YAAY,CAACuB,UAAU,IAAIvB,YAAY,CAACuB,UAAU,KAAK,QAAQ,EAAE;cACnED,SAAS,GAAG,UAAU;YACxB,CAAC,MAAM,IAAItB,YAAY,CAACuB,UAAU,IAAI,IAAI,EAAE;cAC1CD,SAAS,GAAGtE,IAAI,CAACwE,gBAAgB,CAACR,GAAG,CAACS,IAAI,CAAC,GAAG,UAAU,GAAG,MAAM;YACnE;YAEA,IAAIH,SAAS,EAAE;cACb,MAAMI,MAAM,GAAGZ,IAAI,GAAG,MAAM;cAC5B,IAAIQ,SAAS,KAAK,UAAU,EAAE;gBAC5BN,GAAG,CAACS,IAAI,GAAGzE,IAAI,CAAC2E,mBAAmB,CAACX,GAAG,CAACS,IAAI,EAAEC,MAAM,CAAC;cACvD;cACAV,GAAG,CAACK,GAAG,CAACO,IAAI,GAAGrC,MAAGA,CAAC,CAACsC,QAAQ,CAACtB,QAAQ,CAAC;cACtC1B,cAAc,CAAC6C,MAAM,EAAEI,IAAI,CAACC,SAAS,CAACf,GAAG,CAACK,GAAG,CAAC,CAAC;YACjD;UACF;UAEAxC,cAAc,CAACiC,IAAI,EAAEE,GAAG,CAACS,IAAI,CAAC;UAC9BzE,IAAI,CAACgF,KAAK,CAAC3B,GAAG,EAAES,IAAI,CAAC;UAErB,IAAIf,UAAU,CAACkC,OAAO,EAAE;YACtBC,OAAO,CAACC,GAAG,CAAC5C,MAAGA,CAAC,CAACgB,QAAQ,CAACrB,OAAO,CAACkD,GAAG,CAAC,CAAC,EAAE/B,GAAG,CAAC,GAAG,MAAM,GAAGS,IAAI,CAAC;UAChE;UAEA,OAAOxC,SAAS,CAACI,QAAQ;QAC3B,CAAC,CAAC,OAAO2D,GAAG,EAAE;UACZ,IAAItC,UAAU,CAACuC,KAAK,EAAE;YACpBJ,OAAO,CAACK,KAAK,CAACF,GAAG,CAAC;YAClB,OAAO/D,SAAS,CAACM,eAAe;UAClC;UAEA,MAAMyD,GAAG;QACX;MACF,CAAC;MAAA,OAAAjC,MAAA,CAAAjC,KAAA,OAAAD,SAAA;IAAA;IAED,SAAS6C,OAAOA,CAACyB,QAAgB,EAAElC,IAAY,EAAU;MACvD,IAAIP,UAAU,CAACQ,QAAQ,EAAE;QACvB,OAAOhB,MAAGA,CAAC,CAACkD,IAAI,CAACnC,IAAI,EAAEP,UAAU,CAAC2C,MAAM,EAAEF,QAAQ,CAAC;MACrD;MACA,OAAOjD,MAAGA,CAAC,CAACkD,IAAI,CAAC1C,UAAU,CAAC2C,MAAM,EAAEF,QAAQ,CAAC;IAC/C;IAAC,SAEcG,UAAUA,CAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAC,WAAA,CAAA3E,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAA4E,YAAA;MAAAA,WAAA,GAAA7E,iBAAA,CAAzB,WAA0BoC,GAAW,EAAEC,IAAY,EAAoB;QACrE,MAAMyC,OAAO,SAAS9C,KAAK,CAACI,GAAG,EAAEC,IAAI,CAAC;QAEtC,IACGP,UAAU,CAACiD,SAAS,IAAID,OAAO,KAAKzE,SAAS,CAACG,cAAc,IAC5DsB,UAAU,CAACkD,WAAW,IAAIF,OAAO,KAAKzE,SAAS,CAACK,OAAQ,EACzD;UACA,MAAM6D,QAAQ,GAAGjD,MAAGA,CAAC,CAACgB,QAAQ,CAACD,IAAI,EAAED,GAAG,CAAC;UACzC,MAAMS,IAAI,GAAGC,OAAO,CAACyB,QAAQ,EAAElC,IAAI,CAAC;UACpCzB,cAAc,CAACiC,IAAI,EAAEpB,IAACA,CAAC,CAACwD,YAAY,CAAC7C,GAAG,CAAC,CAAC;UAC1CrD,IAAI,CAACgF,KAAK,CAAC3B,GAAG,EAAES,IAAI,CAAC;QACvB;QACA,OAAOiC,OAAO,KAAKzE,SAAS,CAACI,QAAQ;MACvC,CAAC;MAAA,OAAAoE,WAAA,CAAA3E,KAAA,OAAAD,SAAA;IAAA;IAAA,SAEciF,MAAMA,CAAAC,GAAA;MAAA,OAAAC,OAAA,CAAAlF,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAAmF,QAAA;MAAAA,OAAA,GAAApF,iBAAA,CAArB,WAAsBqF,aAAqB,EAAmB;QAC5D,IAAI,CAAC5D,IAACA,CAAC,CAAC6D,UAAU,CAACD,aAAa,CAAC,EAAE,OAAO,CAAC;QAE3C,MAAME,IAAI,GAAG9D,IAACA,CAAC,CAAC+D,QAAQ,CAACH,aAAa,CAAC;QAEvC,IAAIE,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;UACtB,MAAMlE,OAAO,GAAG8D,aAAa;UAE7B,IAAIK,KAAK,GAAG,CAAC;UAEb,MAAMC,KAAK,GAAG5G,IAAI,CAAC6G,OAAO,CAACrE,OAAO,EAAEO,UAAU,CAAC+D,eAAe,CAAC;UAC/D,KAAK,MAAMtB,QAAQ,IAAIoB,KAAK,EAAE;YAC5B,MAAMb,OAAO,SAASJ,UAAU,CAACH,QAAQ,EAAEhD,OAAO,CAAC;YACnD,IAAIuD,OAAO,EAAEY,KAAK,IAAI,CAAC;UACzB;UAEA,OAAOA,KAAK;QACd,CAAC,MAAM;UACL,MAAMnB,QAAQ,GAAGc,aAAa;UAC9B,MAAMP,OAAO,SAASJ,UAAU,CAACH,QAAQ,EAAEjD,MAAGA,CAAC,CAACC,OAAO,CAACgD,QAAQ,CAAC,CAAC;UAElE,OAAOO,OAAO,GAAG,CAAC,GAAG,CAAC;QACxB;MACF,CAAC;MAAA,OAAAM,OAAA,CAAAlF,KAAA,OAAAD,SAAA;IAAA;IAED,IAAI6F,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAkC,GAAG,IAAI;IAE7C,MAAMC,UAAU,GAAGjH,IAAI,CAACkH,QAAQ,CAAC,YAAY;MAC3C,IAAIF,SAAS,KAAK,IAAI,EAAE;QAGtB;MACF;MAEA,MAAMG,IAAI,GAAGjF,OAAO,CAACkF,MAAM,CAACJ,SAAS,CAAC;MAEtC9B,OAAO,CAACC,GAAG,CACT,yBAAyB4B,aAAa,IACpCA,aAAa,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM,gBACxBI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,MAC3D,CAAC;MACDJ,aAAa,GAAG,CAAC;MACjBC,SAAS,GAAG,IAAI;IAClB,CAAC,EAAE,GAAG,CAAC;IAEP,IAAIjE,UAAU,CAACuC,KAAK,EAAErF,OAAO,CAACsH,MAAM,CAAC;MAAEC,cAAc,EAAE;IAAK,CAAC,CAAC;IAE9D,IAAI,CAACzE,UAAU,CAAC0E,gBAAgB,EAAE;MAChC,IAAI1E,UAAU,CAAC2E,gBAAgB,EAAE;QAC/B1H,IAAI,CAAC2H,SAAS,CAAC5E,UAAU,CAAC2C,MAAM,CAAC;MACnC;MAEA,GAAA3D,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,aAAArC,GAAA,GAAAsC,SAAA,GAAAxC,OAAA,aAAAyC,IAAA,EAAaS,UAAU,CAAC2C,MAAM,EAAE;QAAEjD,SAAS,EAAE;MAAK,CAAC,CAAC;MAEpDuE,SAAS,GAAG9E,OAAO,CAACkF,MAAM,CAAC,CAAC;MAE5B,KAAK,MAAM5B,QAAQ,IAAIzC,UAAU,CAAC6E,SAAS,EAAE;QAG3Cb,aAAa,UAAUZ,MAAM,CAACX,QAAQ,CAAC;MACzC;MAEA,IAAI,CAACzC,UAAU,CAAC8E,KAAK,EAAE;QACrBZ,UAAU,CAAC,CAAC;QACZA,UAAU,CAACa,KAAK,CAAC,CAAC;MACpB;IACF;IAEA,IAAI/E,UAAU,CAACuC,KAAK,EAAE;MAGpB,IAAIyC,UAAU,GAAG,CAAC;MAClB,MAAM;QAAEH;MAAU,CAAC,GAAG7E,UAAU;MAChC,IAAIiF,OAA4C;MAChD,IAAIJ,SAAS,CAACK,MAAM,KAAK,CAAC,EAAE;QAE1B,MAAM3E,IAAI,GAAGsE,SAAS,CAAC,CAAC,CAAC;QACzB,MAAMM,YAAY,GAAG3F,MAAGA,CAAC,CAACxB,OAAO,CAACuC,IAAI,CAAC;QACvC0E,OAAO,GAAGxC,QAAQ,IAAI;UACpB,OAAOA,QAAQ,KAAK0C,YAAY,GAAG3F,MAAGA,CAAC,CAACC,OAAO,CAACc,IAAI,CAAC,GAAGA,IAAI;QAC9D,CAAC;MACH,CAAC,MAAM;QAGL,MAAM6E,iBAAsC,GAAG,IAAIC,GAAG,CACpDR,SAAS,CAACvD,GAAG,CAACmB,QAAQ,IAAI;UACxB,MAAM6C,gBAAgB,GAAG9F,MAAGA,CAAC,CAACxB,OAAO,CAACyE,QAAQ,CAAC;UAC/C,OAAO,CAAC6C,gBAAgB,EAAE9F,MAAGA,CAAC,CAACC,OAAO,CAACgD,QAAQ,CAAC,CAAC;QACnD,CAAC,CACH,CAAC;QAED,MAAM8C,iBAAsC,GAAG,IAAIF,GAAG,CACpDR,SAAS,CAACvD,GAAG,CAACmB,QAAQ,IAAI;UACxB,MAAM6C,gBAAgB,GAAG9F,MAAGA,CAAC,CAACxB,OAAO,CAACyE,QAAQ,CAAC;UAC/C,OAAO,CAAC6C,gBAAgB,EAAE7C,QAAQ,CAAC;QACrC,CAAC,CACH,CAAC;QAED,MAAM;UAAE+C;QAAI,CAAC,GAAGhG,MAAGA,CAAC;QAEpByF,OAAO,GAAGxC,QAAQ,IAAI;UACpB,MAAMlC,IAAI,GAAG6E,iBAAiB,CAACK,GAAG,CAAChD,QAAQ,CAAC;UAC5C,IAAIlC,IAAI,KAAKmF,SAAS,EAAE;YACtB,OAAOnF,IAAI;UACb;UACA,KAAK,MAAM,CAACoF,qBAAqB,EAAEnF,QAAQ,CAAC,IAAI+E,iBAAiB,EAAE;YACjE,IAAI9C,QAAQ,CAACmD,UAAU,CAACD,qBAAqB,GAAGH,GAAG,CAAC,EAAE;cACpDJ,iBAAiB,CAACS,GAAG,CAACpD,QAAQ,EAAEjC,QAAQ,CAAC;cACzC,OAAOA,QAAQ;YACjB;UACF;UAEA,OAAO,EAAE;QACX,CAAC;MACH;MAEAqE,SAAS,CAACiB,OAAO,CAACvC,aAAa,IAAI;QACjCrG,OAAO,CAACqF,KAAK,CAACgB,aAAa,CAAC;MAC9B,CAAC,CAAC;MAEFrG,OAAO,CAAC6I,YAAY,CAAC,CAAC;MAGtB7I,OAAO,CAAC8I,aAAa,CAAA9H,iBAAA,CAAC,WAAM2G,SAAS,EAAI;QACvCG,UAAU,EAAE;QACZ,IAAIf,SAAS,KAAK,IAAI,EAAEA,SAAS,GAAG9E,OAAO,CAACkF,MAAM,CAAC,CAAC;QAEpD,IAAI;UACF,MAAMrB,OAAO,SAASjF,OAAO,CAACkI,GAAG,CAC/BpB,SAAS,CAACvD,GAAG,CAACmB,QAAQ,IAAIG,UAAU,CAACH,QAAQ,EAAEwC,OAAO,CAACxC,QAAQ,CAAC,CAAC,CACnE,CAAC;UAEDuB,aAAa,IAAIhB,OAAO,CAACkD,MAAM,CAACC,OAAO,CAAC,CAACjB,MAAM;QACjD,CAAC,CAAC,OAAO5C,GAAG,EAAE;UACZH,OAAO,CAACK,KAAK,CAACF,GAAG,CAAC;QACpB;QAEA0C,UAAU,EAAE;QACZ,IAAIA,UAAU,KAAK,CAAC,IAAI,CAAChF,UAAU,CAAC8E,KAAK,EAAEZ,UAAU,CAAC,CAAC;MACzD,CAAC,EAAC;IACJ;EACF,CAAC;EAAA,OAAAnE,IAAA,CAAA3B,KAAA,OAAAD,SAAA;AAAA", "ignoreList": []}