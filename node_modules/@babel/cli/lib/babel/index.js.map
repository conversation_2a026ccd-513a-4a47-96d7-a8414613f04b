{"version": 3, "names": ["_options", "require", "_dir", "_file", "opts", "parseArgv", "process", "argv", "fn", "cliOptions", "outDir", "dir<PERSON><PERSON><PERSON>", "fileCommand", "catch", "err", "console", "error", "exitCode"], "sources": ["../../src/babel/index.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport parseArgv from \"./options.ts\";\nimport dirCommand from \"./dir.ts\";\nimport fileCommand from \"./file.ts\";\n\nconst opts = parseArgv(process.argv);\n\nif (opts) {\n  const fn = opts.cliOptions.outDir ? dirCommand : fileCommand;\n  fn(opts).catch(err => {\n    console.error(err);\n    process.exitCode = 1;\n  });\n} else {\n  process.exitCode = 2;\n}\n"], "mappings": "AAAA;AAAmB;;AAEnB,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAEA,MAAMG,IAAI,GAAG,IAAAC,gBAAS,EAACC,OAAO,CAACC,IAAI,CAAC;AAEpC,IAAIH,IAAI,EAAE;EACR,MAAMI,EAAE,GAAGJ,IAAI,CAACK,UAAU,CAACC,MAAM,GAAGC,YAAU,GAAGC,aAAW;EAC5DJ,EAAE,CAACJ,IAAI,CAAC,CAACS,KAAK,CAACC,GAAG,IAAI;IACpBC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;IAClBR,OAAO,CAACW,QAAQ,GAAG,CAAC;EACtB,CAAC,CAAC;AACJ,CAAC,MAAM;EACLX,OAAO,CAACW,QAAQ,GAAG,CAAC;AACtB", "ignoreList": []}