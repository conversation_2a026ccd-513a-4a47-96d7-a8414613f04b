"use strict";

function commander() {
  const data = _interopRequireWildcard(require("commander"), true);
  commander = function () {
    return data;
  };
  return data;
}
function _core() {
  const data = require("@babel/core");
  _core = function () {
    return data;
  };
  return data;
}
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
const program = commander().default.program;
function collect(value, previousValue) {
  if (typeof value !== "string") return previousValue;
  const values = value.split(",");
  if (previousValue) {
    previousValue.push(...values);
    return previousValue;
  }
  return values;
}
program.option("-l, --whitelist [whitelist]", "Whitelist of helpers to ONLY include", collect);
program.option("-t, --output-type [type]", "Type of output (global|umd|var)", "global");
program.usage("[options]");
program.parse(process.argv);
const opts = program.opts();
console.log((0, _core().buildExternalHelpers)(opts.whitelist, opts.outputType));

//# sourceMappingURL=babel-external-helpers.js.map
