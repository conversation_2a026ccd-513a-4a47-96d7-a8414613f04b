{"name": "@babel/cli", "version": "7.28.0", "description": "Babel command line.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-cli", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20cli%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-cli"}, "keywords": ["6to5", "babel", "es6", "transpile", "transpiler", "babel-cli", "compiler"], "dependencies": {"@jridgewell/trace-mapping": "^0.3.28", "commander": "^6.2.0", "convert-source-map": "^2.0.0", "fs-readdir-recursive": "^1.1.0", "glob": "^7.2.0", "make-dir": "^2.1.0", "slash": "^2.0.0"}, "optionalDependencies": {"@nicolo-ribaudo/chokidar-2": "2.1.8-no-fsevents.3", "chokidar": "^3.6.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-transform-fixture-test-runner": "^7.28.0", "@types/fs-readdir-recursive": "^1.1.0", "@types/glob": "^7.2.0", "semver": "^6.3.1"}, "bin": {"babel": "./bin/babel.js", "babel-external-helpers": "./bin/babel-external-helpers.js"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}