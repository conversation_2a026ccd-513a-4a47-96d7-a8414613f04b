# Warning: This file is automatically generated. Removing it is fine, but will
# cause your node_modules installation to become invalidated.

__metadata:
  version: 1
  nmMode: classic

"@adobe/css-tools@npm:4.4.3":
  locations:
    - "node_modules/@adobe/css-tools"

"@alloc/quick-lru@npm:5.2.0":
  locations:
    - "node_modules/@alloc/quick-lru"

"@ampproject/remapping@npm:2.3.0":
  locations:
    - "node_modules/@ampproject/remapping"

"@arcanis/slice-ansi@npm:1.1.1":
  locations:
    - "node_modules/@arcanis/slice-ansi"

"@babel/cli@virtual:2730dc02290d35c450f12858e9acc19b1bc3bf7e805b415bb816f887551e8162b2406ff6d2f7b63576c95c86a192320bd941ad30cf3018a5f2f2ecae4550b0f4#npm:7.28.0":
  locations:
    - "node_modules/@babel/cli"

"@babel/code-frame@npm:7.27.1":
  locations:
    - "node_modules/@babel/code-frame"

"@babel/compat-data@npm:7.28.0":
  locations:
    - "node_modules/@babel/compat-data"

"@babel/core@npm:7.28.0":
  locations:
    - "node_modules/@babel/core"

"@babel/generator@npm:7.28.0":
  locations:
    - "node_modules/@babel/generator"

"@babel/helper-compilation-targets@npm:7.27.2":
  locations:
    - "node_modules/@babel/helper-compilation-targets"

"@babel/helper-globals@npm:7.28.0":
  locations:
    - "node_modules/@babel/helper-globals"

"@babel/helper-module-imports@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-module-imports"

"@babel/helper-module-transforms@virtual:2c032490421458ee4e212ed9bd0627762ff65ed1232d4208f2d615b0d0187bb07fc168cbfc1670b2da389400360e723c4eeeceee24d006e509ab345b44149a9f#npm:7.27.3":
  locations:
    - "node_modules/@babel/helper-module-transforms"

"@babel/helper-plugin-utils@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-plugin-utils"

"@babel/helper-string-parser@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-string-parser"

"@babel/helper-validator-identifier@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-identifier"

"@babel/helper-validator-option@npm:7.27.1":
  locations:
    - "node_modules/@babel/helper-validator-option"

"@babel/helpers@npm:7.27.6":
  locations:
    - "node_modules/@babel/helpers"

"@babel/parser@npm:7.28.0":
  locations:
    - "node_modules/@babel/parser"

"@babel/plugin-syntax-async-generators@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.4":
  locations:
    - "node_modules/@babel/plugin-syntax-async-generators"

"@babel/plugin-syntax-bigint@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-bigint"

"@babel/plugin-syntax-class-properties@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.12.13":
  locations:
    - "node_modules/@babel/plugin-syntax-class-properties"

"@babel/plugin-syntax-class-static-block@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-class-static-block"

"@babel/plugin-syntax-import-attributes@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-import-attributes"

"@babel/plugin-syntax-import-meta@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-import-meta"

"@babel/plugin-syntax-json-strings@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-json-strings"

"@babel/plugin-syntax-jsx@virtual:15ef0a4ad61c166598c4d195dc64a0b7270b186e9a584ea25871b4181189fa5a61a49aa37f6bcda6ffed25499ff900f1a33224b0c22868c8eb1eaf1dd4f0dc11#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-jsx"

"@babel/plugin-syntax-logical-assignment-operators@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-logical-assignment-operators"

"@babel/plugin-syntax-nullish-coalescing-operator@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-nullish-coalescing-operator"

"@babel/plugin-syntax-numeric-separator@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.10.4":
  locations:
    - "node_modules/@babel/plugin-syntax-numeric-separator"

"@babel/plugin-syntax-object-rest-spread@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-object-rest-spread"

"@babel/plugin-syntax-optional-catch-binding@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-catch-binding"

"@babel/plugin-syntax-optional-chaining@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.8.3":
  locations:
    - "node_modules/@babel/plugin-syntax-optional-chaining"

"@babel/plugin-syntax-private-property-in-object@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-private-property-in-object"

"@babel/plugin-syntax-top-level-await@virtual:56983eadc3c47b8ff78947bf83bf74beaf98595aa1663469b8284ac81b75ed2a46043c0f61e9ddb974a8fecf3bb0c5cce07c960d63c698b865f157e5cd64d225#npm:7.14.5":
  locations:
    - "node_modules/@babel/plugin-syntax-top-level-await"

"@babel/plugin-syntax-typescript@virtual:15ef0a4ad61c166598c4d195dc64a0b7270b186e9a584ea25871b4181189fa5a61a49aa37f6bcda6ffed25499ff900f1a33224b0c22868c8eb1eaf1dd4f0dc11#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-syntax-typescript"

"@babel/plugin-transform-react-jsx-self@virtual:445b069d2059d7aed0ca1ac25c8e630c9ae4de4827c678689bea000601b389ddc72f177f10bd2ab01e2de02bcc00cd2cfddf6ec1637440037ffcc109d40908a1#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-self"

"@babel/plugin-transform-react-jsx-source@virtual:445b069d2059d7aed0ca1ac25c8e630c9ae4de4827c678689bea000601b389ddc72f177f10bd2ab01e2de02bcc00cd2cfddf6ec1637440037ffcc109d40908a1#npm:7.27.1":
  locations:
    - "node_modules/@babel/plugin-transform-react-jsx-source"

"@babel/runtime@npm:7.27.6":
  locations:
    - "node_modules/@babel/runtime"

"@babel/template@npm:7.27.2":
  locations:
    - "node_modules/@babel/template"

"@babel/traverse@npm:7.28.0":
  locations:
    - "node_modules/@babel/traverse"

"@babel/types@npm:7.28.0":
  locations:
    - "node_modules/@babel/types"

"@bcoe/v8-coverage@npm:0.2.3":
  locations:
    - "node_modules/@bcoe/v8-coverage"

"@esbuild/darwin-arm64@npm:0.21.5":
  locations:
    - "apps/web/node_modules/@esbuild/darwin-arm64"

"@esbuild/darwin-arm64@npm:0.25.6":
  locations:
    - "node_modules/@esbuild/darwin-arm64"

"@eslint-community/eslint-utils@virtual:0ff2be87f8c1fa2aed419686fe66a1aa4327bfec06a06e0d7fe828ec1d4d8df304929cdb944e927e90f2b5639e808c15065c28732d17f4398fcaf672d45d85de#npm:4.7.0":
  locations:
    - "node_modules/@eslint-community/eslint-utils"

"@eslint-community/regexpp@npm:4.12.1":
  locations:
    - "node_modules/@eslint-community/regexpp"

"@eslint/config-array@npm:0.21.0":
  locations:
    - "node_modules/@eslint/config-array"

"@eslint/config-helpers@npm:0.3.0":
  locations:
    - "node_modules/@eslint/config-helpers"

"@eslint/core@npm:0.14.0":
  locations:
    - "node_modules/@eslint/core"

"@eslint/core@npm:0.15.1":
  locations:
    - "node_modules/@eslint/plugin-kit/node_modules/@eslint/core"

"@eslint/eslintrc@npm:3.3.1":
  locations:
    - "node_modules/@eslint/eslintrc"

"@eslint/js@npm:9.30.1":
  locations:
    - "node_modules/@eslint/js"

"@eslint/object-schema@npm:2.1.6":
  locations:
    - "node_modules/@eslint/object-schema"

"@eslint/plugin-kit@npm:0.3.3":
  locations:
    - "node_modules/@eslint/plugin-kit"

"@humanfs/core@npm:0.19.1":
  locations:
    - "node_modules/@humanfs/core"

"@humanfs/node@npm:0.16.6":
  locations:
    - "node_modules/@humanfs/node"

"@humanwhocodes/module-importer@npm:1.0.1":
  locations:
    - "node_modules/@humanwhocodes/module-importer"

"@humanwhocodes/retry@npm:0.3.1":
  locations:
    - "node_modules/@humanwhocodes/retry"

"@humanwhocodes/retry@npm:0.4.3":
  locations:
    - "node_modules/eslint/node_modules/@humanwhocodes/retry"

"@isaacs/cliui@npm:8.0.2":
  locations:
    - "node_modules/@isaacs/cliui"

"@isaacs/fs-minipass@npm:4.0.1":
  locations:
    - "node_modules/@isaacs/fs-minipass"

"@istanbuljs/load-nyc-config@npm:1.1.0":
  locations:
    - "node_modules/@istanbuljs/load-nyc-config"

"@istanbuljs/schema@npm:0.1.3":
  locations:
    - "node_modules/@istanbuljs/schema"

"@jest/console@npm:29.7.0":
  locations:
    - "node_modules/@jest/console"

"@jest/core@virtual:8804a5f463bf95005b5b9007f6a7ec5f9c7c6e52dd5c4bf7dce199e87291971130b329f57f7841aceeb106860eef2c0f02c5912095a816fbdef2513c4866d2bb#npm:29.7.0":
  locations:
    - "node_modules/@jest/core"

"@jest/environment@npm:29.7.0":
  locations:
    - "node_modules/@jest/environment"

"@jest/expect-utils@npm:29.7.0":
  locations:
    - "node_modules/@jest/expect-utils"

"@jest/expect@npm:29.7.0":
  locations:
    - "node_modules/@jest/expect"

"@jest/fake-timers@npm:29.7.0":
  locations:
    - "node_modules/@jest/fake-timers"

"@jest/globals@npm:29.7.0":
  locations:
    - "node_modules/@jest/globals"

"@jest/reporters@virtual:a882ded7dea845fc137dee32b70a29f2a540692e92e1d2f137799519b821ff7a8fa1f4e23787279c94ef1a72c51429f1fb489a21465ca6fdc26bc8cb145faa7b#npm:29.7.0":
  locations:
    - "node_modules/@jest/reporters"

"@jest/schemas@npm:29.6.3":
  locations:
    - "node_modules/@jest/schemas"

"@jest/source-map@npm:29.6.3":
  locations:
    - "node_modules/@jest/source-map"

"@jest/test-result@npm:29.7.0":
  locations:
    - "node_modules/@jest/test-result"

"@jest/test-sequencer@npm:29.7.0":
  locations:
    - "node_modules/@jest/test-sequencer"

"@jest/transform@npm:29.7.0":
  locations:
    - "node_modules/@jest/transform"

"@jest/types@npm:29.6.3":
  locations:
    - "node_modules/@jest/types"

"@joshwooding/vite-plugin-react-docgen-typescript@virtual:7d5086504e30d0065f6ec1f2e98442b9d99e6e311de5464cdcbe9c70488c5ae1e0b8356f38352e2f15c525fd8fd1e31159e4ac4775bbe90a1852fd389e46b855#npm:0.5.0":
  locations:
    - "apps/web/node_modules/@joshwooding/vite-plugin-react-docgen-typescript"

"@jridgewell/gen-mapping@npm:0.3.12":
  locations:
    - "node_modules/@jridgewell/gen-mapping"

"@jridgewell/resolve-uri@npm:3.1.2":
  locations:
    - "node_modules/@jridgewell/resolve-uri"

"@jridgewell/sourcemap-codec@npm:1.5.4":
  locations:
    - "node_modules/@jridgewell/sourcemap-codec"

"@jridgewell/trace-mapping@npm:0.3.29":
  locations:
    - "node_modules/@jridgewell/trace-mapping"

"@mdx-js/react@virtual:f831c30300c4ed6bff40c03f9ebbfaf2ad2c0cc6f4aa92d396e66a5ee5c126a537b7c14d567f80a9756c59aeb08864c19fa6ce7c2e978a79cf22a699a359f6a6#npm:3.1.0":
  locations:
    - "node_modules/@mdx-js/react"

"@nicolo-ribaudo/chokidar-2@npm:2.1.8-no-fsevents.3":
  locations:
    - "node_modules/@nicolo-ribaudo/chokidar-2"

"@nodelib/fs.scandir@npm:2.1.5":
  locations:
    - "node_modules/@nodelib/fs.scandir"

"@nodelib/fs.stat@npm:2.0.5":
  locations:
    - "node_modules/@nodelib/fs.stat"

"@nodelib/fs.walk@npm:1.2.8":
  locations:
    - "node_modules/@nodelib/fs.walk"

"@npmcli/agent@npm:3.0.0":
  locations:
    - "node_modules/@npmcli/agent"

"@npmcli/fs@npm:4.0.0":
  locations:
    - "node_modules/@npmcli/fs"

"@pkgjs/parseargs@npm:0.11.0":
  locations:
    - "node_modules/@pkgjs/parseargs"

"@react-spring/animated@virtual:e8bd79f9a216eb5ea27617a581586b986f117a49145e44c235d6e3aae2ce020f625a9e276716bee03221881c30ace741dc5641b5935865b1a23ac325eed94509#npm:9.5.5":
  locations:
    - "node_modules/@react-spring/animated"

"@react-spring/core@virtual:e8bd79f9a216eb5ea27617a581586b986f117a49145e44c235d6e3aae2ce020f625a9e276716bee03221881c30ace741dc5641b5935865b1a23ac325eed94509#npm:9.5.5":
  locations:
    - "node_modules/@react-spring/core"

"@react-spring/rafz@npm:9.5.5":
  locations:
    - "node_modules/@react-spring/rafz"

"@react-spring/shared@virtual:e8bd79f9a216eb5ea27617a581586b986f117a49145e44c235d6e3aae2ce020f625a9e276716bee03221881c30ace741dc5641b5935865b1a23ac325eed94509#npm:9.5.5":
  locations:
    - "node_modules/@react-spring/shared"

"@react-spring/types@npm:9.5.5":
  locations:
    - "node_modules/@react-spring/types"

"@react-spring/web@virtual:afc99dd9a0edc64c8b499814a740f9494e9e69d4f44d32d594f66a4c07d57ea756351276fb39ec32b4e2e2c40d653a68b687a44f5547247307f3fad6a9a2799b#npm:9.5.5":
  locations:
    - "node_modules/@react-spring/web"

"@remix-run/router@npm:1.23.0":
  locations:
    - "node_modules/@remix-run/router"

"@rolldown/pluginutils@npm:1.0.0-beta.19":
  locations:
    - "node_modules/@rolldown/pluginutils"

"@rollup/pluginutils@virtual:7d5086504e30d0065f6ec1f2e98442b9d99e6e311de5464cdcbe9c70488c5ae1e0b8356f38352e2f15c525fd8fd1e31159e4ac4775bbe90a1852fd389e46b855#npm:5.2.0":
  locations:
    - "node_modules/@rollup/pluginutils"

"@rollup/rollup-darwin-arm64@npm:4.44.2":
  locations:
    - "node_modules/@rollup/rollup-darwin-arm64"

"@sentry/browser@npm:6.19.7":
  locations:
    - "node_modules/@sentry/browser"

"@sentry/core@npm:6.19.7":
  locations:
    - "node_modules/@sentry/core"

"@sentry/hub@npm:6.19.7":
  locations:
    - "node_modules/@sentry/hub"

"@sentry/minimal@npm:6.19.7":
  locations:
    - "node_modules/@sentry/minimal"

"@sentry/tracing@npm:6.19.7":
  locations:
    - "node_modules/@sentry/tracing"

"@sentry/types@npm:6.19.7":
  locations:
    - "node_modules/@sentry/types"

"@sentry/utils@npm:6.19.7":
  locations:
    - "node_modules/@sentry/utils"

"@sinclair/typebox@npm:0.27.8":
  locations:
    - "node_modules/@sinclair/typebox"

"@sindresorhus/is@npm:4.6.0":
  locations:
    - "node_modules/@sindresorhus/is"

"@sinonjs/commons@npm:3.0.1":
  locations:
    - "node_modules/@sinonjs/commons"

"@sinonjs/fake-timers@npm:10.3.0":
  locations:
    - "node_modules/@sinonjs/fake-timers"

"@storybook/addon-actions@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-actions"

"@storybook/addon-backgrounds@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-backgrounds"

"@storybook/addon-controls@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-controls"

"@storybook/addon-docs@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-docs"

"@storybook/addon-essentials@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-essentials"

"@storybook/addon-highlight@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-highlight"

"@storybook/addon-interactions@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-interactions"

"@storybook/addon-links@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "apps/web/node_modules/@storybook/addon-links"

"@storybook/addon-measure@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-measure"

"@storybook/addon-onboarding@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-onboarding"

"@storybook/addon-outline@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-outline"

"@storybook/addon-toolbars@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-toolbars"

"@storybook/addon-viewport@virtual:696eebaff9a2a130dbd477fb56f706056a816ce5c0192b98dcba81724991300b3677204a85c5ea8266798f43b84b2bd98de298ba3ed861a1b290da9c8cef88fe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/addon-viewport"

"@storybook/blocks@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "apps/web/node_modules/@storybook/blocks"

"@storybook/blocks@virtual:f831c30300c4ed6bff40c03f9ebbfaf2ad2c0cc6f4aa92d396e66a5ee5c126a537b7c14d567f80a9756c59aeb08864c19fa6ce7c2e978a79cf22a699a359f6a6#npm:8.6.14":
  locations:
    - "node_modules/@storybook/blocks"

"@storybook/builder-vite@virtual:7d5086504e30d0065f6ec1f2e98442b9d99e6e311de5464cdcbe9c70488c5ae1e0b8356f38352e2f15c525fd8fd1e31159e4ac4775bbe90a1852fd389e46b855#npm:8.6.14":
  locations:
    - "apps/web/node_modules/@storybook/builder-vite"

"@storybook/components@virtual:d8d7b1a8d5a27eebecf846b0121dd4e62ebfff2ed4a23f273664018c376df392bca3adf0f6f02b48db0217e281a68b14c6fba4f726c94c96da9402e2e4de3558#npm:8.6.14":
  locations:
    - "node_modules/@storybook/components"

"@storybook/core@virtual:5e24038e965afa6f3ef0eb16adfa3f75ba9157a0b00a4bd54a0344004724ffecf291a54d05fef3ab96ff8cbd118244b666723330720a0e4fb98313f67b996764#npm:8.6.14":
  locations:
    - "node_modules/@storybook/core"

"@storybook/csf-plugin@virtual:f831c30300c4ed6bff40c03f9ebbfaf2ad2c0cc6f4aa92d396e66a5ee5c126a537b7c14d567f80a9756c59aeb08864c19fa6ce7c2e978a79cf22a699a359f6a6#npm:8.6.14":
  locations:
    - "node_modules/@storybook/csf-plugin"

"@storybook/global@npm:5.0.0":
  locations:
    - "node_modules/@storybook/global"

"@storybook/icons@virtual:40c1852a7f22053eb32b143f87b9f03251501ab6d35506c85cbe3756a020ecd140b37570dd0779328317fe0609e3fbf028e2e2470e509edb94180047cf369a22#npm:1.4.0":
  locations:
    - "node_modules/@storybook/icons"

"@storybook/icons@virtual:dbe19f4a48539ecbe1cf2fca54f7dc59671e925662ff412f19dea15347fa650f5cc3a134872c55632df0fe4a0b2765d6cc532388a81505a915d8266e4e4fa034#npm:1.4.0":
  locations:
    - "apps/web/node_modules/@storybook/icons"

"@storybook/instrumenter@virtual:e3356b82f00e193a3349fddf744f77ebc982df46e29a6bc0775235312316e610a674b408dd302ada85ad3660040aaaace23905d8fb2f4c022f40f3ea45444fbe#npm:8.6.14":
  locations:
    - "node_modules/@storybook/instrumenter"

"@storybook/manager-api@virtual:d8d7b1a8d5a27eebecf846b0121dd4e62ebfff2ed4a23f273664018c376df392bca3adf0f6f02b48db0217e281a68b14c6fba4f726c94c96da9402e2e4de3558#npm:8.6.14":
  locations:
    - "node_modules/@storybook/manager-api"

"@storybook/preview-api@virtual:d8d7b1a8d5a27eebecf846b0121dd4e62ebfff2ed4a23f273664018c376df392bca3adf0f6f02b48db0217e281a68b14c6fba4f726c94c96da9402e2e4de3558#npm:8.6.14":
  locations:
    - "node_modules/@storybook/preview-api"

"@storybook/react-dom-shim@virtual:d8d7b1a8d5a27eebecf846b0121dd4e62ebfff2ed4a23f273664018c376df392bca3adf0f6f02b48db0217e281a68b14c6fba4f726c94c96da9402e2e4de3558#npm:8.6.14":
  locations:
    - "apps/web/node_modules/@storybook/react-dom-shim"

"@storybook/react-dom-shim@virtual:f831c30300c4ed6bff40c03f9ebbfaf2ad2c0cc6f4aa92d396e66a5ee5c126a537b7c14d567f80a9756c59aeb08864c19fa6ce7c2e978a79cf22a699a359f6a6#npm:8.6.14":
  locations:
    - "node_modules/@storybook/react-dom-shim"

"@storybook/react-vite@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "apps/web/node_modules/@storybook/react-vite"

"@storybook/react@virtual:7d5086504e30d0065f6ec1f2e98442b9d99e6e311de5464cdcbe9c70488c5ae1e0b8356f38352e2f15c525fd8fd1e31159e4ac4775bbe90a1852fd389e46b855#npm:8.6.14":
  locations:
    - "apps/web/node_modules/@storybook/react"
  aliases:
    - "virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14"

"@storybook/test@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "node_modules/@storybook/test"

"@storybook/theming@virtual:1121e80a2e84896407cbd13a701d3f7cbb25a506b12449e228b3ac583358d80aa5054f4aa8a141aeb9b1398dded359cfac05f4fbe19b51f3a0a37d7398590e39#npm:8.6.14":
  locations:
    - "node_modules/@storybook/theming"
  aliases:
    - "virtual:d8d7b1a8d5a27eebecf846b0121dd4e62ebfff2ed4a23f273664018c376df392bca3adf0f6f02b48db0217e281a68b14c6fba4f726c94c96da9402e2e4de3558#npm:8.6.14"

"@swc/helpers@npm:0.4.14":
  locations:
    - "node_modules/@swc/legacy-helpers"

"@swc/helpers@npm:0.4.37":
  locations:
    - "node_modules/@swc/helpers"

"@szmarczak/http-timer@npm:4.0.6":
  locations:
    - "node_modules/@szmarczak/http-timer"

"@tailwindcss/forms@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:0.5.10":
  locations:
    - "node_modules/@tailwindcss/forms"

"@taptap/shared@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#workspace:shared":
  locations:
    - "apps/zalo/node_modules/@taptap/shared"
    - "apps/web/node_modules/@taptap/shared"

"@taptap/shared@workspace:shared":
  locations:
    - "node_modules/@taptap/shared"

"@taptap/web@workspace:apps/web":
  locations:
    - "node_modules/@taptap/web"

"@taptap/zalo@workspace:apps/zalo":
  locations:
    - "node_modules/@taptap/zalo"

"@testing-library/dom@npm:10.4.0":
  locations:
    - "node_modules/@testing-library/dom"

"@testing-library/dom@npm:9.3.4":
  locations:
    - "node_modules/@testing-library/react/node_modules/@testing-library/dom"

"@testing-library/jest-dom@npm:6.5.0":
  locations:
    - "node_modules/@storybook/test/node_modules/@testing-library/jest-dom"

"@testing-library/jest-dom@npm:6.6.3":
  locations:
    - "node_modules/@testing-library/jest-dom"

"@testing-library/react@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:14.3.1":
  locations:
    - "node_modules/@testing-library/react"

"@testing-library/user-event@virtual:1fea431d653e9e03586a67b6058d80b008bb4e48b0e2f187159560adbf3581e3035bf2264fdea7dd3f5d9e3c51a3efc32c93960bc325d23953b49ec63d58df9d#npm:14.5.2":
  locations:
    - "node_modules/@storybook/test/node_modules/@testing-library/user-event"

"@testing-library/user-event@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:14.6.1":
  locations:
    - "node_modules/@testing-library/user-event"

"@tootallnate/once@npm:2.0.0":
  locations:
    - "node_modules/@tootallnate/once"

"@types/aria-query@npm:5.0.4":
  locations:
    - "node_modules/@types/aria-query"

"@types/babel__core@npm:7.20.5":
  locations:
    - "node_modules/@types/babel__core"

"@types/babel__generator@npm:7.27.0":
  locations:
    - "node_modules/@types/babel__generator"

"@types/babel__template@npm:7.4.4":
  locations:
    - "node_modules/@types/babel__template"

"@types/babel__traverse@npm:7.20.7":
  locations:
    - "node_modules/@types/babel__traverse"

"@types/cacheable-request@npm:6.0.3":
  locations:
    - "node_modules/@types/cacheable-request"

"@types/doctrine@npm:0.0.9":
  locations:
    - "node_modules/@types/doctrine"

"@types/emscripten@npm:1.40.1":
  locations:
    - "node_modules/@types/emscripten"

"@types/estree@npm:1.0.8":
  locations:
    - "node_modules/@types/estree"

"@types/graceful-fs@npm:4.1.9":
  locations:
    - "node_modules/@types/graceful-fs"

"@types/http-cache-semantics@npm:4.0.4":
  locations:
    - "node_modules/@types/http-cache-semantics"

"@types/istanbul-lib-coverage@npm:2.0.6":
  locations:
    - "node_modules/@types/istanbul-lib-coverage"

"@types/istanbul-lib-report@npm:3.0.3":
  locations:
    - "node_modules/@types/istanbul-lib-report"

"@types/istanbul-reports@npm:3.0.4":
  locations:
    - "node_modules/@types/istanbul-reports"

"@types/jest@npm:29.5.14":
  locations:
    - "node_modules/@types/jest"

"@types/jsdom@npm:20.0.1":
  locations:
    - "node_modules/@types/jsdom"

"@types/json-schema@npm:7.0.15":
  locations:
    - "node_modules/@types/json-schema"

"@types/keyv@npm:3.1.4":
  locations:
    - "node_modules/@types/keyv"

"@types/mdx@npm:2.0.13":
  locations:
    - "node_modules/@types/mdx"

"@types/node@npm:18.19.115":
  locations:
    - "node_modules/@yarnpkg/pnp/node_modules/@types/node"

"@types/node@npm:22.16.0":
  locations:
    - "node_modules/@types/node"

"@types/node@npm:24.0.10":
  locations:
    - "node_modules/jest-worker/node_modules/@types/node"
    - "node_modules/jest-watcher/node_modules/@types/node"
    - "node_modules/jest-util/node_modules/@types/node"
    - "node_modules/jest-runtime/node_modules/@types/node"
    - "node_modules/jest-runner/node_modules/@types/node"
    - "node_modules/jest-mock/node_modules/@types/node"
    - "node_modules/jest-haste-map/node_modules/@types/node"
    - "node_modules/jest-environment-node/node_modules/@types/node"
    - "node_modules/jest-environment-jsdom/node_modules/@types/node"
    - "node_modules/jest-circus/node_modules/@types/node"
    - "node_modules/@types/responselike/node_modules/@types/node"
    - "node_modules/@types/keyv/node_modules/@types/node"
    - "node_modules/@types/jsdom/node_modules/@types/node"
    - "node_modules/@types/graceful-fs/node_modules/@types/node"
    - "node_modules/@types/cacheable-request/node_modules/@types/node"
    - "node_modules/@jest/types/node_modules/@types/node"
    - "node_modules/@jest/reporters/node_modules/@types/node"
    - "node_modules/@jest/fake-timers/node_modules/@types/node"
    - "node_modules/@jest/environment/node_modules/@types/node"
    - "node_modules/@jest/core/node_modules/@types/node"
    - "node_modules/@jest/console/node_modules/@types/node"

"@types/prop-types@npm:15.7.15":
  locations:
    - "node_modules/@types/prop-types"

"@types/react-dom@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:19.1.6":
  locations:
    - "node_modules/@types/react-dom"

"@types/react-dom@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:18.3.7":
  locations:
    - "shared/node_modules/@types/react-dom"
    - "apps/zalo/node_modules/@types/react-dom"
    - "apps/web/node_modules/@types/react-dom"

"@types/react@npm:18.3.23":
  locations:
    - "shared/node_modules/@types/react"
    - "apps/zalo/node_modules/@types/react"
    - "apps/web/node_modules/@types/react"

"@types/react@npm:19.1.8":
  locations:
    - "node_modules/@types/react"

"@types/resolve@npm:1.20.6":
  locations:
    - "node_modules/@types/resolve"

"@types/responselike@npm:1.0.3":
  locations:
    - "node_modules/@types/responselike"

"@types/semver@npm:7.7.0":
  locations:
    - "node_modules/@types/semver"

"@types/stack-utils@npm:2.0.3":
  locations:
    - "node_modules/@types/stack-utils"

"@types/tough-cookie@npm:4.0.5":
  locations:
    - "node_modules/@types/tough-cookie"

"@types/treeify@npm:1.0.3":
  locations:
    - "node_modules/@types/treeify"

"@types/uuid@npm:9.0.8":
  locations:
    - "node_modules/@types/uuid"

"@types/yargs-parser@npm:21.0.3":
  locations:
    - "node_modules/@types/yargs-parser"

"@types/yargs@npm:17.0.33":
  locations:
    - "node_modules/@types/yargs"

"@typescript-eslint/eslint-plugin@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin"

"@typescript-eslint/parser@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/parser"

"@typescript-eslint/project-service@virtual:d50cd76ebb127459dee1aa3d17946b6a7a465d54c5c60b403d03d1ca3482b3beb5d10ed9547576e7a5369e9daa5567a2ad83b813f4e1d97921dd2d0d8e4437e5#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/project-service"

"@typescript-eslint/scope-manager@npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/scope-manager"

"@typescript-eslint/tsconfig-utils@virtual:d50cd76ebb127459dee1aa3d17946b6a7a465d54c5c60b403d03d1ca3482b3beb5d10ed9547576e7a5369e9daa5567a2ad83b813f4e1d97921dd2d0d8e4437e5#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/tsconfig-utils"

"@typescript-eslint/type-utils@virtual:f7f6d4157b204914ab71905f3c980bf89bfcc57aa7ada9d3ab8dc3660209fe3f2ad0cba5c5cfdc6fffb5ada07613901facac414d2b1fe7b78e2b253c6ab8f738#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/type-utils"

"@typescript-eslint/types@npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/types"

"@typescript-eslint/typescript-estree@virtual:c337bb68e5dcc21ed9abe9d464466f43f105343abbdfaeb1ee7a5034d596f51fade27a63188299adf5f00d43cb2804d95c1d224f0c1c05f61b70dcbc1c8b9b09#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/typescript-estree"

"@typescript-eslint/utils@virtual:f7f6d4157b204914ab71905f3c980bf89bfcc57aa7ada9d3ab8dc3660209fe3f2ad0cba5c5cfdc6fffb5ada07613901facac414d2b1fe7b78e2b253c6ab8f738#npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/utils"

"@typescript-eslint/visitor-keys@npm:8.36.0":
  locations:
    - "node_modules/@typescript-eslint/visitor-keys"

"@use-gesture/core@npm:10.3.1":
  locations:
    - "node_modules/@use-gesture/core"

"@use-gesture/react@virtual:afc99dd9a0edc64c8b499814a740f9494e9e69d4f44d32d594f66a4c07d57ea756351276fb39ec32b4e2e2c40d653a68b687a44f5547247307f3fad6a9a2799b#npm:10.3.1":
  locations:
    - "node_modules/@use-gesture/react"

"@vitejs/plugin-react@virtual:1c51e927dc60bf9b37beae673c54ecb2a6ea485d7e1d4392065bc7d7352f20e7fd7b393b045a177b35a8111d4886863da0580a3a75773af15c7ae2422abd2a77#npm:4.6.0":
  locations:
    - "node_modules/@vitejs/plugin-react"

"@vitejs/plugin-react@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:4.6.0":
  locations:
    - "apps/web/node_modules/@vitejs/plugin-react"

"@vitest/expect@npm:2.0.5":
  locations:
    - "node_modules/@vitest/expect"

"@vitest/pretty-format@npm:2.0.5":
  locations:
    - "node_modules/@vitest/expect/node_modules/@vitest/pretty-format"

"@vitest/pretty-format@npm:2.1.9":
  locations:
    - "node_modules/@vitest/pretty-format"

"@vitest/spy@npm:2.0.5":
  locations:
    - "node_modules/@vitest/spy"

"@vitest/utils@npm:2.0.5":
  locations:
    - "node_modules/@vitest/expect/node_modules/@vitest/utils"

"@vitest/utils@npm:2.1.9":
  locations:
    - "node_modules/@vitest/utils"

"@yarnpkg/core@npm:4.4.2":
  locations:
    - "node_modules/@yarnpkg/core"

"@yarnpkg/fslib@npm:3.1.2":
  locations:
    - "node_modules/@yarnpkg/fslib"

"@yarnpkg/libzip@virtual:acfa5f2e9ae5163bc0ed3e43799c912b0e8c2caa485862062844ee151754dfa1b0e299c099d7108a79a35266e75681eb699d2c1b9c4f7ed6ca6dd9a3779095fd#npm:3.2.1":
  locations:
    - "node_modules/@yarnpkg/libzip"

"@yarnpkg/nm@npm:4.0.7":
  locations:
    - "node_modules/@yarnpkg/nm"

"@yarnpkg/parsers@npm:3.0.3":
  locations:
    - "node_modules/@yarnpkg/parsers"

"@yarnpkg/pnp@npm:4.1.1":
  locations:
    - "node_modules/@yarnpkg/pnp"

"@yarnpkg/pnpify@npm:4.1.5":
  locations:
    - "node_modules/@yarnpkg/pnpify"

"@yarnpkg/shell@npm:4.1.3":
  locations:
    - "node_modules/@yarnpkg/shell"

"abab@npm:2.0.6":
  locations:
    - "node_modules/abab"

"abbrev@npm:3.0.1":
  locations:
    - "node_modules/abbrev"

"acorn-globals@npm:7.0.1":
  locations:
    - "node_modules/acorn-globals"

"acorn-jsx@virtual:9633b00e55c5aebf81b0127f50addd44705c175a47a287258963782da8f9f4e66c2da6640a60ed2826e19f024f73cd554a58729ee1644f93800bbd0d7b7ddd79#npm:5.3.2":
  locations:
    - "node_modules/acorn-jsx"

"acorn-walk@npm:8.3.4":
  locations:
    - "node_modules/acorn-walk"

"acorn@npm:8.15.0":
  locations:
    - "node_modules/acorn"

"agent-base@npm:6.0.2":
  locations:
    - "node_modules/https-proxy-agent/node_modules/agent-base"
    - "node_modules/http-proxy-agent/node_modules/agent-base"

"agent-base@npm:7.1.4":
  locations:
    - "node_modules/agent-base"

"ajv@npm:6.12.6":
  locations:
    - "node_modules/ajv"

"ansi-escapes@npm:4.3.2":
  locations:
    - "node_modules/ansi-escapes"

"ansi-regex@npm:5.0.1":
  locations:
    - "node_modules/ansi-regex"

"ansi-regex@npm:6.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/ansi-regex"

"ansi-styles@npm:4.3.0":
  locations:
    - "node_modules/ansi-styles"

"ansi-styles@npm:5.2.0":
  locations:
    - "node_modules/pretty-format/node_modules/ansi-styles"
    - "node_modules/@testing-library/react/node_modules/ansi-styles"
    - "node_modules/@testing-library/dom/node_modules/ansi-styles"

"ansi-styles@npm:6.2.1":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/ansi-styles"

"any-promise@npm:1.3.0":
  locations:
    - "node_modules/any-promise"

"anymatch@npm:3.1.3":
  locations:
    - "node_modules/anymatch"

"arg@npm:5.0.2":
  locations:
    - "node_modules/arg"

"argparse@npm:1.0.10":
  locations:
    - "node_modules/argparse"

"argparse@npm:2.0.1":
  locations:
    - "node_modules/@eslint/eslintrc/node_modules/argparse"

"aria-query@npm:5.1.3":
  locations:
    - "node_modules/@testing-library/react/node_modules/aria-query"

"aria-query@npm:5.3.0":
  locations:
    - "node_modules/@testing-library/dom/node_modules/aria-query"

"aria-query@npm:5.3.2":
  locations:
    - "node_modules/aria-query"

"array-buffer-byte-length@npm:1.0.2":
  locations:
    - "node_modules/array-buffer-byte-length"

"array-includes@npm:3.1.9":
  locations:
    - "node_modules/array-includes"

"array.prototype.findlast@npm:1.2.5":
  locations:
    - "node_modules/array.prototype.findlast"

"array.prototype.flat@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flat"

"array.prototype.flatmap@npm:1.3.3":
  locations:
    - "node_modules/array.prototype.flatmap"

"array.prototype.tosorted@npm:1.1.4":
  locations:
    - "node_modules/array.prototype.tosorted"

"arraybuffer.prototype.slice@npm:1.0.4":
  locations:
    - "node_modules/arraybuffer.prototype.slice"

"assertion-error@npm:2.0.1":
  locations:
    - "node_modules/assertion-error"

"ast-types@npm:0.16.1":
  locations:
    - "node_modules/ast-types"

"async-function@npm:1.0.0":
  locations:
    - "node_modules/async-function"

"async@npm:3.2.6":
  locations:
    - "node_modules/async"

"asynckit@npm:0.4.0":
  locations:
    - "node_modules/asynckit"

"autoprefixer@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:10.4.21":
  locations:
    - "node_modules/autoprefixer"

"available-typed-arrays@npm:1.0.7":
  locations:
    - "node_modules/available-typed-arrays"

"babel-jest@virtual:422515cf8b36517e2bde6d86726653808f7933a738ab57ceae9f81b5b34e7de0315be387c2ad9118da25bab38ef34d34ec9b1e375728613c9bf97f3686b7f7c4#npm:29.7.0":
  locations:
    - "node_modules/babel-jest"

"babel-plugin-istanbul@npm:6.1.1":
  locations:
    - "node_modules/babel-plugin-istanbul"

"babel-plugin-jest-hoist@npm:29.6.3":
  locations:
    - "node_modules/babel-plugin-jest-hoist"

"babel-preset-current-node-syntax@virtual:15ef0a4ad61c166598c4d195dc64a0b7270b186e9a584ea25871b4181189fa5a61a49aa37f6bcda6ffed25499ff900f1a33224b0c22868c8eb1eaf1dd4f0dc11#npm:1.1.0":
  locations:
    - "node_modules/babel-preset-current-node-syntax"
  aliases:
    - "virtual:802a06b57aed07e6f758d2421031bb72bccdb950ed66c63c7755be50536d34ac52ddea49da6da08c325f3edbe8e181560f61103a4cf459915358ba574951c832#npm:1.1.0"

"babel-preset-jest@virtual:c39d7bdc71eb14489d35965b169cd412e06f6bbaaeec23e7880db9a1353d54f59d43a8e00221548ff80df51fb220c8ab30542f930984d409d6febb540a4686a8#npm:29.6.3":
  locations:
    - "node_modules/babel-preset-jest"

"balanced-match@npm:1.0.2":
  locations:
    - "node_modules/balanced-match"

"better-opn@npm:3.0.2":
  locations:
    - "node_modules/better-opn"

"binary-extensions@npm:2.3.0":
  locations:
    - "node_modules/binary-extensions"

"brace-expansion@npm:1.1.12":
  locations:
    - "node_modules/minimatch/node_modules/brace-expansion"

"brace-expansion@npm:2.0.2":
  locations:
    - "node_modules/brace-expansion"

"braces@npm:3.0.3":
  locations:
    - "node_modules/braces"

"browser-assert@npm:1.2.1":
  locations:
    - "node_modules/browser-assert"

"browserslist@npm:4.25.1":
  locations:
    - "node_modules/browserslist"

"bs-logger@npm:0.2.6":
  locations:
    - "node_modules/bs-logger"

"bser@npm:2.1.1":
  locations:
    - "node_modules/bser"

"buffer-from@npm:1.1.2":
  locations:
    - "node_modules/buffer-from"

"cacache@npm:19.0.1":
  locations:
    - "node_modules/cacache"

"cacheable-lookup@npm:5.0.4":
  locations:
    - "node_modules/cacheable-lookup"

"cacheable-request@npm:7.0.4":
  locations:
    - "node_modules/cacheable-request"

"call-bind-apply-helpers@npm:1.0.2":
  locations:
    - "node_modules/call-bind-apply-helpers"

"call-bind@npm:1.0.8":
  locations:
    - "node_modules/call-bind"

"call-bound@npm:1.0.4":
  locations:
    - "node_modules/call-bound"

"callsites@npm:3.1.0":
  locations:
    - "node_modules/callsites"

"camelcase-css@npm:2.0.1":
  locations:
    - "node_modules/camelcase-css"

"camelcase@npm:5.3.1":
  locations:
    - "node_modules/camelcase"

"camelcase@npm:6.3.0":
  locations:
    - "node_modules/jest-validate/node_modules/camelcase"

"caniuse-lite@npm:1.0.30001727":
  locations:
    - "node_modules/caniuse-lite"

"chai@npm:5.2.0":
  locations:
    - "node_modules/chai"

"chalk@npm:3.0.0":
  locations:
    - "node_modules/@testing-library/jest-dom/node_modules/chalk"
    - "node_modules/@storybook/test/node_modules/chalk"

"chalk@npm:4.1.2":
  locations:
    - "node_modules/chalk"

"char-regex@npm:1.0.2":
  locations:
    - "node_modules/char-regex"

"check-error@npm:2.1.1":
  locations:
    - "node_modules/check-error"

"chokidar@npm:3.6.0":
  locations:
    - "node_modules/chokidar"

"chownr@npm:2.0.0":
  locations:
    - "node_modules/chownr"

"chownr@npm:3.0.0":
  locations:
    - "node_modules/tar/node_modules/chownr"

"ci-info@npm:3.9.0":
  locations:
    - "node_modules/ci-info"

"ci-info@npm:4.3.0":
  locations:
    - "node_modules/@yarnpkg/core/node_modules/ci-info"

"cjs-module-lexer@npm:1.4.3":
  locations:
    - "node_modules/cjs-module-lexer"

"clipanion@virtual:b80567f1f726ad28b5f69c2f4d583115762754ed5e643c8ffc700d555267034c078163b8acf27ac9e54b95573c3afeb591dde0dc0755beb1e2c159d60551a612#npm:4.0.0-rc.4":
  locations:
    - "node_modules/clipanion"

"cliui@npm:6.0.0":
  locations:
    - "node_modules/qrcode/node_modules/cliui"

"cliui@npm:8.0.1":
  locations:
    - "node_modules/cliui"

"clone-response@npm:1.0.3":
  locations:
    - "node_modules/clone-response"

"clsx@npm:1.2.1":
  locations:
    - "node_modules/clsx"

"co@npm:4.6.0":
  locations:
    - "node_modules/co"

"collect-v8-coverage@npm:1.0.2":
  locations:
    - "node_modules/collect-v8-coverage"

"color-convert@npm:2.0.1":
  locations:
    - "node_modules/color-convert"

"color-name@npm:1.1.4":
  locations:
    - "node_modules/color-name"

"combined-stream@npm:1.0.8":
  locations:
    - "node_modules/combined-stream"

"commander@npm:4.1.1":
  locations:
    - "node_modules/commander"

"commander@npm:6.2.1":
  locations:
    - "node_modules/@babel/cli/node_modules/commander"

"concat-map@npm:0.0.1":
  locations:
    - "node_modules/concat-map"

"convert-source-map@npm:2.0.0":
  locations:
    - "node_modules/convert-source-map"

"create-jest@npm:29.7.0":
  locations:
    - "node_modules/create-jest"

"cross-spawn@npm:7.0.6":
  locations:
    - "node_modules/cross-spawn"

"css.escape@npm:1.5.1":
  locations:
    - "node_modules/css.escape"

"cssesc@npm:3.0.0":
  locations:
    - "node_modules/cssesc"

"cssom@npm:0.3.8":
  locations:
    - "node_modules/cssstyle/node_modules/cssom"

"cssom@npm:0.5.0":
  locations:
    - "node_modules/cssom"

"cssstyle@npm:2.3.0":
  locations:
    - "node_modules/cssstyle"

"csstype@npm:3.1.3":
  locations:
    - "node_modules/csstype"

"data-urls@npm:3.0.2":
  locations:
    - "node_modules/data-urls"

"data-view-buffer@npm:1.0.2":
  locations:
    - "node_modules/data-view-buffer"

"data-view-byte-length@npm:1.0.2":
  locations:
    - "node_modules/data-view-byte-length"

"data-view-byte-offset@npm:1.0.1":
  locations:
    - "node_modules/data-view-byte-offset"

"debug@virtual:c337bb68e5dcc21ed9abe9d464466f43f105343abbdfaeb1ee7a5034d596f51fade27a63188299adf5f00d43cb2804d95c1d224f0c1c05f61b70dcbc1c8b9b09#npm:4.4.1":
  locations:
    - "node_modules/debug"

"decamelize@npm:1.2.0":
  locations:
    - "node_modules/decamelize"

"decimal.js@npm:10.6.0":
  locations:
    - "node_modules/decimal.js"

"decompress-response@npm:6.0.0":
  locations:
    - "node_modules/decompress-response"

"dedent@virtual:f7679858c638e2e5ade31901dd2b1e5007918fdc7d84fefb11f4200f46ba2e43b9d662fb793507b517bb1e725144e51f6d68f60f9f6100fd52144f042f58f0bc#npm:1.6.0":
  locations:
    - "node_modules/dedent"

"deep-eql@npm:5.0.2":
  locations:
    - "node_modules/deep-eql"

"deep-equal@npm:2.2.3":
  locations:
    - "node_modules/deep-equal"

"deep-is@npm:0.1.4":
  locations:
    - "node_modules/deep-is"

"deepmerge@npm:4.3.1":
  locations:
    - "node_modules/deepmerge"

"defer-to-connect@npm:2.0.1":
  locations:
    - "node_modules/defer-to-connect"

"define-data-property@npm:1.1.4":
  locations:
    - "node_modules/define-data-property"

"define-lazy-prop@npm:2.0.0":
  locations:
    - "node_modules/define-lazy-prop"

"define-properties@npm:1.2.1":
  locations:
    - "node_modules/define-properties"

"delayed-stream@npm:1.0.0":
  locations:
    - "node_modules/delayed-stream"

"dequal@npm:2.0.3":
  locations:
    - "node_modules/dequal"

"detect-newline@npm:3.1.0":
  locations:
    - "node_modules/detect-newline"

"didyoumean@npm:1.2.2":
  locations:
    - "node_modules/didyoumean"

"diff-sequences@npm:29.6.3":
  locations:
    - "node_modules/diff-sequences"

"diff@npm:5.2.0":
  locations:
    - "node_modules/diff"

"dijkstrajs@npm:1.0.3":
  locations:
    - "node_modules/dijkstrajs"

"dlv@npm:1.1.3":
  locations:
    - "node_modules/dlv"

"doctrine@npm:2.1.0":
  locations:
    - "node_modules/doctrine"

"doctrine@npm:3.0.0":
  locations:
    - "node_modules/react-docgen/node_modules/doctrine"

"dom-accessibility-api@npm:0.5.16":
  locations:
    - "node_modules/dom-accessibility-api"

"dom-accessibility-api@npm:0.6.3":
  locations:
    - "node_modules/@testing-library/jest-dom/node_modules/dom-accessibility-api"
    - "node_modules/@storybook/test/node_modules/dom-accessibility-api"

"dom-helpers@npm:5.2.1":
  locations:
    - "node_modules/dom-helpers"

"domexception@npm:4.0.0":
  locations:
    - "node_modules/domexception"

"dotenv@npm:16.6.1":
  locations:
    - "node_modules/dotenv"

"dunder-proto@npm:1.0.1":
  locations:
    - "node_modules/dunder-proto"

"eastasianwidth@npm:0.2.0":
  locations:
    - "node_modules/eastasianwidth"

"ejs@npm:3.1.10":
  locations:
    - "node_modules/ejs"

"electron-to-chromium@npm:1.5.180":
  locations:
    - "node_modules/electron-to-chromium"

"emittery@npm:0.13.1":
  locations:
    - "node_modules/emittery"

"emoji-regex@npm:8.0.0":
  locations:
    - "node_modules/emoji-regex"

"emoji-regex@npm:9.2.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/emoji-regex"

"encoding@npm:0.1.13":
  locations:
    - "node_modules/encoding"

"end-of-stream@npm:1.4.5":
  locations:
    - "node_modules/end-of-stream"

"entities@npm:6.0.1":
  locations:
    - "node_modules/entities"

"env-paths@npm:2.2.1":
  locations:
    - "node_modules/env-paths"

"err-code@npm:2.0.3":
  locations:
    - "node_modules/err-code"

"error-ex@npm:1.3.2":
  locations:
    - "node_modules/error-ex"

"es-abstract@npm:1.24.0":
  locations:
    - "node_modules/es-abstract"

"es-define-property@npm:1.0.1":
  locations:
    - "node_modules/es-define-property"

"es-errors@npm:1.3.0":
  locations:
    - "node_modules/es-errors"

"es-get-iterator@npm:1.1.3":
  locations:
    - "node_modules/es-get-iterator"

"es-iterator-helpers@npm:1.2.1":
  locations:
    - "node_modules/es-iterator-helpers"

"es-object-atoms@npm:1.1.1":
  locations:
    - "node_modules/es-object-atoms"

"es-set-tostringtag@npm:2.1.0":
  locations:
    - "node_modules/es-set-tostringtag"

"es-shim-unscopables@npm:1.1.0":
  locations:
    - "node_modules/es-shim-unscopables"

"es-to-primitive@npm:1.3.0":
  locations:
    - "node_modules/es-to-primitive"

"esbuild-register@virtual:1121e80a2e84896407cbd13a701d3f7cbb25a506b12449e228b3ac583358d80aa5054f4aa8a141aeb9b1398dded359cfac05f4fbe19b51f3a0a37d7398590e39#npm:3.6.0":
  locations:
    - "node_modules/esbuild-register"

"esbuild@npm:0.21.5":
  locations:
    - "apps/web/node_modules/esbuild"

"esbuild@npm:0.25.6":
  locations:
    - "node_modules/esbuild"

"escalade@npm:3.2.0":
  locations:
    - "node_modules/escalade"

"escape-string-regexp@npm:2.0.0":
  locations:
    - "node_modules/escape-string-regexp"

"escape-string-regexp@npm:4.0.0":
  locations:
    - "node_modules/eslint/node_modules/escape-string-regexp"

"escodegen@npm:2.1.0":
  locations:
    - "node_modules/escodegen"

"eslint-plugin-react-hooks@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:4.6.2":
  locations:
    - "node_modules/eslint-plugin-react-hooks"

"eslint-plugin-react-refresh@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:0.4.20":
  locations:
    - "node_modules/eslint-plugin-react-refresh"

"eslint-plugin-react@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:7.37.5":
  locations:
    - "node_modules/eslint-plugin-react"

"eslint-scope@npm:8.4.0":
  locations:
    - "node_modules/eslint-scope"

"eslint-visitor-keys@npm:3.4.3":
  locations:
    - "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys"

"eslint-visitor-keys@npm:4.2.1":
  locations:
    - "node_modules/eslint-visitor-keys"

"eslint@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:9.30.1":
  locations:
    - "node_modules/eslint"

"espree@npm:10.4.0":
  locations:
    - "node_modules/espree"

"esprima@npm:4.0.1":
  locations:
    - "node_modules/esprima"

"esquery@npm:1.6.0":
  locations:
    - "node_modules/esquery"

"esrecurse@npm:4.3.0":
  locations:
    - "node_modules/esrecurse"

"estraverse@npm:5.3.0":
  locations:
    - "node_modules/estraverse"

"estree-walker@npm:2.0.2":
  locations:
    - "node_modules/@rollup/pluginutils/node_modules/estree-walker"

"estree-walker@npm:3.0.3":
  locations:
    - "node_modules/estree-walker"

"esutils@npm:2.0.3":
  locations:
    - "node_modules/esutils"

"eventemitter3@npm:4.0.7":
  locations:
    - "node_modules/eventemitter3"

"execa@npm:5.1.1":
  locations:
    - "node_modules/execa"

"exit@npm:0.1.2":
  locations:
    - "node_modules/exit"

"expect@npm:29.7.0":
  locations:
    - "node_modules/expect"

"exponential-backoff@npm:3.1.2":
  locations:
    - "node_modules/exponential-backoff"

"fast-deep-equal@npm:3.1.3":
  locations:
    - "node_modules/fast-deep-equal"

"fast-glob@npm:3.3.3":
  locations:
    - "node_modules/fast-glob"

"fast-json-stable-stringify@npm:2.1.0":
  locations:
    - "node_modules/fast-json-stable-stringify"

"fast-levenshtein@npm:2.0.6":
  locations:
    - "node_modules/fast-levenshtein"

"fastq@npm:1.19.1":
  locations:
    - "node_modules/fastq"

"fb-watchman@npm:2.0.2":
  locations:
    - "node_modules/fb-watchman"

"fdir@virtual:d4e4bcf80e67f9de0540c123c7c4882e34dce6a8ba807a0a834f267f9132ee6bd264e69a49c6203aa89877ed3a5a5d633bfa002384881be452cc3a2d2fbcce0b#npm:6.4.6":
  locations:
    - "node_modules/fdir"

"file-entry-cache@npm:8.0.0":
  locations:
    - "node_modules/file-entry-cache"

"filelist@npm:1.0.4":
  locations:
    - "node_modules/filelist"

"fill-range@npm:7.1.1":
  locations:
    - "node_modules/fill-range"

"find-up@npm:4.1.0":
  locations:
    - "node_modules/find-up"

"find-up@npm:5.0.0":
  locations:
    - "node_modules/eslint/node_modules/find-up"
    - "apps/web/node_modules/find-up"

"flat-cache@npm:4.0.1":
  locations:
    - "node_modules/flat-cache"

"flatted@npm:3.3.3":
  locations:
    - "node_modules/flatted"

"for-each@npm:0.3.5":
  locations:
    - "node_modules/for-each"

"foreground-child@npm:3.3.1":
  locations:
    - "node_modules/foreground-child"

"form-data@npm:4.0.3":
  locations:
    - "node_modules/form-data"

"fraction.js@npm:4.3.7":
  locations:
    - "node_modules/fraction.js"

"fs-minipass@npm:2.1.0":
  locations:
    - "node_modules/fs-minipass"

"fs-minipass@npm:3.0.3":
  locations:
    - "node_modules/cacache/node_modules/fs-minipass"

"fs-readdir-recursive@npm:1.1.0":
  locations:
    - "node_modules/fs-readdir-recursive"

"fs.realpath@npm:1.0.0":
  locations:
    - "node_modules/fs.realpath"

"fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1":
  locations:
    - "node_modules/fsevents"

"function-bind@npm:1.1.2":
  locations:
    - "node_modules/function-bind"

"function.prototype.name@npm:1.1.8":
  locations:
    - "node_modules/function.prototype.name"

"functions-have-names@npm:1.2.3":
  locations:
    - "node_modules/functions-have-names"

"gensync@npm:1.0.0-beta.2":
  locations:
    - "node_modules/gensync"

"get-caller-file@npm:2.0.5":
  locations:
    - "node_modules/get-caller-file"

"get-intrinsic@npm:1.3.0":
  locations:
    - "node_modules/get-intrinsic"

"get-package-type@npm:0.1.0":
  locations:
    - "node_modules/get-package-type"

"get-proto@npm:1.0.1":
  locations:
    - "node_modules/get-proto"

"get-stream@npm:5.2.0":
  locations:
    - "node_modules/get-stream"

"get-stream@npm:6.0.1":
  locations:
    - "node_modules/execa/node_modules/get-stream"

"get-symbol-description@npm:1.1.0":
  locations:
    - "node_modules/get-symbol-description"

"glob-parent@npm:5.1.2":
  locations:
    - "node_modules/glob-parent"

"glob-parent@npm:6.0.2":
  locations:
    - "node_modules/tailwindcss/node_modules/glob-parent"
    - "node_modules/eslint/node_modules/glob-parent"

"glob@npm:10.4.5":
  locations:
    - "node_modules/sucrase/node_modules/glob"
    - "node_modules/cacache/node_modules/glob"
    - "apps/web/node_modules/glob"

"glob@npm:7.2.3":
  locations:
    - "node_modules/glob"

"globals@npm:14.0.0":
  locations:
    - "node_modules/globals"

"globalthis@npm:1.0.4":
  locations:
    - "node_modules/globalthis"

"gopd@npm:1.2.0":
  locations:
    - "node_modules/gopd"

"got@npm:11.8.6":
  locations:
    - "node_modules/got"

"graceful-fs@npm:4.2.11":
  locations:
    - "node_modules/graceful-fs"

"grapheme-splitter@npm:1.0.4":
  locations:
    - "node_modules/grapheme-splitter"

"graphemer@npm:1.4.0":
  locations:
    - "node_modules/graphemer"

"has-bigints@npm:1.1.0":
  locations:
    - "node_modules/has-bigints"

"has-flag@npm:4.0.0":
  locations:
    - "node_modules/has-flag"

"has-property-descriptors@npm:1.0.2":
  locations:
    - "node_modules/has-property-descriptors"

"has-proto@npm:1.2.0":
  locations:
    - "node_modules/has-proto"

"has-symbols@npm:1.1.0":
  locations:
    - "node_modules/has-symbols"

"has-tostringtag@npm:1.0.2":
  locations:
    - "node_modules/has-tostringtag"

"hasown@npm:2.0.2":
  locations:
    - "node_modules/hasown"

"hpagent@npm:1.2.0":
  locations:
    - "node_modules/hpagent"

"html-encoding-sniffer@npm:3.0.0":
  locations:
    - "node_modules/html-encoding-sniffer"

"html-escaper@npm:2.0.2":
  locations:
    - "node_modules/html-escaper"

"http-cache-semantics@npm:4.2.0":
  locations:
    - "node_modules/http-cache-semantics"

"http-proxy-agent@npm:5.0.0":
  locations:
    - "node_modules/http-proxy-agent"

"http-proxy-agent@npm:7.0.2":
  locations:
    - "node_modules/@npmcli/agent/node_modules/http-proxy-agent"

"http2-wrapper@npm:1.0.3":
  locations:
    - "node_modules/http2-wrapper"

"https-proxy-agent@npm:5.0.1":
  locations:
    - "node_modules/https-proxy-agent"

"https-proxy-agent@npm:7.0.6":
  locations:
    - "node_modules/@npmcli/agent/node_modules/https-proxy-agent"

"human-signals@npm:2.1.0":
  locations:
    - "node_modules/human-signals"

"iconv-lite@npm:0.6.3":
  locations:
    - "node_modules/iconv-lite"

"ignore@npm:5.3.2":
  locations:
    - "node_modules/ignore"

"ignore@npm:7.0.5":
  locations:
    - "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore"

"import-fresh@npm:3.3.1":
  locations:
    - "node_modules/import-fresh"

"import-local@npm:3.2.0":
  locations:
    - "node_modules/import-local"

"imurmurhash@npm:0.1.4":
  locations:
    - "node_modules/imurmurhash"

"indent-string@npm:4.0.0":
  locations:
    - "node_modules/indent-string"

"inflight@npm:1.0.6":
  locations:
    - "node_modules/inflight"

"inherits@npm:2.0.4":
  locations:
    - "node_modules/inherits"

"internal-slot@npm:1.1.0":
  locations:
    - "node_modules/internal-slot"

"ip-address@npm:9.0.5":
  locations:
    - "node_modules/ip-address"

"is-arguments@npm:1.2.0":
  locations:
    - "node_modules/is-arguments"

"is-array-buffer@npm:3.0.5":
  locations:
    - "node_modules/is-array-buffer"

"is-arrayish@npm:0.2.1":
  locations:
    - "node_modules/is-arrayish"

"is-async-function@npm:2.1.1":
  locations:
    - "node_modules/is-async-function"

"is-bigint@npm:1.1.0":
  locations:
    - "node_modules/is-bigint"

"is-binary-path@npm:2.1.0":
  locations:
    - "node_modules/is-binary-path"

"is-boolean-object@npm:1.2.2":
  locations:
    - "node_modules/is-boolean-object"

"is-callable@npm:1.2.7":
  locations:
    - "node_modules/is-callable"

"is-core-module@npm:2.16.1":
  locations:
    - "node_modules/is-core-module"

"is-data-view@npm:1.0.2":
  locations:
    - "node_modules/is-data-view"

"is-date-object@npm:1.1.0":
  locations:
    - "node_modules/is-date-object"

"is-docker@npm:2.2.1":
  locations:
    - "node_modules/is-docker"

"is-extglob@npm:2.1.1":
  locations:
    - "node_modules/is-extglob"

"is-finalizationregistry@npm:1.1.1":
  locations:
    - "node_modules/is-finalizationregistry"

"is-fullwidth-code-point@npm:3.0.0":
  locations:
    - "node_modules/is-fullwidth-code-point"

"is-generator-fn@npm:2.1.0":
  locations:
    - "node_modules/is-generator-fn"

"is-generator-function@npm:1.1.0":
  locations:
    - "node_modules/is-generator-function"

"is-glob@npm:4.0.3":
  locations:
    - "node_modules/is-glob"

"is-map@npm:2.0.3":
  locations:
    - "node_modules/is-map"

"is-negative-zero@npm:2.0.3":
  locations:
    - "node_modules/is-negative-zero"

"is-number-object@npm:1.1.1":
  locations:
    - "node_modules/is-number-object"

"is-number@npm:7.0.0":
  locations:
    - "node_modules/is-number"

"is-potential-custom-element-name@npm:1.0.1":
  locations:
    - "node_modules/is-potential-custom-element-name"

"is-regex@npm:1.2.1":
  locations:
    - "node_modules/is-regex"

"is-set@npm:2.0.3":
  locations:
    - "node_modules/is-set"

"is-shared-array-buffer@npm:1.0.4":
  locations:
    - "node_modules/is-shared-array-buffer"

"is-stream@npm:2.0.1":
  locations:
    - "node_modules/is-stream"

"is-string@npm:1.1.1":
  locations:
    - "node_modules/is-string"

"is-symbol@npm:1.1.1":
  locations:
    - "node_modules/is-symbol"

"is-typed-array@npm:1.1.15":
  locations:
    - "node_modules/is-typed-array"

"is-weakmap@npm:2.0.2":
  locations:
    - "node_modules/is-weakmap"

"is-weakref@npm:1.1.1":
  locations:
    - "node_modules/is-weakref"

"is-weakset@npm:2.0.4":
  locations:
    - "node_modules/is-weakset"

"is-wsl@npm:2.2.0":
  locations:
    - "node_modules/is-wsl"

"isarray@npm:2.0.5":
  locations:
    - "node_modules/isarray"

"isexe@npm:2.0.0":
  locations:
    - "node_modules/isexe"

"isexe@npm:3.1.1":
  locations:
    - "node_modules/node-gyp/node_modules/isexe"

"istanbul-lib-coverage@npm:3.2.2":
  locations:
    - "node_modules/istanbul-lib-coverage"

"istanbul-lib-instrument@npm:5.2.1":
  locations:
    - "node_modules/istanbul-lib-instrument"

"istanbul-lib-instrument@npm:6.0.3":
  locations:
    - "node_modules/@jest/reporters/node_modules/istanbul-lib-instrument"

"istanbul-lib-report@npm:3.0.1":
  locations:
    - "node_modules/istanbul-lib-report"

"istanbul-lib-source-maps@npm:4.0.1":
  locations:
    - "node_modules/istanbul-lib-source-maps"

"istanbul-reports@npm:3.1.7":
  locations:
    - "node_modules/istanbul-reports"

"iterator.prototype@npm:1.1.5":
  locations:
    - "node_modules/iterator.prototype"

"jackspeak@npm:3.4.3":
  locations:
    - "node_modules/jackspeak"

"jake@npm:10.9.2":
  locations:
    - "node_modules/jake"

"jest-changed-files@npm:29.7.0":
  locations:
    - "node_modules/jest-changed-files"

"jest-circus@npm:29.7.0":
  locations:
    - "node_modules/jest-circus"

"jest-cli@virtual:8804a5f463bf95005b5b9007f6a7ec5f9c7c6e52dd5c4bf7dce199e87291971130b329f57f7841aceeb106860eef2c0f02c5912095a816fbdef2513c4866d2bb#npm:29.7.0":
  locations:
    - "node_modules/jest-cli"

"jest-config@virtual:3a6a7b993b4c5b60edc037a265ed4617431cf4c75aee76d6fbd0f2ca65ea68cee61c092e9bd306baebd90cc377234b4a525791e6755ee4d2193076de2c2bdfed#npm:29.7.0":
  locations:
    - "node_modules/jest-config"

"jest-config@virtual:a882ded7dea845fc137dee32b70a29f2a540692e92e1d2f137799519b821ff7a8fa1f4e23787279c94ef1a72c51429f1fb489a21465ca6fdc26bc8cb145faa7b#npm:29.7.0":
  locations:
    - "node_modules/@jest/core/node_modules/jest-config"

"jest-diff@npm:29.7.0":
  locations:
    - "node_modules/jest-diff"

"jest-docblock@npm:29.7.0":
  locations:
    - "node_modules/jest-docblock"

"jest-each@npm:29.7.0":
  locations:
    - "node_modules/jest-each"

"jest-environment-jsdom@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:29.7.0":
  locations:
    - "node_modules/jest-environment-jsdom"

"jest-environment-node@npm:29.7.0":
  locations:
    - "node_modules/jest-environment-node"

"jest-get-type@npm:29.6.3":
  locations:
    - "node_modules/jest-get-type"

"jest-haste-map@npm:29.7.0":
  locations:
    - "node_modules/jest-haste-map"

"jest-leak-detector@npm:29.7.0":
  locations:
    - "node_modules/jest-leak-detector"

"jest-matcher-utils@npm:29.7.0":
  locations:
    - "node_modules/jest-matcher-utils"

"jest-message-util@npm:29.7.0":
  locations:
    - "node_modules/jest-message-util"

"jest-mock@npm:29.7.0":
  locations:
    - "node_modules/jest-mock"

"jest-pnp-resolver@virtual:5c36f0eefbce78ee308fab92b5dcd29e2b0b70713b50365f0168be5bb1facc6582106f851a083d72bbb13e26d984e8612da5ed4b2bae83649e73e7b1ce19525b#npm:1.2.3":
  locations:
    - "node_modules/jest-pnp-resolver"

"jest-regex-util@npm:29.6.3":
  locations:
    - "node_modules/jest-regex-util"

"jest-resolve-dependencies@npm:29.7.0":
  locations:
    - "node_modules/jest-resolve-dependencies"

"jest-resolve@npm:29.7.0":
  locations:
    - "node_modules/jest-resolve"

"jest-runner@npm:29.7.0":
  locations:
    - "node_modules/jest-runner"

"jest-runtime@npm:29.7.0":
  locations:
    - "node_modules/jest-runtime"

"jest-snapshot@npm:29.7.0":
  locations:
    - "node_modules/jest-snapshot"

"jest-util@npm:29.7.0":
  locations:
    - "node_modules/jest-util"

"jest-validate@npm:29.7.0":
  locations:
    - "node_modules/jest-validate"

"jest-watcher@npm:29.7.0":
  locations:
    - "node_modules/jest-watcher"

"jest-worker@npm:29.7.0":
  locations:
    - "node_modules/jest-worker"

"jest@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:29.7.0":
  locations:
    - "node_modules/jest"

"jiti@npm:1.21.7":
  locations:
    - "node_modules/jiti"

"js-tokens@npm:4.0.0":
  locations:
    - "node_modules/js-tokens"

"js-yaml@npm:3.14.1":
  locations:
    - "node_modules/js-yaml"

"js-yaml@npm:4.1.0":
  locations:
    - "node_modules/@eslint/eslintrc/node_modules/js-yaml"

"jsbn@npm:1.1.0":
  locations:
    - "node_modules/jsbn"

"jsdoc-type-pratt-parser@npm:4.1.0":
  locations:
    - "node_modules/jsdoc-type-pratt-parser"

"jsdom@virtual:ca78efcbea6c4a776dca5163018100422b2488af35c01366334700a5c331cda93b127b4d9952de5be9d73986cb46ed3b912324f01398492f752b00749f6a2c91#npm:20.0.3":
  locations:
    - "node_modules/jsdom"

"jsesc@npm:3.1.0":
  locations:
    - "node_modules/jsesc"

"json-buffer@npm:3.0.1":
  locations:
    - "node_modules/json-buffer"

"json-parse-even-better-errors@npm:2.3.1":
  locations:
    - "node_modules/json-parse-even-better-errors"

"json-schema-traverse@npm:0.4.1":
  locations:
    - "node_modules/json-schema-traverse"

"json-stable-stringify-without-jsonify@npm:1.0.1":
  locations:
    - "node_modules/json-stable-stringify-without-jsonify"

"json5@npm:2.2.3":
  locations:
    - "node_modules/json5"

"jsx-ast-utils@npm:3.3.5":
  locations:
    - "node_modules/jsx-ast-utils"

"keyv@npm:4.5.4":
  locations:
    - "node_modules/keyv"

"kleur@npm:3.0.3":
  locations:
    - "node_modules/kleur"

"leven@npm:3.1.0":
  locations:
    - "node_modules/leven"

"levn@npm:0.4.1":
  locations:
    - "node_modules/levn"

"lilconfig@npm:3.1.3":
  locations:
    - "node_modules/lilconfig"

"lines-and-columns@npm:1.2.4":
  locations:
    - "node_modules/lines-and-columns"

"locate-path@npm:5.0.0":
  locations:
    - "node_modules/find-up/node_modules/locate-path"

"locate-path@npm:6.0.0":
  locations:
    - "node_modules/locate-path"

"lodash.memoize@npm:4.1.2":
  locations:
    - "node_modules/lodash.memoize"

"lodash.merge@npm:4.6.2":
  locations:
    - "node_modules/lodash.merge"

"lodash@npm:4.17.21":
  locations:
    - "node_modules/lodash"

"loose-envify@npm:1.4.0":
  locations:
    - "node_modules/loose-envify"

"loupe@npm:3.1.4":
  locations:
    - "node_modules/loupe"

"lowercase-keys@npm:2.0.0":
  locations:
    - "node_modules/lowercase-keys"

"lru-cache@npm:10.4.3":
  locations:
    - "node_modules/lru-cache"

"lru-cache@npm:5.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache"

"lz-string@npm:1.5.0":
  locations:
    - "node_modules/lz-string"

"magic-string@npm:0.27.0":
  locations:
    - "apps/web/node_modules/@joshwooding/vite-plugin-react-docgen-typescript/node_modules/magic-string"

"magic-string@npm:0.30.17":
  locations:
    - "node_modules/magic-string"

"make-dir@npm:2.1.0":
  locations:
    - "node_modules/@babel/cli/node_modules/make-dir"

"make-dir@npm:4.0.0":
  locations:
    - "node_modules/make-dir"

"make-error@npm:1.3.6":
  locations:
    - "node_modules/make-error"

"make-fetch-happen@npm:14.0.3":
  locations:
    - "node_modules/make-fetch-happen"

"makeerror@npm:1.0.12":
  locations:
    - "node_modules/makeerror"

"map-or-similar@npm:1.5.0":
  locations:
    - "node_modules/map-or-similar"

"math-intrinsics@npm:1.1.0":
  locations:
    - "node_modules/math-intrinsics"

"memoizerific@npm:1.11.3":
  locations:
    - "node_modules/memoizerific"

"merge-stream@npm:2.0.0":
  locations:
    - "node_modules/merge-stream"

"merge2@npm:1.4.1":
  locations:
    - "node_modules/merge2"

"micromatch@npm:4.0.8":
  locations:
    - "node_modules/micromatch"

"mime-db@npm:1.52.0":
  locations:
    - "node_modules/mime-db"

"mime-types@npm:2.1.35":
  locations:
    - "node_modules/mime-types"

"mimic-fn@npm:2.1.0":
  locations:
    - "node_modules/mimic-fn"

"mimic-response@npm:1.0.1":
  locations:
    - "node_modules/mimic-response"

"mimic-response@npm:3.1.0":
  locations:
    - "node_modules/decompress-response/node_modules/mimic-response"

"min-indent@npm:1.0.1":
  locations:
    - "node_modules/min-indent"

"mini-svg-data-uri@npm:1.4.4":
  locations:
    - "node_modules/mini-svg-data-uri"

"minimatch@npm:3.1.2":
  locations:
    - "node_modules/minimatch"

"minimatch@npm:5.1.6":
  locations:
    - "node_modules/filelist/node_modules/minimatch"

"minimatch@npm:9.0.5":
  locations:
    - "node_modules/sucrase/node_modules/minimatch"
    - "node_modules/cacache/node_modules/minimatch"
    - "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch"
    - "apps/web/node_modules/minimatch"

"minimist@npm:1.2.8":
  locations:
    - "node_modules/minimist"

"minipass-collect@npm:2.0.1":
  locations:
    - "node_modules/minipass-collect"

"minipass-fetch@npm:4.0.1":
  locations:
    - "node_modules/minipass-fetch"

"minipass-flush@npm:1.0.5":
  locations:
    - "node_modules/minipass-flush"

"minipass-pipeline@npm:1.2.4":
  locations:
    - "node_modules/minipass-pipeline"

"minipass-sized@npm:1.0.3":
  locations:
    - "node_modules/minipass-sized"

"minipass@npm:3.3.6":
  locations:
    - "node_modules/minipass-sized/node_modules/minipass"
    - "node_modules/minipass-pipeline/node_modules/minipass"
    - "node_modules/minipass-flush/node_modules/minipass"
    - "node_modules/fs-minipass/node_modules/minipass"
    - "node_modules/@yarnpkg/core/node_modules/minizlib/node_modules/minipass"

"minipass@npm:5.0.0":
  locations:
    - "node_modules/@yarnpkg/core/node_modules/minipass"

"minipass@npm:7.1.2":
  locations:
    - "node_modules/minipass"

"minizlib@npm:2.1.2":
  locations:
    - "node_modules/@yarnpkg/core/node_modules/minizlib"

"minizlib@npm:3.0.2":
  locations:
    - "node_modules/minizlib"

"mkdirp@npm:1.0.4":
  locations:
    - "node_modules/mkdirp"

"mkdirp@npm:3.0.1":
  locations:
    - "node_modules/tar/node_modules/mkdirp"

"ms@npm:2.1.3":
  locations:
    - "node_modules/ms"

"mz@npm:2.7.0":
  locations:
    - "node_modules/mz"

"nanoid@npm:3.3.11":
  locations:
    - "node_modules/nanoid"

"natural-compare@npm:1.4.0":
  locations:
    - "node_modules/natural-compare"

"negotiator@npm:1.0.0":
  locations:
    - "node_modules/negotiator"

"node-gyp@npm:11.2.0":
  locations:
    - "node_modules/node-gyp"

"node-int64@npm:0.4.0":
  locations:
    - "node_modules/node-int64"

"node-releases@npm:2.0.19":
  locations:
    - "node_modules/node-releases"

"nopt@npm:8.1.0":
  locations:
    - "node_modules/nopt"

"normalize-path@npm:3.0.0":
  locations:
    - "node_modules/normalize-path"

"normalize-range@npm:0.1.2":
  locations:
    - "node_modules/normalize-range"

"normalize-url@npm:6.1.0":
  locations:
    - "node_modules/normalize-url"

"npm-run-path@npm:4.0.1":
  locations:
    - "node_modules/npm-run-path"

"nwsapi@npm:2.2.20":
  locations:
    - "node_modules/nwsapi"

"object-assign@npm:4.1.1":
  locations:
    - "node_modules/object-assign"

"object-hash@npm:3.0.0":
  locations:
    - "node_modules/object-hash"

"object-inspect@npm:1.13.4":
  locations:
    - "node_modules/object-inspect"

"object-is@npm:1.1.6":
  locations:
    - "node_modules/object-is"

"object-keys@npm:1.1.1":
  locations:
    - "node_modules/object-keys"

"object.assign@npm:4.1.7":
  locations:
    - "node_modules/object.assign"

"object.entries@npm:1.1.9":
  locations:
    - "node_modules/object.entries"

"object.fromentries@npm:2.0.8":
  locations:
    - "node_modules/object.fromentries"

"object.values@npm:1.2.1":
  locations:
    - "node_modules/object.values"

"once@npm:1.4.0":
  locations:
    - "node_modules/once"

"onetime@npm:5.1.2":
  locations:
    - "node_modules/onetime"

"open@npm:8.4.2":
  locations:
    - "node_modules/open"

"optionator@npm:0.9.4":
  locations:
    - "node_modules/optionator"

"own-keys@npm:1.0.1":
  locations:
    - "node_modules/own-keys"

"p-cancelable@npm:2.1.1":
  locations:
    - "node_modules/p-cancelable"

"p-limit@npm:2.3.0":
  locations:
    - "node_modules/find-up/node_modules/p-limit"
    - "node_modules/@yarnpkg/core/node_modules/p-limit"

"p-limit@npm:3.1.0":
  locations:
    - "node_modules/p-limit"

"p-locate@npm:4.1.0":
  locations:
    - "node_modules/find-up/node_modules/p-locate"

"p-locate@npm:5.0.0":
  locations:
    - "node_modules/p-locate"

"p-map@npm:7.0.3":
  locations:
    - "node_modules/p-map"

"p-try@npm:2.2.0":
  locations:
    - "node_modules/p-try"

"package-json-from-dist@npm:1.0.1":
  locations:
    - "node_modules/package-json-from-dist"

"parent-module@npm:1.0.1":
  locations:
    - "node_modules/parent-module"

"parse-json@npm:5.2.0":
  locations:
    - "node_modules/parse-json"

"parse5@npm:7.3.0":
  locations:
    - "node_modules/parse5"

"path-exists@npm:4.0.0":
  locations:
    - "node_modules/path-exists"

"path-is-absolute@npm:1.0.1":
  locations:
    - "node_modules/path-is-absolute"

"path-key@npm:3.1.1":
  locations:
    - "node_modules/path-key"

"path-parse@npm:1.0.7":
  locations:
    - "node_modules/path-parse"

"path-scurry@npm:1.11.1":
  locations:
    - "node_modules/path-scurry"

"pathval@npm:2.0.1":
  locations:
    - "node_modules/pathval"

"picocolors@npm:1.1.1":
  locations:
    - "node_modules/picocolors"

"picomatch@npm:2.3.1":
  locations:
    - "node_modules/readdirp/node_modules/picomatch"
    - "node_modules/micromatch/node_modules/picomatch"
    - "node_modules/jest-util/node_modules/picomatch"
    - "node_modules/anymatch/node_modules/picomatch"

"picomatch@npm:4.0.2":
  locations:
    - "node_modules/picomatch"

"pify@npm:2.3.0":
  locations:
    - "node_modules/pify"

"pify@npm:4.0.1":
  locations:
    - "node_modules/@babel/cli/node_modules/pify"

"pirates@npm:4.0.7":
  locations:
    - "node_modules/pirates"

"pkg-dir@npm:4.2.0":
  locations:
    - "node_modules/pkg-dir"

"pngjs@npm:5.0.0":
  locations:
    - "node_modules/pngjs"

"polished@npm:4.3.1":
  locations:
    - "node_modules/polished"

"possible-typed-array-names@npm:1.1.0":
  locations:
    - "node_modules/possible-typed-array-names"

"postcss-import@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:15.1.0":
  locations:
    - "node_modules/postcss-import"

"postcss-js@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:4.0.1":
  locations:
    - "node_modules/postcss-js"

"postcss-load-config@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:4.0.2":
  locations:
    - "node_modules/postcss-load-config"

"postcss-nested@virtual:403059edc194f8eee9f9b8f9ab634e67126b182288b0cf3b56c13ed0ebaeda2fb24025b868e17fbbf02f6cd6a0b5ab93ce2bfe51d215138ea74d0736d87896c8#npm:6.2.0":
  locations:
    - "node_modules/postcss-nested"

"postcss-selector-parser@npm:6.1.2":
  locations:
    - "node_modules/postcss-selector-parser"

"postcss-value-parser@npm:4.2.0":
  locations:
    - "node_modules/postcss-value-parser"

"postcss@npm:8.5.6":
  locations:
    - "node_modules/postcss"

"prelude-ls@npm:1.2.1":
  locations:
    - "node_modules/prelude-ls"

"pretty-format@npm:27.5.1":
  locations:
    - "node_modules/@testing-library/react/node_modules/pretty-format"
    - "node_modules/@testing-library/dom/node_modules/pretty-format"

"pretty-format@npm:29.7.0":
  locations:
    - "node_modules/pretty-format"

"proc-log@npm:5.0.0":
  locations:
    - "node_modules/proc-log"

"process@npm:0.11.10":
  locations:
    - "node_modules/process"

"promise-retry@npm:2.0.1":
  locations:
    - "node_modules/promise-retry"

"prompts@npm:2.4.2":
  locations:
    - "node_modules/prompts"

"prop-types@npm:15.8.1":
  locations:
    - "node_modules/prop-types"

"psl@npm:1.15.0":
  locations:
    - "node_modules/psl"

"pump@npm:3.0.3":
  locations:
    - "node_modules/pump"

"punycode@npm:2.3.1":
  locations:
    - "node_modules/punycode"

"pure-rand@npm:6.1.0":
  locations:
    - "node_modules/pure-rand"

"qrcode@npm:1.5.4":
  locations:
    - "node_modules/qrcode"

"querystringify@npm:2.2.0":
  locations:
    - "node_modules/querystringify"

"queue-microtask@npm:1.2.3":
  locations:
    - "node_modules/queue-microtask"

"quick-lru@npm:5.1.1":
  locations:
    - "node_modules/quick-lru"

"raven-js@npm:3.27.2":
  locations:
    - "node_modules/raven-js"

"react-docgen-typescript@virtual:49cdde2f3b14188b0c6694c794dfd1504b5e0450a373aa2439ccbcf33258697531a5f908f2c69953ce16d06d3dee02ced989ec500a09e7d6f853d65c2bee7bb5#npm:2.4.0":
  locations:
    - "node_modules/react-docgen-typescript"

"react-docgen@npm:7.1.1":
  locations:
    - "node_modules/react-docgen"

"react-dom@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:19.1.0":
  locations:
    - "node_modules/react-dom"
  aliases:
    - "virtual:f831c30300c4ed6bff40c03f9ebbfaf2ad2c0cc6f4aa92d396e66a5ee5c126a537b7c14d567f80a9756c59aeb08864c19fa6ce7c2e978a79cf22a699a359f6a6#npm:19.1.0"

"react-dom@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:18.3.1":
  locations:
    - "shared/node_modules/react-dom"
    - "apps/zalo/node_modules/react-dom"
    - "apps/web/node_modules/react-dom"

"react-is@npm:16.13.1":
  locations:
    - "node_modules/prop-types/node_modules/react-is"

"react-is@npm:17.0.2":
  locations:
    - "node_modules/@testing-library/react/node_modules/react-is"
    - "node_modules/@testing-library/dom/node_modules/react-is"

"react-is@npm:18.3.1":
  locations:
    - "node_modules/react-is"

"react-refresh@npm:0.17.0":
  locations:
    - "node_modules/react-refresh"

"react-router-dom@virtual:afc99dd9a0edc64c8b499814a740f9494e9e69d4f44d32d594f66a4c07d57ea756351276fb39ec32b4e2e2c40d653a68b687a44f5547247307f3fad6a9a2799b#npm:6.30.1":
  locations:
    - "node_modules/react-router-dom"

"react-router-dom@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:6.30.1":
  locations:
    - "apps/zalo/node_modules/react-router-dom"
    - "apps/web/node_modules/react-router-dom"

"react-router@virtual:169d114f7dc59973b2f0e43a7b27d2aa9ce2666874043e031eb20d55980e2b481c7c66a4bf9309e584094b9247abf12bd7a8babcc02e28324f3a2c3b5a0febaf#npm:6.30.1":
  locations:
    - "apps/zalo/node_modules/react-router"
    - "apps/web/node_modules/react-router"

"react-router@virtual:3676a1ba187d45796f834a35cd4235c3adade0cab7ea858095b9b6216b9f18b9d6d4811016d9dfb429172d75543718263341dd995f6a1783358b3ef9c2696532#npm:6.30.1":
  locations:
    - "node_modules/react-router"

"react-transition-group@virtual:afc99dd9a0edc64c8b499814a740f9494e9e69d4f44d32d594f66a4c07d57ea756351276fb39ec32b4e2e2c40d653a68b687a44f5547247307f3fad6a9a2799b#npm:4.4.5":
  locations:
    - "node_modules/react-transition-group"

"react@npm:18.3.1":
  locations:
    - "shared/node_modules/react"
    - "apps/zalo/node_modules/react"
    - "apps/web/node_modules/react"

"react@npm:19.1.0":
  locations:
    - "node_modules/react"

"read-cache@npm:1.0.0":
  locations:
    - "node_modules/read-cache"

"readdirp@npm:3.6.0":
  locations:
    - "node_modules/readdirp"

"recast@npm:0.23.11":
  locations:
    - "node_modules/recast"

"redent@npm:3.0.0":
  locations:
    - "node_modules/redent"

"reflect.getprototypeof@npm:1.0.10":
  locations:
    - "node_modules/reflect.getprototypeof"

"regexp.prototype.flags@npm:1.5.4":
  locations:
    - "node_modules/regexp.prototype.flags"

"require-directory@npm:2.1.1":
  locations:
    - "node_modules/require-directory"

"require-main-filename@npm:2.0.0":
  locations:
    - "node_modules/require-main-filename"

"requires-port@npm:1.0.0":
  locations:
    - "node_modules/requires-port"

"resize-observer-polyfill@npm:1.5.1":
  locations:
    - "node_modules/resize-observer-polyfill"

"resolve-alpn@npm:1.2.1":
  locations:
    - "node_modules/resolve-alpn"

"resolve-cwd@npm:3.0.0":
  locations:
    - "node_modules/resolve-cwd"

"resolve-from@npm:4.0.0":
  locations:
    - "node_modules/import-fresh/node_modules/resolve-from"

"resolve-from@npm:5.0.0":
  locations:
    - "node_modules/resolve-from"

"resolve.exports@npm:2.0.3":
  locations:
    - "node_modules/resolve.exports"

"resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d":
  locations:
    - "node_modules/resolve"

"resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d":
  locations:
    - "node_modules/eslint-plugin-react/node_modules/resolve"

"responselike@npm:2.0.1":
  locations:
    - "node_modules/responselike"

"retry@npm:0.12.0":
  locations:
    - "node_modules/retry"

"reusify@npm:1.1.0":
  locations:
    - "node_modules/reusify"

"rollup@npm:4.44.2":
  locations:
    - "node_modules/rollup"

"run-parallel@npm:1.2.0":
  locations:
    - "node_modules/run-parallel"

"safe-array-concat@npm:1.1.3":
  locations:
    - "node_modules/safe-array-concat"

"safe-push-apply@npm:1.0.0":
  locations:
    - "node_modules/safe-push-apply"

"safe-regex-test@npm:1.1.0":
  locations:
    - "node_modules/safe-regex-test"

"safer-buffer@npm:2.1.2":
  locations:
    - "node_modules/safer-buffer"

"saxes@npm:6.0.0":
  locations:
    - "node_modules/saxes"

"scheduler@npm:0.23.2":
  locations:
    - "shared/node_modules/scheduler"
    - "apps/zalo/node_modules/scheduler"
    - "apps/web/node_modules/scheduler"

"scheduler@npm:0.26.0":
  locations:
    - "node_modules/scheduler"

"semver@npm:5.7.2":
  locations:
    - "node_modules/@babel/cli/node_modules/semver"

"semver@npm:6.3.1":
  locations:
    - "node_modules/istanbul-lib-instrument/node_modules/semver"
    - "node_modules/eslint-plugin-react/node_modules/semver"
    - "node_modules/@babel/helper-compilation-targets/node_modules/semver"
    - "node_modules/@babel/core/node_modules/semver"

"semver@npm:7.7.2":
  locations:
    - "node_modules/semver"

"set-blocking@npm:2.0.0":
  locations:
    - "node_modules/set-blocking"

"set-function-length@npm:1.2.2":
  locations:
    - "node_modules/set-function-length"

"set-function-name@npm:2.0.2":
  locations:
    - "node_modules/set-function-name"

"set-proto@npm:1.0.0":
  locations:
    - "node_modules/set-proto"

"shebang-command@npm:2.0.0":
  locations:
    - "node_modules/shebang-command"

"shebang-regex@npm:3.0.0":
  locations:
    - "node_modules/shebang-regex"

"side-channel-list@npm:1.0.0":
  locations:
    - "node_modules/side-channel-list"

"side-channel-map@npm:1.0.1":
  locations:
    - "node_modules/side-channel-map"

"side-channel-weakmap@npm:1.0.2":
  locations:
    - "node_modules/side-channel-weakmap"

"side-channel@npm:1.1.0":
  locations:
    - "node_modules/side-channel"

"signal-exit@npm:3.0.7":
  locations:
    - "node_modules/signal-exit"

"signal-exit@npm:4.1.0":
  locations:
    - "node_modules/foreground-child/node_modules/signal-exit"

"sisteransi@npm:1.0.5":
  locations:
    - "node_modules/sisteransi"

"slash@npm:2.0.0":
  locations:
    - "node_modules/@babel/cli/node_modules/slash"

"slash@npm:3.0.0":
  locations:
    - "node_modules/slash"

"smart-buffer@npm:4.2.0":
  locations:
    - "node_modules/smart-buffer"

"socks-proxy-agent@npm:8.0.5":
  locations:
    - "node_modules/socks-proxy-agent"

"socks@npm:2.8.5":
  locations:
    - "node_modules/socks"

"source-map-js@npm:1.2.1":
  locations:
    - "node_modules/source-map-js"

"source-map-support@npm:0.5.13":
  locations:
    - "node_modules/source-map-support"

"source-map@npm:0.6.1":
  locations:
    - "node_modules/source-map"

"sprintf-js@npm:1.0.3":
  locations:
    - "node_modules/sprintf-js"

"sprintf-js@npm:1.1.3":
  locations:
    - "node_modules/ip-address/node_modules/sprintf-js"

"ssri@npm:12.0.0":
  locations:
    - "node_modules/ssri"

"stack-utils@npm:2.0.6":
  locations:
    - "node_modules/stack-utils"

"stop-iteration-iterator@npm:1.1.0":
  locations:
    - "node_modules/stop-iteration-iterator"

"storybook@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:8.6.14":
  locations:
    - "node_modules/storybook"

"string-length@npm:4.0.2":
  locations:
    - "node_modules/string-length"

"string-width@npm:4.2.3":
  locations:
    - "node_modules/string-width-cjs"
    - "node_modules/string-width"

"string-width@npm:5.1.2":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/string-width"

"string.prototype.matchall@npm:4.0.12":
  locations:
    - "node_modules/string.prototype.matchall"

"string.prototype.repeat@npm:1.0.0":
  locations:
    - "node_modules/string.prototype.repeat"

"string.prototype.trim@npm:1.2.10":
  locations:
    - "node_modules/string.prototype.trim"

"string.prototype.trimend@npm:1.0.9":
  locations:
    - "node_modules/string.prototype.trimend"

"string.prototype.trimstart@npm:1.0.8":
  locations:
    - "node_modules/string.prototype.trimstart"

"strip-ansi@npm:6.0.1":
  locations:
    - "node_modules/strip-ansi-cjs"
    - "node_modules/strip-ansi"

"strip-ansi@npm:7.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/strip-ansi"

"strip-bom@npm:3.0.0":
  locations:
    - "node_modules/tsconfig-paths/node_modules/strip-bom"

"strip-bom@npm:4.0.0":
  locations:
    - "node_modules/strip-bom"

"strip-final-newline@npm:2.0.0":
  locations:
    - "node_modules/strip-final-newline"

"strip-indent@npm:3.0.0":
  locations:
    - "node_modules/strip-indent"

"strip-indent@npm:4.0.0":
  locations:
    - "node_modules/react-docgen/node_modules/strip-indent"

"strip-json-comments@npm:3.1.1":
  locations:
    - "node_modules/strip-json-comments"

"sucrase@npm:3.35.0":
  locations:
    - "node_modules/sucrase"

"supports-color@npm:7.2.0":
  locations:
    - "node_modules/supports-color"

"supports-color@npm:8.1.1":
  locations:
    - "node_modules/jest-worker/node_modules/supports-color"

"supports-preserve-symlinks-flag@npm:1.0.0":
  locations:
    - "node_modules/supports-preserve-symlinks-flag"

"symbol-tree@npm:3.2.4":
  locations:
    - "node_modules/symbol-tree"

"tailwindcss@npm:3.4.17":
  locations:
    - "node_modules/tailwindcss"

"taptap-web-zalo-monorepo@workspace:.":
  locations:
    - ""
  bin:
    "node_modules/eslint-plugin-react":
      "resolve": "resolve/bin/resolve"
      "semver": "semver/bin/semver.js"
    "node_modules/istanbul-lib-instrument":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/helper-compilation-targets":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/core":
      "semver": "semver/bin/semver.js"
    "node_modules/@babel/cli":
      "semver": "semver/bin/semver"
    "node_modules/sucrase":
      "glob": "glob/dist/esm/bin.mjs"
    "node_modules/cacache":
      "glob": "glob/dist/esm/bin.mjs"
    "node_modules/@eslint/eslintrc":
      "js-yaml": "js-yaml/bin/js-yaml.js"
    "node_modules/node-gyp":
      "node-which": "which/bin/which.js"
    "node_modules/tar":
      "mkdirp": "mkdirp/dist/cjs/src/bin.js"
    ".":
      "pnpify": "@yarnpkg/pnpify/lib/cli.js"
      "shell": "@yarnpkg/shell/lib/cli.js"
      "turbo": "turbo/bin/turbo"
      "tsc": "typescript/bin/tsc"
      "tsserver": "typescript/bin/tsserver"
      "parser": "@babel/parser/bin/babel-parser.js"
      "babel": "@babel/cli/bin/babel.js"
      "babel-external-helpers": "@babel/cli/bin/babel-external-helpers.js"
      "eslint": "eslint/bin/eslint.js"
      "tailwind": "tailwindcss/lib/cli.js"
      "tailwindcss": "tailwindcss/lib/cli.js"
      "jest": "jest-cli/bin/jest.js"
      "ts-jest": "ts-jest/cli.js"
      "vite": "vite/bin/vite.js"
      "sucrase": "sucrase/bin/sucrase"
      "sucrase-node": "sucrase/bin/sucrase-node"
      "lz-string": "lz-string/bin/bin.js"
      "import-local-fixture": "import-local/fixtures/cli.js"
      "ejs": "ejs/bin/cli.js"
      "json5": "json5/lib/cli.js"
      "semver": "semver/bin/semver.js"
      "esbuild": "esbuild/bin/esbuild"
      "rollup": "rollup/dist/bin/rollup"
      "getstorybook": "storybook/bin/index.cjs"
      "sb": "storybook/bin/index.cjs"
      "storybook": "storybook/bin/index.cjs"
      "autoprefixer": "autoprefixer/bin/autoprefixer"
      "loose-envify": "loose-envify/cli.js"
      "resolve": "resolve/bin/resolve"
      "mkdirp": "mkdirp/bin/cmd.js"
      "node-which": "which/bin/node-which"
      "acorn": "acorn/bin/acorn"
      "esgenerate": "escodegen/bin/esgenerate.js"
      "escodegen": "escodegen/bin/escodegen.js"
      "create-jest": "create-jest/bin/create-jest.js"
      "jake": "jake/bin/cli.js"
      "node-gyp": "node-gyp/bin/node-gyp.js"
      "nanoid": "nanoid/bin/nanoid.cjs"
      "jiti": "jiti/bin/jiti.js"
      "mini-svg-data-uri": "mini-svg-data-uri/cli.js"
      "browserslist": "browserslist/cli.js"
      "qrcode": "qrcode/bin/qrcode"
      "js-yaml": "js-yaml/bin/js-yaml.js"
      "esparse": "esprima/bin/esparse.js"
      "esvalidate": "esprima/bin/esvalidate.js"
      "nopt": "nopt/bin/nopt.js"
      "cssesc": "cssesc/bin/cssesc"
      "yaml": "yaml/bin.mjs"
      "uuid": "uuid/dist/bin/uuid"
      "update-browserslist-db": "update-browserslist-db/cli.js"
      "jsesc": "jsesc/bin/jsesc"
      "is-docker": "is-docker/cli.js"
    "apps/web":
      "vite": "vite/bin/vite.js"
      "esbuild": "esbuild/bin/esbuild"
      "glob": "glob/dist/esm/bin.mjs"

"tar@npm:6.2.1":
  locations:
    - "node_modules/@yarnpkg/core/node_modules/tar"

"tar@npm:7.4.3":
  locations:
    - "node_modules/tar"

"test-exclude@npm:6.0.0":
  locations:
    - "node_modules/test-exclude"

"thenify-all@npm:1.6.0":
  locations:
    - "node_modules/thenify-all"

"thenify@npm:3.3.1":
  locations:
    - "node_modules/thenify"

"tiny-invariant@npm:1.3.3":
  locations:
    - "node_modules/tiny-invariant"

"tinyglobby@npm:0.2.14":
  locations:
    - "node_modules/tinyglobby"

"tinylogic@npm:2.0.0":
  locations:
    - "node_modules/tinylogic"

"tinyrainbow@npm:1.2.0":
  locations:
    - "node_modules/tinyrainbow"

"tinyspy@npm:3.0.2":
  locations:
    - "node_modules/tinyspy"

"tmpl@npm:1.0.5":
  locations:
    - "node_modules/tmpl"

"to-regex-range@npm:5.0.1":
  locations:
    - "node_modules/to-regex-range"

"tough-cookie@npm:4.1.4":
  locations:
    - "node_modules/tough-cookie"

"tr46@npm:3.0.0":
  locations:
    - "node_modules/tr46"

"treeify@npm:1.1.0":
  locations:
    - "node_modules/treeify"

"ts-api-utils@virtual:f7f6d4157b204914ab71905f3c980bf89bfcc57aa7ada9d3ab8dc3660209fe3f2ad0cba5c5cfdc6fffb5ada07613901facac414d2b1fe7b78e2b253c6ab8f738#npm:2.1.0":
  locations:
    - "node_modules/ts-api-utils"

"ts-dedent@npm:2.2.0":
  locations:
    - "node_modules/ts-dedent"

"ts-interface-checker@npm:0.1.13":
  locations:
    - "node_modules/ts-interface-checker"

"ts-jest@virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:29.4.0":
  locations:
    - "node_modules/ts-jest"

"tsconfig-paths@npm:4.2.0":
  locations:
    - "node_modules/tsconfig-paths"

"tslib@npm:1.14.1":
  locations:
    - "node_modules/@sentry/utils/node_modules/tslib"
    - "node_modules/@sentry/tracing/node_modules/tslib"
    - "node_modules/@sentry/minimal/node_modules/tslib"
    - "node_modules/@sentry/hub/node_modules/tslib"
    - "node_modules/@sentry/core/node_modules/tslib"
    - "node_modules/@sentry/browser/node_modules/tslib"

"tslib@npm:2.8.1":
  locations:
    - "node_modules/tslib"

"turbo-darwin-arm64@npm:2.5.4":
  locations:
    - "node_modules/turbo-darwin-arm64"

"turbo@npm:2.5.4":
  locations:
    - "node_modules/turbo"

"typanion@npm:3.14.0":
  locations:
    - "node_modules/typanion"

"type-check@npm:0.4.0":
  locations:
    - "node_modules/type-check"

"type-detect@npm:4.0.8":
  locations:
    - "node_modules/type-detect"

"type-fest@npm:0.21.3":
  locations:
    - "node_modules/type-fest"

"type-fest@npm:4.41.0":
  locations:
    - "node_modules/ts-jest/node_modules/type-fest"

"typed-array-buffer@npm:1.0.3":
  locations:
    - "node_modules/typed-array-buffer"

"typed-array-byte-length@npm:1.0.3":
  locations:
    - "node_modules/typed-array-byte-length"

"typed-array-byte-offset@npm:1.0.4":
  locations:
    - "node_modules/typed-array-byte-offset"

"typed-array-length@npm:1.0.7":
  locations:
    - "node_modules/typed-array-length"

"typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=74658d":
  locations:
    - "node_modules/typescript"

"unbox-primitive@npm:1.1.0":
  locations:
    - "node_modules/unbox-primitive"

"undici-types@npm:5.26.5":
  locations:
    - "node_modules/@yarnpkg/pnp/node_modules/undici-types"

"undici-types@npm:6.21.0":
  locations:
    - "node_modules/@types/node/node_modules/undici-types"

"undici-types@npm:7.8.0":
  locations:
    - "node_modules/undici-types"

"unique-filename@npm:4.0.0":
  locations:
    - "node_modules/unique-filename"

"unique-slug@npm:5.0.0":
  locations:
    - "node_modules/unique-slug"

"universalify@npm:0.2.0":
  locations:
    - "node_modules/universalify"

"unplugin@npm:1.16.1":
  locations:
    - "node_modules/unplugin"

"update-browserslist-db@virtual:7df10d33cd6842659a3529d46decd4f1eeb5ec25fc4c848cff54ea69abd11a20a55277c57a073bbb3a702942d2ae57b9433c8450dcbffbc4f38ee3eb9668c39d#npm:1.1.3":
  locations:
    - "node_modules/update-browserslist-db"

"uri-js@npm:4.4.1":
  locations:
    - "node_modules/uri-js"

"url-parse@npm:1.5.10":
  locations:
    - "node_modules/url-parse"

"use-sync-external-store@virtual:7c2c92252d83674db8a888abb73726e1754435fe3890afe0ceff48958ec8ab06cb82520019c816687c91e49db519f808ae30b3b95b8307d666f04c2d33193b54#npm:1.5.0":
  locations:
    - "shared/node_modules/use-sync-external-store"
    - "apps/zalo/node_modules/use-sync-external-store"
    - "apps/web/node_modules/use-sync-external-store"

"util-deprecate@npm:1.0.2":
  locations:
    - "node_modules/util-deprecate"

"util@npm:0.12.5":
  locations:
    - "node_modules/util"

"uuid@npm:9.0.1":
  locations:
    - "node_modules/uuid"

"v8-to-istanbul@npm:9.3.0":
  locations:
    - "node_modules/v8-to-istanbul"

"vite@virtual:1c51e927dc60bf9b37beae673c54ecb2a6ea485d7e1d4392065bc7d7352f20e7fd7b393b045a177b35a8111d4886863da0580a3a75773af15c7ae2422abd2a77#npm:7.0.3":
  locations:
    - "node_modules/vite"
  aliases:
    - "virtual:6d8b0d4452e63ccff88e6ef545f1f9e21fa2fe870814f74a0b3bb99f6eee0ad34d559c11697d77c94a133529f982393da20ea3dcc0475a22c866382f9f37f5cb#npm:7.0.3"

"vite@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:5.4.19":
  locations:
    - "apps/web/node_modules/vite"

"w3c-xmlserializer@npm:4.0.0":
  locations:
    - "node_modules/w3c-xmlserializer"

"walker@npm:1.0.8":
  locations:
    - "node_modules/walker"

"webidl-conversions@npm:7.0.0":
  locations:
    - "node_modules/webidl-conversions"

"webpack-virtual-modules@npm:0.6.2":
  locations:
    - "node_modules/webpack-virtual-modules"

"whatwg-encoding@npm:2.0.0":
  locations:
    - "node_modules/whatwg-encoding"

"whatwg-mimetype@npm:3.0.0":
  locations:
    - "node_modules/whatwg-mimetype"

"whatwg-url@npm:11.0.0":
  locations:
    - "node_modules/whatwg-url"

"which-boxed-primitive@npm:1.1.1":
  locations:
    - "node_modules/which-boxed-primitive"

"which-builtin-type@npm:1.2.1":
  locations:
    - "node_modules/which-builtin-type"

"which-collection@npm:1.0.2":
  locations:
    - "node_modules/which-collection"

"which-module@npm:2.0.1":
  locations:
    - "node_modules/which-module"

"which-typed-array@npm:1.1.19":
  locations:
    - "node_modules/which-typed-array"

"which@npm:2.0.2":
  locations:
    - "node_modules/which"

"which@npm:5.0.0":
  locations:
    - "node_modules/node-gyp/node_modules/which"

"word-wrap@npm:1.2.5":
  locations:
    - "node_modules/word-wrap"

"wrap-ansi@npm:6.2.0":
  locations:
    - "node_modules/qrcode/node_modules/wrap-ansi"

"wrap-ansi@npm:7.0.0":
  locations:
    - "node_modules/wrap-ansi-cjs"
    - "node_modules/wrap-ansi"

"wrap-ansi@npm:8.1.0":
  locations:
    - "node_modules/@isaacs/cliui/node_modules/wrap-ansi"

"wrappy@npm:1.0.2":
  locations:
    - "node_modules/wrappy"

"write-file-atomic@npm:4.0.2":
  locations:
    - "node_modules/write-file-atomic"

"ws@virtual:0ab5a747d12ad5379b9c55587dea94bcb157783b85398af323f51e8e8de037120a4f7be7d14de9ba97b4b5fdce23fa745189306194eb865d61144ff55611f187#npm:8.18.3":
  locations:
    - "node_modules/ws"

"xml-name-validator@npm:4.0.0":
  locations:
    - "node_modules/xml-name-validator"

"xmlchars@npm:2.2.0":
  locations:
    - "node_modules/xmlchars"

"y18n@npm:4.0.3":
  locations:
    - "node_modules/qrcode/node_modules/y18n"

"y18n@npm:5.0.8":
  locations:
    - "node_modules/y18n"

"yallist@npm:3.1.1":
  locations:
    - "node_modules/@babel/helper-compilation-targets/node_modules/yallist"

"yallist@npm:4.0.0":
  locations:
    - "node_modules/yallist"

"yallist@npm:5.0.0":
  locations:
    - "node_modules/tar/node_modules/yallist"

"yaml@npm:2.8.0":
  locations:
    - "node_modules/yaml"

"yargs-parser@npm:18.1.3":
  locations:
    - "node_modules/qrcode/node_modules/yargs-parser"

"yargs-parser@npm:21.1.1":
  locations:
    - "node_modules/yargs-parser"

"yargs@npm:15.4.1":
  locations:
    - "node_modules/qrcode/node_modules/yargs"

"yargs@npm:17.7.2":
  locations:
    - "node_modules/yargs"

"yocto-queue@npm:0.1.0":
  locations:
    - "node_modules/yocto-queue"

"zmp-sdk@npm:2.46.5":
  locations:
    - "node_modules/zmp-sdk"

"zmp-ui@npm:1.11.11":
  locations:
    - "node_modules/zmp-ui"

"zustand@virtual:d7b372507202d06465fbb39bc574ea7c57a92cca1a3f4a7d7a974d36d17af47ebd5cfc094d6dd88a8db32ab0e43faf1d7746718fb4831a2ea3e5dc9fd31e3d47#npm:4.5.7":
  locations:
    - "shared/node_modules/zustand"
    - "apps/zalo/node_modules/zustand"
    - "apps/web/node_modules/zustand"
