{"version": 3, "file": "react-router-dom.production.min.js", "sources": ["../dom.ts", "../index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "names": ["defaultEncType", "isHtmlElement", "object", "tagName", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "_formDataSupportsSubmitter", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "has", "getFormSubmissionInfo", "target", "basename", "method", "action", "formData", "body", "toLowerCase", "attr", "getAttribute", "stripBasename", "FormData", "isButtonElement", "isInputElement", "type", "form", "Error", "document", "createElement", "e", "isFormDataSubmitterSupported", "name", "prefix", "append", "undefined", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "mapRouteProperties", "dataStrategy", "patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "React", "createContext", "isTransitioning", "FetchersContext", "Map", "startTransitionImpl", "flushSyncImpl", "ReactDOM", "useIdImpl", "flushSyncSafe", "cb", "Deferred", "constructor", "this", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "startTransitionSafe", "setState", "newState", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "for<PERSON>ach", "fetcher", "current", "set", "delete", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "async", "location", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "logV6DeprecationWarnings", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "v7_partialHydration", "MemoizedDataRoutes", "DataRoutes", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HistoryRouter", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "onClick", "relative", "reloadDocument", "viewTransition", "rest", "ref", "absoluteHref", "useContext", "NavigationContext", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "assign", "event", "defaultPrevented", "NavLink", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "fetcher<PERSON>ey", "onSubmit", "props", "forwardedRef", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "useDataRouterContext", "<PERSON><PERSON><PERSON>", "ctx", "invariant", "useDataRouterState", "replaceProp", "useNavigate", "button", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isModifiedEvent", "shouldProcessLinkClick", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "searchParams", "locationSearch", "defaultSearchParams", "_", "getAll", "getSearchParamsForLocation", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "useRouteId", "options", "validateClientSideSubmission", "fetch", "formEncType", "fromRouteId", "routeContext", "RouteContext", "match", "matches", "slice", "params", "indexValues", "some", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "useFetchers", "UseFetchers", "from", "savedScrollPositions", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "callback", "capture", "addEventListener", "removeEventListener", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "scrollTo", "useBeforeUnload", "usePrompt", "when", "blocker", "useBlocker", "confirm", "setTimeout", "proceed", "reset", "currentPath", "nextPath", "matchPath"], "mappings": ";;;;;;;;;;m9CAOO,MACDA,EAA8B,oCAE7B,SAASC,EAAcC,GAC5B,OAAiB,MAAVA,GAA4C,iBAAnBA,EAAOC,OACzC,CA+DO,SAASC,EACdC,EAA4B,IAE5B,OAAO,IAAIC,gBACO,iBAATD,GACPE,MAAMC,QAAQH,IACdA,aAAgBC,gBACZD,EACAI,OAAOC,KAAKL,GAAMM,QAAO,CAACC,EAAMC,KAC9B,IAAIC,EAAQT,EAAKQ,GACjB,OAAOD,EAAKG,OACVR,MAAMC,QAAQM,GAASA,EAAME,KAAKC,GAAM,CAACJ,EAAKI,KAAM,CAAC,CAACJ,EAAKC,IAC5D,GACA,IAEX,CA4CA,IAAII,EAA6C,KAgGjD,MAAMC,EAA0C,IAAIC,IAAI,CACtD,oCACA,sBACA,eAGF,SAASC,EAAeC,GACtB,OAAe,MAAXA,GAAoBH,EAAsBI,IAAID,GAS3CA,EAFE,IAGX,CAEO,SAASE,EACdC,EACAC,GAQA,IAAIC,EACAC,EACAN,EACAO,EACAC,EAEJ,GAtPO7B,EADqBC,EAuPVuB,IAtP+C,SAAjCvB,EAAOC,QAAQ4B,cAsPpB,CAIzB,IAAIC,EAAOP,EAAOQ,aAAa,UAC/BL,EAASI,EAAOE,EAAcF,EAAMN,GAAY,KAChDC,EAASF,EAAOQ,aAAa,WAxQY,MAyQzCX,EAAUD,EAAeI,EAAOQ,aAAa,aAAejC,EAE5D6B,EAAW,IAAIM,SAASV,EACzB,MAAM,GArQF,SAAyBvB,GAC9B,OAAOD,EAAcC,IAA4C,WAAjCA,EAAOC,QAAQ4B,aACjD,CAoQIK,CAAgBX,IA9Pb,SAAwBvB,GAC7B,OAAOD,EAAcC,IAA4C,UAAjCA,EAAOC,QAAQ4B,aACjD,CA6PKM,CAAeZ,KACG,WAAhBA,EAAOa,MAAqC,UAAhBb,EAAOa,MACtC,CACA,IAAIC,EAAOd,EAAOc,KAElB,GAAY,MAARA,EACF,MAAM,IAAIC,MACP,sEASL,IAAIR,EAAOP,EAAOQ,aAAa,eAAiBM,EAAKN,aAAa,UAmBlE,GAlBAL,EAASI,EAAOE,EAAcF,EAAMN,GAAY,KAEhDC,EACEF,EAAOQ,aAAa,eACpBM,EAAKN,aAAa,WAnSqB,MAqSzCX,EACED,EAAeI,EAAOQ,aAAa,iBACnCZ,EAAekB,EAAKN,aAAa,aACjCjC,EAGF6B,EAAW,IAAIM,SAASI,EAAMd,IA1KlC,WACE,GAAmC,OAA/BP,EACF,IACE,IAAIiB,SACFM,SAASC,cAAc,QAEvB,GAEFxB,GAA6B,CAG/B,CAFE,MAAOyB,GACPzB,GAA6B,CAC/B,CAEF,OAAOA,CACT,CAkKS0B,GAAgC,CACnC,IAAIC,KAAEA,EAAIP,KAAEA,EAAIxB,MAAEA,GAAUW,EAC5B,GAAa,UAATa,EAAkB,CACpB,IAAIQ,EAASD,EAAQ,GAAEA,KAAU,GACjChB,EAASkB,OAAQ,GAAED,KAAW,KAC9BjB,EAASkB,OAAQ,GAAED,KAAW,IAC/B,MAAUD,GACThB,EAASkB,OAAOF,EAAM/B,EAE1B,CACF,KAAO,IAAIb,EAAcwB,GACvB,MAAM,IAAIe,MACP,sFAIHb,EAjUyC,MAkUzCC,EAAS,KACTN,EAAUtB,EACV8B,EAAOL,CACT,CA1TK,IAAuBvB,EAkU5B,OALI2B,GAAwB,eAAZP,IACdQ,EAAOD,EACPA,OAAWmB,GAGN,CAAEpB,SAAQD,OAAQA,EAAOI,cAAeT,UAASO,WAAUC,OACpE,CC7FA,IACEmB,OAAOC,qBAHT,GAKE,CADA,MAAOP,IACP,CAgBK,SAASQ,EACdC,EACAC,GAEA,OAAOC,EAAa,CAClB5B,SAAU2B,GAAM3B,SAChB6B,OAAQ,IACHF,GAAME,OACTC,oBAAoB,GAEtBC,QAASC,EAAqB,CAAET,OAAQI,GAAMJ,SAC9CU,cAAeN,GAAMM,eAAiBC,IACtCR,4BACAS,EACAC,aAAcT,GAAMS,aACpBC,wBAAyBV,GAAMU,wBAC/Bd,OAAQI,GAAMJ,SACbe,YACL,CAEO,SAASC,EACdb,EACAC,GAEA,OAAOC,EAAa,CAClB5B,SAAU2B,GAAM3B,SAChB6B,OAAQ,IACHF,GAAME,OACTC,oBAAoB,GAEtBC,QAASS,EAAkB,CAAEjB,OAAQI,GAAMJ,SAC3CU,cAAeN,GAAMM,eAAiBC,IACtCR,4BACAS,EACAC,aAAcT,GAAMS,aACpBC,wBAAyBV,GAAMU,wBAC/Bd,OAAQI,GAAMJ,SACbe,YACL,CAEA,SAASJ,IACP,IAAIO,EAAQlB,QAAQmB,4BAOpB,OANID,GAASA,EAAME,SACjBF,EAAQ,IACHA,EACHE,OAAQC,EAAkBH,EAAME,UAG7BF,CACT,CAEA,SAASG,EACPD,GAEA,IAAKA,EAAQ,OAAO,KACpB,IAAIE,EAAU9D,OAAO8D,QAAQF,GACzBG,EAA6C,CAAA,EACjD,IAAK,IAAK3D,EAAK4D,KAAQF,EAGrB,GAAIE,GAAsB,uBAAfA,EAAIC,OACbF,EAAW3D,GAAO,IAAI8D,EACpBF,EAAIG,OACJH,EAAII,WACJJ,EAAIK,MACa,IAAjBL,EAAIM,eAED,GAAIN,GAAsB,UAAfA,EAAIC,OAAoB,CAExC,GAAID,EAAIO,UAAW,CACjB,IAAIC,EAAmBhC,OAAOwB,EAAIO,WAClC,GAAgC,mBAArBC,EACT,IAEE,IAAIC,EAAQ,IAAID,EAAiBR,EAAIU,SAGrCD,EAAME,MAAQ,GACdZ,EAAW3D,GAAOqE,CAElB,CADA,MAAOvC,IACP,CAGN,CAEA,GAAuB,MAAnB6B,EAAW3D,GAAc,CAC3B,IAAIqE,EAAQ,IAAI1C,MAAMiC,EAAIU,SAG1BD,EAAME,MAAQ,GACdZ,EAAW3D,GAAOqE,CACpB,CACF,MACEV,EAAW3D,GAAO4D,EAGtB,OAAOD,CACT,CAmBA,MAAMa,EAAwBC,EAAMC,cAA2C,CAC7EC,iBAAiB,IAWbC,EAAkBH,EAAMC,cAAqC,IAAIG,KAmCjEC,EAAsBL,EAAsB,gBAE5CM,EAAgBC,EAAmB,UAEnCC,EAAYR,EAAY,MAU9B,SAASS,EAAcC,GACjBJ,EACFA,EAAcI,GAEdA,GAEJ,CASA,MAAMC,EACJrB,OAA8C,UAM9CsB,cACEC,KAAKC,QAAU,IAAIC,SAAQ,CAACC,EAASC,KACnCJ,KAAKG,QAAWxF,IACM,YAAhBqF,KAAKvB,SACPuB,KAAKvB,OAAS,WACd0B,EAAQxF,GACV,EAEFqF,KAAKI,OAAUC,IACO,YAAhBL,KAAKvB,SACPuB,KAAKvB,OAAS,WACd2B,EAAOC,GACT,CACD,GAEL,EAMK,SAASC,GAAeC,gBAC7BA,EAAeC,OACfA,EAAMpD,OACNA,IAEA,IAAKY,EAAOyC,GAAgBtB,EAAMuB,SAASF,EAAOxC,QAC7C2C,EAAcC,GAAmBzB,EAAMuB,YACvCG,EAAWC,GAAgB3B,EAAMuB,SAAsC,CAC1ErB,iBAAiB,KAEd0B,EAAWC,GAAgB7B,EAAMuB,YACjCO,EAAYC,GAAiB/B,EAAMuB,YACnCS,EAAcC,GAAmBjC,EAAMuB,WAKxCW,EAAclC,EAAMmC,OAAyB,IAAI/B,MACjDgC,mBAAEA,GAAuBnE,GAAU,CAAA,EAEnCoE,EAAuBrC,EAAMsC,aAC9B5B,IACK0B,EAzEV,SAA6B1B,GACvBL,EACFA,EAAoBK,GAEpBA,GAEJ,CAoEQ6B,CAAoB7B,GAEpBA,GACF,GAEF,CAAC0B,IAGCI,EAAWxC,EAAMsC,aACnB,CACEG,GAEEC,kBACAC,UAAWA,EACXC,mBAAoBA,MAGtBH,EAASI,SAASC,SAAQ,CAACC,EAASxH,UACbmC,IAAjBqF,EAAQvD,MACV0C,EAAYc,QAAQC,IAAI1H,EAAKwH,EAAQvD,KACvC,IAEFkD,EAAgBI,SAASvH,GAAQ2G,EAAYc,QAAQE,OAAO3H,KAE5D,IAAI4H,EACe,MAAjB9B,EAAO1D,QACmB,MAA1B0D,EAAO1D,OAAOR,UACwC,mBAA/CkE,EAAO1D,OAAOR,SAASiG,oBAIhC,GAAKR,IAAsBO,EAA3B,CAUA,GAAIR,EAAW,CAEblC,GAAc,KAERqB,IACFF,GAAaA,EAAUZ,UACvBc,EAAWuB,kBAEb1B,EAAa,CACXzB,iBAAiB,EACjByC,WAAW,EACXW,gBAAiBV,EAAmBU,gBACpCC,aAAcX,EAAmBW,cACjC,IAIJ,IAAIC,EAAInC,EAAO1D,OAAQR,SAASiG,qBAAoB,KAClD3C,GAAc,IAAMa,EAAamB,IAAU,IAc7C,OAVAe,EAAEC,SAASC,SAAQ,KACjBjD,GAAc,KACZoB,OAAanE,GACbqE,OAAcrE,GACd+D,OAAgB/D,GAChBiE,EAAa,CAAEzB,iBAAiB,GAAQ,GACxC,SAGJO,GAAc,IAAMsB,EAAcyB,IAEpC,CAGI1B,GAGFF,GAAaA,EAAUZ,UACvBc,EAAWuB,iBACXpB,EAAgB,CACdpD,MAAO4D,EACPa,gBAAiBV,EAAmBU,gBACpCC,aAAcX,EAAmBW,iBAInC9B,EAAgBgB,GAChBd,EAAa,CACXzB,iBAAiB,EACjByC,WAAW,EACXW,gBAAiBV,EAAmBU,gBACpCC,aAAcX,EAAmBW,eAxDrC,MANMZ,EACFlC,GAAc,IAAMa,EAAamB,KAEjCJ,GAAqB,IAAMf,EAAamB,IA6D5C,GAEF,CAACpB,EAAO1D,OAAQmE,EAAYF,EAAWM,EAAaG,IAKtDrC,EAAM2D,iBAAgB,IAAMtC,EAAOuC,UAAUpB,IAAW,CAACnB,EAAQmB,IAIjExC,EAAM6D,WAAU,KACVnC,EAAUxB,kBAAoBwB,EAAUiB,WAC1Cd,EAAa,IAAIlB,EACnB,GACC,CAACe,IAKJ1B,EAAM6D,WAAU,KACd,GAAIjC,GAAaJ,GAAgBH,EAAO1D,OAAQ,CAC9C,IAAI8E,EAAWjB,EACXsC,EAAgBlC,EAAUd,QAC1BgB,EAAaT,EAAO1D,OAAOR,SAASiG,qBAAoBW,UAC1D1B,GAAqB,IAAMf,EAAamB,WAClCqB,CAAa,IAErBhC,EAAW2B,SAASC,SAAQ,KAC1B7B,OAAanE,GACbqE,OAAcrE,GACd+D,OAAgB/D,GAChBiE,EAAa,CAAEzB,iBAAiB,GAAQ,IAE1C6B,EAAcD,EAChB,IACC,CAACO,EAAsBb,EAAcI,EAAWP,EAAO1D,SAI1DqC,EAAM6D,WAAU,KAEZjC,GACAJ,GACA3C,EAAMmF,SAASzI,MAAQiG,EAAawC,SAASzI,KAE7CqG,EAAUZ,SACZ,GACC,CAACY,EAAWE,EAAYjD,EAAMmF,SAAUxC,IAI3CxB,EAAM6D,WAAU,MACTnC,EAAUxB,iBAAmB8B,IAChCP,EAAgBO,EAAanD,OAC7B8C,EAAa,CACXzB,iBAAiB,EACjByC,WAAW,EACXW,gBAAiBtB,EAAasB,gBAC9BC,aAAcvB,EAAauB,eAE7BtB,OAAgBvE,GAClB,GACC,CAACgE,EAAUxB,gBAAiB8B,IAE/BhC,EAAM6D,WAAU,QAQb,IAEH,IAAII,EAAYjE,EAAMkE,SAAQ,KACrB,CACLC,WAAY9C,EAAO8C,WACnBC,eAAgB/C,EAAO+C,eACvBC,GAAKC,GAAMjD,EAAOkD,SAASD,GAC3BE,KAAMA,CAACC,EAAI5F,EAAOd,IAChBsD,EAAOkD,SAASE,EAAI,CAClB5F,QACA6F,mBAAoB3G,GAAM2G,qBAE9BC,QAASA,CAACF,EAAI5F,EAAOd,IACnBsD,EAAOkD,SAASE,EAAI,CAClBE,SAAS,EACT9F,QACA6F,mBAAoB3G,GAAM2G,wBAG/B,CAACrD,IAEAjF,EAAWiF,EAAOjF,UAAY,IAE9BwI,EAAoB5E,EAAMkE,SAC5B,KAAO,CACL7C,SACA4C,YACAY,QAAQ,EACRzI,cAEF,CAACiF,EAAQ4C,EAAW7H,IAGlB0I,EAAe9E,EAAMkE,SACvB,KAAO,CACLa,qBAAsB1D,EAAOpD,OAAO8G,wBAEtC,CAAC1D,EAAOpD,OAAO8G,uBAcjB,OAXA/E,EAAM6D,WACJ,IAAMmB,EAAyB/G,EAAQoD,EAAOpD,SAC9C,CAACA,EAAQoD,EAAOpD,SAUhB+B,EAAA5C,cAAA4C,EAAAiF,SACEjF,KAAAA,EAAA5C,cAAC8H,EAAkBC,SAAQ,CAAC3J,MAAOoJ,GACjC5E,EAAA5C,cAACgI,EAAuBD,SAAQ,CAAC3J,MAAOqD,GACtCmB,EAAA5C,cAAC+C,EAAgBgF,SAAQ,CAAC3J,MAAO0G,EAAYc,SAC3ChD,EAAA5C,cAAC2C,EAAsBoF,SAAQ,CAAC3J,MAAOkG,GACrC1B,EAAA5C,cAACiI,EAAM,CACLjJ,SAAUA,EACV4H,SAAUnF,EAAMmF,SAChBsB,eAAgBzG,EAAM0G,cACtBtB,UAAWA,EACXhG,OAAQ6G,GAEPjG,EAAM2G,aAAenE,EAAOpD,OAAOwH,oBAClCzF,EAAA5C,cAACsI,EAAkB,CACjB5H,OAAQuD,EAAOvD,OACfG,OAAQoD,EAAOpD,OACfY,MAAOA,IAGTuC,OAOX,KAGP,CAGA,MAAMsE,EAAqB1F,EAAM1E,KAAKqK,GAEtC,SAASA,GAAW7H,OAClBA,EAAMG,OACNA,EAAMY,MACNA,IAMA,OAAO+G,EAAc9H,OAAQJ,EAAWmB,EAAOZ,EACjD,CAYO,SAAS4H,GAAczJ,SAC5BA,EAAQ0J,SACRA,EAAQ7H,OACRA,EAAMN,OACNA,IAEA,IAAIoI,EAAa/F,EAAMmC,SACG,MAAtB4D,EAAW/C,UACb+C,EAAW/C,QAAU5E,EAAqB,CAAET,SAAQqI,UAAU,KAGhE,IAAI7H,EAAU4H,EAAW/C,SACpBnE,EAAOyC,GAAgBtB,EAAMuB,SAAS,CACzCjF,OAAQ6B,EAAQ7B,OAChB0H,SAAU7F,EAAQ6F,YAEhB5B,mBAAEA,GAAuBnE,GAAU,CAAA,EACnCuE,EAAWxC,EAAMsC,aAClBG,IACCL,GAAsB/B,EAClBA,GAAoB,IAAMiB,EAAamB,KACvCnB,EAAamB,EAAS,GAE5B,CAACnB,EAAcc,IAOjB,OAJApC,EAAM2D,iBAAgB,IAAMxF,EAAQ8H,OAAOzD,IAAW,CAACrE,EAASqE,IAEhExC,EAAM6D,WAAU,IAAMmB,EAAyB/G,IAAS,CAACA,IAGvD+B,EAAA5C,cAACiI,EAAM,CACLjJ,SAAUA,EACV0J,SAAUA,EACV9B,SAAUnF,EAAMmF,SAChBsB,eAAgBzG,EAAMvC,OACtB2H,UAAW9F,EACXF,OAAQA,GAGd,CAaO,SAASiI,GAAW9J,SACzBA,EAAQ0J,SACRA,EAAQ7H,OACRA,EAAMN,OACNA,IAEA,IAAIoI,EAAa/F,EAAMmC,SACG,MAAtB4D,EAAW/C,UACb+C,EAAW/C,QAAUpE,EAAkB,CAAEjB,SAAQqI,UAAU,KAG7D,IAAI7H,EAAU4H,EAAW/C,SACpBnE,EAAOyC,GAAgBtB,EAAMuB,SAAS,CACzCjF,OAAQ6B,EAAQ7B,OAChB0H,SAAU7F,EAAQ6F,YAEhB5B,mBAAEA,GAAuBnE,GAAU,CAAA,EACnCuE,EAAWxC,EAAMsC,aAClBG,IACCL,GAAsB/B,EAClBA,GAAoB,IAAMiB,EAAamB,KACvCnB,EAAamB,EAAS,GAE5B,CAACnB,EAAcc,IAOjB,OAJApC,EAAM2D,iBAAgB,IAAMxF,EAAQ8H,OAAOzD,IAAW,CAACrE,EAASqE,IAEhExC,EAAM6D,WAAU,IAAMmB,EAAyB/G,IAAS,CAACA,IAGvD+B,EAAA5C,cAACiI,EAAM,CACLjJ,SAAUA,EACV0J,SAAUA,EACV9B,SAAUnF,EAAMmF,SAChBsB,eAAgBzG,EAAMvC,OACtB2H,UAAW9F,EACXF,OAAQA,GAGd,CAeA,SAASkI,GAAc/J,SACrBA,EAAQ0J,SACRA,EAAQ7H,OACRA,EAAME,QACNA,IAEA,IAAKU,EAAOyC,GAAgBtB,EAAMuB,SAAS,CACzCjF,OAAQ6B,EAAQ7B,OAChB0H,SAAU7F,EAAQ6F,YAEhB5B,mBAAEA,GAAuBnE,GAAU,CAAA,EACnCuE,EAAWxC,EAAMsC,aAClBG,IACCL,GAAsB/B,EAClBA,GAAoB,IAAMiB,EAAamB,KACvCnB,EAAamB,EAAS,GAE5B,CAACnB,EAAcc,IAOjB,OAJApC,EAAM2D,iBAAgB,IAAMxF,EAAQ8H,OAAOzD,IAAW,CAACrE,EAASqE,IAEhExC,EAAM6D,WAAU,IAAMmB,EAAyB/G,IAAS,CAACA,IAGvD+B,EAAA5C,cAACiI,EAAM,CACLjJ,SAAUA,EACV0J,SAAUA,EACV9B,SAAUnF,EAAMmF,SAChBsB,eAAgBzG,EAAMvC,OACtB2H,UAAW9F,EACXF,OAAQA,GAGd,CAmBA,MAAMmI,EACc,oBAAXzI,aACoB,IAApBA,OAAOR,eAC2B,IAAlCQ,OAAOR,SAASC,cAEnBiJ,EAAqB,gCAKdC,GAAOtG,EAAMuG,YACxB,UACEC,QACEA,EAAOC,SACPA,EAAQC,eACRA,EAAc/B,QACdA,EAAO9F,MACPA,EAAK1C,OACLA,EAAMsI,GACNA,EAAEC,mBACFA,EAAkBiC,eAClBA,KACGC,GAELC,GAEA,IAGIC,GAHA1K,SAAEA,GAAa4D,EAAM+G,WAAWC,GAIhCC,GAAa,EAEjB,GAAkB,iBAAPxC,GAAmB4B,EAAmBa,KAAKzC,KAEpDqC,EAAerC,EAGX2B,GACF,IACE,IAAIe,EAAa,IAAIC,IAAIzJ,OAAOqG,SAASqD,MACrCC,EAAY7C,EAAG8C,WAAW,MAC1B,IAAIH,IAAID,EAAWK,SAAW/C,GAC9B,IAAI2C,IAAI3C,GACRgD,EAAO7K,EAAc0K,EAAUI,SAAUtL,GAEzCkL,EAAUK,SAAWR,EAAWQ,QAAkB,MAARF,EAE5ChD,EAAKgD,EAAOH,EAAUM,OAASN,EAAUO,KAEzCZ,GAAa,CASjB,CAPE,MAAO5J,IAOT,CAKJ,IAAIgK,EAAOS,EAAQrD,EAAI,CAAEgC,aAErBsB,EAAkBC,GAAoBvD,EAAI,CAC5CE,UACA9F,QACA1C,SACAuI,qBACA+B,WACAE,mBAWF,OAEE3G,EAAA5C,cAAA,IAAAjC,OAAA8M,UACMrB,EAAI,CAAAS,KACFP,GAAgBO,EAAIb,QACjBS,GAAcP,EAAiBF,EAd5C,SACE0B,GAEI1B,GAASA,EAAQ0B,GAChBA,EAAMC,kBACTJ,EAAgBG,EAEpB,EAOiErB,IACxDA,EAAG1K,OACAA,IAGd,IA2BWiM,GAAUpI,EAAMuG,YAC3B,UAEI,eAAgB8B,EAAkB,OAAMC,cACxCA,GAAgB,EAChBC,UAAWC,EAAgB,GAAEC,IAC7BA,GAAM,EACNC,MAAOC,EAASlE,GAChBA,EAAEkC,eACFA,EAAcb,SACdA,KACGc,GAELC,GAEA,IAAIY,EAAOmB,EAAgBnE,EAAI,CAAEgC,SAAUG,EAAKH,WAC5CzC,EAAW6E,IACXC,EAAc9I,EAAM+G,WAAW3B,IAC/BnB,UAAEA,EAAS7H,SAAEA,GAAa4D,EAAM+G,WAAWC,GAC3C9G,EACa,MAAf4I,GAGAC,GAAuBtB,KACJ,IAAnBd,EAEEqC,EAAa/E,EAAUG,eACvBH,EAAUG,eAAeqD,GAAMC,SAC/BD,EAAKC,SACLuB,EAAmBjF,EAAS0D,SAC5BwB,EACFJ,GAAeA,EAAYK,YAAcL,EAAYK,WAAWnF,SAC5D8E,EAAYK,WAAWnF,SAAS0D,SAChC,KAEDY,IACHW,EAAmBA,EAAiBxM,cACpCyM,EAAuBA,EACnBA,EAAqBzM,cACrB,KACJuM,EAAaA,EAAWvM,eAGtByM,GAAwB9M,IAC1B8M,EACEtM,EAAcsM,EAAsB9M,IAAa8M,GAQrD,MAAME,EACW,MAAfJ,GAAsBA,EAAWK,SAAS,KACtCL,EAAWM,OAAS,EACpBN,EAAWM,OACjB,IAqBIf,EArBAgB,EACFN,IAAqBD,IACnBP,GACAQ,EAAiB1B,WAAWyB,IACkB,MAA9CC,EAAiBO,OAAOJ,GAExBK,EACsB,MAAxBP,IACCA,IAAyBF,IACtBP,GACAS,EAAqB3B,WAAWyB,IACmB,MAAnDE,EAAqBM,OAAOR,EAAWM,SAEzCI,EAAc,CAChBH,WACAE,YACAvJ,mBAGEyJ,EAAcJ,EAAWlB,OAAkB3K,EAI7C6K,EAD2B,mBAAlBC,EACGA,EAAckB,GAOd,CACVlB,EACAe,EAAW,SAAW,KACtBE,EAAY,UAAY,KACxBvJ,EAAkB,gBAAkB,MAEnC0J,OAAOC,SACPC,KAAK,KAGV,IAAIpB,EACmB,mBAAdC,EAA2BA,EAAUe,GAAef,EAE7D,OACE3I,EAAA5C,cAACkJ,GAAInL,OAAA8M,OAAA,CAAA,EACCrB,EAAI,CAAA,eACM+C,EAAWpB,UACdA,EAAS1B,IACfA,EAAG6B,MACDA,EAAKjE,GACRA,EAAEkC,eACUA,IAEK,mBAAbb,EAA0BA,EAAS4D,GAAe5D,EAGhE,IA2GWiE,GAAO/J,EAAMuG,YACxB,EAEIyD,aACAzF,WACAmC,iBACA/B,UACA9F,QACAxC,OAAAA,EDxwCuC,MCywCvCC,SACA2N,WACAxD,WACA/B,qBACAiC,oBACGuD,GAELC,KAEA,IAAIC,EAASC,KACTC,EAAaC,GAAcjO,EAAQ,CAAEmK,aACrC+D,EACuB,QAAzBnO,EAAOI,cAA0B,MAAQ,OA0B3C,OACEuD,EAAA5C,cAAAjC,OAAAA,OAAA8M,OAAA,CAAApB,IACOsD,EAAY9N,OACTmO,EAAUlO,OACVgO,EAAUL,SACRvD,EAAiBuD,EA7B+B/B,IAE5D,GADA+B,GAAYA,EAAS/B,GACjBA,EAAMC,iBAAkB,OAC5BD,EAAMuC,iBAEN,IAAIC,EAAaxC,EAAqCyC,YACnDD,UAECE,EACDF,GAAW/N,aAAa,eACzBN,EAEF+N,EAAOM,GAAaxC,EAAM2C,cAAe,CACvCb,aACA3N,OAAQuO,EACRrG,WACAI,UACA9F,QACA4H,WACA/B,qBACAiC,kBACA,GASIuD,GACJ,IAkBD,SAASY,IAAkBC,OAChCA,EAAMC,WACNA,IAGA,OADAC,GAAqB,CAAEF,SAAQC,eACxB,IACT,CASA,IAEKE,YAAAA,GAAc,OAAdA,EAAc,qBAAA,uBAAdA,EAAc,UAAA,YAAdA,EAAc,iBAAA,mBAAdA,EAAc,WAAA,aAAdA,EAAc,uBAAA,yBAAdA,CAAc,EAAdA,IAAc,CAAA,GAQdC,YAAAA,GAAmB,OAAnBA,EAAmB,WAAA,aAAnBA,EAAmB,YAAA,cAAnBA,EAAmB,qBAAA,uBAAnBA,CAAmB,EAAnBA,IAML,CAAA,GAQA,SAASC,GAAqBC,GAC5B,IAAIC,EAAMtL,EAAM+G,WAAW7B,GAE3B,OADUoG,GAAVC,GAAS,GACFD,CACT,CAEA,SAASE,GAAmBH,GAC1B,IAAIxM,EAAQmB,EAAM+G,WAAW3B,GAE7B,OADUvG,GAAV0M,GAAS,GACF1M,CACT,CASO,SAASmJ,GACdvD,GACAtI,OACEA,EACAwI,QAAS8G,EAAW5M,MACpBA,EAAK6F,mBACLA,EAAkB+B,SAClBA,EAAQE,eACRA,GAQE,IAEJ,IAAIpC,EAAWmH,IACX1H,EAAW6E,IACXpB,EAAOmB,EAAgBnE,EAAI,CAAEgC,aAEjC,OAAOzG,EAAMsC,aACV4F,IACC,GD93CC,SACLA,EACA/L,GAEA,QACmB,IAAjB+L,EAAMyD,QACJxP,GAAqB,UAAXA,GAVhB,SAAyB+L,GACvB,SAAUA,EAAM0D,SAAW1D,EAAM2D,QAAU3D,EAAM4D,SAAW5D,EAAM6D,SACpE,CASKC,CAAgB9D,GAErB,CCq3CU+D,CAAuB/D,EAAO/L,GAAS,CACzC+L,EAAMuC,iBAIN,IAAI9F,OACcjH,IAAhB+N,EACIA,EACAS,EAAWlI,KAAckI,EAAWzE,GAE1ClD,EAASE,EAAI,CACXE,UACA9F,QACA6F,qBACA+B,WACAE,kBAEJ,IAEF,CACE3C,EACAO,EACAkD,EACAgE,EACA5M,EACA1C,EACAsI,EACAC,EACA+B,EACAE,GAGN,CAMO,SAASwF,GACdC,GAUA,IAAIC,EAAyBrM,EAAMmC,OAAOrH,EAAmBsR,IACzDE,EAAwBtM,EAAMmC,QAAO,GAErC6B,EAAW6E,IACX0D,EAAevM,EAAMkE,SACvB,ID33CG,SACLsI,EACAC,GAEA,IAAIF,EAAezR,EAAmB0R,GAiBtC,OAfIC,GAMFA,EAAoB3J,SAAQ,CAAC4J,EAAGnR,KACzBgR,EAAatQ,IAAIV,IACpBkR,EAAoBE,OAAOpR,GAAKuH,SAAStH,IACvC+Q,EAAa9O,OAAOlC,EAAKC,EAAM,GAEnC,IAIG+Q,CACT,CCy2CMK,CACE5I,EAAS4D,OACT0E,EAAsBtJ,QAAU,KAAOqJ,EAAuBrJ,UAElE,CAACgB,EAAS4D,SAGRrD,EAAWmH,IACXmB,EAAkB7M,EAAMsC,aAC1B,CAACwK,EAAUC,KACT,MAAMC,EAAkBlS,EACF,mBAAbgS,EAA0BA,EAASP,GAAgBO,GAE5DR,EAAsBtJ,SAAU,EAChCuB,EAAS,IAAMyI,EAAiBD,EAAgB,GAElD,CAACxI,EAAUgI,IAGb,MAAO,CAACA,EAAcM,EACxB,CAoDA,IAAII,GAAY,EACZC,GAAqBA,IAAO,KAAIC,SAASF,QAMtC,SAAS5C,KACd,IAAIhJ,OAAEA,GAAW+J,GAAqBF,GAAekC,YACjDhR,SAAEA,GAAa4D,EAAM+G,WAAWC,GAChCqG,EAAiBC,IAErB,OAAOtN,EAAMsC,aACX,CAACnG,EAAQoR,EAAU,CAAA,MAtBvB,WACE,GAAwB,oBAAbpQ,SACT,MAAM,IAAID,MACR,gHAIN,CAgBMsQ,GAEA,IAAIlR,OAAEA,EAAMD,OAAEA,EAAML,QAAEA,EAAOO,SAAEA,EAAQC,KAAEA,GAASN,EAChDC,EACAC,GAGF,IAAyB,IAArBmR,EAAQhJ,SAAoB,CAC9B,IAAIhJ,EAAMgS,EAAQvD,YAAckD,KAChC7L,EAAOoM,MAAMlS,EAAK8R,EAAgBE,EAAQjR,QAAUA,EAAQ,CAC1DoI,mBAAoB6I,EAAQ7I,mBAC5BnI,WACAC,OACAgO,WAAY+C,EAAQlR,QAAWA,EAC/BqR,YAAaH,EAAQvR,SAAYA,EACjC2G,UAAW4K,EAAQ5K,WAEvB,MACEtB,EAAOkD,SAASgJ,EAAQjR,QAAUA,EAAQ,CACxCoI,mBAAoB6I,EAAQ7I,mBAC5BnI,WACAC,OACAgO,WAAY+C,EAAQlR,QAAWA,EAC/BqR,YAAaH,EAAQvR,SAAYA,EACjC2I,QAAS4I,EAAQ5I,QACjB9F,MAAO0O,EAAQ1O,MACf8O,YAAaN,EACb1K,UAAW4K,EAAQ5K,UACnBgE,eAAgB4G,EAAQ5G,gBAE5B,GAEF,CAACtF,EAAQjF,EAAUiR,GAEvB,CAIO,SAAS9C,GACdjO,GACAmK,SAAEA,GAAiD,IAEnD,IAAIrK,SAAEA,GAAa4D,EAAM+G,WAAWC,GAChC4G,EAAe5N,EAAM+G,WAAW8G,GAC1BD,GAAVrC,GAAS,GAET,IAAKuC,GAASF,EAAaG,QAAQC,OAAO,GAGtCvG,EAAO,IAAKmB,EAAgBtM,GAAkB,IAAK,CAAEmK,cAKrDzC,EAAW6E,IACf,GAAc,MAAVvM,EAAgB,CAGlBmL,EAAKG,OAAS5D,EAAS4D,OAKvB,IAAIqG,EAAS,IAAIjT,gBAAgByM,EAAKG,QAClCsG,EAAcD,EAAOtB,OAAO,SAEhC,GADyBuB,EAAYC,MAAMxS,GAAY,KAANA,IACzB,CACtBsS,EAAO/K,OAAO,SACdgL,EAAYtE,QAAQjO,GAAMA,IAAGmH,SAASnH,GAAMsS,EAAOxQ,OAAO,QAAS9B,KACnE,IAAIyS,EAAKH,EAAOI,WAChB5G,EAAKG,OAASwG,EAAM,IAAGA,IAAO,EAChC,CACF,CAiBA,OAfM9R,GAAqB,MAAXA,IAAmBwR,EAAMQ,MAAMC,QAC7C9G,EAAKG,OAASH,EAAKG,OACfH,EAAKG,OAAOjD,QAAQ,MAAO,WAC3B,UAOW,MAAbvI,IACFqL,EAAKC,SACe,MAAlBD,EAAKC,SAAmBtL,EAAWoS,EAAU,CAACpS,EAAUqL,EAAKC,YAG1DwE,EAAWzE,EACpB,CAgBO,SAASgH,IAAwBlT,IACtCA,GACoB,IACpB,IAAI8F,OAAEA,GAAW+J,GAAqBF,GAAewD,YACjD7P,EAAQ2M,GAAmBL,GAAoBuD,YAC/CxM,EAAclC,EAAM+G,WAAW5G,GAC/BmO,EAAQtO,EAAM+G,WAAW8G,GACzBc,EAAUL,EAAMP,QAAQO,EAAMP,QAAQzE,OAAS,IAAIgF,MAAMM,GAEnD1M,GAAVqJ,GAAS,GACC+C,GAAV/C,GAAS,GAEI,MAAXoD,GADFpD,GAAS,GAQT,IAAIsD,EAAarO,EAAYA,IAAc,IACtCwJ,EAAY8E,GAAiB9O,EAAMuB,SAAiBhG,GAAOsT,GAC5DtT,GAAOA,IAAQyO,EACjB8E,EAAcvT,GACJyO,GAEV8E,EAAc5B,MAIhBlN,EAAM6D,WAAU,KACdxC,EAAO0N,WAAW/E,GACX,KAIL3I,EAAO2N,cAAchF,EAAW,IAEjC,CAAC3I,EAAQ2I,IAGZ,IAAIiF,EAAOjP,EAAMsC,aACf,CAAC+E,EAActJ,KACH4Q,GAAVpD,GAAS,GACTlK,EAAOoM,MAAMzD,EAAY2E,EAAStH,EAAMtJ,EAAK,GAE/C,CAACiM,EAAY2E,EAAStN,IAGpB6N,EAAa7E,KACbD,EAASpK,EAAMsC,aACjB,CAACnG,EAAQ4B,KACPmR,EAAW/S,EAAQ,IACd4B,EACHwG,UAAU,EACVyF,cACA,GAEJ,CAACA,EAAYkF,IAGXC,EAAcnP,EAAMkE,SAAQ,IACZlE,EAAMuG,YACtB,CAAC2D,EAAOrD,IAEJ7G,EAAA5C,cAAC2M,GAAI5O,OAAA8M,OAAA,CAAA,EAAKiC,EAAK,CAAA3F,UAAY,EAAKyF,WAAcA,EAAUnD,IAAOA,QAQpE,CAACmD,IAGAjH,EAAUlE,EAAMgE,SAASuM,IAAIpF,IAAeqF,EAC5C7P,EAAO0C,EAAYkN,IAAIpF,GAY3B,OAX4BhK,EAAMkE,SAChC,KAAO,CACL6F,KAAMoF,EACN/E,SACA6E,UACGlM,EACHvD,UAEF,CAAC2P,EAAa/E,EAAQ6E,EAAMlM,EAASvD,GAIzC,CAMO,SAAS8P,KACd,IAAIzQ,EAAQ2M,GAAmBL,GAAoBoE,aACnD,OAAOtU,MAAMuU,KAAK3Q,EAAMgE,SAAS5D,WAAWvD,KAAI,EAAEH,EAAKwH,MAAc,IAChEA,EACHxH,SAEJ,CAGA,IAAIkU,GAA+C,CAAA,EAKnD,SAASxE,IAAqBF,OAC5BA,EAAMC,WACNA,GAIE,IACF,IAAI3J,OAAEA,GAAW+J,GAAqBF,GAAewE,uBACjDC,sBAAEA,EAAqBjL,mBAAEA,GAAuB8G,GAClDL,GAAoBuE,uBAElBtT,SAAEA,GAAa4D,EAAM+G,WAAWC,GAChChD,EAAW6E,IACXkF,EAAU6B,IACVzG,EAAa0G,IAGjB7P,EAAM6D,WAAU,KACdlG,OAAOQ,QAAQ2R,kBAAoB,SAC5B,KACLnS,OAAOQ,QAAQ2R,kBAAoB,MAAM,IAE1C,IAqIL,SACEC,EACAxC,GAEA,IAAIyC,QAAEA,GAAYzC,GAAW,CAAA,EAC7BvN,EAAM6D,WAAU,KACd,IAAI9F,EAAkB,MAAXiS,EAAkB,CAAEA,gBAAYtS,EAE3C,OADAC,OAAOsS,iBAAiB,WAAYF,EAAUhS,GACvC,KACLJ,OAAOuS,oBAAoB,WAAYH,EAAUhS,EAAK,CACvD,GACA,CAACgS,EAAUC,GAChB,CA9IEG,CACEnQ,EAAMsC,aAAY,KAChB,GAAyB,SAArB6G,EAAWtK,MAAkB,CAC/B,IAAItD,GAAOwP,EAASA,EAAO/G,EAAU+J,GAAW,OAAS/J,EAASzI,IAClEkU,GAAqBlU,GAAOoC,OAAOyS,OACrC,CACA,IACEC,eAAeC,QACbtF,GAvC6B,gCAwC7BuF,KAAKC,UAAUf,IAOnB,CALE,MAAO7P,GAKT,CACAjC,OAAOQ,QAAQ2R,kBAAoB,MAAM,GACxC,CAAC9E,EAAYD,EAAQ5B,EAAWtK,MAAOmF,EAAU+J,KAI9B,oBAAb5Q,WAET6C,EAAM2D,iBAAgB,KACpB,IACE,IAAI8M,EAAmBJ,eAAeK,QACpC1F,GA1D6B,iCA4D3ByF,IACFhB,GAAuBc,KAAKI,MAAMF,GAGpC,CADA,MAAOpT,IACP,IAED,CAAC2N,IAIJhL,EAAM2D,iBAAgB,KACpB,IAAIiN,EACF7F,GAAuB,MAAb3O,EACN,CAAC4H,EAAU+J,IACThD,EAEE,IACK/G,EACH0D,SACE9K,EAAcoH,EAAS0D,SAAUtL,IACjC4H,EAAS0D,UAEbqG,GAEJhD,EACF8F,EAA2BxP,GAAQyP,wBACrCrB,IACA,IAAM9R,OAAOyS,SACbQ,GAEF,MAAO,IAAMC,GAA4BA,GAA0B,GAClE,CAACxP,EAAQjF,EAAU2O,IAItB/K,EAAM2D,iBAAgB,KAEpB,IAA8B,IAA1BgM,EAKJ,GAAqC,iBAA1BA,EAAX,CAMA,GAAI3L,EAAS6D,KAAM,CACjB,IAAIkJ,EAAK5T,SAAS6T,eAChBC,mBAAmBjN,EAAS6D,KAAKmG,MAAM,KAEzC,GAAI+C,EAEF,YADAA,EAAGG,gBAGP,EAG2B,IAAvBxM,GAKJ/G,OAAOwT,SAAS,EAAG,EAnBnB,MAFExT,OAAOwT,SAAS,EAAGxB,EAqBA,GACpB,CAAC3L,EAAU2L,EAAuBjL,IAEzC,CAYO,SAAS0M,GACdrB,EACAxC,GAEA,IAAIyC,QAAEA,GAAYzC,GAAW,CAAA,EAC7BvN,EAAM6D,WAAU,KACd,IAAI9F,EAAkB,MAAXiS,EAAkB,CAAEA,gBAAYtS,EAE3C,OADAC,OAAOsS,iBAAiB,eAAgBF,EAAUhS,GAC3C,KACLJ,OAAOuS,oBAAoB,eAAgBH,EAAUhS,EAAK,CAC3D,GACA,CAACgS,EAAUC,GAChB,CAgCA,SAASqB,IAAUC,KACjBA,EAAIzR,QACJA,IAKA,IAAI0R,EAAUC,EAAWF,GAEzBtR,EAAM6D,WAAU,KACd,GAAsB,YAAlB0N,EAAQ1S,MAAqB,CACjBlB,OAAO8T,QAAQ5R,GAK3B6R,WAAWH,EAAQI,QAAS,GAE5BJ,EAAQK,OAEZ,IACC,CAACL,EAAS1R,IAEbG,EAAM6D,WAAU,KACQ,YAAlB0N,EAAQ1S,OAAwByS,GAClCC,EAAQK,OACV,GACC,CAACL,EAASD,GACf,CAYA,SAASvI,GACPtE,EACA1G,EAA2C,IAE3C,IAAI2D,EAAY1B,EAAM+G,WAAWhH,GAGlB,MAAb2B,GADF6J,GAAS,GAMT,IAAInP,SAAEA,GAAagP,GACjBF,GAAenC,wBAEbtB,EAAOmB,EAAgBnE,EAAI,CAAEgC,SAAU1I,EAAK0I,WAChD,IAAK/E,EAAUxB,gBACb,OAAO,EAGT,IAAI2R,EACFjV,EAAc8E,EAAU4B,gBAAgBoE,SAAUtL,IAClDsF,EAAU4B,gBAAgBoE,SACxBoK,EACFlV,EAAc8E,EAAU6B,aAAamE,SAAUtL,IAC/CsF,EAAU6B,aAAamE,SAezB,OACwC,MAAtCqK,EAAUtK,EAAKC,SAAUoK,IACgB,MAAzCC,EAAUtK,EAAKC,SAAUmK,EAE7B"}