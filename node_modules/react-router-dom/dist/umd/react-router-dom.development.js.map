{"version": 3, "file": "react-router-dom.development.js", "sources": ["../../dom.ts", "../../index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "mapRouteProperties", "dataStrategy", "patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "React", "createContext", "isTransitioning", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "fetcher", "current", "set", "delete", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "logV6DeprecationWarnings", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "DataRoutes", "_ref3", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "HistoryRouter", "_ref6", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "useRouteId", "options", "fetch", "formEncType", "fromRouteId", "_temp2", "routeContext", "RouteContext", "match", "matches", "slice", "params", "indexValues", "hasNakedIndexParam", "some", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "_temp3", "_route$matches", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "fetcherWithComponents", "useFetchers", "UseFetchers", "from", "_ref11", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp4", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref12", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAOO,MAAMA,aAA6B,GAAG,KAAK,CAAA;EAClD,MAAMC,cAA2B,GAAG,mCAAmC,CAAA;EAEhE,SAASC,aAAaA,CAACC,MAAW,EAAyB;IAChE,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ,CAAA;EAC7D,CAAA;EAEO,SAASC,eAAeA,CAACF,MAAW,EAA+B;EACxE,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,QAAQ,CAAA;EAC3E,CAAA;EAEO,SAASC,aAAaA,CAACJ,MAAW,EAA6B;EACpE,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,MAAM,CAAA;EACzE,CAAA;EAEO,SAASE,cAAcA,CAACL,MAAW,EAA8B;EACtE,EAAA,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,OAAO,CAAA;EAC1E,CAAA;EAOA,SAASG,eAAeA,CAACC,KAAwB,EAAE;EACjD,EAAA,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,QAAQ,CAAC,CAAA;EAC7E,CAAA;EAEO,SAASC,sBAAsBA,CACpCL,KAAwB,EACxBM,MAAe,EACf;EACA,EAAA,OACEN,KAAK,CAACO,MAAM,KAAK,CAAC;EAAI;EACrB,EAAA,CAACD,MAAM,IAAIA,MAAM,KAAK,OAAO,CAAC;EAAI;EACnC,EAAA,CAACP,eAAe,CAACC,KAAK,CAAC;EAAC,GAAA;EAE5B,CAAA;;EAUA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASQ,kBAAkBA,CAChCC,IAAyB,EACR;EAAA,EAAA,IADjBA,IAAyB,KAAA,KAAA,CAAA,EAAA;EAAzBA,IAAAA,IAAyB,GAAG,EAAE,CAAA;EAAA,GAAA;EAE9B,EAAA,OAAO,IAAIC,eAAe,CACxB,OAAOD,IAAI,KAAK,QAAQ,IACxBE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IACnBA,IAAI,YAAYC,eAAe,GAC3BD,IAAI,GACJI,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EACtC,IAAA,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAG,CAAC,CAAA;EACrB,IAAA,OAAOD,IAAI,CAACG,MAAM,CAChBR,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,CAAEC,CAAC,IAAK,CAACJ,GAAG,EAAEI,CAAC,CAAC,CAAC,GAAG,CAAC,CAACJ,GAAG,EAAEC,KAAK,CAAC,CACnE,CAAC,CAAA;KACF,EAAE,EAAyB,CAClC,CAAC,CAAA;EACH,CAAA;EAEO,SAASI,0BAA0BA,CACxCC,cAAsB,EACtBC,mBAA2C,EAC3C;EACA,EAAA,IAAIC,YAAY,GAAGjB,kBAAkB,CAACe,cAAc,CAAC,CAAA;EAErD,EAAA,IAAIC,mBAAmB,EAAE;EACvB;EACA;EACA;EACA;EACA;EACAA,IAAAA,mBAAmB,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEV,GAAG,KAAK;EACtC,MAAA,IAAI,CAACQ,YAAY,CAACG,GAAG,CAACX,GAAG,CAAC,EAAE;UAC1BO,mBAAmB,CAACK,MAAM,CAACZ,GAAG,CAAC,CAACS,OAAO,CAAER,KAAK,IAAK;EACjDO,UAAAA,YAAY,CAACK,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC,CAAA;EACjC,SAAC,CAAC,CAAA;EACJ,OAAA;EACF,KAAC,CAAC,CAAA;EACJ,GAAA;EAEA,EAAA,OAAOO,YAAY,CAAA;EACrB,CAAA;;EAEA;;EAiBA;EACA,IAAIM,0BAA0C,GAAG,IAAI,CAAA;EAErD,SAASC,4BAA4BA,GAAG;IACtC,IAAID,0BAA0B,KAAK,IAAI,EAAE;MACvC,IAAI;EACF,MAAA,IAAIE,QAAQ,CACVC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EAC9B;EACA,MAAA,CACF,CAAC,CAAA;EACDJ,MAAAA,0BAA0B,GAAG,KAAK,CAAA;OACnC,CAAC,OAAOK,CAAC,EAAE;EACVL,MAAAA,0BAA0B,GAAG,IAAI,CAAA;EACnC,KAAA;EACF,GAAA;EACA,EAAA,OAAOA,0BAA0B,CAAA;EACnC,CAAA;;EAEA;EACA;EACA;;EAuCA;EACA;EACA;;EAGA;EACA;EACA;;EA8BA,MAAMM,qBAAuC,GAAG,IAAIC,GAAG,CAAC,CACtD,mCAAmC,EACnC,qBAAqB,EACrB,YAAY,CACb,CAAC,CAAA;EAEF,SAASC,cAAcA,CAACC,OAAsB,EAAE;IAC9C,IAAIA,OAAO,IAAI,IAAI,IAAI,CAACH,qBAAqB,CAACT,GAAG,CAACY,OAAsB,CAAC,EAAE;EACzEC,IAAAC,qBAAO,CACL,KAAK,EACL,IAAIF,GAAAA,OAAO,GACejD,4DAAAA,IAAAA,wBAAAA,GAAAA,cAAc,QAC1C,CAAC,CAAA,CAAA;EAED,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EACA,EAAA,OAAOiD,OAAO,CAAA;EAChB,CAAA;EAEO,SAASG,qBAAqBA,CACnCrC,MAAoB,EACpBsC,QAAgB,EAOhB;EACA,EAAA,IAAIC,MAAc,CAAA;EAClB,EAAA,IAAIC,MAAqB,CAAA;EACzB,EAAA,IAAIN,OAAe,CAAA;EACnB,EAAA,IAAIO,QAA8B,CAAA;EAClC,EAAA,IAAIC,IAAS,CAAA;EAEb,EAAA,IAAInD,aAAa,CAACS,MAAM,CAAC,EAAE;EACzB;EACA;EACA;EACA,IAAA,IAAI2C,IAAI,GAAG3C,MAAM,CAAC4C,YAAY,CAAC,QAAQ,CAAC,CAAA;MACxCJ,MAAM,GAAGG,IAAI,GAAGE,oBAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI,CAAA;MACpDC,MAAM,GAAGvC,MAAM,CAAC4C,YAAY,CAAC,QAAQ,CAAC,IAAI5D,aAAa,CAAA;MACvDkD,OAAO,GAAGD,cAAc,CAACjC,MAAM,CAAC4C,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI3D,cAAc,CAAA;EAE1EwD,IAAAA,QAAQ,GAAG,IAAId,QAAQ,CAAC3B,MAAM,CAAC,CAAA;KAChC,MAAM,IACLX,eAAe,CAACW,MAAM,CAAC,IACtBR,cAAc,CAACQ,MAAM,CAAC,KACpBA,MAAM,CAAC8C,IAAI,KAAK,QAAQ,IAAI9C,MAAM,CAAC8C,IAAI,KAAK,OAAO,CAAE,EACxD;EACA,IAAA,IAAIC,IAAI,GAAG/C,MAAM,CAAC+C,IAAI,CAAA;MAEtB,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAA,sEAEf,CAAC,CAAA;EACH,KAAA;;EAEA;;EAEA;EACA;EACA;EACA,IAAA,IAAIL,IAAI,GAAG3C,MAAM,CAAC4C,YAAY,CAAC,YAAY,CAAC,IAAIG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,CAAA;MAC3EJ,MAAM,GAAGG,IAAI,GAAGE,oBAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI,CAAA;EAEpDC,IAAAA,MAAM,GACJvC,MAAM,CAAC4C,YAAY,CAAC,YAAY,CAAC,IACjCG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,IAC3B5D,aAAa,CAAA;MACfkD,OAAO,GACLD,cAAc,CAACjC,MAAM,CAAC4C,YAAY,CAAC,aAAa,CAAC,CAAC,IAClDX,cAAc,CAACc,IAAI,CAACH,YAAY,CAAC,SAAS,CAAC,CAAC,IAC5C3D,cAAc,CAAA;;EAEhB;EACAwD,IAAAA,QAAQ,GAAG,IAAId,QAAQ,CAACoB,IAAI,EAAE/C,MAAM,CAAC,CAAA;;EAErC;EACA;EACA;EACA;EACA,IAAA,IAAI,CAAC0B,4BAA4B,EAAE,EAAE;QACnC,IAAI;UAAEuB,IAAI;UAAEH,IAAI;EAAElC,QAAAA,KAAAA;EAAM,OAAC,GAAGZ,MAAM,CAAA;QAClC,IAAI8C,IAAI,KAAK,OAAO,EAAE;EACpB,QAAA,IAAII,MAAM,GAAGD,IAAI,GAAMA,IAAI,SAAM,EAAE,CAAA;EACnCR,QAAAA,QAAQ,CAACjB,MAAM,CAAI0B,MAAM,GAAA,GAAA,EAAK,GAAG,CAAC,CAAA;EAClCT,QAAAA,QAAQ,CAACjB,MAAM,CAAI0B,MAAM,GAAA,GAAA,EAAK,GAAG,CAAC,CAAA;SACnC,MAAM,IAAID,IAAI,EAAE;EACfR,QAAAA,QAAQ,CAACjB,MAAM,CAACyB,IAAI,EAAErC,KAAK,CAAC,CAAA;EAC9B,OAAA;EACF,KAAA;EACF,GAAC,MAAM,IAAI1B,aAAa,CAACc,MAAM,CAAC,EAAE;EAChC,IAAA,MAAM,IAAIgD,KAAK,CACb,yDAAA,GAAA,+BAEF,CAAC,CAAA;EACH,GAAC,MAAM;EACLT,IAAAA,MAAM,GAAGvD,aAAa,CAAA;EACtBwD,IAAAA,MAAM,GAAG,IAAI,CAAA;EACbN,IAAAA,OAAO,GAAGjD,cAAc,CAAA;EACxByD,IAAAA,IAAI,GAAG1C,MAAM,CAAA;EACf,GAAA;;EAEA;EACA,EAAA,IAAIyC,QAAQ,IAAIP,OAAO,KAAK,YAAY,EAAE;EACxCQ,IAAAA,IAAI,GAAGD,QAAQ,CAAA;EACfA,IAAAA,QAAQ,GAAGU,SAAS,CAAA;EACtB,GAAA;IAEA,OAAO;MAAEX,MAAM;EAAED,IAAAA,MAAM,EAAEA,MAAM,CAACjD,WAAW,EAAE;MAAE4C,OAAO;MAAEO,QAAQ;EAAEC,IAAAA,IAAAA;KAAM,CAAA;EAC1E;;;;;ECjHA;EAUA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA,MAAAU,oBAAA,GAAA,GAAA,CAAA;EAEA,IAAI;IACFC,MAAM,CAACC,oBAAoB,GAAGF,oBAAoB,CAAA;EACpD,CAAC,CAAC,OAAOtB,CAAC,EAAE;EACV;EAAA,CAAA;;EAGF;EACA;EACA;EAWO,SAASyB,mBAAmBA,CACjCC,MAAqB,EACrBC,IAAoB,EACP;EACb,EAAA,OAAOC,mBAAY,CAAC;EAClBpB,IAAAA,QAAQ,EAAEmB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEnB,QAAQ;EACxBqB,IAAAA,MAAM,EAAAC,QAAA,CAAA,EAAA,EACDH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEE,MAAM,EAAA;EACfE,MAAAA,kBAAkB,EAAE,IAAA;OACrB,CAAA;MACDC,OAAO,EAAEC,2BAAoB,CAAC;EAAEV,MAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;EAAO,KAAC,CAAC;MACvDW,aAAa,EAAE,CAAAP,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEO,aAAa,KAAIC,kBAAkB,EAAE;MAC1DT,MAAM;0BACNU,qCAAkB;EAClBC,IAAAA,YAAY,EAAEV,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEU,YAAY;EAChCC,IAAAA,uBAAuB,EAAEX,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEW,uBAAuB;EACtDf,IAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;EAChB,GAAC,CAAC,CAACgB,UAAU,EAAE,CAAA;EACjB,CAAA;EAEO,SAASC,gBAAgBA,CAC9Bd,MAAqB,EACrBC,IAAoB,EACP;EACb,EAAA,OAAOC,mBAAY,CAAC;EAClBpB,IAAAA,QAAQ,EAAEmB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEnB,QAAQ;EACxBqB,IAAAA,MAAM,EAAAC,QAAA,CAAA,EAAA,EACDH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEE,MAAM,EAAA;EACfE,MAAAA,kBAAkB,EAAE,IAAA;OACrB,CAAA;MACDC,OAAO,EAAES,wBAAiB,CAAC;EAAElB,MAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;EAAO,KAAC,CAAC;MACpDW,aAAa,EAAE,CAAAP,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEO,aAAa,KAAIC,kBAAkB,EAAE;MAC1DT,MAAM;0BACNU,qCAAkB;EAClBC,IAAAA,YAAY,EAAEV,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEU,YAAY;EAChCC,IAAAA,uBAAuB,EAAEX,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEW,uBAAuB;EACtDf,IAAAA,MAAM,EAAEI,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEJ,MAAAA;EAChB,GAAC,CAAC,CAACgB,UAAU,EAAE,CAAA;EACjB,CAAA;EAEA,SAASJ,kBAAkBA,GAA+B;EAAA,EAAA,IAAAO,OAAA,CAAA;IACxD,IAAIC,KAAK,IAAAD,OAAA,GAAGnB,MAAM,KAANmB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAQE,2BAA2B,CAAA;EAC/C,EAAA,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAM,EAAE;MACzBF,KAAK,GAAAb,QAAA,CAAA,EAAA,EACAa,KAAK,EAAA;EACRE,MAAAA,MAAM,EAAEC,iBAAiB,CAACH,KAAK,CAACE,MAAM,CAAA;OACvC,CAAA,CAAA;EACH,GAAA;EACA,EAAA,OAAOF,KAAK,CAAA;EACd,CAAA;EAEA,SAASG,iBAAiBA,CACxBD,MAAsC,EACN;EAChC,EAAA,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI,CAAA;EACxB,EAAA,IAAIE,OAAO,GAAGtE,MAAM,CAACsE,OAAO,CAACF,MAAM,CAAC,CAAA;IACpC,IAAIG,UAA0C,GAAG,EAAE,CAAA;IACnD,KAAK,IAAI,CAACnE,GAAG,EAAEoE,GAAG,CAAC,IAAIF,OAAO,EAAE;EAC9B;EACA;EACA,IAAA,IAAIE,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;QAC9CF,UAAU,CAACnE,GAAG,CAAC,GAAG,IAAIsE,+BAAiB,CACrCF,GAAG,CAACG,MAAM,EACVH,GAAG,CAACI,UAAU,EACdJ,GAAG,CAACK,IAAI,EACRL,GAAG,CAACM,QAAQ,KAAK,IACnB,CAAC,CAAA;OACF,MAAM,IAAIN,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;EACxC;QACA,IAAID,GAAG,CAACO,SAAS,EAAE;EACjB,QAAA,IAAIC,gBAAgB,GAAGlC,MAAM,CAAC0B,GAAG,CAACO,SAAS,CAAC,CAAA;EAC5C,QAAA,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;YAC1C,IAAI;EACF;cACA,IAAIC,KAAK,GAAG,IAAID,gBAAgB,CAACR,GAAG,CAACU,OAAO,CAAC,CAAA;EAC7C;EACA;cACAD,KAAK,CAACE,KAAK,GAAG,EAAE,CAAA;EAChBZ,YAAAA,UAAU,CAACnE,GAAG,CAAC,GAAG6E,KAAK,CAAA;aACxB,CAAC,OAAO1D,CAAC,EAAE;EACV;EAAA,WAAA;EAEJ,SAAA;EACF,OAAA;EAEA,MAAA,IAAIgD,UAAU,CAACnE,GAAG,CAAC,IAAI,IAAI,EAAE;UAC3B,IAAI6E,KAAK,GAAG,IAAIxC,KAAK,CAAC+B,GAAG,CAACU,OAAO,CAAC,CAAA;EAClC;EACA;UACAD,KAAK,CAACE,KAAK,GAAG,EAAE,CAAA;EAChBZ,QAAAA,UAAU,CAACnE,GAAG,CAAC,GAAG6E,KAAK,CAAA;EACzB,OAAA;EACF,KAAC,MAAM;EACLV,MAAAA,UAAU,CAACnE,GAAG,CAAC,GAAGoE,GAAG,CAAA;EACvB,KAAA;EACF,GAAA;EACA,EAAA,OAAOD,UAAU,CAAA;EACnB,CAAA;;EAEA;;EAEA;EACA;EACA;AAaA,QAAMa,qBAAqB,gBAAGC,gBAAK,CAACC,aAAa,CAA8B;EAC7EC,EAAAA,eAAe,EAAE,KAAA;EACnB,CAAC,EAAC;EACW;IACXH,qBAAqB,CAACI,WAAW,GAAG,gBAAgB,CAAA;EACtD,CAAA;;EAIA;;AAGMC,QAAAA,eAAe,gBAAGJ,gBAAK,CAACC,aAAa,CAAwB,IAAII,GAAG,EAAE,EAAC;EAChE;IACXD,eAAe,CAACD,WAAW,GAAG,UAAU,CAAA;EAC1C,CAAA;;EAIA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA,MAAMG,gBAAgB,GAAG,iBAAiB,CAAA;EAC1C,MAAMC,mBAAmB,GAAGP,gBAAK,CAACM,gBAAgB,CAAC,CAAA;EACnD,MAAME,UAAU,GAAG,WAAW,CAAA;EAC9B,MAAMC,aAAa,GAAGC,mBAAQ,CAACF,UAAU,CAAC,CAAA;EAC1C,MAAMG,MAAM,GAAG,OAAO,CAAA;EACtB,MAAMC,SAAS,GAAGZ,gBAAK,CAACW,MAAM,CAAC,CAAA;EAE/B,SAASE,mBAAmBA,CAACC,EAAc,EAAE;EAC3C,EAAA,IAAIP,mBAAmB,EAAE;MACvBA,mBAAmB,CAACO,EAAE,CAAC,CAAA;EACzB,GAAC,MAAM;EACLA,IAAAA,EAAE,EAAE,CAAA;EACN,GAAA;EACF,CAAA;EAEA,SAASC,aAAaA,CAACD,EAAc,EAAE;EACrC,EAAA,IAAIL,aAAa,EAAE;MACjBA,aAAa,CAACK,EAAE,CAAC,CAAA;EACnB,GAAC,MAAM;EACLA,IAAAA,EAAE,EAAE,CAAA;EACN,GAAA;EACF,CAAA;EASA,MAAME,QAAQ,CAAI;EAGhB;;EAEA;;EAEAC,EAAAA,WAAWA,GAAG;MAAA,IANd3B,CAAAA,MAAM,GAAwC,SAAS,CAAA;MAOrD,IAAI,CAAC4B,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;EAC9C,MAAA,IAAI,CAACD,OAAO,GAAIpG,KAAK,IAAK;EACxB,QAAA,IAAI,IAAI,CAACsE,MAAM,KAAK,SAAS,EAAE;YAC7B,IAAI,CAACA,MAAM,GAAG,UAAU,CAAA;YACxB8B,OAAO,CAACpG,KAAK,CAAC,CAAA;EAChB,SAAA;SACD,CAAA;EACD,MAAA,IAAI,CAACqG,MAAM,GAAIC,MAAM,IAAK;EACxB,QAAA,IAAI,IAAI,CAAChC,MAAM,KAAK,SAAS,EAAE;YAC7B,IAAI,CAACA,MAAM,GAAG,UAAU,CAAA;YACxB+B,MAAM,CAACC,MAAM,CAAC,CAAA;EAChB,SAAA;SACD,CAAA;EACH,KAAC,CAAC,CAAA;EACJ,GAAA;EACF,CAAA;;EAEA;EACA;EACA;EACO,SAASC,cAAcA,CAAAC,IAAA,EAIc;IAAA,IAJb;MAC7BC,eAAe;cACfC,QAAM;EACN3D,IAAAA,MAAAA;EACmB,GAAC,GAAAyD,IAAA,CAAA;EACpB,EAAA,IAAI,CAAC3C,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,gBAAK,CAAC4B,QAAQ,CAACF,QAAM,CAAC7C,KAAK,CAAC,CAAA;IACxD,IAAI,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAG9B,gBAAK,CAAC4B,QAAQ,EAAe,CAAA;IACnE,IAAI,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGhC,gBAAK,CAAC4B,QAAQ,CAA8B;EAC1E1B,IAAAA,eAAe,EAAE,KAAA;EACnB,GAAC,CAAC,CAAA;IACF,IAAI,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGlC,gBAAK,CAAC4B,QAAQ,EAAkB,CAAA;IAChE,IAAI,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGpC,gBAAK,CAAC4B,QAAQ,EAAkB,CAAA;IAClE,IAAI,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGtC,gBAAK,CAAC4B,QAAQ,EAIhD,CAAA;IACJ,IAAIW,WAAW,GAAGvC,gBAAK,CAACwC,MAAM,CAAmB,IAAInC,GAAG,EAAE,CAAC,CAAA;IAC3D,IAAI;EAAEoC,IAAAA,kBAAAA;EAAmB,GAAC,GAAG1E,MAAM,IAAI,EAAE,CAAA;EAEzC,EAAA,IAAI2E,oBAAoB,GAAG1C,gBAAK,CAAC2C,WAAW,CACzC7B,EAAc,IAAK;EAClB,IAAA,IAAI2B,kBAAkB,EAAE;QACtB5B,mBAAmB,CAACC,EAAE,CAAC,CAAA;EACzB,KAAC,MAAM;EACLA,MAAAA,EAAE,EAAE,CAAA;EACN,KAAA;EACF,GAAC,EACD,CAAC2B,kBAAkB,CACrB,CAAC,CAAA;IAED,IAAIG,QAAQ,GAAG5C,gBAAK,CAAC2C,WAAW,CAC9B,CACEE,QAAqB,EAAAC,KAAA,KAMlB;MAAA,IALH;QACEC,eAAe;EACfC,MAAAA,SAAS,EAAEA,SAAS;EACpBC,MAAAA,kBAAkB,EAAEA,kBAAAA;EACtB,KAAC,GAAAH,KAAA,CAAA;MAEDD,QAAQ,CAACK,QAAQ,CAAC1H,OAAO,CAAC,CAAC2H,OAAO,EAAEpI,GAAG,KAAK;EAC1C,MAAA,IAAIoI,OAAO,CAAC3D,IAAI,KAAKjC,SAAS,EAAE;UAC9BgF,WAAW,CAACa,OAAO,CAACC,GAAG,CAACtI,GAAG,EAAEoI,OAAO,CAAC3D,IAAI,CAAC,CAAA;EAC5C,OAAA;EACF,KAAC,CAAC,CAAA;EACFuD,IAAAA,eAAe,CAACvH,OAAO,CAAET,GAAG,IAAKwH,WAAW,CAACa,OAAO,CAACE,MAAM,CAACvI,GAAG,CAAC,CAAC,CAAA;MAEjE,IAAIwI,2BAA2B,GAC7B7B,QAAM,CAACjE,MAAM,IAAI,IAAI,IACrBiE,QAAM,CAACjE,MAAM,CAACzB,QAAQ,IAAI,IAAI,IAC9B,OAAO0F,QAAM,CAACjE,MAAM,CAACzB,QAAQ,CAACwH,mBAAmB,KAAK,UAAU,CAAA;;EAElE;EACA;EACA,IAAA,IAAI,CAACP,kBAAkB,IAAIM,2BAA2B,EAAE;EACtD,MAAA,IAAIP,SAAS,EAAE;EACbjC,QAAAA,aAAa,CAAC,MAAMY,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;EAC7C,OAAC,MAAM;EACLH,QAAAA,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;EACpD,OAAA;EACA,MAAA,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAIG,SAAS,EAAE;EACb;EACAjC,MAAAA,aAAa,CAAC,MAAM;EAClB;EACA,QAAA,IAAIoB,UAAU,EAAE;EACdF,UAAAA,SAAS,IAAIA,SAAS,CAACb,OAAO,EAAE,CAAA;YAChCe,UAAU,CAACsB,cAAc,EAAE,CAAA;EAC7B,SAAA;EACAzB,QAAAA,YAAY,CAAC;EACX9B,UAAAA,eAAe,EAAE,IAAI;EACrB8C,UAAAA,SAAS,EAAE,IAAI;YACfU,eAAe,EAAET,kBAAkB,CAACS,eAAe;YACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;EACnC,SAAC,CAAC,CAAA;EACJ,OAAC,CAAC,CAAA;;EAEF;QACA,IAAIC,CAAC,GAAGlC,QAAM,CAACjE,MAAM,CAAEzB,QAAQ,CAACwH,mBAAmB,CAAC,MAAM;EACxDzC,QAAAA,aAAa,CAAC,MAAMY,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;EAC7C,OAAC,CAAC,CAAA;;EAEF;EACAe,MAAAA,CAAC,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAM;EACvB/C,QAAAA,aAAa,CAAC,MAAM;YAClBmB,YAAY,CAAC3E,SAAS,CAAC,CAAA;YACvB6E,aAAa,CAAC7E,SAAS,CAAC,CAAA;YACxBuE,eAAe,CAACvE,SAAS,CAAC,CAAA;EAC1ByE,UAAAA,YAAY,CAAC;EAAE9B,YAAAA,eAAe,EAAE,KAAA;EAAM,WAAC,CAAC,CAAA;EAC1C,SAAC,CAAC,CAAA;EACJ,OAAC,CAAC,CAAA;EAEFa,MAAAA,aAAa,CAAC,MAAMqB,aAAa,CAACwB,CAAC,CAAC,CAAC,CAAA;EACrC,MAAA,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAIzB,UAAU,EAAE;EACd;EACA;EACAF,MAAAA,SAAS,IAAIA,SAAS,CAACb,OAAO,EAAE,CAAA;QAChCe,UAAU,CAACsB,cAAc,EAAE,CAAA;EAC3BnB,MAAAA,eAAe,CAAC;EACdzD,QAAAA,KAAK,EAAEgE,QAAQ;UACfa,eAAe,EAAET,kBAAkB,CAACS,eAAe;UACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;EACnC,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM;EACL;QACA7B,eAAe,CAACe,QAAQ,CAAC,CAAA;EACzBb,MAAAA,YAAY,CAAC;EACX9B,QAAAA,eAAe,EAAE,IAAI;EACrB8C,QAAAA,SAAS,EAAE,KAAK;UAChBU,eAAe,EAAET,kBAAkB,CAACS,eAAe;UACnDC,YAAY,EAAEV,kBAAkB,CAACU,YAAAA;EACnC,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAC,EACD,CAACjC,QAAM,CAACjE,MAAM,EAAE0E,UAAU,EAAEF,SAAS,EAAEM,WAAW,EAAEG,oBAAoB,CAC1E,CAAC,CAAA;;EAED;EACA;EACA1C,EAAAA,gBAAK,CAAC+D,eAAe,CAAC,MAAMrC,QAAM,CAACsC,SAAS,CAACpB,QAAQ,CAAC,EAAE,CAAClB,QAAM,EAAEkB,QAAQ,CAAC,CAAC,CAAA;;EAE3E;EACA;IACA5C,gBAAK,CAACiE,SAAS,CAAC,MAAM;MACpB,IAAIlC,SAAS,CAAC7B,eAAe,IAAI,CAAC6B,SAAS,CAACiB,SAAS,EAAE;EACrDd,MAAAA,YAAY,CAAC,IAAIlB,QAAQ,EAAQ,CAAC,CAAA;EACpC,KAAA;EACF,GAAC,EAAE,CAACe,SAAS,CAAC,CAAC,CAAA;;EAEf;EACA;EACA;IACA/B,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpB,IAAA,IAAIhC,SAAS,IAAIJ,YAAY,IAAIH,QAAM,CAACjE,MAAM,EAAE;QAC9C,IAAIoF,QAAQ,GAAGhB,YAAY,CAAA;EAC3B,MAAA,IAAIqC,aAAa,GAAGjC,SAAS,CAACf,OAAO,CAAA;QACrC,IAAIiB,UAAU,GAAGT,QAAM,CAACjE,MAAM,CAACzB,QAAQ,CAACwH,mBAAmB,CAAC,YAAY;EACtEd,QAAAA,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAA;EAClD,QAAA,MAAMqB,aAAa,CAAA;EACrB,OAAC,CAAC,CAAA;EACF/B,MAAAA,UAAU,CAAC0B,QAAQ,CAACC,OAAO,CAAC,MAAM;UAChC5B,YAAY,CAAC3E,SAAS,CAAC,CAAA;UACvB6E,aAAa,CAAC7E,SAAS,CAAC,CAAA;UACxBuE,eAAe,CAACvE,SAAS,CAAC,CAAA;EAC1ByE,QAAAA,YAAY,CAAC;EAAE9B,UAAAA,eAAe,EAAE,KAAA;EAAM,SAAC,CAAC,CAAA;EAC1C,OAAC,CAAC,CAAA;QACFkC,aAAa,CAACD,UAAU,CAAC,CAAA;EAC3B,KAAA;EACF,GAAC,EAAE,CAACO,oBAAoB,EAAEb,YAAY,EAAEI,SAAS,EAAEP,QAAM,CAACjE,MAAM,CAAC,CAAC,CAAA;;EAElE;EACA;IACAuC,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpB,IAAA,IACEhC,SAAS,IACTJ,YAAY,IACZhD,KAAK,CAACsF,QAAQ,CAACpJ,GAAG,KAAK8G,YAAY,CAACsC,QAAQ,CAACpJ,GAAG,EAChD;QACAkH,SAAS,CAACb,OAAO,EAAE,CAAA;EACrB,KAAA;EACF,GAAC,EAAE,CAACa,SAAS,EAAEE,UAAU,EAAEtD,KAAK,CAACsF,QAAQ,EAAEtC,YAAY,CAAC,CAAC,CAAA;;EAEzD;EACA;IACA7B,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpB,IAAA,IAAI,CAAClC,SAAS,CAAC7B,eAAe,IAAImC,YAAY,EAAE;EAC9CP,MAAAA,eAAe,CAACO,YAAY,CAACxD,KAAK,CAAC,CAAA;EACnCmD,MAAAA,YAAY,CAAC;EACX9B,QAAAA,eAAe,EAAE,IAAI;EACrB8C,QAAAA,SAAS,EAAE,KAAK;UAChBU,eAAe,EAAErB,YAAY,CAACqB,eAAe;UAC7CC,YAAY,EAAEtB,YAAY,CAACsB,YAAAA;EAC7B,OAAC,CAAC,CAAA;QACFrB,eAAe,CAAC/E,SAAS,CAAC,CAAA;EAC5B,KAAA;KACD,EAAE,CAACwE,SAAS,CAAC7B,eAAe,EAAEmC,YAAY,CAAC,CAAC,CAAA;IAE7CrC,gBAAK,CAACiE,SAAS,CAAC,MAAM;MACpBzH,qBAAO,CACLiF,eAAe,IAAI,IAAI,IAAI,CAACC,QAAM,CAAC3D,MAAM,CAACqG,mBAAmB,EAC7D,8DAA8D,GAC5D,kEACJ,CAAC,CAAA,CAAA;EACD;EACA;KACD,EAAE,EAAE,CAAC,CAAA;EAEN,EAAA,IAAIC,SAAS,GAAGrE,gBAAK,CAACsE,OAAO,CAAC,MAAiB;MAC7C,OAAO;QACLC,UAAU,EAAE7C,QAAM,CAAC6C,UAAU;QAC7BC,cAAc,EAAE9C,QAAM,CAAC8C,cAAc;QACrCC,EAAE,EAAGC,CAAC,IAAKhD,QAAM,CAACiD,QAAQ,CAACD,CAAC,CAAC;EAC7BE,MAAAA,IAAI,EAAEA,CAACC,EAAE,EAAEhG,KAAK,EAAEhB,IAAI,KACpB6D,QAAM,CAACiD,QAAQ,CAACE,EAAE,EAAE;UAClBhG,KAAK;EACLiG,QAAAA,kBAAkB,EAAEjH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEiH,kBAAAA;EAC5B,OAAC,CAAC;EACJC,MAAAA,OAAO,EAAEA,CAACF,EAAE,EAAEhG,KAAK,EAAEhB,IAAI,KACvB6D,QAAM,CAACiD,QAAQ,CAACE,EAAE,EAAE;EAClBE,QAAAA,OAAO,EAAE,IAAI;UACblG,KAAK;EACLiG,QAAAA,kBAAkB,EAAEjH,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEiH,kBAAAA;SAC3B,CAAA;OACJ,CAAA;EACH,GAAC,EAAE,CAACpD,QAAM,CAAC,CAAC,CAAA;EAEZ,EAAA,IAAIhF,QAAQ,GAAGgF,QAAM,CAAChF,QAAQ,IAAI,GAAG,CAAA;EAErC,EAAA,IAAIsI,iBAAiB,GAAGhF,gBAAK,CAACsE,OAAO,CACnC,OAAO;cACL5C,QAAM;MACN2C,SAAS;EACTY,IAAAA,MAAM,EAAE,KAAK;EACbvI,IAAAA,QAAAA;KACD,CAAC,EACF,CAACgF,QAAM,EAAE2C,SAAS,EAAE3H,QAAQ,CAC9B,CAAC,CAAA;EAED,EAAA,IAAIwI,YAAY,GAAGlF,gBAAK,CAACsE,OAAO,CAC9B,OAAO;EACLa,IAAAA,oBAAoB,EAAEzD,QAAM,CAAC3D,MAAM,CAACoH,oBAAAA;KACrC,CAAC,EACF,CAACzD,QAAM,CAAC3D,MAAM,CAACoH,oBAAoB,CACrC,CAAC,CAAA;IAEDnF,gBAAK,CAACiE,SAAS,CACb,MAAMmB,2CAAwB,CAACrH,MAAM,EAAE2D,QAAM,CAAC3D,MAAM,CAAC,EACrD,CAACA,MAAM,EAAE2D,QAAM,CAAC3D,MAAM,CACxB,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,oBACEiC,gBAAA,CAAA/D,aAAA,CAAA+D,gBAAA,CAAAqF,QAAA,EACErF,IAAAA,eAAAA,gBAAA,CAAA/D,aAAA,CAACqJ,oCAAiB,CAACC,QAAQ,EAAA;EAACvK,IAAAA,KAAK,EAAEgK,iBAAAA;EAAkB,GAAA,eACnDhF,gBAAA,CAAA/D,aAAA,CAACuJ,yCAAsB,CAACD,QAAQ,EAAA;EAACvK,IAAAA,KAAK,EAAE6D,KAAAA;EAAM,GAAA,eAC5CmB,gBAAA,CAAA/D,aAAA,CAACmE,eAAe,CAACmF,QAAQ,EAAA;MAACvK,KAAK,EAAEuH,WAAW,CAACa,OAAAA;EAAQ,GAAA,eACnDpD,gBAAA,CAAA/D,aAAA,CAAC8D,qBAAqB,CAACwF,QAAQ,EAAA;EAACvK,IAAAA,KAAK,EAAE+G,SAAAA;EAAU,GAAA,eAC/C/B,gBAAA,CAAA/D,aAAA,CAACwJ,kBAAM,EAAA;EACL/I,IAAAA,QAAQ,EAAEA,QAAS;MACnByH,QAAQ,EAAEtF,KAAK,CAACsF,QAAS;MACzBuB,cAAc,EAAE7G,KAAK,CAAC8G,aAAc;EACpCtB,IAAAA,SAAS,EAAEA,SAAU;EACrBtG,IAAAA,MAAM,EAAEmH,YAAAA;EAAa,GAAA,EAEpBrG,KAAK,CAAC+G,WAAW,IAAIlE,QAAM,CAAC3D,MAAM,CAACqG,mBAAmB,gBACrDpE,gBAAA,CAAA/D,aAAA,CAAC4J,kBAAkB,EAAA;MACjBjI,MAAM,EAAE8D,QAAM,CAAC9D,MAAO;MACtBG,MAAM,EAAE2D,QAAM,CAAC3D,MAAO;EACtBc,IAAAA,KAAK,EAAEA,KAAAA;KACR,CAAC,GAEF4C,eAEI,CACsB,CACR,CACK,CACP,CAAC,EAC5B,IACD,CAAC,CAAA;EAEP,CAAA;;EAEA;EACA,MAAMoE,kBAAkB,gBAAG7F,gBAAK,CAAClF,IAAI,CAACgL,UAAU,CAAC,CAAA;EAEjD,SAASA,UAAUA,CAAAC,KAAA,EAQW;IAAA,IARV;MAClBnI,MAAM;MACNG,MAAM;EACNc,IAAAA,KAAAA;EAKF,GAAC,GAAAkH,KAAA,CAAA;IACC,OAAOC,gCAAa,CAACpI,MAAM,EAAEL,SAAS,EAAEsB,KAAK,EAAEd,MAAM,CAAC,CAAA;EACxD,CAAA;EASA;EACA;EACA;EACO,SAASkI,aAAaA,CAAAC,KAAA,EAKN;IAAA,IALO;MAC5BxJ,QAAQ;MACRyJ,QAAQ;MACRpI,MAAM;EACNN,IAAAA,MAAAA;EACkB,GAAC,GAAAyI,KAAA,CAAA;EACnB,EAAA,IAAIE,UAAU,GAAGpG,gBAAK,CAACwC,MAAM,EAAkB,CAAA;EAC/C,EAAA,IAAI4D,UAAU,CAAChD,OAAO,IAAI,IAAI,EAAE;EAC9BgD,IAAAA,UAAU,CAAChD,OAAO,GAAGjF,2BAAoB,CAAC;QAAEV,MAAM;EAAE4I,MAAAA,QAAQ,EAAE,IAAA;EAAK,KAAC,CAAC,CAAA;EACvE,GAAA;EAEA,EAAA,IAAInI,OAAO,GAAGkI,UAAU,CAAChD,OAAO,CAAA;IAChC,IAAI,CAACvE,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,gBAAK,CAAC4B,QAAQ,CAAC;MACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;MACtBuH,QAAQ,EAAEjG,OAAO,CAACiG,QAAAA;EACpB,GAAC,CAAC,CAAA;IACF,IAAI;EAAE1B,IAAAA,kBAAAA;EAAmB,GAAC,GAAG1E,MAAM,IAAI,EAAE,CAAA;EACzC,EAAA,IAAI6E,QAAQ,GAAG5C,gBAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAK;EAC5DJ,IAAAA,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;EAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CACnC,CAAC,CAAA;EAEDzC,EAAAA,gBAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC,CAAA;EAE1E5C,EAAAA,gBAAK,CAACiE,SAAS,CAAC,MAAMmB,2CAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;EAEjE,EAAA,oBACEiC,gBAAA,CAAA/D,aAAA,CAACwJ,kBAAM,EAAA;EACL/I,IAAAA,QAAQ,EAAEA,QAAS;EACnByJ,IAAAA,QAAQ,EAAEA,QAAS;MACnBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAS;MACzBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAO;EAC7ByH,IAAAA,SAAS,EAAEnG,OAAQ;EACnBH,IAAAA,MAAM,EAAEA,MAAAA;EAAO,GAChB,CAAC,CAAA;EAEN,CAAA;EASA;EACA;EACA;EACA;EACO,SAASwI,UAAUA,CAAAC,KAAA,EAKN;IAAA,IALO;MACzB9J,QAAQ;MACRyJ,QAAQ;MACRpI,MAAM;EACNN,IAAAA,MAAAA;EACe,GAAC,GAAA+I,KAAA,CAAA;EAChB,EAAA,IAAIJ,UAAU,GAAGpG,gBAAK,CAACwC,MAAM,EAAe,CAAA;EAC5C,EAAA,IAAI4D,UAAU,CAAChD,OAAO,IAAI,IAAI,EAAE;EAC9BgD,IAAAA,UAAU,CAAChD,OAAO,GAAGzE,wBAAiB,CAAC;QAAElB,MAAM;EAAE4I,MAAAA,QAAQ,EAAE,IAAA;EAAK,KAAC,CAAC,CAAA;EACpE,GAAA;EAEA,EAAA,IAAInI,OAAO,GAAGkI,UAAU,CAAChD,OAAO,CAAA;IAChC,IAAI,CAACvE,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,gBAAK,CAAC4B,QAAQ,CAAC;MACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;MACtBuH,QAAQ,EAAEjG,OAAO,CAACiG,QAAAA;EACpB,GAAC,CAAC,CAAA;IACF,IAAI;EAAE1B,IAAAA,kBAAAA;EAAmB,GAAC,GAAG1E,MAAM,IAAI,EAAE,CAAA;EACzC,EAAA,IAAI6E,QAAQ,GAAG5C,gBAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAK;EAC5DJ,IAAAA,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;EAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CACnC,CAAC,CAAA;EAEDzC,EAAAA,gBAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC,CAAA;EAE1E5C,EAAAA,gBAAK,CAACiE,SAAS,CAAC,MAAMmB,2CAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;EAEjE,EAAA,oBACEiC,gBAAA,CAAA/D,aAAA,CAACwJ,kBAAM,EAAA;EACL/I,IAAAA,QAAQ,EAAEA,QAAS;EACnByJ,IAAAA,QAAQ,EAAEA,QAAS;MACnBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAS;MACzBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAO;EAC7ByH,IAAAA,SAAS,EAAEnG,OAAQ;EACnBH,IAAAA,MAAM,EAAEA,MAAAA;EAAO,GAChB,CAAC,CAAA;EAEN,CAAA;EASA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS0I,aAAaA,CAAAC,KAAA,EAKC;IAAA,IALA;MACrBhK,QAAQ;MACRyJ,QAAQ;MACRpI,MAAM;EACNG,IAAAA,OAAAA;EACkB,GAAC,GAAAwI,KAAA,CAAA;IACnB,IAAI,CAAC7H,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,gBAAK,CAAC4B,QAAQ,CAAC;MACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;MACtBuH,QAAQ,EAAEjG,OAAO,CAACiG,QAAAA;EACpB,GAAC,CAAC,CAAA;IACF,IAAI;EAAE1B,IAAAA,kBAAAA;EAAmB,GAAC,GAAG1E,MAAM,IAAI,EAAE,CAAA;EACzC,EAAA,IAAI6E,QAAQ,GAAG5C,gBAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAK;EAC5DJ,IAAAA,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC,CAAA;EAC5B,GAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CACnC,CAAC,CAAA;EAEDzC,EAAAA,gBAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC,CAAA;EAE1E5C,EAAAA,gBAAK,CAACiE,SAAS,CAAC,MAAMmB,2CAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAA;EAEjE,EAAA,oBACEiC,gBAAA,CAAA/D,aAAA,CAACwJ,kBAAM,EAAA;EACL/I,IAAAA,QAAQ,EAAEA,QAAS;EACnByJ,IAAAA,QAAQ,EAAEA,QAAS;MACnBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAS;MACzBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAO;EAC7ByH,IAAAA,SAAS,EAAEnG,OAAQ;EACnBH,IAAAA,MAAM,EAAEA,MAAAA;EAAO,GAChB,CAAC,CAAA;EAEN,CAAA;EAEa;IACX0I,aAAa,CAACtG,WAAW,GAAG,wBAAwB,CAAA;EACtD,CAAA;EAeA,MAAMwG,SAAS,GACb,OAAOlJ,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACzB,QAAQ,KAAK,WAAW,IACtC,OAAOyB,MAAM,CAACzB,QAAQ,CAACC,aAAa,KAAK,WAAW,CAAA;EAEtD,MAAM2K,kBAAkB,GAAG,+BAA+B,CAAA;;EAE1D;EACA;EACA;AACaC,QAAAA,IAAI,gBAAG7G,gBAAK,CAAC8G,UAAU,CAClC,SAASC,WAAWA,CAAAC,KAAA,EAalBC,GAAG,EACH;IAAA,IAbA;QACEC,OAAO;QACPC,QAAQ;QACRC,cAAc;QACdrC,OAAO;QACPlG,KAAK;QACLzE,MAAM;QACNyK,EAAE;QACFC,kBAAkB;EAClBuC,MAAAA,cAAAA;EAEF,KAAC,GAAAL,KAAA;EADIM,IAAAA,IAAI,GAAAC,6BAAA,CAAAP,KAAA,EAAAQ,SAAA,CAAA,CAAA;IAIT,IAAI;EAAE9K,IAAAA,QAAAA;EAAS,GAAC,GAAGsD,gBAAK,CAACyH,UAAU,CAACC,oCAAiB,CAAC,CAAA;;EAEtD;EACA,EAAA,IAAIC,YAAY,CAAA;IAChB,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB,IAAI,OAAO/C,EAAE,KAAK,QAAQ,IAAI+B,kBAAkB,CAACiB,IAAI,CAAChD,EAAE,CAAC,EAAE;EACzD;EACA8C,IAAAA,YAAY,GAAG9C,EAAE,CAAA;;EAEjB;EACA,IAAA,IAAI8B,SAAS,EAAE;QACb,IAAI;UACF,IAAImB,UAAU,GAAG,IAAIC,GAAG,CAACtK,MAAM,CAAC0G,QAAQ,CAAC6D,IAAI,CAAC,CAAA;UAC9C,IAAIC,SAAS,GAAGpD,EAAE,CAACqD,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAIH,GAAG,CAACD,UAAU,CAACK,QAAQ,GAAGtD,EAAE,CAAC,GACjC,IAAIkD,GAAG,CAAClD,EAAE,CAAC,CAAA;UACf,IAAIuD,IAAI,GAAGnL,oBAAa,CAACgL,SAAS,CAACI,QAAQ,EAAE3L,QAAQ,CAAC,CAAA;UAEtD,IAAIuL,SAAS,CAACK,MAAM,KAAKR,UAAU,CAACQ,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;EAC1D;YACAvD,EAAE,GAAGuD,IAAI,GAAGH,SAAS,CAACM,MAAM,GAAGN,SAAS,CAACO,IAAI,CAAA;EAC/C,SAAC,MAAM;EACLZ,UAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,SAAA;SACD,CAAC,OAAO1L,CAAC,EAAE;EACV;EACAK,QAAAC,qBAAO,CACL,KAAK,EACL,aAAA,GAAaqI,EAAE,GAAA,wDAAA,GAAA,mDAEjB,CAAC,CAAA,CAAA;EACH,OAAA;EACF,KAAA;EACF,GAAA;;EAEA;EACA,EAAA,IAAImD,IAAI,GAAGS,mBAAO,CAAC5D,EAAE,EAAE;EAAEsC,IAAAA,QAAAA;EAAS,GAAC,CAAC,CAAA;EAEpC,EAAA,IAAIuB,eAAe,GAAGC,mBAAmB,CAAC9D,EAAE,EAAE;MAC5CE,OAAO;MACPlG,KAAK;MACLzE,MAAM;MACN0K,kBAAkB;MAClBqC,QAAQ;EACRE,IAAAA,cAAAA;EACF,GAAC,CAAC,CAAA;IACF,SAASuB,WAAWA,CAClB9O,KAAsD,EACtD;EACA,IAAA,IAAIoN,OAAO,EAAEA,OAAO,CAACpN,KAAK,CAAC,CAAA;EAC3B,IAAA,IAAI,CAACA,KAAK,CAAC+O,gBAAgB,EAAE;QAC3BH,eAAe,CAAC5O,KAAK,CAAC,CAAA;EACxB,KAAA;EACF,GAAA;EAEA,EAAA;EAAA;EACE;EACAkG,IAAAA,gBAAA,CAAA/D,aAAA,CAAA+B,GAAAA,EAAAA,QAAA,KACMsJ,IAAI,EAAA;QACRU,IAAI,EAAEL,YAAY,IAAIK,IAAK;EAC3Bd,MAAAA,OAAO,EAAEU,UAAU,IAAIR,cAAc,GAAGF,OAAO,GAAG0B,WAAY;EAC9D3B,MAAAA,GAAG,EAAEA,GAAI;EACT7M,MAAAA,MAAM,EAAEA,MAAAA;OACT,CAAA,CAAA;EAAC,IAAA;EAEN,CACF,EAAC;EAEY;IACXyM,IAAI,CAAC1G,WAAW,GAAG,MAAM,CAAA;EAC3B,CAAA;EAmBA;EACA;EACA;AACa2I,QAAAA,OAAO,gBAAG9I,gBAAK,CAAC8G,UAAU,CACrC,SAASiC,cAAcA,CAAAC,KAAA,EAYrB/B,GAAG,EACH;IAAA,IAZA;QACE,cAAc,EAAEgC,eAAe,GAAG,MAAM;EACxCC,MAAAA,aAAa,GAAG,KAAK;QACrBC,SAAS,EAAEC,aAAa,GAAG,EAAE;EAC7BC,MAAAA,GAAG,GAAG,KAAK;EACXC,MAAAA,KAAK,EAAEC,SAAS;QAChB1E,EAAE;QACFwC,cAAc;EACdlB,MAAAA,QAAAA;EAEF,KAAC,GAAA6C,KAAA;EADI1B,IAAAA,IAAI,GAAAC,6BAAA,CAAAyB,KAAA,EAAAQ,UAAA,CAAA,CAAA;EAIT,EAAA,IAAIpB,IAAI,GAAGqB,2BAAe,CAAC5E,EAAE,EAAE;MAAEsC,QAAQ,EAAEG,IAAI,CAACH,QAAAA;EAAS,GAAC,CAAC,CAAA;EAC3D,EAAA,IAAIhD,QAAQ,GAAGuF,uBAAW,EAAE,CAAA;EAC5B,EAAA,IAAIC,WAAW,GAAG3J,gBAAK,CAACyH,UAAU,CAACjC,yCAAsB,CAAC,CAAA;IAC1D,IAAI;MAAEnB,SAAS;EAAE3H,IAAAA,QAAAA;EAAS,GAAC,GAAGsD,gBAAK,CAACyH,UAAU,CAACC,oCAAiB,CAAC,CAAA;EACjE,EAAA,IAAIxH,eAAe,GACjByJ,WAAW,IAAI,IAAI;EACnB;EACA;EACAC,EAAAA,sBAAsB,CAACxB,IAAI,CAAC,IAC5Bf,cAAc,KAAK,IAAI,CAAA;EAEzB,EAAA,IAAIwC,UAAU,GAAGxF,SAAS,CAACG,cAAc,GACrCH,SAAS,CAACG,cAAc,CAAC4D,IAAI,CAAC,CAACC,QAAQ,GACvCD,IAAI,CAACC,QAAQ,CAAA;EACjB,EAAA,IAAIyB,gBAAgB,GAAG3F,QAAQ,CAACkE,QAAQ,CAAA;IACxC,IAAI0B,oBAAoB,GACtBJ,WAAW,IAAIA,WAAW,CAACK,UAAU,IAAIL,WAAW,CAACK,UAAU,CAAC7F,QAAQ,GACpEwF,WAAW,CAACK,UAAU,CAAC7F,QAAQ,CAACkE,QAAQ,GACxC,IAAI,CAAA;IAEV,IAAI,CAACa,aAAa,EAAE;EAClBY,IAAAA,gBAAgB,GAAGA,gBAAgB,CAACpQ,WAAW,EAAE,CAAA;MACjDqQ,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACrQ,WAAW,EAAE,GAClC,IAAI,CAAA;EACRmQ,IAAAA,UAAU,GAAGA,UAAU,CAACnQ,WAAW,EAAE,CAAA;EACvC,GAAA;IAEA,IAAIqQ,oBAAoB,IAAIrN,QAAQ,EAAE;MACpCqN,oBAAoB,GAClB9M,oBAAa,CAAC8M,oBAAoB,EAAErN,QAAQ,CAAC,IAAIqN,oBAAoB,CAAA;EACzE,GAAA;;EAEA;EACA;EACA;EACA;EACA;IACA,MAAME,gBAAgB,GACpBJ,UAAU,KAAK,GAAG,IAAIA,UAAU,CAACK,QAAQ,CAAC,GAAG,CAAC,GAC1CL,UAAU,CAACM,MAAM,GAAG,CAAC,GACrBN,UAAU,CAACM,MAAM,CAAA;IACvB,IAAIC,QAAQ,GACVN,gBAAgB,KAAKD,UAAU,IAC9B,CAACR,GAAG,IACHS,gBAAgB,CAAC5B,UAAU,CAAC2B,UAAU,CAAC,IACvCC,gBAAgB,CAACO,MAAM,CAACJ,gBAAgB,CAAC,KAAK,GAAI,CAAA;EAEtD,EAAA,IAAIK,SAAS,GACXP,oBAAoB,IAAI,IAAI,KAC3BA,oBAAoB,KAAKF,UAAU,IACjC,CAACR,GAAG,IACHU,oBAAoB,CAAC7B,UAAU,CAAC2B,UAAU,CAAC,IAC3CE,oBAAoB,CAACM,MAAM,CAACR,UAAU,CAACM,MAAM,CAAC,KAAK,GAAI,CAAC,CAAA;EAE9D,EAAA,IAAII,WAAW,GAAG;MAChBH,QAAQ;MACRE,SAAS;EACTpK,IAAAA,eAAAA;KACD,CAAA;EAED,EAAA,IAAIsK,WAAW,GAAGJ,QAAQ,GAAGnB,eAAe,GAAG1L,SAAS,CAAA;EAExD,EAAA,IAAI4L,SAA6B,CAAA;EACjC,EAAA,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;EACvCD,IAAAA,SAAS,GAAGC,aAAa,CAACmB,WAAW,CAAC,CAAA;EACxC,GAAC,MAAM;EACL;EACA;EACA;EACA;EACA;EACApB,IAAAA,SAAS,GAAG,CACVC,aAAa,EACbgB,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAC1BE,SAAS,GAAG,SAAS,GAAG,IAAI,EAC5BpK,eAAe,GAAG,eAAe,GAAG,IAAI,CACzC,CACEuK,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC,CAAA;EACd,GAAA;EAEA,EAAA,IAAIrB,KAAK,GACP,OAAOC,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACgB,WAAW,CAAC,GAAGhB,SAAS,CAAA;IAEtE,oBACEvJ,gBAAA,CAAA/D,aAAA,CAAC4K,IAAI,EAAA7I,QAAA,KACCsJ,IAAI,EAAA;EACR,IAAA,cAAA,EAAckD,WAAY;EAC1BrB,IAAAA,SAAS,EAAEA,SAAU;EACrBlC,IAAAA,GAAG,EAAEA,GAAI;EACTqC,IAAAA,KAAK,EAAEA,KAAM;EACbzE,IAAAA,EAAE,EAAEA,EAAG;EACPwC,IAAAA,cAAc,EAAEA,cAAAA;KAEf,CAAA,EAAA,OAAOlB,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACoE,WAAW,CAAC,GAAGpE,QACtD,CAAC,CAAA;EAEX,CACF,EAAC;EAEY;IACX2C,OAAO,CAAC3I,WAAW,GAAG,SAAS,CAAA;EACjC,CAAA;;EAEA;EACA;EACA;;EA0CA;EACA;EACA;;EAGA;EACA;EACA;;EA2CA;EACA;EACA;EACA;EACA;EACA;AACO,QAAMyK,IAAI,gBAAG5K,gBAAK,CAAC8G,UAAU,CAClC,CAAA+D,KAAA,EAeEC,YAAY,KACT;IAAA,IAfH;QACEC,UAAU;QACVpG,QAAQ;QACRyC,cAAc;QACdrC,OAAO;QACPlG,KAAK;EACLlC,MAAAA,MAAM,GAAGvD,aAAa;QACtBwD,MAAM;QACNoO,QAAQ;QACR7D,QAAQ;QACRrC,kBAAkB;EAClBuC,MAAAA,cAAAA;EAEF,KAAC,GAAAwD,KAAA;EADII,IAAAA,KAAK,GAAA1D,6BAAA,CAAAsD,KAAA,EAAAK,UAAA,CAAA,CAAA;EAIV,EAAA,IAAIC,MAAM,GAAGC,SAAS,EAAE,CAAA;EACxB,EAAA,IAAIC,UAAU,GAAGC,aAAa,CAAC1O,MAAM,EAAE;EAAEuK,IAAAA,QAAAA;EAAS,GAAC,CAAC,CAAA;EACpD,EAAA,IAAIoE,UAA0B,GAC5B5O,MAAM,CAACjD,WAAW,EAAE,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM,CAAA;IAEjD,IAAI8R,aAAsD,GAAI1R,KAAK,IAAK;EACtEkR,IAAAA,QAAQ,IAAIA,QAAQ,CAAClR,KAAK,CAAC,CAAA;MAC3B,IAAIA,KAAK,CAAC+O,gBAAgB,EAAE,OAAA;MAC5B/O,KAAK,CAAC2R,cAAc,EAAE,CAAA;EAEtB,IAAA,IAAIC,SAAS,GAAI5R,KAAK,CAAgC6R,WAAW,CAC9DD,SAAqC,CAAA;EAExC,IAAA,IAAIE,YAAY,GACd,CAACF,SAAS,IAATA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAE1O,YAAY,CAAC,YAAY,CAAC,KACtCL,MAAM,CAAA;EAERwO,IAAAA,MAAM,CAACO,SAAS,IAAI5R,KAAK,CAAC+R,aAAa,EAAE;QACvCd,UAAU;EACVpO,MAAAA,MAAM,EAAEiP,YAAY;QACpBjH,QAAQ;QACRI,OAAO;QACPlG,KAAK;QACLsI,QAAQ;QACRrC,kBAAkB;EAClBuC,MAAAA,cAAAA;EACF,KAAC,CAAC,CAAA;KACH,CAAA;EAED,EAAA,oBACErH,gBAAA,CAAA/D,aAAA,CAAA,MAAA,EAAA+B,QAAA,CAAA;EACEiJ,IAAAA,GAAG,EAAE6D,YAAa;EAClBnO,IAAAA,MAAM,EAAE4O,UAAW;EACnB3O,IAAAA,MAAM,EAAEyO,UAAW;EACnBL,IAAAA,QAAQ,EAAE5D,cAAc,GAAG4D,QAAQ,GAAGQ,aAAAA;KAClCP,EAAAA,KAAK,CACV,CAAC,CAAA;EAEN,CACF,EAAC;EAEY;IACXL,IAAI,CAACzK,WAAW,GAAG,MAAM,CAAA;EAC3B,CAAA;EAOA;EACA;EACA;EACA;EACO,SAAS2L,iBAAiBA,CAAAC,MAAA,EAGN;IAAA,IAHO;MAChCC,MAAM;EACNC,IAAAA,UAAAA;EACsB,GAAC,GAAAF,MAAA,CAAA;EACvBG,EAAAA,oBAAoB,CAAC;MAAEF,MAAM;EAAEC,IAAAA,UAAAA;EAAW,GAAC,CAAC,CAAA;EAC5C,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;EAEa;IACXH,iBAAiB,CAAC3L,WAAW,GAAG,mBAAmB,CAAA;EACrD,CAAA;EACA;;EAEA;EACA;EACA;EAAA,IAEKgM,cAAc,0BAAdA,cAAc,EAAA;IAAdA,cAAc,CAAA,sBAAA,CAAA,GAAA,sBAAA,CAAA;IAAdA,cAAc,CAAA,WAAA,CAAA,GAAA,WAAA,CAAA;IAAdA,cAAc,CAAA,kBAAA,CAAA,GAAA,kBAAA,CAAA;IAAdA,cAAc,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;IAAdA,cAAc,CAAA,wBAAA,CAAA,GAAA,wBAAA,CAAA;EAAA,EAAA,OAAdA,cAAc,CAAA;EAAA,CAAA,CAAdA,cAAc,IAAA,EAAA,CAAA,CAAA;EAAA,IAQdC,mBAAmB,0BAAnBA,mBAAmB,EAAA;IAAnBA,mBAAmB,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;IAAnBA,mBAAmB,CAAA,aAAA,CAAA,GAAA,aAAA,CAAA;IAAnBA,mBAAmB,CAAA,sBAAA,CAAA,GAAA,sBAAA,CAAA;EAAA,EAAA,OAAnBA,mBAAmB,CAAA;EAAA,CAAA,CAAnBA,mBAAmB,IAMxB,EAAA,CAAA,CAAA;EAEA,SAASC,yBAAyBA,CAChCC,QAA8C,EAC9C;EACA,EAAA,OAAUA,QAAQ,GAAA,+FAAA,CAAA;EACpB,CAAA;EAEA,SAASC,oBAAoBA,CAACD,QAAwB,EAAE;EACtD,EAAA,IAAIE,GAAG,GAAGxM,gBAAK,CAACyH,UAAU,CAACnC,oCAAiB,CAAC,CAAA;EAC7C,EAAA,CAAUkH,GAAG,GAAbC,uBAAS,QAAMJ,yBAAyB,CAACC,QAAQ,CAAC,EAAzC,GAAA,KAAA,CAAA,CAAA;EACT,EAAA,OAAOE,GAAG,CAAA;EACZ,CAAA;EAEA,SAASE,kBAAkBA,CAACJ,QAA6B,EAAE;EACzD,EAAA,IAAIzN,KAAK,GAAGmB,gBAAK,CAACyH,UAAU,CAACjC,yCAAsB,CAAC,CAAA;EACpD,EAAA,CAAU3G,KAAK,GAAf4N,uBAAS,QAAQJ,yBAAyB,CAACC,QAAQ,CAAC,EAA3C,GAAA,KAAA,CAAA,CAAA;EACT,EAAA,OAAOzN,KAAK,CAAA;EACd,CAAA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAAS8J,mBAAmBA,CACjC9D,EAAM,EAAA8H,KAAA,EAgB4C;IAAA,IAflD;MACEvS,MAAM;EACN2K,IAAAA,OAAO,EAAE6H,WAAW;MACpB/N,KAAK;MACLiG,kBAAkB;MAClBqC,QAAQ;EACRE,IAAAA,cAAAA;EAQF,GAAC,GAAAsF,KAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,KAAA,CAAA;EAEN,EAAA,IAAIhI,QAAQ,GAAGkI,uBAAW,EAAE,CAAA;EAC5B,EAAA,IAAI1I,QAAQ,GAAGuF,uBAAW,EAAE,CAAA;EAC5B,EAAA,IAAItB,IAAI,GAAGqB,2BAAe,CAAC5E,EAAE,EAAE;EAAEsC,IAAAA,QAAAA;EAAS,GAAC,CAAC,CAAA;EAE5C,EAAA,OAAOnH,gBAAK,CAAC2C,WAAW,CACrB7I,KAAsC,IAAK;EAC1C,IAAA,IAAIK,sBAAsB,CAACL,KAAK,EAAEM,MAAM,CAAC,EAAE;QACzCN,KAAK,CAAC2R,cAAc,EAAE,CAAA;;EAEtB;EACA;EACA,MAAA,IAAI1G,OAAO,GACT6H,WAAW,KAAKrP,SAAS,GACrBqP,WAAW,GACXE,sBAAU,CAAC3I,QAAQ,CAAC,KAAK2I,sBAAU,CAAC1E,IAAI,CAAC,CAAA;QAE/CzD,QAAQ,CAACE,EAAE,EAAE;UACXE,OAAO;UACPlG,KAAK;UACLiG,kBAAkB;UAClBqC,QAAQ;EACRE,QAAAA,cAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;KACD,EACD,CACElD,QAAQ,EACRQ,QAAQ,EACRyD,IAAI,EACJwE,WAAW,EACX/N,KAAK,EACLzE,MAAM,EACNyK,EAAE,EACFC,kBAAkB,EAClBqC,QAAQ,EACRE,cAAc,CAElB,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAAS0F,eAAeA,CAC7BC,WAAiC,EACM;EACvCzQ,EAAAC,qBAAO,CACL,OAAOhC,eAAe,KAAK,WAAW,EACtC,uEACqE,GAAA,mEAAA,GAAA,wDACX,gDAE5D,CAAC,CAAA,CAAA;IAED,IAAIyS,sBAAsB,GAAGjN,gBAAK,CAACwC,MAAM,CAAClI,kBAAkB,CAAC0S,WAAW,CAAC,CAAC,CAAA;EAC1E,EAAA,IAAIE,qBAAqB,GAAGlN,gBAAK,CAACwC,MAAM,CAAC,KAAK,CAAC,CAAA;EAE/C,EAAA,IAAI2B,QAAQ,GAAGuF,uBAAW,EAAE,CAAA;EAC5B,EAAA,IAAInO,YAAY,GAAGyE,gBAAK,CAACsE,OAAO,CAC9B;EACE;EACA;EACA;IACAlJ,0BAA0B,CACxB+I,QAAQ,CAACoE,MAAM,EACf2E,qBAAqB,CAAC9J,OAAO,GAAG,IAAI,GAAG6J,sBAAsB,CAAC7J,OAChE,CAAC,EACH,CAACe,QAAQ,CAACoE,MAAM,CAClB,CAAC,CAAA;EAED,EAAA,IAAI5D,QAAQ,GAAGkI,uBAAW,EAAE,CAAA;IAC5B,IAAIM,eAAe,GAAGnN,gBAAK,CAAC2C,WAAW,CACrC,CAACyK,QAAQ,EAAEC,eAAe,KAAK;EAC7B,IAAA,MAAMC,eAAe,GAAGhT,kBAAkB,CACxC,OAAO8S,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC7R,YAAY,CAAC,GAAG6R,QAC5D,CAAC,CAAA;MACDF,qBAAqB,CAAC9J,OAAO,GAAG,IAAI,CAAA;EACpCuB,IAAAA,QAAQ,CAAC,GAAG,GAAG2I,eAAe,EAAED,eAAe,CAAC,CAAA;EAClD,GAAC,EACD,CAAC1I,QAAQ,EAAEpJ,YAAY,CACzB,CAAC,CAAA;EAED,EAAA,OAAO,CAACA,YAAY,EAAE4R,eAAe,CAAC,CAAA;EACxC,CAAA;;EASA;EACA;EACA;;EAqBA;EACA;EACA;;EASA,SAASI,4BAA4BA,GAAG;EACtC,EAAA,IAAI,OAAOvR,QAAQ,KAAK,WAAW,EAAE;EACnC,IAAA,MAAM,IAAIoB,KAAK,CACb,mDAAmD,GACjD,8DACJ,CAAC,CAAA;EACH,GAAA;EACF,CAAA;EAEA,IAAIoQ,SAAS,GAAG,CAAC,CAAA;EACjB,IAAIC,kBAAkB,GAAGA,MAAA,IAAA,GAAWC,MAAM,CAAC,EAAEF,SAAS,CAAC,GAAI,IAAA,CAAA;;EAE3D;EACA;EACA;EACA;EACO,SAASpC,SAASA,GAAmB;IAC1C,IAAI;EAAE1J,IAAAA,MAAAA;EAAO,GAAC,GAAG6K,oBAAoB,CAACJ,cAAc,CAACwB,SAAS,CAAC,CAAA;IAC/D,IAAI;EAAEjR,IAAAA,QAAAA;EAAS,GAAC,GAAGsD,gBAAK,CAACyH,UAAU,CAACC,oCAAiB,CAAC,CAAA;EACtD,EAAA,IAAIkG,cAAc,GAAGC,6BAAU,EAAE,CAAA;IAEjC,OAAO7N,gBAAK,CAAC2C,WAAW,CACtB,UAACvI,MAAM,EAAE0T,OAAO,EAAU;EAAA,IAAA,IAAjBA,OAAO,KAAA,KAAA,CAAA,EAAA;QAAPA,OAAO,GAAG,EAAE,CAAA;EAAA,KAAA;EACnBP,IAAAA,4BAA4B,EAAE,CAAA;MAE9B,IAAI;QAAE3Q,MAAM;QAAED,MAAM;QAAEL,OAAO;QAAEO,QAAQ;EAAEC,MAAAA,IAAAA;EAAK,KAAC,GAAGL,qBAAqB,CACrErC,MAAM,EACNsC,QACF,CAAC,CAAA;EAED,IAAA,IAAIoR,OAAO,CAACnJ,QAAQ,KAAK,KAAK,EAAE;QAC9B,IAAI5J,GAAG,GAAG+S,OAAO,CAAC/C,UAAU,IAAI0C,kBAAkB,EAAE,CAAA;EACpD/L,MAAAA,MAAM,CAACqM,KAAK,CAAChT,GAAG,EAAE6S,cAAc,EAAEE,OAAO,CAAClR,MAAM,IAAIA,MAAM,EAAE;UAC1DkI,kBAAkB,EAAEgJ,OAAO,CAAChJ,kBAAkB;UAC9CjI,QAAQ;UACRC,IAAI;EACJyO,QAAAA,UAAU,EAAEuC,OAAO,CAACnR,MAAM,IAAKA,MAAyB;EACxDqR,QAAAA,WAAW,EAAEF,OAAO,CAACxR,OAAO,IAAKA,OAAuB;UACxD0G,SAAS,EAAE8K,OAAO,CAAC9K,SAAAA;EACrB,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM;QACLtB,MAAM,CAACiD,QAAQ,CAACmJ,OAAO,CAAClR,MAAM,IAAIA,MAAM,EAAE;UACxCkI,kBAAkB,EAAEgJ,OAAO,CAAChJ,kBAAkB;UAC9CjI,QAAQ;UACRC,IAAI;EACJyO,QAAAA,UAAU,EAAEuC,OAAO,CAACnR,MAAM,IAAKA,MAAyB;EACxDqR,QAAAA,WAAW,EAAEF,OAAO,CAACxR,OAAO,IAAKA,OAAuB;UACxDyI,OAAO,EAAE+I,OAAO,CAAC/I,OAAO;UACxBlG,KAAK,EAAEiP,OAAO,CAACjP,KAAK;EACpBoP,QAAAA,WAAW,EAAEL,cAAc;UAC3B5K,SAAS,EAAE8K,OAAO,CAAC9K,SAAS;UAC5BqE,cAAc,EAAEyG,OAAO,CAACzG,cAAAA;EAC1B,OAAC,CAAC,CAAA;EACJ,KAAA;KACD,EACD,CAAC3F,MAAM,EAAEhF,QAAQ,EAAEkR,cAAc,CACnC,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACO,SAAStC,aAAaA,CAC3B1O,MAAe,EAAAsR,MAAA,EAEP;IAAA,IADR;EAAE/G,IAAAA,QAAAA;EAA6C,GAAC,GAAA+G,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IAErD,IAAI;EAAExR,IAAAA,QAAAA;EAAS,GAAC,GAAGsD,gBAAK,CAACyH,UAAU,CAACC,oCAAiB,CAAC,CAAA;EACtD,EAAA,IAAIyG,YAAY,GAAGnO,gBAAK,CAACyH,UAAU,CAAC2G,+BAAY,CAAC,CAAA;EACjD,EAAA,CAAUD,YAAY,GAAtB1B,uBAAS,CAAA,KAAA,EAAe,kDAAkD,CAAA,CAAjE,GAAA,KAAA,CAAA,CAAA;EAET,EAAA,IAAI,CAAC4B,KAAK,CAAC,GAAGF,YAAY,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;EAC5C;EACA;IACA,IAAInG,IAAI,GAAApK,QAAA,CAAQyL,EAAAA,EAAAA,2BAAe,CAAC7M,MAAM,GAAGA,MAAM,GAAG,GAAG,EAAE;EAAEuK,IAAAA,QAAAA;EAAS,GAAC,CAAC,CAAE,CAAA;;EAEtE;EACA;EACA;EACA,EAAA,IAAIhD,QAAQ,GAAGuF,uBAAW,EAAE,CAAA;IAC5B,IAAI9M,MAAM,IAAI,IAAI,EAAE;EAClB;EACA;EACAwL,IAAAA,IAAI,CAACG,MAAM,GAAGpE,QAAQ,CAACoE,MAAM,CAAA;;EAE7B;EACA;EACA;MACA,IAAIiG,MAAM,GAAG,IAAIhU,eAAe,CAAC4N,IAAI,CAACG,MAAM,CAAC,CAAA;EAC7C,IAAA,IAAIkG,WAAW,GAAGD,MAAM,CAAC7S,MAAM,CAAC,OAAO,CAAC,CAAA;MACxC,IAAI+S,kBAAkB,GAAGD,WAAW,CAACE,IAAI,CAAExT,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CAAA;EAC1D,IAAA,IAAIuT,kBAAkB,EAAE;EACtBF,MAAAA,MAAM,CAAClL,MAAM,CAAC,OAAO,CAAC,CAAA;QACtBmL,WAAW,CAAChE,MAAM,CAAEtP,CAAC,IAAKA,CAAC,CAAC,CAACK,OAAO,CAAEL,CAAC,IAAKqT,MAAM,CAAC5S,MAAM,CAAC,OAAO,EAAET,CAAC,CAAC,CAAC,CAAA;EACtE,MAAA,IAAIyT,EAAE,GAAGJ,MAAM,CAACK,QAAQ,EAAE,CAAA;EAC1BzG,MAAAA,IAAI,CAACG,MAAM,GAAGqG,EAAE,GAAOA,GAAAA,GAAAA,EAAE,GAAK,EAAE,CAAA;EAClC,KAAA;EACF,GAAA;EAEA,EAAA,IAAI,CAAC,CAAChS,MAAM,IAAIA,MAAM,KAAK,GAAG,KAAKyR,KAAK,CAACS,KAAK,CAACC,KAAK,EAAE;EACpD3G,IAAAA,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACG,MAAM,GACrBH,IAAI,CAACG,MAAM,CAACxD,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ,CAAA;EACd,GAAA;;EAEA;EACA;EACA;EACA;IACA,IAAIrI,QAAQ,KAAK,GAAG,EAAE;MACpB0L,IAAI,CAACC,QAAQ,GACXD,IAAI,CAACC,QAAQ,KAAK,GAAG,GAAG3L,QAAQ,GAAGsS,gBAAS,CAAC,CAACtS,QAAQ,EAAE0L,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAA;EAC3E,GAAA;IAEA,OAAOyE,sBAAU,CAAC1E,IAAI,CAAC,CAAA;EACzB,CAAA;EAUA;EAEA;EACA;EACA;EACA;EACO,SAAS6G,UAAUA,CAAAC,MAAA,EAE8B;EAAA,EAAA,IAAAC,cAAA,CAAA;IAAA,IAFhB;EACtCpU,IAAAA,GAAAA;EACgB,GAAC,GAAAmU,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IACtB,IAAI;EAAExN,YAAAA,QAAAA;EAAO,GAAC,GAAG6K,oBAAoB,CAACJ,cAAc,CAACiD,UAAU,CAAC,CAAA;EAChE,EAAA,IAAIvQ,KAAK,GAAG6N,kBAAkB,CAACN,mBAAmB,CAACgD,UAAU,CAAC,CAAA;EAC9D,EAAA,IAAI7M,WAAW,GAAGvC,gBAAK,CAACyH,UAAU,CAACrH,eAAe,CAAC,CAAA;EACnD,EAAA,IAAI0O,KAAK,GAAG9O,gBAAK,CAACyH,UAAU,CAAC2G,+BAAY,CAAC,CAAA;IAC1C,IAAIiB,OAAO,IAAAF,cAAA,GAAGL,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAACnE,MAAM,GAAG,CAAC,CAAC,qBAAvCgF,cAAA,CAAyCL,KAAK,CAACQ,EAAE,CAAA;IAE/D,CAAU/M,WAAW,GAArBkK,uBAAS,CAAA,KAAA,EAAA,kDAAA,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IACT,CAAUqC,KAAK,GAAfrC,uBAAS,CAAA,KAAA,EAAA,+CAAA,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EACT,EAAA,EACE4C,OAAO,IAAI,IAAI,CAAA,GADjB5C,uBAAS,CAAA,KAAA,EAAA,oEAAA,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;;EAKT;EACA;EACA;IACA,IAAI8C,UAAU,GAAG3O,SAAS,GAAGA,SAAS,EAAE,GAAG,EAAE,CAAA;EAC7C,EAAA,IAAI,CAACmK,UAAU,EAAEyE,aAAa,CAAC,GAAGxP,gBAAK,CAAC4B,QAAQ,CAAS7G,GAAG,IAAIwU,UAAU,CAAC,CAAA;EAC3E,EAAA,IAAIxU,GAAG,IAAIA,GAAG,KAAKgQ,UAAU,EAAE;MAC7ByE,aAAa,CAACzU,GAAG,CAAC,CAAA;EACpB,GAAC,MAAM,IAAI,CAACgQ,UAAU,EAAE;EACtB;EACAyE,IAAAA,aAAa,CAAC/B,kBAAkB,EAAE,CAAC,CAAA;EACrC,GAAA;;EAEA;IACAzN,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpBvC,IAAAA,QAAM,CAAC+N,UAAU,CAAC1E,UAAU,CAAC,CAAA;EAC7B,IAAA,OAAO,MAAM;EACX;EACA;EACA;EACArJ,MAAAA,QAAM,CAACgO,aAAa,CAAC3E,UAAU,CAAC,CAAA;OACjC,CAAA;EACH,GAAC,EAAE,CAACrJ,QAAM,EAAEqJ,UAAU,CAAC,CAAC,CAAA;;EAExB;IACA,IAAI4E,IAAI,GAAG3P,gBAAK,CAAC2C,WAAW,CAC1B,CAACqF,IAAY,EAAEnK,IAA8B,KAAK;EAChD,IAAA,CAAUwR,OAAO,GAAjB5C,uBAAS,CAAA,KAAA,EAAU,yCAAyC,CAAA,CAAnD,GAAA,KAAA,CAAA,CAAA;MACT/K,QAAM,CAACqM,KAAK,CAAChD,UAAU,EAAEsE,OAAO,EAAErH,IAAI,EAAEnK,IAAI,CAAC,CAAA;KAC9C,EACD,CAACkN,UAAU,EAAEsE,OAAO,EAAE3N,QAAM,CAC9B,CAAC,CAAA;EAED,EAAA,IAAIkO,UAAU,GAAGxE,SAAS,EAAE,CAAA;IAC5B,IAAID,MAAM,GAAGnL,gBAAK,CAAC2C,WAAW,CAC5B,CAACvI,MAAM,EAAEyD,IAAI,KAAK;EAChB+R,IAAAA,UAAU,CAACxV,MAAM,EAAA4D,QAAA,KACZH,IAAI,EAAA;EACP8G,MAAAA,QAAQ,EAAE,KAAK;EACfoG,MAAAA,UAAAA;EAAU,KAAA,CACX,CAAC,CAAA;EACJ,GAAC,EACD,CAACA,UAAU,EAAE6E,UAAU,CACzB,CAAC,CAAA;EAED,EAAA,IAAIC,WAAW,GAAG7P,gBAAK,CAACsE,OAAO,CAAC,MAAM;MACpC,IAAIuL,WAAW,gBAAG7P,gBAAK,CAAC8G,UAAU,CAChC,CAACmE,KAAK,EAAEhE,GAAG,KAAK;QACd,oBACEjH,gBAAA,CAAA/D,aAAA,CAAC2O,IAAI,EAAA5M,QAAA,KAAKiN,KAAK,EAAA;EAAEtG,QAAAA,QAAQ,EAAE,KAAM;EAACoG,QAAAA,UAAU,EAAEA,UAAW;EAAC9D,QAAAA,GAAG,EAAEA,GAAAA;EAAI,OAAA,CAAE,CAAC,CAAA;EAE1E,KACF,CAAC,CAAA;EACD,IAAa;QACX4I,WAAW,CAAC1P,WAAW,GAAG,cAAc,CAAA;EAC1C,KAAA;EACA,IAAA,OAAO0P,WAAW,CAAA;EACpB,GAAC,EAAE,CAAC9E,UAAU,CAAC,CAAC,CAAA;;EAEhB;IACA,IAAI5H,OAAO,GAAGtE,KAAK,CAACqE,QAAQ,CAAC4M,GAAG,CAAC/E,UAAU,CAAC,IAAIgF,mBAAY,CAAA;EAC5D,EAAA,IAAIvQ,IAAI,GAAG+C,WAAW,CAACuN,GAAG,CAAC/E,UAAU,CAAC,CAAA;EACtC,EAAA,IAAIiF,qBAAqB,GAAGhQ,gBAAK,CAACsE,OAAO,CACvC,MAAAtG,QAAA,CAAA;EACE4M,IAAAA,IAAI,EAAEiF,WAAW;MACjB1E,MAAM;EACNwE,IAAAA,IAAAA;EAAI,GAAA,EACDxM,OAAO,EAAA;EACV3D,IAAAA,IAAAA;EAAI,GAAA,CACJ,EACF,CAACqQ,WAAW,EAAE1E,MAAM,EAAEwE,IAAI,EAAExM,OAAO,EAAE3D,IAAI,CAC3C,CAAC,CAAA;EAED,EAAA,OAAOwQ,qBAAqB,CAAA;EAC9B,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAASC,WAAWA,GAAkC;EAC3D,EAAA,IAAIpR,KAAK,GAAG6N,kBAAkB,CAACN,mBAAmB,CAAC8D,WAAW,CAAC,CAAA;EAC/D,EAAA,OAAOzV,KAAK,CAAC0V,IAAI,CAACtR,KAAK,CAACqE,QAAQ,CAACjE,OAAO,EAAE,CAAC,CAAC/D,GAAG,CAACkV,MAAA,IAAA;EAAA,IAAA,IAAC,CAACrV,GAAG,EAAEoI,OAAO,CAAC,GAAAiN,MAAA,CAAA;MAAA,OAAApS,QAAA,KAC1DmF,OAAO,EAAA;EACVpI,MAAAA,GAAAA;EAAG,KAAA,CAAA,CAAA;EAAA,GACH,CAAC,CAAA;EACL,CAAA;EAEA,MAAMsV,8BAA8B,GAAG,+BAA+B,CAAA;EACtE,IAAIC,oBAA4C,GAAG,EAAE,CAAA;;EAErD;EACA;EACA;EACA,SAASpE,oBAAoBA,CAAAqE,MAAA,EAMrB;IAAA,IANsB;MAC5BvE,MAAM;EACNC,IAAAA,UAAAA;EAIF,GAAC,GAAAsE,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IACJ,IAAI;EAAE7O,YAAAA,QAAAA;EAAO,GAAC,GAAG6K,oBAAoB,CAACJ,cAAc,CAACqE,oBAAoB,CAAC,CAAA;IAC1E,IAAI;MAAEC,qBAAqB;EAAE3L,IAAAA,kBAAAA;EAAmB,GAAC,GAAG4H,kBAAkB,CACpEN,mBAAmB,CAACoE,oBACtB,CAAC,CAAA;IACD,IAAI;EAAE9T,IAAAA,QAAAA;EAAS,GAAC,GAAGsD,gBAAK,CAACyH,UAAU,CAACC,oCAAiB,CAAC,CAAA;EACtD,EAAA,IAAIvD,QAAQ,GAAGuF,uBAAW,EAAE,CAAA;EAC5B,EAAA,IAAI4E,OAAO,GAAGoC,sBAAU,EAAE,CAAA;EAC1B,EAAA,IAAI1G,UAAU,GAAG2G,yBAAa,EAAE,CAAA;;EAEhC;IACA3Q,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpBxG,IAAAA,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,QAAQ,CAAA;EAC3C,IAAA,OAAO,MAAM;EACXnT,MAAAA,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,MAAM,CAAA;OAC1C,CAAA;KACF,EAAE,EAAE,CAAC,CAAA;;EAEN;EACAC,EAAAA,WAAW,CACT7Q,gBAAK,CAAC2C,WAAW,CAAC,MAAM;EACtB,IAAA,IAAIqH,UAAU,CAACnL,KAAK,KAAK,MAAM,EAAE;EAC/B,MAAA,IAAI9D,GAAG,GAAG,CAACiR,MAAM,GAAGA,MAAM,CAAC7H,QAAQ,EAAEmK,OAAO,CAAC,GAAG,IAAI,KAAKnK,QAAQ,CAACpJ,GAAG,CAAA;EACrEuV,MAAAA,oBAAoB,CAACvV,GAAG,CAAC,GAAG0C,MAAM,CAACqT,OAAO,CAAA;EAC5C,KAAA;MACA,IAAI;EACFC,MAAAA,cAAc,CAACC,OAAO,CACpB/E,UAAU,IAAIoE,8BAA8B,EAC5CY,IAAI,CAACC,SAAS,CAACZ,oBAAoB,CACrC,CAAC,CAAA;OACF,CAAC,OAAO1Q,KAAK,EAAE;QACdpD,qBAAO,CACL,KAAK,EAAA,mGAAA,GAC+FoD,KAAK,GAAA,IAC3G,CAAC,CAAA,CAAA;EACH,KAAA;EACAnC,IAAAA,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,MAAM,CAAA;EAC3C,GAAC,EAAE,CAAC3E,UAAU,EAAED,MAAM,EAAEhC,UAAU,CAACnL,KAAK,EAAEsF,QAAQ,EAAEmK,OAAO,CAAC,CAC9D,CAAC,CAAA;;EAED;EACA,EAAA,IAAI,OAAOtS,QAAQ,KAAK,WAAW,EAAE;EACnC;MACAgE,gBAAK,CAAC+D,eAAe,CAAC,MAAM;QAC1B,IAAI;UACF,IAAIoN,gBAAgB,GAAGJ,cAAc,CAACK,OAAO,CAC3CnF,UAAU,IAAIoE,8BAChB,CAAC,CAAA;EACD,QAAA,IAAIc,gBAAgB,EAAE;EACpBb,UAAAA,oBAAoB,GAAGW,IAAI,CAACI,KAAK,CAACF,gBAAgB,CAAC,CAAA;EACrD,SAAA;SACD,CAAC,OAAOjV,CAAC,EAAE;EACV;EAAA,OAAA;EAEJ,KAAC,EAAE,CAAC+P,UAAU,CAAC,CAAC,CAAA;;EAEhB;EACA;MACAjM,gBAAK,CAAC+D,eAAe,CAAC,MAAM;EAC1B,MAAA,IAAIuN,qBAAkE,GACpEtF,MAAM,IAAItP,QAAQ,KAAK,GAAG,GACtB,CAACyH,QAAQ,EAAEmK,OAAO,KAChBtC,MAAM;EACJhO,MAAAA,QAAA,KAEKmG,QAAQ,EAAA;UACXkE,QAAQ,EACNpL,oBAAa,CAACkH,QAAQ,CAACkE,QAAQ,EAAE3L,QAAQ,CAAC,IAC1CyH,QAAQ,CAACkE,QAAAA;SAEbiG,CAAAA,EAAAA,OACF,CAAC,GACHtC,MAAM,CAAA;EACZ,MAAA,IAAIuF,wBAAwB,GAAG7P,QAAM,IAANA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAM,CAAE8P,uBAAuB,CAC5DlB,oBAAoB,EACpB,MAAM7S,MAAM,CAACqT,OAAO,EACpBQ,qBACF,CAAC,CAAA;EACD,MAAA,OAAO,MAAMC,wBAAwB,IAAIA,wBAAwB,EAAE,CAAA;OACpE,EAAE,CAAC7P,QAAM,EAAEhF,QAAQ,EAAEsP,MAAM,CAAC,CAAC,CAAA;;EAE9B;EACA;MACAhM,gBAAK,CAAC+D,eAAe,CAAC,MAAM;EAC1B;QACA,IAAI0M,qBAAqB,KAAK,KAAK,EAAE;EACnC,QAAA,OAAA;EACF,OAAA;;EAEA;EACA,MAAA,IAAI,OAAOA,qBAAqB,KAAK,QAAQ,EAAE;EAC7ChT,QAAAA,MAAM,CAACgU,QAAQ,CAAC,CAAC,EAAEhB,qBAAqB,CAAC,CAAA;EACzC,QAAA,OAAA;EACF,OAAA;;EAEA;QACA,IAAItM,QAAQ,CAACqE,IAAI,EAAE;EACjB,QAAA,IAAIkJ,EAAE,GAAG1V,QAAQ,CAAC2V,cAAc,CAC9BC,kBAAkB,CAACzN,QAAQ,CAACqE,IAAI,CAAC+F,KAAK,CAAC,CAAC,CAAC,CAC3C,CAAC,CAAA;EACD,QAAA,IAAImD,EAAE,EAAE;YACNA,EAAE,CAACG,cAAc,EAAE,CAAA;EACnB,UAAA,OAAA;EACF,SAAA;EACF,OAAA;;EAEA;QACA,IAAI/M,kBAAkB,KAAK,IAAI,EAAE;EAC/B,QAAA,OAAA;EACF,OAAA;;EAEA;EACArH,MAAAA,MAAM,CAACgU,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;OACtB,EAAE,CAACtN,QAAQ,EAAEsM,qBAAqB,EAAE3L,kBAAkB,CAAC,CAAC,CAAA;EAC3D,GAAA;EACF,CAAA;;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASgN,eAAeA,CAC7BC,QAA2C,EAC3CjE,OAA+B,EACzB;IACN,IAAI;EAAEkE,IAAAA,OAAAA;EAAQ,GAAC,GAAGlE,OAAO,IAAI,EAAE,CAAA;IAC/B9N,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpB,IAAA,IAAIpG,IAAI,GAAGmU,OAAO,IAAI,IAAI,GAAG;EAAEA,MAAAA,OAAAA;EAAQ,KAAC,GAAGzU,SAAS,CAAA;MACpDE,MAAM,CAACwU,gBAAgB,CAAC,cAAc,EAAEF,QAAQ,EAAElU,IAAI,CAAC,CAAA;EACvD,IAAA,OAAO,MAAM;QACXJ,MAAM,CAACyU,mBAAmB,CAAC,cAAc,EAAEH,QAAQ,EAAElU,IAAI,CAAC,CAAA;OAC3D,CAAA;EACH,GAAC,EAAE,CAACkU,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAA;EACzB,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASnB,WAAWA,CAClBkB,QAA6C,EAC7CjE,OAA+B,EACzB;IACN,IAAI;EAAEkE,IAAAA,OAAAA;EAAQ,GAAC,GAAGlE,OAAO,IAAI,EAAE,CAAA;IAC/B9N,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpB,IAAA,IAAIpG,IAAI,GAAGmU,OAAO,IAAI,IAAI,GAAG;EAAEA,MAAAA,OAAAA;EAAQ,KAAC,GAAGzU,SAAS,CAAA;MACpDE,MAAM,CAACwU,gBAAgB,CAAC,UAAU,EAAEF,QAAQ,EAAElU,IAAI,CAAC,CAAA;EACnD,IAAA,OAAO,MAAM;QACXJ,MAAM,CAACyU,mBAAmB,CAAC,UAAU,EAAEH,QAAQ,EAAElU,IAAI,CAAC,CAAA;OACvD,CAAA;EACH,GAAC,EAAE,CAACkU,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAA;EACzB,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASG,SAASA,CAAAC,MAAA,EAMf;IAAA,IANgB;MACjBC,IAAI;EACJxS,IAAAA,OAAAA;EAIF,GAAC,GAAAuS,MAAA,CAAA;EACC,EAAA,IAAIE,OAAO,GAAGC,sBAAU,CAACF,IAAI,CAAC,CAAA;IAE9BrS,gBAAK,CAACiE,SAAS,CAAC,MAAM;EACpB,IAAA,IAAIqO,OAAO,CAACzT,KAAK,KAAK,SAAS,EAAE;EAC/B,MAAA,IAAI2T,OAAO,GAAG/U,MAAM,CAACgV,OAAO,CAAC5S,OAAO,CAAC,CAAA;EACrC,MAAA,IAAI2S,OAAO,EAAE;EACX;EACA;EACA;EACAE,QAAAA,UAAU,CAACJ,OAAO,CAACE,OAAO,EAAE,CAAC,CAAC,CAAA;EAChC,OAAC,MAAM;UACLF,OAAO,CAACK,KAAK,EAAE,CAAA;EACjB,OAAA;EACF,KAAA;EACF,GAAC,EAAE,CAACL,OAAO,EAAEzS,OAAO,CAAC,CAAC,CAAA;IAEtBG,gBAAK,CAACiE,SAAS,CAAC,MAAM;MACpB,IAAIqO,OAAO,CAACzT,KAAK,KAAK,SAAS,IAAI,CAACwT,IAAI,EAAE;QACxCC,OAAO,CAACK,KAAK,EAAE,CAAA;EACjB,KAAA;EACF,GAAC,EAAE,CAACL,OAAO,EAAED,IAAI,CAAC,CAAC,CAAA;EACrB,CAAA;;EAIA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASzI,sBAAsBA,CAC7B/E,EAAM,EACNhH,IAAwC,EACxC;EAAA,EAAA,IADAA,IAAwC,KAAA,KAAA,CAAA,EAAA;MAAxCA,IAAwC,GAAG,EAAE,CAAA;EAAA,GAAA;EAE7C,EAAA,IAAIkE,SAAS,GAAG/B,gBAAK,CAACyH,UAAU,CAAC1H,qBAAqB,CAAC,CAAA;EAEvD,EAAA,EACEgC,SAAS,IAAI,IAAI,CAAAxF,GADnBkQ,uBAAS,CAEP,KAAA,EAAA,uFAAuF,GACrF,mEAAmE,EAH9D,GAAA,KAAA,CAAA,CAAA;IAMT,IAAI;EAAE/P,IAAAA,QAAAA;EAAS,GAAC,GAAG6P,oBAAoB,CACrCJ,cAAc,CAACvC,sBACjB,CAAC,CAAA;EACD,EAAA,IAAIxB,IAAI,GAAGqB,2BAAe,CAAC5E,EAAE,EAAE;MAAEsC,QAAQ,EAAEtJ,IAAI,CAACsJ,QAAAA;EAAS,GAAC,CAAC,CAAA;EAC3D,EAAA,IAAI,CAACpF,SAAS,CAAC7B,eAAe,EAAE;EAC9B,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EAEA,EAAA,IAAI0S,WAAW,GACb3V,oBAAa,CAAC8E,SAAS,CAAC2B,eAAe,CAAC2E,QAAQ,EAAE3L,QAAQ,CAAC,IAC3DqF,SAAS,CAAC2B,eAAe,CAAC2E,QAAQ,CAAA;EACpC,EAAA,IAAIwK,QAAQ,GACV5V,oBAAa,CAAC8E,SAAS,CAAC4B,YAAY,CAAC0E,QAAQ,EAAE3L,QAAQ,CAAC,IACxDqF,SAAS,CAAC4B,YAAY,CAAC0E,QAAQ,CAAA;;EAEjC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACA,OACEyK,gBAAS,CAAC1K,IAAI,CAACC,QAAQ,EAAEwK,QAAQ,CAAC,IAAI,IAAI,IAC1CC,gBAAS,CAAC1K,IAAI,CAACC,QAAQ,EAAEuK,WAAW,CAAC,IAAI,IAAI,CAAA;EAEjD,CAAA;;EAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}