# Design System Component Implementation Tasks

## Task 1: Design Tokens Setup

### Objective
Set up the foundation design tokens based on the Figma design system analysis.

### Files to Create/Modify
- `shared/styles/tokens.ts` - Design token definitions
- `apps/web/tailwind.config.js` - Tailwind configuration
- `apps/zalo/tailwind.config.js` - Tailwind configuration
- `shared/styles/fonts.css` - Font definitions

### Implementation Details

#### 1. Design Tokens (`shared/styles/tokens.ts`)
```typescript
export const designTokens = {
  colors: {
    primary: {
      pink: '#F65D79',
      white: '#FFFFFF',
      black: '#1A1818',
    },
    secondary: {
      yellow: {
        70: '#F9DB5B',
        90: '#88700c',
        40: '#fadd62',
      },
    },
    grey: {
      1: '#5A5A5A',
      2: '#9A9A9A',
      3: '#CACACA',
      5: '#ECECEC',
      6: '#F8F8F8',
    },
  },
  typography: {
    fontFamily: {
      archia: ['Archia', 'sans-serif'],
    },
    fontSize: {
      10: '10px',
      12: '12px',
      14: '14px',
      18: '18px',
      24: '24px',
    },
    fontWeight: {
      regular: 400,
      medium: 500,
      demibold: 600,
      bold: 700,
    },
    lineHeight: {
      16: '16px',
      18: '18px',
      22: '22px',
      24: '24px',
      32: '32px',
    },
  },
  spacing: {
    0: '0px',
    3: '6px',
    6: '16px',
  },
} as const;
```

#### 2. Tailwind Configuration Updates
```javascript
// Extend both apps/web/tailwind.config.js and apps/zalo/tailwind.config.js
module.exports = {
  content: [
    // ... existing content
  ],
  theme: {
    extend: {
      colors: {
        'primary-pink': '#F65D79',
        'primary-white': '#FFFFFF',
        'primary-black': '#1A1818',
        'secondary-yellow-70': '#F9DB5B',
        'secondary-yellow-90': '#88700c',
        'secondary-yellow-40': '#fadd62',
        'grey-1': '#5A5A5A',
        'grey-2': '#9A9A9A',
        'grey-3': '#CACACA',
        'grey-5': '#ECECEC',
        'grey-6': '#F8F8F8',
      },
      fontFamily: {
        'archia': ['Archia', 'sans-serif'],
      },
      fontSize: {
        '10': '10px',
        '12': '12px',
        '14': '14px',
        '18': '18px',
        '24': '24px',
      },
      lineHeight: {
        '16': '16px',
        '18': '18px',
        '22': '22px',
        '24': '24px',
        '32': '32px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
};
```

---

## Task 2: Button Component Implementation

### Objective
Implement a comprehensive Button component with all variants seen in the design system.

### Files to Create
- `shared/components/ui/Button/Button.tsx`
- `shared/components/ui/Button/Button.stories.tsx`
- `shared/components/ui/Button/index.ts`

### Component Specifications

#### Button Variants
1. **Primary** - Pink background (`bg-primary-pink`)
2. **Secondary** - White background with pink border
3. **Outline** - Transparent background with pink border
4. **Text** - No background, pink text
5. **Disabled** - Greyed out state

#### Button Sizes
1. **Small** - `px-3 py-1.5 text-12`
2. **Medium** - `px-4 py-2 text-14`
3. **Large** - `px-6 py-3 text-18`

#### Implementation Template
```typescript
import React from 'react';
import { isZaloMiniApp } from '../../../utils/platform';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  onClick,
  className = '',
  type = 'button',
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-archia font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variantClasses = {
    primary: 'bg-primary-pink text-primary-white hover:bg-opacity-90 focus:ring-primary-pink',
    secondary: 'bg-primary-white text-primary-pink border border-primary-pink hover:bg-grey-6 focus:ring-primary-pink',
    outline: 'bg-transparent text-primary-pink border border-primary-pink hover:bg-primary-pink hover:text-primary-white focus:ring-primary-pink',
    text: 'bg-transparent text-primary-pink hover:bg-grey-6 focus:ring-primary-pink',
  };
  
  const sizeClasses = {
    small: 'px-3 py-1.5 text-12 rounded-md',
    medium: 'px-4 py-2 text-14 rounded-lg',
    large: 'px-6 py-3 text-18 rounded-xl',
  };
  
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default Button;
```

---

## Task 3: Input Component Implementation

### Objective
Implement Input component with validation states and different types.

### Files to Create
- `shared/components/ui/Input/Input.tsx`
- `shared/components/ui/Input/Input.stories.tsx`
- `shared/components/ui/Input/index.ts`

### Input Specifications

#### Input States
1. **Normal** - Default state
2. **Focus** - Focused state with pink border
3. **Error** - Error state with red border
4. **Success** - Success state with green border
5. **Disabled** - Disabled state

#### Input Types
1. **Text** - Regular text input
2. **Email** - Email input with validation
3. **Password** - Password input with visibility toggle
4. **Number** - Number input
5. **Search** - Search input with icon

---

## Task 4: Rating Component Implementation

### Objective
Implement star rating component as seen in the design system.

### Files to Create
- `shared/components/ui/Rating/Rating.tsx`
- `shared/components/ui/Rating/Rating.stories.tsx`
- `shared/components/ui/Rating/index.ts`

### Rating Specifications
- 5-star rating system
- Interactive (clickable) and read-only modes
- Half-star support
- Customizable colors
- Size variants (small, medium, large)

---

## Task 5: Card Component Implementation

### Objective
Implement card component for content display.

### Files to Create
- `shared/components/ui/Card/Card.tsx`
- `shared/components/ui/Card/Card.stories.tsx`
- `shared/components/ui/Card/index.ts`

### Card Specifications
- Shadow variants
- Padding options
- Border radius options
- Hover states
- Loading states

---

## Task 6: Form Components Implementation

### Objective
Implement form-related components (Checkbox, Toggle, Select).

### Files to Create
- `shared/components/ui/Checkbox/Checkbox.tsx`
- `shared/components/ui/Toggle/Toggle.tsx`
- `shared/components/ui/Select/Select.tsx`
- Respective stories and index files

---

## Task 7: Navigation Components

### Objective
Implement navigation components (Tabs, Breadcrumb, Pagination).

### Files to Create
- `shared/components/ui/Tabs/Tabs.tsx`
- `shared/components/ui/Breadcrumb/Breadcrumb.tsx`
- `shared/components/ui/Pagination/Pagination.tsx`
- Respective stories and index files

---

## Task 8: Feedback Components

### Objective
Implement feedback components (Alert, Toast, Progress).

### Files to Create
- `shared/components/ui/Alert/Alert.tsx`
- `shared/components/ui/Toast/Toast.tsx`
- `shared/components/ui/Progress/Progress.tsx`
- Respective stories and index files

---

## Task 9: Icon System Setup

### Objective
Set up icon system for consistent icon usage across components.

### Files to Create
- `shared/components/ui/Icon/Icon.tsx`
- `shared/components/ui/Icon/icons.ts`
- `shared/components/ui/Icon/index.ts`

---

## Task 10: Typography Components

### Objective
Implement typography components for consistent text styling.

### Files to Create
- `shared/components/ui/Typography/Typography.tsx`
- `shared/components/ui/Typography/Typography.stories.tsx`
- `shared/components/ui/Typography/index.ts`

### Typography Variants
- **Header 1** - Bold, 24px, line-height 32px
- **Header 2** - DemiBold, 18px, line-height 24px
- **Body** - Regular/DemiBold, 14px, line-height 22px
- **Caption** - Regular/DemiBold, 10px, line-height 16px
- **Status** - Regular/DemiBold/Bold, 12px, line-height 18px

---

## Implementation Guidelines

### 1. Component Structure
Each component should follow the TapTap component template:
- TypeScript interfaces for props
- Platform detection when needed
- Tailwind CSS classes only
- Named and default exports
- Proper error handling

### 2. Storybook Integration
Each component should have comprehensive Storybook stories showing:
- All variants
- All sizes
- All states
- Interactive examples
- Accessibility examples

### 3. Testing
Each component should include:
- Unit tests for functionality
- Accessibility tests
- Platform-specific tests when applicable

### 4. Documentation
Each component should include:
- JSDoc comments
- Usage examples
- Props documentation
- Best practices

## Priority Order
1. Task 1: Design Tokens Setup (Foundation)
2. Task 2: Button Component (Most used)
3. Task 3: Input Component (Forms)
4. Task 10: Typography Components (Text styling)
5. Task 4: Rating Component (Specific to TapTap)
6. Task 5: Card Component (Layout)
7. Task 6: Form Components (User interaction)
8. Task 7: Navigation Components (Navigation)
9. Task 8: Feedback Components (User feedback)
10. Task 9: Icon System (Enhancement)
