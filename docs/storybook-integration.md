# Storybook Integration Documentation

## Overview

Storybook has been successfully integrated into the TapTap multi-platform project to provide a comprehensive component development and documentation environment. This setup enables developers to build, test, and document UI components in isolation while maintaining consistency across Web and Zalo platforms.

## Installation & Setup

### Dependencies Added
```json
{
  "devDependencies": {
    "@storybook/react": "^9.0.15",
    "@storybook/react-vite": "^9.0.15",
    "@storybook/addon-essentials": "^9.0.15",
    "@storybook/addon-interactions": "^9.0.15",
    "@storybook/addon-links": "^9.0.15",
    "storybook": "^9.0.15"
  }
}
```

### Project Structure
```
apps/web/
├── .storybook/
│   ├── main.ts              # Storybook configuration
│   └── preview.ts           # Global settings and decorators
├── src/
│   ├── Introduction.stories.tsx    # Welcome documentation
│   └── components/
│       ├── Layout.stories.tsx
│       └── LoadingSpinner.stories.tsx
└── package.json             # Updated with Storybook scripts

shared/components/ui/
├── Button/
│   └── Button.stories.tsx   # Shared component stories
└── Input/
    └── Input.stories.tsx
```

## Configuration Details

### Main Configuration (`apps/web/.storybook/main.ts`)
- **Framework**: React with Vite integration
- **Story locations**: Both `apps/web/src/**` and `shared/**` directories
- **Addons**: Essential addons for controls, docs, and interactions
- **TypeScript**: Full TypeScript support with react-docgen
- **Aliases**: Configured to match project structure
- **Process polyfill**: Browser compatibility for shared code

### Preview Configuration (`apps/web/.storybook/preview.ts`)
- **Tailwind CSS**: Integrated styling system
- **Backgrounds**: Light, dark, and gray options
- **Platform selector**: Web/Zalo context switching
- **Documentation**: Auto-generated docs with table of contents

## Available Scripts

### Development
```bash
# Start Storybook development server
yarn storybook                    # From root directory
yarn workspace @taptap/web storybook  # From specific workspace

# Access at: http://localhost:6006
```

### Build
```bash
# Build static Storybook for deployment
yarn build-storybook
yarn workspace @taptap/web build-storybook
```

## Component Stories Created

### Shared Components

#### Button Component (`shared/components/ui/Button/Button.stories.tsx`)
- **Primary, Secondary, Outline, Ghost** variants
- **Small, Medium, Large** sizes
- **Disabled and Loading** states
- **All Variants showcase** story
- **Interactive examples** with click actions
- **Comprehensive prop documentation**

#### Input Component (`shared/components/ui/Input/Input.stories.tsx`)
- **Text, Email, Password** input types
- **Small, Medium, Large** sizes
- **Label and Error** states
- **Required and Disabled** states
- **All sizes comparison** story
- **All states showcase** story

### Web-Specific Components

#### Layout Component (`apps/web/src/components/Layout.stories.tsx`)
- **Default layout** with sample content
- **Long content** scrolling behavior
- **Mobile and Tablet** responsive views
- **Fullscreen layout** demonstration

#### LoadingSpinner Component (`apps/web/src/components/LoadingSpinner.stories.tsx`)
- **Default spinner** centered display
- **Card integration** example
- **Full page loading** state
- **Multiple spinners** with different contexts

### Introduction Story (`apps/web/src/Introduction.stories.tsx`)
- **Welcome documentation** with project overview
- **Getting started** guide
- **Feature highlights** (multi-platform, design system, TypeScript, accessibility)
- **Component categories** overview
- **Visual layout** with cards and sections

## Features & Benefits

### Development Workflow
- **Isolated component development** without application context
- **Real-time prop testing** with interactive controls
- **Visual regression testing** capabilities
- **Multi-platform preview** with platform selector
- **Responsive design testing** with viewport controls

### Documentation
- **Auto-generated documentation** from TypeScript interfaces
- **Interactive examples** with editable props
- **Code snippets** for implementation
- **Design system documentation** in living format
- **Accessibility guidelines** and testing

### Testing Capabilities
- **Visual testing** of all component states
- **Interaction testing** with play functions
- **Accessibility testing** with addon tools
- **Cross-browser testing** support
- **Responsive behavior** validation

## Multi-Platform Support

### Platform Detection
The Storybook is configured with a platform selector that allows testing components in different contexts:
- **Web platform** - Standard web environment
- **Zalo platform** - Zalo Mini App context (future implementation)

### Shared Component Testing
Stories for shared components demonstrate how they work across both platforms while maintaining consistent behavior and styling.

## Best Practices Implemented

### Story Organization
- **Logical categorization** (Shared/, Web/, Zalo/)
- **Consistent naming** conventions
- **Comprehensive coverage** of all component variants
- **Meaningful story names** describing use cases

### Documentation Standards
- **Component descriptions** explaining purpose and usage
- **Prop documentation** with types and defaults
- **Story descriptions** for specific examples
- **Code examples** for implementation

### Technical Standards
- **TypeScript integration** for type safety
- **Tailwind CSS** for consistent styling
- **Accessibility compliance** with ARIA labels
- **Responsive design** considerations

## Integration with Development Workflow

### Component Development Process
1. Create component with TypeScript interfaces
2. Write comprehensive stories covering all variants
3. Test component behavior in Storybook
4. Document props and usage guidelines
5. Review visual design and interactions
6. Integrate component into application

### Maintenance
- Stories are updated automatically when component props change
- Documentation stays synchronized with code
- Visual regressions are caught during development
- Component API changes are immediately visible

## Future Enhancements

### Planned Additions
- **Zalo Mini App specific stories** with ZMP SDK integration
- **Visual regression testing** with Chromatic
- **Accessibility testing** with axe addon
- **Design tokens documentation** for color and spacing
- **Animation testing** for interactive components

### Deployment Options
- **Static hosting** for design team access
- **CI/CD integration** for automated updates
- **Team collaboration** features
- **Design handoff** workflows

## Troubleshooting

### Common Issues Resolved
- **Process polyfill** added for Node.js globals in browser
- **Path aliases** configured to match project structure
- **Tailwind CSS** properly imported for styling
- **TypeScript** configuration aligned with project settings

### Development Tips
- Use `yarn workspace @taptap/web storybook` for faster startup
- Stories automatically reload when components change
- Use the Controls panel to test different prop combinations
- Check the Actions panel for event handling verification

## Performance Considerations

### Optimizations Implemented
- **Tree shaking** for smaller bundle sizes
- **Lazy loading** of story content
- **Efficient re-rendering** with React optimization
- **Fast refresh** for development workflow

### Bundle Analysis
- Shared components are properly externalized
- Vite's efficient bundling reduces load times
- Static asset optimization for production builds

---

This Storybook integration provides a robust foundation for component development and documentation in the TapTap multi-platform project, ensuring consistency, quality, and excellent developer experience across the entire development team.
