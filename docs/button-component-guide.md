# Button Component Implementation Guide

## Overview
This guide provides detailed implementation instructions for the Button component based on the TapTap design system.

## Design Analysis
From the Figma design system, the Button component has the following specifications:

### Visual Variants
1. **Primary Button** - Pink background (`#F65D79`) with white text
2. **Secondary Button** - White background with pink border
3. **Outline Button** - Transparent background with pink border
4. **Text Button** - No background, pink text only
5. **Disabled Button** - Greyed out appearance

### States
- **Default** - Normal state
- **Hover** - Slightly darker/lighter background
- **Focus** - Focus ring with pink color
- **Active** - Pressed state
- **Disabled** - 50% opacity, cursor not allowed

### Sizes
- **Small** - `px-3 py-1.5 text-12 rounded-md`
- **Medium** - `px-4 py-2 text-14 rounded-lg`
- **Large** - `px-6 py-3 text-18 rounded-xl`

## Implementation

### 1. Component File Structure
```
shared/components/ui/Button/
├── Button.tsx           # Main component
├── Button.stories.tsx   # Storybook stories
└── index.ts            # Exports
```

### 2. Component Implementation

#### Button.tsx
```typescript
import React from 'react';
import { isZaloMiniApp } from '../../../utils/platform';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  fullWidth?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  className = '',
  type = 'button',
  fullWidth = false,
}) => {
  const isZalo = isZaloMiniApp();
  
  const baseClasses = 'inline-flex items-center justify-center font-archia font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variantClasses = {
    primary: 'bg-primary-pink text-primary-white hover:bg-opacity-90 focus:ring-primary-pink',
    secondary: 'bg-primary-white text-primary-pink border border-primary-pink hover:bg-grey-6 focus:ring-primary-pink',
    outline: 'bg-transparent text-primary-pink border border-primary-pink hover:bg-primary-pink hover:text-primary-white focus:ring-primary-pink',
    text: 'bg-transparent text-primary-pink hover:bg-grey-6 focus:ring-primary-pink',
  };
  
  const sizeClasses = {
    small: 'px-3 py-1.5 text-12 rounded-md',
    medium: 'px-4 py-2 text-14 rounded-lg',
    large: 'px-6 py-3 text-18 rounded-xl',
  };
  
  const widthClasses = fullWidth ? 'w-full' : '';
  
  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClasses} ${className}`;
  
  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };
  
  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;
```

#### Button.stories.tsx
```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'text'],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      control: 'boolean',
    },
    loading: {
      control: 'boolean',
    },
    fullWidth: {
      control: 'boolean',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    children: 'Primary Button',
    variant: 'primary',
  },
};

export const Secondary: Story = {
  args: {
    children: 'Secondary Button',
    variant: 'secondary',
  },
};

export const Outline: Story = {
  args: {
    children: 'Outline Button',
    variant: 'outline',
  },
};

export const Text: Story = {
  args: {
    children: 'Text Button',
    variant: 'text',
  },
};

export const Small: Story = {
  args: {
    children: 'Small Button',
    size: 'small',
  },
};

export const Medium: Story = {
  args: {
    children: 'Medium Button',
    size: 'medium',
  },
};

export const Large: Story = {
  args: {
    children: 'Large Button',
    size: 'large',
  },
};

export const Disabled: Story = {
  args: {
    children: 'Disabled Button',
    disabled: true,
  },
};

export const Loading: Story = {
  args: {
    children: 'Loading Button',
    loading: true,
  },
};

export const FullWidth: Story = {
  args: {
    children: 'Full Width Button',
    fullWidth: true,
  },
  parameters: {
    layout: 'padded',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-x-4">
        <Button variant="primary">Primary</Button>
        <Button variant="secondary">Secondary</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="text">Text</Button>
      </div>
      <div className="space-x-4">
        <Button size="small">Small</Button>
        <Button size="medium">Medium</Button>
        <Button size="large">Large</Button>
      </div>
      <div className="space-x-4">
        <Button disabled>Disabled</Button>
        <Button loading>Loading</Button>
      </div>
    </div>
  ),
};
```

#### index.ts
```typescript
export { Button } from './Button';
export default Button;
```

### 3. Usage Examples

#### Basic Usage
```typescript
import { Button } from '@taptap/shared/components/ui/Button';

// Primary button
<Button onClick={() => console.log('Clicked!')}>
  Click me
</Button>

// Secondary button
<Button variant="secondary" size="large">
  Large Secondary
</Button>

// Disabled button
<Button disabled>
  Disabled
</Button>

// Loading button
<Button loading>
  Loading...
</Button>
```

#### Form Usage
```typescript
import { Button } from '@taptap/shared/components/ui/Button';

<form onSubmit={handleSubmit}>
  <Button type="submit" fullWidth>
    Submit Form
  </Button>
</form>
```

#### Platform-Specific Usage
```typescript
import { Button } from '@taptap/shared/components/ui/Button';
import { isZaloMiniApp } from '@taptap/shared/utils/platform';

const MyComponent = () => {
  const isZalo = isZaloMiniApp();
  
  return (
    <Button 
      variant={isZalo ? 'primary' : 'secondary'}
      size={isZalo ? 'medium' : 'large'}
    >
      {isZalo ? 'Zalo Action' : 'Web Action'}
    </Button>
  );
};
```

## Testing

### Unit Tests
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  test('renders with correct text', () => {
    render(<Button>Test Button</Button>);
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled Button</Button>);
    expect(screen.getByText('Disabled Button')).toBeDisabled();
  });

  test('shows loading state', () => {
    render(<Button loading>Loading Button</Button>);
    expect(screen.getByText('Loading Button')).toBeDisabled();
  });
});
```

## Accessibility

### ARIA Support
- Proper button semantics
- Focus management
- Keyboard navigation
- Screen reader support

### Implementation Notes
- Use semantic `<button>` element
- Proper focus states with visible focus ring
- Keyboard support (Enter and Space keys)
- Proper disabled state handling

## Platform Considerations

### Web Platform
- Standard button behavior
- Mouse and keyboard interactions
- Focus management
- Form integration

### Zalo Platform
- Touch-friendly sizing
- Zalo-specific styling considerations
- Performance optimization for mobile
- Touch gesture support

## Best Practices

1. **Use semantic HTML** - Always use `<button>` element
2. **Proper loading states** - Show loading indicator for async actions
3. **Accessible focus** - Ensure focus is visible and properly managed
4. **Consistent sizing** - Use defined size variants
5. **Platform detection** - Consider platform-specific behavior when needed

This implementation provides a solid foundation for the Button component that follows TapTap's design system and development standards.
