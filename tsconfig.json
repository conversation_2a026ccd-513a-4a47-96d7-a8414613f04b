{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@shared/*": ["./shared/*"], "@taptap/shared": ["./shared/index"], "@taptap/web": ["./apps/web/src"], "@taptap/zalo": ["./apps/zalo/src"]}}, "include": ["shared/**/*", "apps/**/*", "packages/**/*"], "exclude": ["node_modules", "dist", "build"]}